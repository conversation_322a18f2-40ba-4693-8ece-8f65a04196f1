import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, SimpleChang<PERSON> } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, MessageService } from "primeng/api";
import { firstValueFrom } from "rxjs";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { KonektorResponseModel } from "src/verazial-common-frontend/core/general/konektor/data/model/konektor-response.model";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { StopTakePhotoUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/stop-take-photo.use-case";
import { TakePhotoUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/take-photo.use-case";
import { CreateStaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { CreateStaticResourceUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case";
import { DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/delete-static-resource-by-subject-id-and-name-and-number.use-case";
import { GetStaticResourcesBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
    selector: 'app-physical-data',
    templateUrl: './physical-data.component.html',
    styleUrl: './physical-data.component.css',
    providers: [MessageService, ConfirmationService]
})
export class PhysicalDataComponent implements OnInit, OnChanges {

    @Input() canReadAndWrite: boolean = false;
    @Input() userSubject: SubjectEntity | UserEntity | undefined;
    @Input() isUser: boolean = false;
    @Input() userIsVerified: boolean = false;
    @Input() subjectIsVerified: boolean = false;

    maxRecords = 5;

    loading: boolean = false;

    responsiveOptions: any[] | undefined;

    userSubjectDocuments: StaticResourceEntity[] = [];
    documentName: string = 'physical-data-document';
    userSubjectProfiles: StaticResourceEntity[] = [];
    profileName: string = 'physical-data-profile';
    userSubjectTattoos: StaticResourceEntity[] = [];
    tattooName: string = 'physical-data-tattoo';
    userSubjectScars: StaticResourceEntity[] = [];
    scarName: string = 'physical-data-scar';

    pDataPlaceholderDocument: string = 'verazial-common-frontend/assets/images/admin/physicalDataPlaceholder.svg';
    pDataPlaceholderProfiles: string = 'verazial-common-frontend/assets/images/admin/physicalDataPlaceholderProfiles.svg';
    pDataPlaceholderScars: string = 'verazial-common-frontend/assets/images/admin/physicalDataPlaceholderScars.svg';
    pDataPlaceholderTattoos: string = 'verazial-common-frontend/assets/images/admin/physicalDataPlaceholderTattoos.svg';

    showPhysicalDataDialog: boolean = false;
    selectedCurrentResult: StaticResourceEntity | undefined;

    constructor(
        private loggerService: ConsoleLoggerService,
        private localStorageService: LocalStorageService,
        private translateService: TranslateService,
        private messageService: MessageService,
        private auditTrailService: AuditTrailService,
        private getStaticResourcesBySubjectIdUseCase: GetStaticResourcesBySubjectIdUseCase,
        private createStaticResourceUseCase: CreateStaticResourceUseCase,
        private deleteStaticResourcesBySubjectIdAndNameAndNumberUseCase: DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase,
    ) {}

    /* Component Functions */

    ngOnInit() {
        this.responsiveOptions = [
            {
                breakpoint: '1199px',
                numVisible: 1,
                numScroll: 1
            },
            {
                breakpoint: '991px',
                numVisible: 2,
                numScroll: 1
            },
            {
                breakpoint: '767px',
                numVisible: 1,
                numScroll: 1
            }
        ];
        if (this.userSubject) {
            this.getUserSubjectPhysicalData();
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['userSubject'].currentValue) {
            this.getUserSubjectPhysicalData();
        }
    }

    /* Physical Data Functions */

    getUserSubjectPhysicalData() {
        this.loading = true;
        let userSubjectDocumentsTMP: StaticResourceEntity[] = [];
        let userSubjectProfilesTMP: StaticResourceEntity[] = [];
        let userSubjectTattoosTMP: StaticResourceEntity[] = [];
        let userSubjectScarsTMP: StaticResourceEntity[] = [];
        for (let i = 0; i < this.maxRecords; i++) {
            let staticResourceEntity = new StaticResourceEntity();
            staticResourceEntity.number = i;
            staticResourceEntity.createdAt = new Date();
            staticResourceEntity.content = '';
            userSubjectDocumentsTMP.push({ ... staticResourceEntity});
            userSubjectProfilesTMP.push({ ... staticResourceEntity});
            userSubjectTattoosTMP.push({ ... staticResourceEntity});
            userSubjectScarsTMP.push({ ... staticResourceEntity});
        }

        userSubjectDocumentsTMP.forEach(element => {
            element.name = this.documentName;
            element.content = this.pDataPlaceholderDocument;
        });
        userSubjectProfilesTMP.forEach(element => {
            element.name = this.profileName;
            element.content = this.pDataPlaceholderProfiles;
        });
        userSubjectTattoosTMP.forEach(element => {
            element.name = this.tattooName;
            element.content = this.pDataPlaceholderTattoos;
        });
        userSubjectScarsTMP.forEach(element => {
            element.name = this.scarName;
            element.content = this.pDataPlaceholderScars;
        });

        this.getStaticResourcesBySubjectIdUseCase.execute({ subjectId: this.userSubject?.id! }).then(
            (data) => {
                if (data) {
                    for (let staticResource of data) {
                        // Documents
                        if (staticResource.name?.includes(this.documentName)) {
                            userSubjectDocumentsTMP[staticResource.number!] = staticResource;
                        }
                        // Profiles
                        else if (staticResource.name?.includes(this.profileName)) {
                            userSubjectProfilesTMP[staticResource.number!] = staticResource;
                        }
                        // Tattoos
                        else if (staticResource.name?.includes(this.tattooName)) {
                            userSubjectTattoosTMP[staticResource.number!] = staticResource;
                        }
                        // Scars
                        else if (staticResource.name?.includes(this.scarName)) {
                            userSubjectScarsTMP[staticResource.number!] = staticResource;
                        }
                    }
                    this.userSubjectDocuments = userSubjectDocumentsTMP;
                    this.userSubjectProfiles = userSubjectProfilesTMP;
                    this.userSubjectTattoos = userSubjectTattoosTMP;
                    this.userSubjectScars = userSubjectScarsTMP;
                }
                this.loading = false;
            },
            (e) => {
                this.loggerService.error('Error Getting User/Subject Static Resources:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_downloading_image')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.loading = false;
            }
        );
    }

    onSubmitDelete(selectedCurrentResult: StaticResourceEntity) {
        if (selectedCurrentResult) {
            this.closeDialog();
            this.loading = true;
            const param = {
                subjectId: selectedCurrentResult.subjectId!,
                name: selectedCurrentResult.name!,
                number: selectedCurrentResult.number!
            }
            this.deleteStaticResourcesBySubjectIdAndNameAndNumberUseCase.execute(param).then(
                () => {
                    this.messageService.add({
                        severity: 'success',
                        summary: this.translateService.instant('content.successTitle'),
                        detail: this.translateService.instant('messages.success_general'),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.getUserSubjectPhysicalData();
                },
                (e) => {
                    this.loggerService.error('Error Deleting User/Subject Static Resource:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: 'error', value: JSON.stringify(e) },
                        { name: 'static_resource_name', value: param.name },
                        { name: 'static_resource_number', value: param.number.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_STA_RES, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_removing_image')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.loading = false;
                }
            );
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_data_to_save"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    onSubmitCreate(selectedCurrentResult: StaticResourceEntity) {
        if (selectedCurrentResult) {
            this.loading = true;
            let newStaticResource = new CreateStaticResourceEntity();
            // subjectId?: string;
            newStaticResource.subjectId = this.userSubject?.id!;
            // name?: string;
            newStaticResource.name = selectedCurrentResult.name;
            // number?: number;
            newStaticResource.number = selectedCurrentResult.number;
            // content?: string;
            newStaticResource.content = selectedCurrentResult.content;
            // async?: boolean;
            newStaticResource.async = false;
            this.createStaticResourceUseCase.execute({ createStaticResourceRequest: newStaticResource }).then(
                () => {
                    switch (newStaticResource.name) {
                        case this.documentName:
                            this.userSubjectDocuments[newStaticResource.number!] = { ...selectedCurrentResult };
                            break;
                        case this.profileName:
                            this.userSubjectProfiles[newStaticResource.number!] = { ...selectedCurrentResult };
                            break;
                        case this.tattooName:
                            this.userSubjectTattoos[newStaticResource.number!] = { ...selectedCurrentResult };
                            break;
                        case this.scarName:
                            this.userSubjectScars[newStaticResource.number!] = { ...selectedCurrentResult };
                            break;
                    }
                    const at_attributes: ExtraData[] = [
                        { name: 'static_resource_name', value: newStaticResource.name! },
                        { name: 'static_resource_number', value: newStaticResource.number!.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                    this.messageService.add({
                        severity: 'success',
                        summary: this.translateService.instant('content.successTitle'),
                        detail: this.translateService.instant('messages.success_general'),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.closeDialog();
                    this.getUserSubjectPhysicalData();
                },
                (e) => {
                    this.loggerService.error('Error Creating User/Subject Static Resource:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: 'error', value: JSON.stringify(e) },
                        { name: 'static_resource_name', value: newStaticResource.name! },
                        { name: 'static_resource_number', value: newStaticResource.number!.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_uploading_image')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.loading = false;
                }
            );
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_data_to_save"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    /* Camera Functions */

    onCameraResult(event: {action: string, staticResource: StaticResourceEntity}){
        switch (event.action) {
            case 'close':
                this.closeDialog();
                break;
            case 'delete':
                const at_attributes: ExtraData[] = [
                    { name: 'static_resource_name', value: event.staticResource.name! },
                    { name: 'static_resource_number', value: event.staticResource.number!.toString() }
                ];
                this.auditTrailService.auditTrailSelectReason(this.isUser ? ReasonTypeEnum.USER : ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_STA_RES, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete(event.staticResource); }, at_attributes);
                break;
            case 'create':
                this.onSubmitCreate(event.staticResource);
                break;
        }
    }

    openUserSubjectPhysicalData(staticResource: StaticResourceEntity) {
        this.selectedCurrentResult = { ...staticResource };
        if (
            this.selectedCurrentResult?.content == this.pDataPlaceholderDocument ||
            this.selectedCurrentResult?.content == this.pDataPlaceholderProfiles ||
            this.selectedCurrentResult?.content == this.pDataPlaceholderScars ||
            this.selectedCurrentResult?.content == this.pDataPlaceholderTattoos
        ) {
            this.selectedCurrentResult.content = '';
        }
        this.showPhysicalDataDialog = true;
    }

    async closeDialog() {
        this.selectedCurrentResult = undefined;
        this.showPhysicalDataDialog = false;
    }
}
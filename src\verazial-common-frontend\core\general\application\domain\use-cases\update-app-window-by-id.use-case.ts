import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationWindowEntity } from "../entities/application-window.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationWindowRepository } from "../repositories/application-window.repository";

export class UpdateAppWindowByIdUseCase implements UseCaseGrpc<ApplicationWindowEntity, SuccessResponse> {
    constructor(private applicationWindowRepository: ApplicationWindowRepository) { }
    execute(params: ApplicationWindowEntity): Promise<SuccessResponse> {
        return this.applicationWindowRepository.updateAppWindowById(params)
    }
}
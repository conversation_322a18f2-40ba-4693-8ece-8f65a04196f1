@import '@angular/material/prebuilt-themes/indigo-pink.css';



.main-container {
    margin: 10px 10px 10px 10px;
}

.main-progress {
    margin: -20px -20px 0px -20px
}

.container-plot {
    display: flex;
    margin: 10px;
    flex-direction: row;
    flex-wrap: wrap;
    height: auto;
}

.card-style {
    margin: 10px 10px 10px 10px;
}

.card-m {
    width: 400px;
}

.whole-card {
    width: 1200px !important;
    margin: 10px;
}

.searchButtonGroupSmall {
    background-color: #D7F6FF;
    border-radius: 12px;
    padding: 5px;
}

.p-card {
    width: 500px !important;
}

    .p-card .p-card-title {
        font-size: 18px;
        margin-bottom: .5rem;
    }

.divActions {
    display: flex;
    justify-content: center;
    padding: 0px 5px 0px 5px;
}

    .divActions div img {
        cursor: pointer;
    }

.tableField {
    cursor: pointer;
}

.table td {
    padding: 0px;
}

.filter {
    background-color: transparent;
    outline: none;
    border: none;
    color: #A0A0A0;
    width: 75%;
}

    .filter::placeholder {
        color: #A0A0A0;
    }

.ngx-pagination-wrapper {
    display: flex;
    justify-content: center;
}

.ngx-pagination-steps {
    margin-right: 10px;
}

.ngx-pagination {
    padding-left: 0px !important;
}

    .ngx-pagination li {
        border: none !important;
    }

    .ngx-pagination .current,
    .btn-info {
        background: #17a2b8 !important;
        border: none !important;
    }

        .ngx-pagination .current:hover {
            background: #17a2b8 !important;
            border: none !important;
        }

.centerpoint {
    position: absolute;
    top: 30%;
    left: 32%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

dialog {
    width: 400px;
    padding: 25px;
    border-radius: 20px;
    background: #EEFCFF;
}

.close-button-wrap {
    display: flex;
    position: absolute;
    left: 460px;
    cursor: pointer;
}

.close-button-img {
    height: 20px;
    width: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="style=fill"> <g id="close-circle"> <path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM8.46967 8.46967C8.76257 8.17678 9.23744 8.17678 9.53033 8.46967L12 10.9393L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967C15.8232 8.76257 15.8232 9.23744 15.5303 9.53033L13.0606 12L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7625 15.8232 14.4696 15.5303L12 13.0607L9.53033 15.5303C9.23743 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7625 8.46967 14.4696L10.9393 12L8.46967 9.53033C8.17678 9.23743 8.17678 8.76256 8.46967 8.46967Z" fill="black"></path> </g> </g> </g></svg>');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
}

.close-label {
    padding-top: 8px;
}

.notification {
    display: block;
    position: fixed;
    /* transform: translate(-5%,-50%); */
    /* top: 50%; */
    /* left: 50%; */
    right: 1%;
    top: 10%;
    /* bottom: 0; */
}

.centerDiv {
    flex-direction: row;
    display: flex;
    justify-content: center;
    align-items: center;
}

.searchButton {
    margin: 5px 15px 5px 15px;
}

.searchButtonNormal {
    display: block;
}

.searchButtonSmall {
    display: none;
}

.topBarItems {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    margin-top: 10px;
}

.topBarItemsDefault {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    margin-top: 0px;
}

.leftSideItems {
    display: flex;
    align-items: center;
}

.rightSideItems {
    display: flex;
    align-items: center;
}

.recrodsTitle {
    margin: 10px 10px 10px 0px;
    color: #0AB4BA;
    font-weight: bold;
    font-size: x-large;
    margin-right: 25px;
}

.searchButtonGroup {
    background-color: #D7F6FF;
    border-radius: 12px;
    padding: 15px;
}

.dateSelectorContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px;
}

.dateSelectorLabel {
    font-weight: bold;
    color: #495057;
    margin: 15px;
}

.error {
    color: #E24C4C;
}

.dateSelectorGroup {
    display: flex;
    flex-direction: column;
    align-items: start;
}

.tableCaption {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.exportButton {
    margin-left: 2px;
    margin-right: 2px;
}

@media (max-width: 939px) {
    .centerpoint {
        position: absolute;
        top: 30%;
        left: 25%;
        transform: translate(-50%, -50%);
        z-index: 1;
    }

    dialog {
        width: 350px;
        height: 200px;
        /*z-index: -1;*/
        border-radius: 20px;
        border-width: 1px;
    }
}

@media (max-width: 1450px) {
    .centerDiv {
        align-items: flex-end;
    }

    .dateSelectorContainer {
        flex-direction: column;
        align-items: start;
    }

    .dateSelectorLabel {
        margin: 5px;
    }
}

@media (max-width: 700px) {
    .searchButtonNormal {
        display: none;
    }

    .searchButtonSmall {
        display: block;
    }

    .searchButton {
        margin-top: 15px;
    }
}

@media (max-width: 790px) {
    .topBarItems {
        flex-direction: column;
        margin-top: 50px;
    }

    .topBarItemsDefault {
        flex-direction: column;
        margin-top: 0px;
    }

    .searchBar {
        width: inherit;
    }

    .buttonWithLable {
        display: none;
    }

    .buttonWithoutLable {
        display: block;
    }

    .searchButton {
        margin: 5px;
    }
}

@media (max-height: 520px) {
    .centerpoint {
        position: absolute;
        top: 30%;
        left: 25%;
        transform: translate(-50%, -50%);
        z-index: 1;
    }

    dialog {
        width: 350px;
        height: 200px;
        /*z-index: -1;*/
        border-radius: 20px;
        border-width: 1px;
    }
}
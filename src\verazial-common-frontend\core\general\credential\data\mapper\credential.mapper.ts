import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { CredentialModel } from "../model/credential.model";
import { CredentialEntity } from "../../domain/entity/credential.entity";

export class CredentialMapper extends Mapper<CredentialModel, CredentialEntity> {
    override mapFrom(param: CredentialModel): CredentialEntity {
        return {
            id: param._id,
            numId: param.numId,
            subjectApplicationId: param.subjectApplicationId,
            parameters: param.parameters
        }
    }
    override mapTo(param: CredentialEntity): CredentialModel {
        return {
            _id: param.id!!,
            numId: param.numId,
            subjectApplicationId: param.subjectApplicationId,
            parameters: param.parameters
        }
    }

}
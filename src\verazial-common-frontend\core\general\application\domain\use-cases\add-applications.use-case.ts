import { ApplicationEntity } from "../entities/application.entity";
import { ApplicationRepository } from "../repositories/application.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class AddApplicationsUseCase implements UseCaseGrpc<ApplicationEntity[], ApplicationEntity[]> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: ApplicationEntity[]): Promise<ApplicationEntity[]> {
        return this.applicationRepository.addApplications(params)
    }

}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EditFlowsComponent } from './edit-flows/edit-flows.component';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DragDropModule } from 'primeng/dragdrop';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TranslateModule } from '@ngx-translate/core';
import { AccordionModule } from 'primeng/accordion';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ChipsModule } from 'primeng/chips';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ContextMenuModule } from 'primeng/contextmenu';
import { SplitButtonModule } from 'primeng/splitbutton';
import { ListboxModule } from 'primeng/listbox';
import { SkeletonModule } from 'primeng/skeleton';
import { InputSwitchModule } from 'primeng/inputswitch';
import { MessagesModule } from 'primeng/messages';
import { ToastModule } from 'primeng/toast';

@NgModule({
  declarations: [
    EditFlowsComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    ButtonModule,
    InputTextModule,
    DragDropModule,
    DialogModule,
    InputTextareaModule,
    AccordionModule,
    DropdownModule,
    CheckboxModule,
    ChipsModule,
    ConfirmDialogModule,
    ContextMenuModule,
    SplitButtonModule,
    ListboxModule,
    SkeletonModule,
    InputSwitchModule,
    MessagesModule,
    ToastModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    EditFlowsComponent
  ]
})
export class EditFlowsModule { }

import { DataSourceParametersRepository } from "../repositories/data-source-parameters.repository";
import { DataSourceParametersEntity } from "../entities/data-source-parameters.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class AddAppDataSourceParamsUseCase implements UseCaseGrpc<DataSourceParametersEntity[], DataSourceParametersEntity[]> {
    constructor(private dataSourceParamsRepository: DataSourceParametersRepository) { }
    execute(params: DataSourceParametersEntity[]): Promise<DataSourceParametersEntity[]> {
        return this.dataSourceParamsRepository.addAppDataSourceParams(params);
    }
}
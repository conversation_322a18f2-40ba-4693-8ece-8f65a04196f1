// source: manager/settings/common/tenants_licenses.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var manager_settings_common_generic_data_pb = require('../../../manager/settings/common/generic_data_pb.js');
goog.object.extend(proto, manager_settings_common_generic_data_pb);
goog.exportSymbol('proto.ArrayOfTenantLicenses', null, global);
goog.exportSymbol('proto.ArrayOfTenantsLicenses', null, global);
goog.exportSymbol('proto.TenantsLicensesGrpcModel', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.TenantsLicensesGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.TenantsLicensesGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.TenantsLicensesGrpcModel.displayName = 'proto.TenantsLicensesGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfTenantLicenses = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfTenantLicenses.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfTenantLicenses, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfTenantLicenses.displayName = 'proto.ArrayOfTenantLicenses';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfTenantsLicenses = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfTenantsLicenses.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfTenantsLicenses, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfTenantsLicenses.displayName = 'proto.ArrayOfTenantsLicenses';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.TenantsLicensesGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.TenantsLicensesGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.TenantsLicensesGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TenantsLicensesGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
tenantid: jspb.Message.getFieldWithDefault(msg, 1, ""),
licensesinfo: (f = msg.getLicensesinfo()) && proto.ArrayOfTenantLicenses.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.TenantsLicensesGrpcModel}
 */
proto.TenantsLicensesGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.TenantsLicensesGrpcModel;
  return proto.TenantsLicensesGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.TenantsLicensesGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.TenantsLicensesGrpcModel}
 */
proto.TenantsLicensesGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTenantid(value);
      break;
    case 2:
      var value = new proto.ArrayOfTenantLicenses;
      reader.readMessage(value,proto.ArrayOfTenantLicenses.deserializeBinaryFromReader);
      msg.setLicensesinfo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.TenantsLicensesGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.TenantsLicensesGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.TenantsLicensesGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TenantsLicensesGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTenantid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLicensesinfo();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ArrayOfTenantLicenses.serializeBinaryToWriter
    );
  }
};


/**
 * optional string tenantId = 1;
 * @return {string}
 */
proto.TenantsLicensesGrpcModel.prototype.getTenantid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.TenantsLicensesGrpcModel} returns this
 */
proto.TenantsLicensesGrpcModel.prototype.setTenantid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional ArrayOfTenantLicenses licensesInfo = 2;
 * @return {?proto.ArrayOfTenantLicenses}
 */
proto.TenantsLicensesGrpcModel.prototype.getLicensesinfo = function() {
  return /** @type{?proto.ArrayOfTenantLicenses} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfTenantLicenses, 2));
};


/**
 * @param {?proto.ArrayOfTenantLicenses|undefined} value
 * @return {!proto.TenantsLicensesGrpcModel} returns this
*/
proto.TenantsLicensesGrpcModel.prototype.setLicensesinfo = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.TenantsLicensesGrpcModel} returns this
 */
proto.TenantsLicensesGrpcModel.prototype.clearLicensesinfo = function() {
  return this.setLicensesinfo(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.TenantsLicensesGrpcModel.prototype.hasLicensesinfo = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfTenantLicenses.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfTenantLicenses.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfTenantLicenses.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfTenantLicenses} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfTenantLicenses.toObject = function(includeInstance, msg) {
  var f, obj = {
licenseList: jspb.Message.toObjectList(msg.getLicenseList(),
    manager_settings_common_generic_data_pb.GenericDataGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfTenantLicenses}
 */
proto.ArrayOfTenantLicenses.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfTenantLicenses;
  return proto.ArrayOfTenantLicenses.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfTenantLicenses} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfTenantLicenses}
 */
proto.ArrayOfTenantLicenses.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new manager_settings_common_generic_data_pb.GenericDataGrpcModel;
      reader.readMessage(value,manager_settings_common_generic_data_pb.GenericDataGrpcModel.deserializeBinaryFromReader);
      msg.addLicense(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfTenantLicenses.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfTenantLicenses.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfTenantLicenses} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfTenantLicenses.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLicenseList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      manager_settings_common_generic_data_pb.GenericDataGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated GenericDataGrpcModel license = 1;
 * @return {!Array<!proto.GenericDataGrpcModel>}
 */
proto.ArrayOfTenantLicenses.prototype.getLicenseList = function() {
  return /** @type{!Array<!proto.GenericDataGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, manager_settings_common_generic_data_pb.GenericDataGrpcModel, 1));
};


/**
 * @param {!Array<!proto.GenericDataGrpcModel>} value
 * @return {!proto.ArrayOfTenantLicenses} returns this
*/
proto.ArrayOfTenantLicenses.prototype.setLicenseList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.GenericDataGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.GenericDataGrpcModel}
 */
proto.ArrayOfTenantLicenses.prototype.addLicense = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.GenericDataGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfTenantLicenses} returns this
 */
proto.ArrayOfTenantLicenses.prototype.clearLicenseList = function() {
  return this.setLicenseList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfTenantsLicenses.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfTenantsLicenses.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfTenantsLicenses.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfTenantsLicenses} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfTenantsLicenses.toObject = function(includeInstance, msg) {
  var f, obj = {
licensesList: jspb.Message.toObjectList(msg.getLicensesList(),
    proto.TenantsLicensesGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfTenantsLicenses}
 */
proto.ArrayOfTenantsLicenses.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfTenantsLicenses;
  return proto.ArrayOfTenantsLicenses.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfTenantsLicenses} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfTenantsLicenses}
 */
proto.ArrayOfTenantsLicenses.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.TenantsLicensesGrpcModel;
      reader.readMessage(value,proto.TenantsLicensesGrpcModel.deserializeBinaryFromReader);
      msg.addLicenses(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfTenantsLicenses.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfTenantsLicenses.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfTenantsLicenses} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfTenantsLicenses.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLicensesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.TenantsLicensesGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated TenantsLicensesGrpcModel licenses = 1;
 * @return {!Array<!proto.TenantsLicensesGrpcModel>}
 */
proto.ArrayOfTenantsLicenses.prototype.getLicensesList = function() {
  return /** @type{!Array<!proto.TenantsLicensesGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.TenantsLicensesGrpcModel, 1));
};


/**
 * @param {!Array<!proto.TenantsLicensesGrpcModel>} value
 * @return {!proto.ArrayOfTenantsLicenses} returns this
*/
proto.ArrayOfTenantsLicenses.prototype.setLicensesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.TenantsLicensesGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.TenantsLicensesGrpcModel}
 */
proto.ArrayOfTenantsLicenses.prototype.addLicenses = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.TenantsLicensesGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfTenantsLicenses} returns this
 */
proto.ArrayOfTenantsLicenses.prototype.clearLicensesList = function() {
  return this.setLicensesList([]);
};


goog.object.extend(exports, proto);

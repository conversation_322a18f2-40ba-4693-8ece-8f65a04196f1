import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FlowsPageRoutingModule } from './flows-page-routing.module';
import { FlowsPageComponent } from './flows-page/flows-page.component';
import { ListFlowsModule } from '../../components/list-flows/list-flows.module';
import { EditFlowsModule } from '../../components/edit-flows/edit-flows.module';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { DialogModule } from 'primeng/dialog';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [
    FlowsPageComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Routing */
    FlowsPageRoutingModule,
    /* PrimeNG */
    DialogModule,
    /* Custom */
    EmptyModule,
    ListFlowsModule,
    EditFlowsModule,
    LoadingSpinnerModule,
  ],
  exports: [
    FlowsPageComponent
  ]
})
export class FlowsPageModule { }

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuditPageRoutingModule } from './audit-page-routing.module';
import { AuditPageComponent } from './audit-page/audit-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { VirtualScrollerModule } from 'primeng/virtualscroller';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { ListActionsModule } from '../../components/list-actions/list-actions.module';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
  declarations: [
    AuditPageComponent
  ],
  imports: [
    CommonModule,
    AuditPageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    VirtualScrollerModule,
    ProgressSpinnerModule,
    TableModule,
    DropdownModule,
    ProgressBarModule,
    SkeletonModule,
    InputTextModule,

    ListActionsModule,
    DialogModule,
  ],
  exports: [
    AuditPageComponent
  ]
})
export class AuditPageModule { }

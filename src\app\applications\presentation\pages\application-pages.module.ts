import { CommonModule } from '@angular/common';
import { ApplicationPagesRoutingModule } from './application-pages-routing.module';
import { UserApplicationsPageComponent } from './user-applications-page/user-applications-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgModule } from '@angular/core';
import { TableModule } from 'primeng/table';
import { AccordionModule } from 'primeng/accordion';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';
import { PanelModule } from 'primeng/panel';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';
import { RippleModule } from 'primeng/ripple';
import { PasswordModule } from 'primeng/password';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SidebarModule } from 'primeng/sidebar';
import { ContextMenuModule } from 'primeng/contextmenu';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { CalendarModule } from 'primeng/calendar';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { ListUserSubjectModule } from 'src/app/user-subject/presentation/components/list-user-subject/list-user-subject.module';
import { ApplicationsPageComponent } from './applications-page/applications-page.component';
import { ApplicationsListModule } from '../components/applications/applications-list/applications-list.module';
import { TagModule } from 'primeng/tag';

@NgModule({
  declarations: [
    UserApplicationsPageComponent,
    ApplicationsPageComponent,
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Routing */
    ApplicationPagesRoutingModule,
    /* Table module */
    TableModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    /* Other */
    NgMultiSelectDropDownModule,
    /* PrimeNG */
    AccordionModule,
    DropdownModule,
    InputTextModule,
    CheckboxModule,
    PanelModule,
    ButtonModule,
    DialogModule,
    ToastModule,
    SidebarModule,
    RippleModule,
    PasswordModule,
    InputSwitchModule,
    ConfirmDialogModule,
    ContextMenuModule,
    IconFieldModule,
    InputIconModule,
    CalendarModule,
    TagModule,
    /* Custom */
    LoadingSpinnerModule,
    ListUserSubjectModule,
    ApplicationsListModule,
  ]
})
export class ApplicationPagesModule { }

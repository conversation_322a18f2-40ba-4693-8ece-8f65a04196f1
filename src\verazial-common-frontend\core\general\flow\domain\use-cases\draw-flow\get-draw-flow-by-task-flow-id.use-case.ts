import { DrawFlowRepository } from "../../repository/draw-flow.repository";
import { DrawFlowEntity } from "../../entity/draw-flow.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetDrawFlowByTaskFlowIdUseCase implements UseCaseGrpc<{ taskFlowId: string }, DrawFlowEntity> {
    constructor(private drawFlowRepository: DrawFlowRepository) { }
    execute(params: { taskFlowId: string; }): Promise<DrawFlowEntity> {
        return this.drawFlowRepository.getDrawFlowByTaskFlowId(params);
    }
}
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ActionPageComponent } from './action-page/action-page.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';

const routes: Routes = [
  {path: '', component: ActionPageComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ActionPageRoutingModule { }

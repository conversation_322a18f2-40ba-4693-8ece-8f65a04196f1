<div>
    <p-table
        [value]="inputData"
        editMode="row"
        dataKey="id"
        [paginator]="true"
        [rows]="4"
        styleClass="p-datatable-sm"
        [tableStyle]="{'max-width': '50rem', 'min-width': '50rem'}"
        styleClass="fixed-table"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
        [globalFilterFields]="['parameter', 'value', 'type']"
        [sortField]="'parameter'" [sortOrder]="1">
        <ng-template pTemplate="header">
            <tr>
                <th class="fixed-column" pSortableColumn="parameter">{{ 'pass_datasource.parameter' | translate }}<p-sortIcon field="parameter"></p-sortIcon></th>
                <th class="fixed-column" pSortableColumn="type"> {{ 'content.type' | translate }} <p-sortIcon field="type"></p-sortIcon></th>
                <th class="fixed-column" pSortableColumn="value">{{ 'content.value' | translate }}<p-sortIcon field="value"></p-sortIcon></th>
                <th pFrozenColumn [frozen]="true"></th>
            </tr>
            <tr>
                <th>
                    <p-columnFilter type="text" field="parameter" [showMenu]="false" matchMode="contains">
                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                            <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                        </ng-template>
                    </p-columnFilter>
                </th>
                <th>
                    <p-columnFilter type="text" field="value" [showMenu]="false" matchMode="contains">
                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                            <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                        </ng-template>
                    </p-columnFilter>
                </th>
                <th>
                    <p-columnFilter type="text" field="type" [showMenu]="false" matchMode="contains">
                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                            <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                        </ng-template>
                    </p-columnFilter>
                </th>
                <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-data let-editing="editing" let-ri="rowIndex">
            <tr [pEditableRow]="data">
                <td showDelay="1000" pTooltip="{{data.parameter}}" tooltipPosition="top" class="ellipsis-cell">
                    <p-cellEditor>
                        <ng-template pTemplate="input">
                            <input pInputText type="text" [(ngModel)]="data.parameter">
                        </ng-template>
                        <ng-template pTemplate="output">
                            {{data.parameter}}
                        </ng-template>
                    </p-cellEditor>
                </td>
                <td showDelay="1000" pTooltip="{{data.type}}" tooltipPosition="top" class="ellipsis-cell">
                    <p-cellEditor>
                        <ng-template pTemplate="input">
                            <p-dropdown
                                [options]="listDataSourceParamTypes"
                                appendTo="body"
                                [(ngModel)]="data.type"
                                [style]="{'width':'100%'}"
                            >
                            </p-dropdown>
                        </ng-template>
                        <ng-template pTemplate="output">
                            {{data.type}}
                        </ng-template>
                    </p-cellEditor>
                </td>
                <td showDelay="1000" pTooltip="{{data.value}}" tooltipPosition="top" class="ellipsis-cell">
                    <p-cellEditor>
                        <ng-template pTemplate="input">
                            <input pInputText type="text" [(ngModel)]="data.value" required>
                        </ng-template>
                        <ng-template pTemplate="output">
                            {{data.value}}
                        </ng-template>
                    </p-cellEditor>
                </td>
                <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                    <div *ngIf="readAndWritePermissions" class="flex align-items-center justify-content-center gap-2">
                        <button *ngIf="!editing" pButton pRipple type="button" pInitEditableRow icon="pi pi-pencil" (click)="onRowEditInit(data)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                        <button *ngIf="!editing" pButton pRipple type="button" icon="pi pi-trash" (click)="onRowDelete(data)"  [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                        <button *ngIf="editing" pButton pRipple type="button" pSaveEditableRow icon="pi pi-save" (click)="onRowEditSave(data)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                        <button *ngIf="editing" pButton pRipple type="button" pCancelEditableRow icon="pi pi-times" (click)="onRowEditCancel(data, ri)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                    </div>
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td colspan="4" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
            </tr>
        </ng-template>
    </p-table>
</div>

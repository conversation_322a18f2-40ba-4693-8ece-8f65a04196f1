import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';
import { LocationsPageComponent } from './locations-page/locations-page.component';

const routes: Routes = [
  {
    path: '',
    component: LocationsPageComponent,
    canActivate: [AuthGuard, NavigationGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LocationsPageRoutingModule { }

import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { UnenrollIdentityResponseEntity } from "../entity/unenroll-identity.entity";
import { ApiBinderRepository } from "../repository/api-binder.repository";

export class UnenrollIdentityUseCase implements UseCaseGrpc<{ numId: string }, UnenrollIdentityResponseEntity> {
    constructor(private apiBinderRepository: ApiBinderRepository) { }
    execute(params: { numId: string; }): Promise<UnenrollIdentityResponseEntity> {
        return this.apiBinderRepository.unenrollIdentity(params);
    }
}
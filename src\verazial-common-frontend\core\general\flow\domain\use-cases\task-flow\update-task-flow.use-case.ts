import { TaskFlowRepository } from "../../repository/task-flow.repository";
import { TaskFlowEntity } from "../../entity/task-flow.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateTaskFlowUseCase implements UseCaseGrpc<{ taskFlow: TaskFlowEntity }, TaskFlowEntity> {
    constructor(private taskFlowRepository: TaskFlowRepository) { }
    execute(params: { taskFlow: TaskFlowEntity; }): Promise<TaskFlowEntity> {
        return this.taskFlowRepository.updateTaskFlow(params);
    }
}
import { RoleTypeGrpc } from "src/verazial-common-frontend/core/generated/role/role_pb";
import { RoleType } from "../../role/common/enum/role-type.enum";

export function toRoleTypeEnum(roleType?: RoleTypeGrpc): RoleType {
    switch (roleType) {
        case RoleTypeGrpc.SUBJECT:
            return RoleType.SUBJECT;
        case RoleTypeGrpc.USER:
            return RoleType.USER;
        default:
            throw new Error('Role type not found');
    }
}

export function toRoleTypeGrpc(roleType?: RoleType): RoleTypeGrpc {
    switch (roleType) {
        case RoleType.SUBJECT:
            return RoleTypeGrpc.SUBJECT;
        case RoleType.USER:
            return RoleTypeGrpc.USER;
        default:
            throw new Error('Role type not found');
    }
}
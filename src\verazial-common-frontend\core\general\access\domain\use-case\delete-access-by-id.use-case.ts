import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AccessRepository } from "../repository/access.repository";

export class DeleteAccessByIdUseCase implements UseCaseGrpc<{ id: number }, SuccessResponse> {
    constructor(private accessRepository: AccessRepository) { }
    execute(params: { id: number; }): Promise<SuccessResponse> {
        return this.accessRepository.deleteAccessById(params)
    }
}
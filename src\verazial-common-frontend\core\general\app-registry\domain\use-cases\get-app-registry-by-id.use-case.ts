import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AppRegistryRepository } from "../repositories/app-registry.repository";
import { AppRegistryEntity } from "../entities/app-registry.entity";

export class GetAppRegistryByIdUseCase implements UseCaseGrpc<{ id: string }, AppRegistryEntity> {
    constructor(
        private appRegistryRepository: AppRegistryRepository
    ) { }
    execute(params: { id: string }): Promise<AppRegistryEntity> {
        return this.appRegistryRepository.getAppRegistryById(params);
    }
}
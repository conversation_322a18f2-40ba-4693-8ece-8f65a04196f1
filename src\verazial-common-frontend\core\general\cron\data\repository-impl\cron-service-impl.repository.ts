import { Injectable } from "@angular/core";
import { CronServiceRepository } from "../../domain/repository/cron-service.repository";
import { ScheduleJobRequestMapper } from "../mapper/schedule-job-request.mapper";
import { HttpClient } from "@angular/common/http";
import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";
import { CoreCronServiceClient } from "src/verazial-common-frontend/core/generated/cron/Cron-serviceServiceClientPb";
import { environment } from "src/environments/environment";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { StringParam } from "src/verazial-common-frontend/core/generated/util_pb";
import { ScheduleJobRequestModel } from "../../common/models/schedule-job-request.model";

@Injectable({
    providedIn: 'root'
})
export class CronServiceRepositoryImpl extends CronServiceRepository {
    scheduleJobRequestMapper = new ScheduleJobRequestMapper();

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override triggerJobSchedulerInit(): Promise<SuccessResponse> {
        let success!: SuccessResponse;

        let coreCronServiceClient = new CoreCronServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreCronServiceClient.triggerJobSchedulerInit(new Empty, null, (error, response) => {
                if (error) {
                    let failure: FailureResponse = {
                        code: error.code,
                        message: error.message
                    };
                    reject(failure);
                }
                else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.getSuccess()?.getSuccess()!
                        };
                        resolve(success);
                    }
                    else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    }
                }
            });
        });

    }

    override triggerJobById(params: { id: string; }): Promise<SuccessResponse> {
        let idRequest = new StringParam();
        idRequest.setParameter(params.id);

        let coreCronServiceClient = new CoreCronServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreCronServiceClient.triggerJobById(idRequest, null, (error, response) => {
                if (error) {
                    let failure: FailureResponse = {
                        code: error.code,
                        message: error.message
                    };
                    reject(failure);
                }
                else {
                    if (response.hasSuccess()) {
                        let success: SuccessResponse = {
                            success: response.getSuccess()?.getSuccess()!
                        };
                        resolve(success);
                    }
                    else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    }
                }
            });
        });
    }

    override initJobScheduleByJobId(params: ScheduleJobRequestModel): Promise<SuccessResponse> {
        let request = this.scheduleJobRequestMapper.mapTo(params);

        let coreCronServiceClient = new CoreCronServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreCronServiceClient.initJobScheduleByJobId(request, null, (error, response) => {
                if (error) {
                    let failure: FailureResponse = {
                        code: error.code,
                        message: error.message
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        let success: SuccessResponse = {
                            success: response.getSuccess()?.getSuccess()!
                        };
                        resolve(success);
                    }
                    else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    }
                }
            });
        });
    }

    override removeJobScheduleByJobId(params: { id: string; }): Promise<SuccessResponse> {
        let idRequest = new StringParam();
        idRequest.setParameter(params.id);

        let coreCronServiceClient = new CoreCronServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreCronServiceClient.removeJobScheduleByJobId(idRequest, null, (error, response) => {
                if (error) {
                    let failure: FailureResponse = {
                        code: error.code,
                        message: error.message
                    };
                    reject(failure);
                }
                else {
                    if (response.hasSuccess()) {
                        let success: SuccessResponse = {
                            success: response.getSuccess()?.getSuccess()!
                        };
                        resolve(success);
                    }
                    else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    }
                }
            });
        });
    }
}
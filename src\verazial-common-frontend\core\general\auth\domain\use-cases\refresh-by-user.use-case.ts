import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AuthEntity } from "../entity/auth.entity";
import { AuthRepository } from "../repository/auth.repository";

export class RefreshByUserUseCase implements UseCaseGrpc<{ oldToken: string }, AuthEntity> {
    constructor(private authRepository: AuthRepository) { }
    execute(params: { oldToken: string; }): Promise<AuthEntity> {
        return this.authRepository.refreshByUser(params);
    }
}
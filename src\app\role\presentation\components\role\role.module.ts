import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RoleComponent } from './role/role.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ToastModule } from 'primeng/toast';
import { InputNumberModule } from 'primeng/inputnumber';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';


@NgModule({
  declarations: [
    RoleComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    InputTextModule,
    ButtonModule,
    InputTextareaModule,
    ToastModule,
    InputNumberModule,
    DropdownModule,
    InputSwitchModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    RoleComponent
  ]
})
export class RoleModule { }

import { DataSourceParametersRepository } from "../repositories/data-source-parameters.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class DeleteDataSourceParamsByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private dataSourceParamsRepository: DataSourceParametersRepository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.dataSourceParamsRepository.deleteDataSourceParamsById(params);
    }
}
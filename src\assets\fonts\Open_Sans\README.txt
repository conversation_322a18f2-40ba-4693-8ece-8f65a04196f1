Open Sans Variable Font
=======================

This download contains Open Sans as both variable fonts and static fonts.

Open Sans is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in these files:
  OpenSans-VariableFont_wdth,wght.ttf
  OpenSans-Italic-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Open Sans:
  static/OpenSans_Condensed-Light.ttf
  static/OpenSans_Condensed-Regular.ttf
  static/OpenSans_Condensed-Medium.ttf
  static/OpenSans_Condensed-SemiBold.ttf
  static/OpenSans_Condensed-Bold.ttf
  static/OpenSans_Condensed-ExtraBold.ttf
  static/OpenSans_SemiCondensed-Light.ttf
  static/OpenSans_SemiCondensed-Regular.ttf
  static/OpenSans_SemiCondensed-Medium.ttf
  static/OpenSans_SemiCondensed-SemiBold.ttf
  static/OpenSans_SemiCondensed-Bold.ttf
  static/OpenSans_SemiCondensed-ExtraBold.ttf
  static/OpenSans-Light.ttf
  static/OpenSans-Regular.ttf
  static/OpenSans-Medium.ttf
  static/OpenSans-SemiBold.ttf
  static/OpenSans-Bold.ttf
  static/OpenSans-ExtraBold.ttf
  static/OpenSans_Condensed-LightItalic.ttf
  static/OpenSans_Condensed-Italic.ttf
  static/OpenSans_Condensed-MediumItalic.ttf
  static/OpenSans_Condensed-SemiBoldItalic.ttf
  static/OpenSans_Condensed-BoldItalic.ttf
  static/OpenSans_Condensed-ExtraBoldItalic.ttf
  static/OpenSans_SemiCondensed-LightItalic.ttf
  static/OpenSans_SemiCondensed-Italic.ttf
  static/OpenSans_SemiCondensed-MediumItalic.ttf
  static/OpenSans_SemiCondensed-SemiBoldItalic.ttf
  static/OpenSans_SemiCondensed-BoldItalic.ttf
  static/OpenSans_SemiCondensed-ExtraBoldItalic.ttf
  static/OpenSans-LightItalic.ttf
  static/OpenSans-Italic.ttf
  static/OpenSans-MediumItalic.ttf
  static/OpenSans-SemiBoldItalic.ttf
  static/OpenSans-BoldItalic.ttf
  static/OpenSans-ExtraBoldItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.

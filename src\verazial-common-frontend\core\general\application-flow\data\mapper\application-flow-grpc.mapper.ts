import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ApplicationFlowEntity } from "../../domain/entity/application-flow.entity";
import { ApplicationFlowGrpcModel } from "src/verazial-common-frontend/core/generated/application-flow/application_flow_pb";

export class ApplicationFlowGrpcMapper extends Mapper<ApplicationFlowGrpcModel, ApplicationFlowEntity> {
    override mapFrom(param: ApplicationFlowGrpcModel): ApplicationFlowEntity {
        return {
            id: param.getId(),
            applicationId: param.getApplicationid(),
            drawFlow: param.getDrawflow(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }
    override mapTo(param: ApplicationFlowEntity): ApplicationFlowGrpcModel {
        let model = new ApplicationFlowGrpcModel();
        model.setId(param.id!!);
        model.setApplicationid(param.applicationId!!);
        model.setDrawflow(param.drawFlow!!);
        return model;
    }
}
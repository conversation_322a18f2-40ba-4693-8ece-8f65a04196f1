import { WindowParametersRepository } from "../repositories/window-parameters.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { WindowParametersEntity } from "../entities/window-parameters.entity";

export class GetWindowParamsByWindowIdUseCase implements UseCaseGrpc<{ windowId: string }, WindowParametersEntity[]> {
    constructor(private windowParametersRepository: WindowParametersRepository) { }
    execute(params: { windowId: string; }): Promise<WindowParametersEntity[]> {
        return this.windowParametersRepository.getWindowParamsByWindowId(params);
    }
}
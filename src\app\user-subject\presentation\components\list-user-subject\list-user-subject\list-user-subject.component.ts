import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FilterMetadata, MessageService, TreeNode } from 'primeng/api';
import { Table, TableFilterEvent, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { WidgetResult } from 'src/verazial-common-frontend/modules/shared/models/widget-response.model';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { KonektorPropertiesEntity } from 'src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { SubjectEntity, SubjectLastActionEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { UserEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/user.entity';
import { UserSubjectActionType } from 'src/verazial-common-frontend/core/models/user-subject-action-type.enum';
import { UserSubjectEnum } from 'src/verazial-common-frontend/core/models/user-subject.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { FilterService } from 'primeng/api';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CheckTokenUseCase } from 'src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { InsideEnum } from 'src/verazial-common-frontend/core/models/inside.enum';
import { NewLocationsService } from 'src/verazial-common-frontend/core/services/new-locations.service';
import { GetAllGroupsCategoriesUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-all-groups-categories.use-case';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { GroupCategoryService } from 'src/verazial-common-frontend/core/services/group-category.service';
import { GetGroupCategoryByIdUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-group-category-by-id.use-case';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';
import { RequiredScheduleActionStatus } from 'src/verazial-common-frontend/core/models/required-schedule-action-status.enum';
import { GetAllTaskFlowsUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-all-task-flows.use-case';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { CustomFilterType, FilterConditionType, FilterEntity } from 'src/verazial-common-frontend/core/models/filter.entity';
import { GetSubjectsRequestEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/get-subjects-request.entity';
import { SortOrderEnum } from 'src/verazial-common-frontend/core/models/sort-order.enum';
import { GetUsersRequestEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/get-users-request.entity';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';

@Component({
  selector: 'app-list-user-subject',
  templateUrl: './list-user-subject.component.html',
  styleUrl: './list-user-subject.component.css',
  providers: [MessageService]
})
export class ListUserSubjectComponent implements OnInit, OnDestroy, OnChanges {

  /** Inputs */
  @Input() showHeader: boolean = true;
  @Input() headerText: string = "";
  @Input() showBioSearch: boolean = true;
  @Input() showGlobalSearch: boolean = true;
  @Input() showTableRecordButton: boolean = true;
  @Input() tableRecordButtonText: string = "";
  @Input() tableRecordButtonIcon: string = "plus";
  @Input() showMainButton: boolean = true;
  @Input() mainButtonIcon: string = 'pencil';
  @Input() mainActionOnRowClick: boolean = true;
  @Input() showSecondaryButton: boolean = true;
  @Input() secondaryButtonIcon: string = 'trash';
  @Input() clearMultipleOnSecondaryAction: boolean = true;
  @Input() lazyLoad: boolean = false;
  @Input() readAndWritePermissions: boolean = false;
  @Input() readOnly: boolean = false;
  @Input() userIsVerified: boolean = true;
  @Input() isLoading: boolean = false;
  @Input() type: UserSubjectEnum | undefined
  @Input() listOfUsersSubjects: SubjectEntity[] | UserEntity[] = [];
  @Input() selectedUsersSubjects: SubjectEntity[] | UserEntity[] = [];
  @Input() totalRecords: number = 0;
  @Input() offset: number = 0;
  @Input() limit: number = 0;
  @Input() allRoles: RoleEntity[] = [];
  @Input() specificRole?: number;
  @Input() managerSettings?: GeneralSettings;
  @Input() konektorProperties?: KonektorPropertiesEntity;
  @Input() recordManagementMode?: boolean = true;
  @Input() dialogBoxText: string = "";
  /** Outputs */
  @Output() onAdd = new EventEmitter<SubjectEntity | UserEntity>;
  @Output() onTableRecordAction = new EventEmitter<void>();
  @Output() onMainAction = new EventEmitter<SubjectEntity | UserEntity>;
  @Output() onSecondaryAction = new EventEmitter<SubjectEntity[] | UserEntity[]>;
  @Output() onSelectionChange = new EventEmitter<SubjectEntity[] | UserEntity[]>();
  @Output() onTableLazyLoadEvent = new EventEmitter<GetSubjectsRequestEntity | GetUsersRequestEntity>();
  @Output() onBioSearch = new EventEmitter<string>();
  @Output() userVerified = new EventEmitter<boolean>();

  private intervalId: any;

  userSubjectData: SubjectEntity | UserEntity | undefined;
  selectedUserSubject: SubjectEntity | UserEntity | undefined;

  isUser: boolean = false;
  segmentedSearchAttributes: { name: string, value: string, secondSearch?: string }[] = [];

  isDisableSaveButton: boolean = true;
  saveAllowed: boolean = true;
  allDataSources: { key: string | undefined, value: string | undefined, data: any }[] = [];

  showNewUserSubjectDialog: boolean = false;
  showUpdateUserSubjectDialog: boolean = false;

  actionType: UserSubjectActionType = UserSubjectActionType.CREATE;
  userSubjectActionTypes = UserSubjectActionType;

  searchValue: string | undefined;

  specificRoleName: string = "";
  noDataText: string = "";

  /* User in Session */

  /* Biometric Search */
  numId: string = "";
  tech: string = "";
  widgetUrl: string = "";
  searchReady: boolean = false;
  // Enter NumId
  showEnterNumId: boolean = false;
  formNumId: FormGroup = new FormGroup({
    numId: new FormControl('', Validators.required)
  });
  verificationSubjectId: string = "";

  filteredValues: any[] = [];

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  InsideEnum = InsideEnum;

  lLocationOptions: any[] = [];
  lCentreOptions: TreeNode[] = [];

  // Tag Colours
  tagColours = {
    red: 'rgb(212, 19, 19)',
    orange: 'rgb(245, 93, 11)',
    green: 'rgb(19, 141, 20)',
    yellow: 'rgb(245, 158, 11)',
    default: 'var(--text-color)',
  };

  listOfGroupsCategories: GroupCategoryEntity[] = [];

  requiredScheduleActionStatus = RequiredScheduleActionStatus;

  requiredScheduleActionStatusOptions = [
    {value: RequiredScheduleActionStatus.COMPLETED_ON_TIME},
    {value: RequiredScheduleActionStatus.COMPLETED_LATE},
    {value: RequiredScheduleActionStatus.ON_TIME},
    {value: RequiredScheduleActionStatus.LATE},
  ];

  listOfFlows: TaskFlowEntity[] = [];

  @ViewChild('dt') table!: Table;

  showVerificationIcon: boolean = false;
  showSegmentedSearchIcon: boolean = false;

  disabledBiometricButtonsToolTip: string = '';

  all_locations_idenfitier = AccessIdentifier.GET_ALL_LOCATIONS;
  canSeeAllLocations: boolean = false;

  selectedRoleFilter: number | null = null;

  selectedCenter: TreeNode[] | null = null;

  constructor(
    private messageService: MessageService,
    private translateService: TranslateService,
    private loggerService: ConsoleLoggerService,
    private localStorageService: LocalStorageService,
    private auditTrailService: AuditTrailService,
    private checkPermissions: CheckPermissionsService,
    private filterService: FilterService,
    private checkTokenUseCase: CheckTokenUseCase,
    private newLocationsService: NewLocationsService,
    private getAllGroupsCategoriesUseCase: GetAllGroupsCategoriesUseCase,
    private groupCategoryService: GroupCategoryService,
    private getGroupCategoryByIdUseCase: GetGroupCategoryByIdUseCase,
    private getAllTaskFlowsUseCase: GetAllTaskFlowsUseCase,
    private cdr: ChangeDetectorRef,
  ) {

    this.filterService.register('customStringArray', (value: any, filter: any): boolean => {
      if (!filter) return true; // If no filter provided, show all
      else if (typeof value === 'object' && typeof filter === 'string') {
        return value.map((role: RoleEntity) => role.name == 'SYSTEM_USER' ? this.translateService.instant('role_names.SYSTEM_USER') : role.name).join(', ').toLowerCase().includes(filter.toLowerCase());
      }
      else if (typeof value === 'object' && typeof filter === 'number') {
        return value.map((role: RoleEntity) => role.id).join(', ').toLowerCase().includes(filter.toString());
      }
      return false;
    });
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
    this.filterService.register('requiredScheduleActionStatus', (value: any, filter: any): boolean => {
      if (!filter) return true; // If no filter provided, show all
      else if (typeof value === 'object' && typeof filter === 'string') {
        return value.map((action: SubjectLastActionEntity) => this.getActionData(action)).includes(filter.toLowerCase());
      }
      return false;
    });

    this.filterService.register('centerOrEmpty', (value: any, filter: any): boolean => {
      if (!filter) return true; // If no filter provided, show all

      /*else if (typeof filter === 'object') {
        if(!value)
          return true;
        return value == filter.key;
      }*/
      else if(typeof filter == 'string') {
        if(!value) return true;
        return value == filter;
      }
      return false;
    });
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

ngOnChanges(changes: SimpleChanges): void {
  if (changes['specificRole'] && changes['specificRole'].currentValue) {

    this.table?.clear();

    this.selectedRoleFilter = this.allRoles.find(role => role.id === changes['specificRole'].currentValue)?.id ?? null;

    this.specificRoleName = this.allRoles.find(role => role.id === this.specificRole)?.name ?? "";

    if (this.specificRoleName != '') {
      this.updateSegmentedSearchAttributes();
    }

    if (this.table) {
      this.table.filter(changes['specificRole'].currentValue, 'defaultRole', 'equals');
      if(!this.canSeeAllLocations && this.selectedCenter) {  
          this.table.filter(this.selectedCenter[0].key, 'center', 'centerOrEmpty');
      }
    }
  }
}

  startTimeInterval() {
    this.intervalId = setInterval(() => {
      this.cdr.detectChanges(); // Trigger change detection manually
      // trigger change detection or update the state here if necessary
    }, 1000); // every second
  }

  async ngOnInit() {
    await this.getAllGroupsCategories();
    await this.getAllFlows();
    this.startTimeInterval();
    switch (this.type) {
      case UserSubjectEnum.SUBJECT: {
        this.headerText = this.headerText == '' ? "titles.subjects" : this.headerText;
        this.tableRecordButtonText = this.tableRecordButtonText == '' ? "create_new_subject" : this.tableRecordButtonText;
        this.noDataText = "subject.no_subjects_available";
        this.dialogBoxText = this.dialogBoxText == '' ? "createRecord" : this.dialogBoxText;
        this.isUser = false;
        this.segmentedSearchAttributes = []
        this.showSegmentedSearchIcon = false;
        break;
      }
      case UserSubjectEnum.USER: {
        this.headerText = this.headerText == '' ? "titles.users" : this.headerText;
        this.tableRecordButtonText = this.tableRecordButtonText == '' ? "create_new_user" : this.tableRecordButtonText;
        this.noDataText = "user.no_users_available";
        this.dialogBoxText = this.dialogBoxText == '' ? "create_new_user" : this.dialogBoxText;
        this.isUser = true;
        this.segmentedSearchAttributes = [
          { name: 'Is User', value: 'true', secondSearch: 'false' },
        ]
        this.showSegmentedSearchIcon = true;
        break;
      }
      default: {
        //statements;
        break;
      }
    }
    let options = this.managerSettings?.continued1?.newLocations!;
    this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptions(options, false, false);
    this.lCentreOptions = this.newLocationsService.centresToTreeSelectOptions(options);


    this.canSeeAllLocations = this.checkPermissions.hasReadPermissions(this.all_locations_idenfitier);
    if(!this.canSeeAllLocations) {
      this.selectedCenter = this.lCentreOptions.filter(centre => centre.label === this.konektorProperties?.locationId) ?? null;

      if (this.table) {
        this.table.filter(this.selectedCenter[0].key, 'center', 'centerOrEmpty');
        //this.updateSegmentedSearchAttributes();
      }
    }

    this.loggerService.debug(this.listOfUsersSubjects);
  }

  async getAllGroupsCategories() {
    try {
      const data = await this.getAllGroupsCategoriesUseCase.execute();
      if (data.length > 0) {
        this.listOfGroupsCategories = await Promise.all(
          data.filter((groupCategory: GroupCategoryEntity) => groupCategory.type == GroupCategoryType.SCHEDULES)
          .map(async (groupCategory: GroupCategoryEntity) => {
            if (groupCategory.id) {
              try {
                const categoryData = await this.getGroupCategoryByIdUseCase.execute({ id: groupCategory.id });
                if (categoryData) {
                  groupCategory.categorySchedule = categoryData.categorySchedule;
                }
              } catch (error) {
                this.loggerService.error(error);
              }
            } else {
              groupCategory.categorySchedule = undefined;
            }
            return groupCategory;
          })
        );
      }
    } catch (e) {
      this.loggerService.error(e);
      const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId,AuditTrailActions.GET_ALL_GROUP_CATEGORIES,0,'ERROR','',at_attributes);
    }
  }

  async getAllFlows() {
    try {
      let data = await this.getAllTaskFlowsUseCase.execute()
      if (data.length > 0) {
        this.listOfFlows = data
      }
    }
    catch(e) {
      this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_TASK_FLOWS, 0, 'ERROR', '', at_attributes);
    }
  }

  resetSegmentedSearchAttributes() {
    switch (this.type) {
      case UserSubjectEnum.USER: {
        this.segmentedSearchAttributes = [
          { name: 'Is User', value: 'true', secondSearch: 'false' },
        ]
        this.showSegmentedSearchIcon = true;
        break;
      }
      default: {
        this.segmentedSearchAttributes = [];
        this.showSegmentedSearchIcon = false;
        break;
      }
    }
  }

  // Convert the list of roles to string
  listOfRolesToString(roles?: RoleEntity[]): string {
    let stringOfRoles: string = "";

    if (roles && roles?.length > 0) {
      stringOfRoles = roles?.map(role => role.name == 'SYSTEM_USER' ? this.translateService.instant('role_names.SYSTEM_USER') : role.name).join(', ');
    }

    return stringOfRoles
  }

  principalRoleToString(rolId?:  number): string {
    let principalRole: string = "";

    if(rolId && this.allRoles && this.allRoles.length > 0)
    {
      principalRole = this.allRoles.find(role => role.id == rolId)?.name ?? "";
    }

    return principalRole;
  }

  onUserSubjectSelectionChange(event: any) {
    this.loggerService.debug(this.selectedUsersSubjects)
    this.onSelectionChange.emit(this.selectedUsersSubjects);
  }

  // Check the user data to enable the save button
  onAddUserSubject(data: SubjectEntity | UserEntity) {
    this.userSubjectData = data;
    if (data.numId && data.names && data.lastNames && data.roles && data.roles.length > 0) {
      if (this.isUser) {
        if (data.email) {
          this.isDisableSaveButton = false;
        }
        else {
          this.isDisableSaveButton = true;
        }
      }
      else {
        this.isDisableSaveButton = false;
      }
    }
    else {
      this.isDisableSaveButton = true;
    }
  }

  allowSave(allow: boolean) {
    this.saveAllowed = allow;
  }

  checkPermisions(dataSourceId?: string): { allowSave: boolean, allowDelete: boolean } {
    if (!this.recordManagementMode){
      return { allowSave: true, allowDelete: true };
    }
    if (dataSourceId == 'LOCAL' || dataSourceId == '' || dataSourceId == undefined || dataSourceId == null) {
      return { allowSave: true, allowDelete: true };
    }
    const apiDataSources = this.managerSettings?.userSubjectApiExternalConnections?.filter(v => v.isActive == true).map(v => {
      return { key: v.connectionId, value: v.connectionName, data: v }
    });
    const ldapDataSources = this.managerSettings?.userSubjectLdapConnections?.filter(v => v.isActive == true).map(v => {
      return { key: v.connectionId, value: v.connectionName, data: v }
    });
    const dataSources = [...apiDataSources!, ...ldapDataSources!];
    this.allDataSources = [...dataSources];
    let allowActions: { allowSave: boolean, allowDelete: boolean } = { allowSave: true, allowDelete: true };
    if (dataSources.length > 0) {
      const userSubjectDataSource = dataSources.find(v => v.key == dataSourceId);
      allowActions = {
        allowSave: userSubjectDataSource?.data.allowUpdating ?? false,
        allowDelete: userSubjectDataSource?.data.allowDeleting ?? false
      }
    }
    else {
      allowActions = {
        allowSave: true,
        allowDelete: true
      }
    }
    return allowActions;
  }

  getDataSourceName(dataSourceId?: string): string {
    const apiDataSources = this.managerSettings?.userSubjectApiExternalConnections?.filter(v => v.isActive == true).map(v => {
      return { key: v.connectionId, value: v.connectionName, data: v }
    });
    const ldapDataSources = this.managerSettings?.userSubjectLdapConnections?.filter(v => v.isActive == true).map(v => {
      return { key: v.connectionId, value: v.connectionName, data: v }
    });
    const dataSources = [...apiDataSources!, ...ldapDataSources!];
    this.allDataSources = [...dataSources];
    let dataSourceName: string = '';
    if (dataSourceId != '' && dataSourceId != undefined && dataSourceId != null) {
      const dataSource = this.allDataSources.find(v => v.key == dataSourceId);
      dataSourceName = dataSource?.value ?? '';
    }
    return dataSourceName;
  }

  mainActionUserSubject(userSubject?: SubjectEntity | UserEntity) {
    if (userSubject) {
      this.onMainAction.emit(userSubject);

    }
    else {
      if (this.selectedUsersSubjects.length === 1) {
        this.onMainAction.emit(this.selectedUsersSubjects[0]);
      }
    }
  }

  updateSelection(userSubject?: SubjectEntity | UserEntity) {
    if (this.selectedUsersSubjects.includes(userSubject!)) {
      this.selectedUsersSubjects = this.selectedUsersSubjects.filter((value) => value !== userSubject);
    }
    else {
      this.selectedUsersSubjects.push(userSubject!);
    }
    this.onUserSubjectSelectionChange(this.selectedUsersSubjects);
  }

  secondaryActionUserSubject(userSubject?: SubjectEntity | UserEntity) {
    let usersSubjectToEmit: SubjectEntity[] | UserEntity[] = [];
    if (this.selectedUsersSubjects.length >= 1 && !userSubject) {
      usersSubjectToEmit = this.selectedUsersSubjects;
      if (this.clearMultipleOnSecondaryAction) this.selectedUsersSubjects = [];
    } else if (userSubject) {
      usersSubjectToEmit.push(userSubject)
    }
    this.onSecondaryAction.emit(usersSubjectToEmit);
  }

  tableRecordAction() {
    if (!this.recordManagementMode) {
      this.onTableRecordAction.emit();
    }
    else {
      this.showNewUserSubjectDialog = true
    }
  }

  // Emit a new User/Subject
  saveUserSubject() {
    this.onAdd.emit(this.userSubjectData);
    this.showNewUserSubjectDialog = false;
  }

  /* Table Lazy Load */
  loadUserSubjects(event: TableLazyLoadEvent) {
    console.log("Lazy Load Event: ");
    console.log(event);
    let getSubjectsRequest = this.type == UserSubjectEnum.SUBJECT ? new GetSubjectsRequestEntity() : new GetUsersRequestEntity();
    getSubjectsRequest.offset = event.first;
    getSubjectsRequest.limit = event.rows ?? 10;
    getSubjectsRequest.sortField = typeof event.sortField === 'string' ? this.getAllowedSortField(event.sortField) : 'numId';
    getSubjectsRequest.sortOrder = event.sortOrder === 1 ? SortOrderEnum.ASC : SortOrderEnum.DESC;
    getSubjectsRequest.filters = this.setupFilters(event.filters);
    console.log(getSubjectsRequest);
    this.onTableLazyLoadEvent.emit(getSubjectsRequest);
  }

  getAllowedSortField(sortField: string): string | undefined {
    switch (sortField) {
      case 'roles':
        return undefined;
      default:
        return sortField;
    }
  }

  setupFilters(event?: { [s: string]: FilterMetadata | FilterMetadata[] | undefined }): FilterEntity[] {
    const dateRangeFields = ['birthdate', 'createdAt', 'updatedAt'];
    const equalsFields = ['center', 'defaultRole', 'gender', 'locationId', 'datasource'];
    const containsFields = ['email', 'names', 'lastNames', 'numId', 'inside'];
    const customFields = ['roles', 'lastActions'];

    let filters: FilterEntity[] = [];
    if (this.specificRole) {
      filters.push({
        condition: {
          type: FilterConditionType.EQUALS,
          field: 'defaultRole',
          value: this.specificRole.toString()
        }
      });
    }
    if (!event) return filters;
    for (const key in event) {
      let filter = new FilterEntity();
      if (Object.prototype.hasOwnProperty.call(event, key)) {
        if (event[key] != undefined && !Array.isArray(event[key])) {
          let eventValue = event[key] as FilterMetadata;
          if (eventValue.value) {
            if (dateRangeFields.includes(key)) {
              filter.condition = {
                type: FilterConditionType.BETWEEN,
                field: key,
                from: eventValue.value.startDate,
                to: eventValue.value.endDate
              };
            }
            else if (equalsFields.includes(key)) {
              let value = eventValue.value;
              if (key == 'datasource') {
                if (eventValue.value == 'LOCAL') {
                  value = '';
                }
              }
              filter.condition = {
                type: FilterConditionType.EQUALS,
                field: key,
                value: eventValue.value.toString()
              };
            }
            else if (containsFields.includes(key)) {
              filter.condition = {
                type: FilterConditionType.CONTAINS,
                field: key,
                value: eventValue.value.toString()
              };
            }
            else if (customFields.includes(key)) {
              let customType: CustomFilterType = CustomFilterType.NONE;
              switch (key) {
                case 'roles':
                  customType = CustomFilterType.ROLES;
                  break;
                case 'lastActions':
                  customType = CustomFilterType.LAST_ACTIONS;
                  break;
              }
              filter.condition = {
                type: FilterConditionType.CUSTOM,
                field: key,
                value: eventValue.value.toString(),
                customType: customType,
              };
            }
          }
        }
      }
      if (filter.condition) filters.push(filter);
    }

    return filters;
  }

  /* Search */
  onFilter(event: TableFilterEvent, dt: Table) {
    this.filteredValues = event.filteredValue;
    if (event.filters && event.filters['birthdate'] && !event.filters['birthdate'].value) {
      this.rangeDates = null;
      this.formGroup.reset();
    }

    this.updateSegmentedSearchAttributes();
    if(this.filteredValues.length == 1) {
      this.showVerificationIcon = true;
      this.showSegmentedSearchIcon = false;
    }
    else
    {
      this.showVerificationIcon = false;
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    this.rangeDates = this.formGroup.get('date')?.value;
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  /* Widget Search Functions */
  updateSegmentedSearchAttributes() {
    let presentAbsent = Array.isArray(this.table.filters["inside"]) ? this.table.filters["inside"].map(f => f.value).join(", ") : this.table.filters["inside"]?.value;
    let role = this.specificRoleName != '' ? this.specificRole : Array.isArray(this.table.filters["defaultRole"]) ? this.table.filters["defaultRole"].map(f => f.value).join(", ") : this.table.filters["defaultRole"]?.value;
    let gender = Array.isArray(this.table.filters["gender"]) ? this.table.filters["gender"].map(f => f.value).join(", ") : this.table.filters["gender"]?.value;
    let center = Array.isArray(this.table.filters["center"]) ? this.table.filters["center"].map(f => f.value).join(", ") : this.table.filters["center"]?.value;

    /*console.log("Present Absent:", presentAbsent);
    console.log("Role:", role);
    console.log("Gender:", gender);
    console.log("Center:", center);*/


    this.resetSegmentedSearchAttributes();

    if (presentAbsent) {
      this.showSegmentedSearchIcon = true;
      this.segmentedSearchAttributes.push({ name: 'PRESENTABSENT', value: presentAbsent, secondSearch: 'false' });
    }
    if (role) {
      this.showSegmentedSearchIcon = true;
      this.segmentedSearchAttributes.push({ name: 'PROFILE', value: role, secondSearch: 'false' });
    }
    if(gender) {
      this.showSegmentedSearchIcon = true;
      this.segmentedSearchAttributes.push({ name: 'GENDER', value: gender, secondSearch: 'false' });
    }
    if(center && this.canSeeAllLocations) { // TODO: esto es para no poner el filtro de búsqueda segmentada cambiar cuando el widget permita filtros guays
      this.showSegmentedSearchIcon = true;
      const centerValue = Array.isArray(center) ? center[0]?.key : center;
      this.segmentedSearchAttributes.push({ name: 'CENTER', value: centerValue, secondSearch: 'false' });
    }
  }

  widgetSearch(tech: string) {
    this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
    this.tech = tech;
    if (this.konektorProperties?.verificationEnabled) {
      if (this.konektorProperties?.verificationSubjectId) {
        this.numId = this.konektorProperties.verificationSubjectId;
        this.startSearch();
      }
      else {
        this.showEnterNumId = true;
        return;
      }
    }
    else {
      this.numId = this.filteredValues.length == 1 ? this.filteredValues[0].numId : '';
      this.startSearch();
    }
  }

  startSearch() {
    let allowWidget = false;
    switch (this.tech) {
      case 'fingerprint':
        allowWidget = this.managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (this.numId == '' ? this.managerSettings?.allowSearch?.dactilar == true : this.managerSettings?.allowVerify?.dactilar == true);
        break;
      case 'facial':
        allowWidget = this.managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (this.numId == '' ? this.managerSettings?.allowSearch?.facial == true : this.managerSettings?.allowVerify?.facial == true);
        break;
      case 'iris':
        allowWidget = this.managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (this.numId == '' ? this.managerSettings?.allowSearch?.iris == true : this.managerSettings?.allowVerify?.iris == true);
        break;
    }
    if (allowWidget) {
      this.searchReady = true;
    }
    else {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('titles.access_denied'),
        detail: this.translateService.instant('messages.error_technology_not_allowed'),
        life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
      });
    }
  }

  onWidgetSearchResult(event: WidgetResult) {
    if (!this.searchReady) {
      return;
    }
    switch (event.action) {
      case "verify":
        this.searchReady = false;
        if (event.result == "success") {
          if (event.data.isMatched) {
            this.onBioSearch.emit(this.numId);
          }
        }
        break;
      case 'search':
        const responseData = event.data.nId;
        if (responseData) {
          this.onBioSearch.emit(responseData);
        }
        this.searchReady = false;
        break;
      case "process":
        break;
      case "close_search":
      case "error":
        this.searchReady = false;
        this.numId = '';
        this.tech = '';
        break;
    }
  }

  closeDialog() {
    this.formNumId.reset();
    this.showEnterNumId = false;
  }

  onDialogSubmit() {
    this.formNumId.markAllAsTouched();
    const numId = this.formNumId.get('numId')?.value;
    if (numId && numId != '') {
      this.formNumId.reset();
      this.showEnterNumId = false;
      this.numId = numId;
      this.startSearch();
    }
    else {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant("titles.important"),
        detail: this.translateService.instant("messages.ERROR_INVALID_NUMID"),
        life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
      })
    }
  }

  async userVerifying(mod: boolean) {
    this.updateModified(mod);
    let userIsVerified: boolean = false;
    if (this.localStorageService.isUserVerified()) {
      await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
        (response) => {
          userIsVerified = !response;
        },
        (e) => {
          this.loggerService.error(e);
        }
      );
    }
    this.userVerified.emit(userIsVerified)
  }

  updateModified(modified: boolean) {
    this.localStorageService.setLockMenu(modified);
  }

  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
  }

  isValidDate2(date: Date | undefined): boolean {
    if(!date) return false;
    return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
  }

  insideStatus(status: InsideEnum, dateEntry: Date, dateExit: Date, visitTime: Date, roles: RoleEntity[]): string {

    var isPrisoner = roles?.filter((role: RoleEntity) => role.id?.toString() == this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId).length != 0;

    if (isPrisoner) {
      switch (status) {

        case InsideEnum.OUTSIDE: {
          if( this.isValidDate2(visitTime))
            return this.translateService.instant('table.absent') + " " + this.getTimeRemaining(visitTime);
          else
            return "";
        }
        case InsideEnum.TRANSFER: {
          return this.translateService.instant('table.transfer') + " " +  this.getTimeRemaining(visitTime);
        }

        default: {
          return "";
        }
      }
    }

    else {
      switch (status) {
        case InsideEnum.INSIDE: {
          return this.translateService.instant('table.present') + " " + this.getTimeRemaining(visitTime);
        }
        default: {
          return "";
        }
      }
    }
  }

  getInsideName(status: InsideEnum): string {
    switch (status) {
      case InsideEnum.INSIDE: {
        return this.translateService.instant('table.present');
      }
      case InsideEnum.OUTSIDE: {
        return this.translateService.instant('table.absent');
      }
      case InsideEnum.TRANSFER: {
        return this.translateService.instant('table.transfer');
      }

    };
  }

  getTimeRemainingColor(visitTime: Date): string {

    if (visitTime != undefined) {
      const now = new Date();
      const remainingTime = visitTime.getTime() - now.getTime();

      if (remainingTime <= 0) {
        return this.tagColours.red;
      }
      else {
        return this.tagColours.green;
      }
    }
    else
    {
      return this.tagColours.green;
    }
  }

  getTimeRemaining(visitTime: Date): string {
    const diff = (visitTime.getTime() - Date.now()) / 1000;
    const sign = diff < 0 ? "-" : "";
    const absDiff = Math.abs(diff);
    const hours = Math.floor(absDiff / 3600);
    const minutes = Math.floor((absDiff % 3600) / 60);
    const seconds = Math.floor(absDiff % 60);
    return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  getTimeRemainingFromString(visitTime: string): string {
    const date = new Date(visitTime);
    return this.getTimeRemaining(date);
  }

  getLocationName(centerId: string ): string {
    if(centerId != "")
    {
      return this.newLocationsService.getNameAndTree(centerId, this.lLocationOptions) ?? "";
    }
    else
    {
      return "";
    }
  }

  getFlowName(flowId: string): string {
    return this.listOfFlows.find(flow => flow.id == flowId)?.name ?? "";
  }

  getActionColor(action: string): string {
    switch (action) {
      case (RequiredScheduleActionStatus.COMPLETED_ON_TIME): {
        return this.tagColours.green;
      }
      case (RequiredScheduleActionStatus.COMPLETED_LATE): {
        return this.tagColours.orange;
      }
      case (RequiredScheduleActionStatus.ON_TIME): {
        return this.tagColours.yellow;
      }
      case (RequiredScheduleActionStatus.LATE): {
        return this.tagColours.red;
      }
      default: {
        return this.tagColours.default;
      }
    }
  }

  getActionData(action: SubjectLastActionEntity): string {
    let result = ''
    let today = new Date();
    let actionDate: Date | undefined = action.actionDateTime ? new Date(action.actionDateTime) : undefined;
    let requiredDateTimeInit: Date | undefined = action.requiredDateTimeInit ? new Date(action.requiredDateTimeInit) : undefined;
    let requiredDateTimeEnd: Date | undefined = action.requiredDateTimeEnd ? new Date(action.requiredDateTimeEnd) : undefined;
    let nextRequiredDateTimeInit: Date | undefined = action.nextRequiredDateTimeInit ? new Date(action.nextRequiredDateTimeInit) : undefined;
    let nextRequiredDateTimeEnd: Date | undefined = action.nextRequiredDateTimeEnd ? new Date(action.nextRequiredDateTimeEnd) : undefined;
    let schedule: GroupCategoryEntity | undefined = this.listOfGroupsCategories.find((groupCategory: GroupCategoryEntity) => groupCategory.id == action.configScheduleId);
    // Check if data is valid
    if (actionDate && requiredDateTimeInit && requiredDateTimeEnd && schedule && schedule.categorySchedule) {
      let scheduleIncluded = this.groupCategoryService.isWithinDateRange(new Date(today), schedule.categorySchedule);
      // Check if today is within the schedule date range
      if (scheduleIncluded) {
        // CHECK
        // if today is within the schedule
        // OR
        // if today is within the end of the last schedule and the start of the next schedule
        if ((today >= requiredDateTimeInit && today <= requiredDateTimeEnd) || (today > requiredDateTimeEnd)) {
          // check if the action date is within the schedule
          if (actionDate >= requiredDateTimeInit && actionDate <= requiredDateTimeEnd) {
            result = RequiredScheduleActionStatus.COMPLETED_ON_TIME;
          }
          // check if the action date is after the schedule
          else if (actionDate > requiredDateTimeEnd) {
            result = RequiredScheduleActionStatus.COMPLETED_LATE;
          }
          if (nextRequiredDateTimeInit && nextRequiredDateTimeEnd) {
            // check if today is within the next schedule
            if (today >= nextRequiredDateTimeInit && today <= nextRequiredDateTimeEnd) {
              result = RequiredScheduleActionStatus.ON_TIME;
            }
            // check if today is after the next schedule
            else if (today > nextRequiredDateTimeEnd) {
              result = RequiredScheduleActionStatus.LATE;
            }
          }
        }
      }
    }
    return result;
  }
}
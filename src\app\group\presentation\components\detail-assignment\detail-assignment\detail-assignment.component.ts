import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { AssignmentEntity } from 'src/verazial-common-frontend/core/general/assignment/group/domain/entity/assignment.entity';
import { TaskFlow } from 'src/verazial-common-frontend/core/models/task-flow.interface';

@Component({
  selector: 'app-detail-assignment',
  templateUrl: './detail-assignment.component.html',
  styleUrl: './detail-assignment.component.css'
})
export class DetailAssignmentComponent implements OnInit {
  @Input() listSelectedFlows : TaskFlow[] = [];
  @Input() listSelectedCategories: GroupCategoryEntity[] = [];
  @Input() assignmentData: AssignmentEntity | undefined;

  constructor(
    private translateService: TranslateService
  ){}

  ngOnInit(): void {
  }

  getCategoryTranslation(type: GroupCategoryType){
    let translation: string = "";
    switch(type){
      case GroupCategoryType.USERS:
        translation = this.translateService.instant('content.user');
        break;
      case GroupCategoryType.LOCATIONS:
        translation = this.translateService.instant('content.location');
        break;
      case GroupCategoryType.SCHEDULES:
        translation = this.translateService.instant('content.schedule');
        break
    }
    return translation
  }

}

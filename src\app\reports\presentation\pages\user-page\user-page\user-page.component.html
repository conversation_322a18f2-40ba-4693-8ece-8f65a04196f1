<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<p-toast></p-toast>
<div class="main-container">
    <div class="main-progress">
        <p-progressBar [value]="progressValue" [color]="progressColor" [showValue]="progressEnabled" >
            <ng-template pTemplate="content" let-value style="background-color: #0ab4ba; border: solid 1px white; height: 10px ">
            </ng-template>
        </p-progressBar>
    </div>
    <div class="topBarItems" id="topBar">
        <div class="leftSideItems">
            <div class="recrodsTitle">
                {{'menu.reports_users' | translate}}
            </div>
        </div>
        <div class="rightSideItems">
            
            <div [formGroup]="datesForm" class="centerDiv searchButtonGroupSmall">

                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}"
                                    id="profile" formControlName="profile" [(ngModel)]="selectedProfile"
                                    [options]="profileOptions" optionLabel="name" dataKey="name" optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedProfile">
                                    <div>{{ selectedProfile }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-profileOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ profileOption.name }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-calendar appendTo="body" formControlName="rangeDates" [iconDisplay]="'input'" [showIcon]="true"
                            [(ngModel)]="dates" selectionMode="range" [class.ng-invalid]="dateError"
                            [class.ng-dirty]="dateError" aria-describedby="date-help1" [readonlyInput]="true"
                            inputId="multiple" dateFormat="{{ 'dateFormat' | translate }}" [showButtonBar]="true"[showTime]="true" 
                            [hourFormat]="'24'"></p-calendar>                        <div *ngIf="dateError">
                            <small class="error" id="date-help1">{{ dateErrorMessage | translate }}</small>
                        </div>
                    </div>
                </div>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}" class="searchButton searchButtonNormal" label="{{ 'update' | translate }}" [rounded]="true" (click)="getAllUsers()"></p-button>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}" class="searchButton searchButtonSmall" icon="pi pi-refresh" [rounded]="true" (click)="getAllUsers()"></p-button>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="container-plot">

            <div class="whole-card">
                <p-card header="" styleClass="p-datatable-sm" subheader="" [style]="{ height: '300px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tUsers1' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData1" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>

                    <p-table *ngIf="!showNoData1" [value]="usersData" [scrollable]="true" scrollHeight="650px" class="hoverTable" [tableStyle]="{'min-width': '40rem'}">
                        <ng-template pTemplate="header">
                            <tr>
                                <th *ngIf="!showSpinners">{{ 'table.numId' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.first_name' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.username' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.email' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.profile' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.enroll_date' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-usersData>
                            <tr>
                                <td *ngIf="!showSpinners">{{usersData.numId}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{usersData.names}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{usersData.username}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{usersData.email}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{usersData.profile}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{usersData.createdAt}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                            </tr>
                        </ng-template>
                    </p-table>
                </p-card>
            </div>


            <div class="card-style-right">
                <p-card header="" [style]="{ width: '320px', height: '320px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tUsers2' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="280px" height="250px" />
                    </div>
                    <div [hidden]="showSpinners" style="margin-left: 20px">
                        <canvas style="width: 210px;" id="pieChartUser">{{ pieChartUser }}</canvas>
                    </div>
                </p-card>
            </div>
            <div class="card-style-right">
                <p-card header="" [style]="{ width: '320px', height: '320px'}">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tUsers4' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="280px" height="250px" />
                    </div>
                    <div [hidden]="showSpinners" style="margin-left: 20px">
                        <canvas style="width: 210px;" id="dChartUser">{{ dChartUser }}</canvas>
                    </div>
                </p-card>
            </div>

            <div class="card-style-right">
                <p-card header="" [style]="{ width: '320px', height: '320px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tUsers3' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton width="280px" height="250px" />
                    </div>
                    <div [hidden]="showSpinners" style="margin-left: 20px">
                        <canvas style="width: 200px;" id="lChartUser">{{ lChartUser }}</canvas>
                    </div>
                </p-card>
            </div>
            <div class="card-style-right">
                <p-card header="" [style]="{ width: '320px', height: '320px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tUsers5' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="280px" height="250px" />
                    </div>
                    <div [hidden]="showSpinners" style="margin-left: 20px">
                        <canvas style="width: 200px;" id="fChartUser">{{ fChartUser }}</canvas>
                    </div>
                </p-card>
            </div>

        </div>
    </div>    
</div>

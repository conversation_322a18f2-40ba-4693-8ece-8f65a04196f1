syntax = "proto3";

import "google/protobuf/timestamp.proto";

option java_multiple_files = true;
option java_package = "com.verazial.microservice.settings.core.config.domain.model.grpc";

message LicenseGrpcModel {
  optional string guid = 1;
  optional string serialNumber = 2;
  optional string macString = 3;
  optional string ipString = 4;
  optional google.protobuf.Timestamp createdAt = 9;
}

message ArrayOfLicenses {
  repeated LicenseGrpcModel licenses = 1;
}

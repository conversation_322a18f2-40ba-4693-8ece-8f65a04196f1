import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CronServiceRepository } from "../repository/cron-service.repository";

export class TriggerJobSchedulerInitUseCase implements UseCaseGrpc<{}, SuccessResponse> {
    constructor(private cronServiceRepository: CronServiceRepository) { }
    execute(params: {}): Promise<SuccessResponse> {
        return this.cronServiceRepository.triggerJobSchedulerInit();
    }
}
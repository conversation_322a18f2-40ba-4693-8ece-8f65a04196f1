import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AccessEntity } from "../entity/access.entity";
import { AccessRepository } from "../repository/access.repository";

export class AddAccessUseCase implements UseCaseGrpc<{ access: AccessEntity }, AccessEntity> {
    constructor(private accessRepository: AccessRepository) { }
    execute(params: { access: AccessEntity; }): Promise<AccessEntity> {
        return this.accessRepository.addAccess(params)
    }
}
import { SortOrderAction } from "../../common/enums/sort-order-action.enum";
import { Mapper } from "src/verazial-common-frontend/core/mapper";

export class SortOrderMapper extends Mapper<number, SortOrderAction>{
    override mapFrom(param: number): SortOrderAction {
        switch(param){
            case 0:
                return SortOrderAction.ASC;
            case 1:
                return SortOrderAction.DESC;
            case 2:
                return SortOrderAction.NONE
            default:
                throw new Error("Invalid Sort Order");
        }
    }
    override mapTo(param: SortOrderAction): number {
        switch(param){
            case SortOrderAction.ASC:
                return 0;
            case SortOrderAction.DESC:
                return 1;
            case SortOrderAction.NONE:
                return 2;
        }
    }
}
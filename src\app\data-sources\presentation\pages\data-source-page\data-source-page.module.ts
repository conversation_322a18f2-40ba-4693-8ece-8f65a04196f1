import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DataSourcePageRoutingModule } from './data-source-page-routing.module';
import { DataSourcePageComponent } from './data-source-page/data-source-page.component';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';
import { DataSourcesModule } from '../../components/data-sources/data-sources.module';
import { ListDataSourcesModule } from '../../components/list-data-sources/list-data-sources.module';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';


@NgModule({
  declarations: [DataSourcePageComponent],
  imports: [
    CommonModule,
    DataSourcePageRoutingModule,
    TranslateModule,
    EmptyModule,
    DataSourcesModule,
    ListDataSourcesModule,
    ToastModule,
    LoadingSpinnerModule,
    /* PrimeNG modules */
    DialogModule,
    ButtonModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports:[
    DataSourcePageComponent
  ]
})
export class DataSourcePageModule { }

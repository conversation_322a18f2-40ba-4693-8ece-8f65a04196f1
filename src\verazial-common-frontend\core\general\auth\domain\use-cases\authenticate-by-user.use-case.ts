import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AuthEntity } from "../entity/auth.entity";
import { AuthRepository } from "../repository/auth.repository";

export class AuthenticateByUserUseCase implements UseCaseGrpc<{ tenantId: string, username: string, password: string }, AuthEntity> {
    constructor(private authRepository: AuthRepository) { }
    execute(params: { tenantId: string; username: string; password: string; }): Promise<AuthEntity> {
        return this.authRepository.authenticateByUser(params);
    }
}
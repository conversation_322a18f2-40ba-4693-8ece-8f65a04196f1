import { Mapper } from "src/verazial-common-frontend/core/mapper"
import { KonektorResponseEntity } from "../../domain/entity/konektor-response.entity"
import { KonektorResponseModel } from "../model/konektor-response.model"

export class KonektorResponseMapper extends Mapper<KonektorResponseModel, KonektorResponseEntity> {
    override mapFrom(param: KonektorResponseModel): KonektorResponseEntity {
        return {
            errorCode: param.ErrorCode,
            errorStatus: param.ErrorStatus,
        }
    }
    override mapTo(param: KonektorResponseEntity): KonektorResponseModel {
        return {
            ErrorCode: param.errorCode!,
            ErrorStatus: param.errorStatus!,
            biometricTechnology: null!,
        }
    }
}
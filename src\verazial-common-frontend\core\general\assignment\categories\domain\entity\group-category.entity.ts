import { GroupCategoryType } from "../../common/models/group-category-type.enum";
import { CategoryLocationEntity } from "./category-location.entity";
import { CategoryScheduleEntity } from "./category-schedule.entity";
import { CategorySubjectEntity } from "./category-subject.entity";

export class GroupCategoryEntity {
    id: string | undefined;
    name: string | undefined;
    type: GroupCategoryType | undefined;
    description: string | undefined;
    attributeType: string | undefined;
    categoryLocations: CategoryLocationEntity[] | undefined;
    categorySubjects: CategorySubjectEntity[] | undefined;
    categorySchedule: CategoryScheduleEntity | undefined
    createdAt: Date | undefined;
    updatedAt: Date | undefined;
}
<div class="layout-topbar">
    <div class="flex align-items-center justify-content-center">
        <button #menubutton class="p-link layout-topbar-button mr-2"
            (click)="layoutService.onMenuToggle()">
            <i class="pi pi-bars"></i>
        </button>
        <a class="layout-topbar-logo" routerLink="home">
            <div *ngIf="customTopBarLogo else logo">
                <img [src]="customTopBarLogoSource" [style]="{height: '45px'}"/>
            </div>
            <ng-template #logo>
                <img class="layout-topbar-logo-full" src="verazial-common-frontend/assets/images/all/appLogoLight.svg" alt="logo">
                <img class="layout-topbar-logo-mini" src="verazial-common-frontend/assets/images/all/vlogo.svg" alt="logo">
            </ng-template>
        </a>
    </div>

    <!--button #topbarmenubutton class="p-link layout-topbar-menu-button layout-topbar-button" (click)="layoutService.showProfileSidebar()">
        <i class="pi pi-ellipsis-v"></i>
    </button-->

    <div #topbarmenu class="layout-topbar-menu layout-topbar-menu-button">
        <div>
            <app-language-translator></app-language-translator>
        </div>
        <button #userAvatar class="p-link layout-topbar-profile" (click)="toggleMenu()">
            @if (subject?.pic!=null && subject?.pic!="") {
            <p-avatar [image]="'data:image/jpeg;base64,' + subject?.pic?.replace('data:image/jpeg;base64,', '')" styleClass="mr-2" size="large" shape="circle" />
            }@else{
            <p-avatar image="verazial-common-frontend/assets/images/all/Users.svg" styleClass="mr-2" size="large" shape="circle"
                [style]="{'background': '#DEE2E6'}" />
            }
        </button>
        <div #userOptionsMenu *ngIf="userOptionsMenuVisible"
            class="ng-trigger ng-trigger-overlayAnimation p-tieredmenu p-component p-tieredmenu-overlay ng-star-inserted"
            style="z-index: 1002; transform-origin: center top; margin-top: calc(var(--p-anchor-gutter)); opacity: 0">
            <div *ngFor="let item of items" class="p-element p-menuitem">
                <div class="p-menuitem-content">
                    @if(item.type == "profile"){
                    <div class="flex flex-column p-2 m-3">
                        <label
                            [style]="{'text-align': 'start', 'font-size': '14px', 'font-weight': '600', 'color': '#009BA9' }">
                            {{item.user}}
                        </label>
                        <label
                            [style]="{'text-align': 'start', 'font-weight': '400', 'color': '#495057', 'margin-top': '5px' }">
                            {{item.profile}}
                        </label>
                    </div>
                    }@else{
                    <a pRipple class="flex align-items-center p-menuitem-link {{ disabledItem ? 'item-disabled' : '' }}" (click)="item.command()">
                        <span [class]="item.icon" class="p-menuitem-icon"></span>
                        <span class="ml-2">{{ item.label | translate }}</span>
                        <p-badge *ngIf="item.badge" class="ml-auto" [value]="item.badge" />
                        <span *ngIf="item.shortcut"
                            class="ml-auto border-1 surface-border border-round surface-100 text-xs p-1">
                            {{ item.shortcut }}
                        </span>
                        <i *ngIf="hasSubmenu" class="pi pi-angle-right ml-auto text-primary"></i>
                    </a>
                    }
                </div>
            </div>
        </div>
    </div>
    <p-tieredMenu #menu [model]="items" [popup]="true"></p-tieredMenu>
</div>

<p-dialog [modal]="true" [draggable]="false" [(visible)]="showRoleSelection" [style]="{ width: '25rem' }" header="Header">
    <ng-template pTemplate="header">
        <div class="flex justify-content-center align-items-center align-content-center">
            <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                {{'role.change_role' | translate}}</label>
        </div>
    </ng-template>
    <app-role-selection [subjectRoles]="subjectRoles" [subjectId]="subjectId" [selectedRole]="selectedRole" (onAccept)="onAccept($event)"
        (onCancel)="onCancel()"></app-role-selection>
</p-dialog>

<p-dialog [modal]="true" [draggable]="false" [(visible)]="showChangeTenant" [style]="{ width: '25rem' }" header="Header">
    <ng-template pTemplate="header">
        <div class="flex justify-content-center align-items-center align-content-center">
            <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                {{'tenant.change_tenant' | translate}}</label>
        </div>
    </ng-template>
    @if (adminToken) {
        <div class="layout-topbar-logo" [style]="{ 'margin-left': '1rem', 'width': '265px'}">
            <p-dropdown appendTo="body"
            [style]="{'width': '265px'}"
            [options]="listOfTenants"
            [(ngModel)]="selectedTenant"
            optionLabel="name"
            [filter]="true"
            filterBy="name" 
            [showClear]="true"
            (onChange)="onTenantChange($event)"
            (onClear)="onResetTenant()"
            placeholder="Tenant">
            </p-dropdown>
        </div>
    }
    @else {
        <div class="flex justify-content-center align-items-center align-content-center">
            <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                {{'tenant.no_admin_token' | translate}}</label>
        </div>
    }
</p-dialog>
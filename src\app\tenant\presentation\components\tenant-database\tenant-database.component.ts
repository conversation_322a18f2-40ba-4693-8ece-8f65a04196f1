import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TenantDBConfigEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/tenant-db-config.entity';
import { GetTenantDBConfigByTenantIdUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/get-tenant-db-config-by-tenant-id.use-case';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';

@Component({
  selector: 'app-tenant-database',
  templateUrl: './tenant-database.component.html',
  styleUrl: './tenant-database.component.css'
})
export class TenantDatabaseComponent implements OnInit {
  @Input() tenantId: string | undefined;

  isLoading: boolean = false;

  constructor(
    private fb: FormBuilder,
    private getTenantDBConfigByTenantIdUseCase: GetTenantDBConfigByTenantIdUseCase,
    private loggerService: ConsoleLoggerService,
  ){}

  ngOnInit(): void {
    this.form.disable();
    this.getTenantDBConfig();
  }

  public form: FormGroup = this.fb.group({
    dbName:['',[Validators.required]],
    dbEngine:['',[Validators.required]],
    dbHost:['',[Validators.required]],
    dbPort:['',[Validators.required]],
    dbDatabase:['',[Validators.required]],
    dbScheme:['',[Validators.required]],
    dbUsername:['',[Validators.required]],
    dbPassword:['',[Validators.required]],
  });

  getTenantDBConfig(){
    if(this.tenantId){
      this.isLoading = true;
      this.getTenantDBConfigByTenantIdUseCase.execute({tenantId: this.tenantId}).then(
        (data)=>{
          this.fillFields(data);
        },
        (error)=>{
          this.loggerService.error(error);
        }
      )
      .finally(()=>{
        this.isLoading = false;
      });

    }
  }

  fillFields(tenantDBConfig: TenantDBConfigEntity){
    this.form.get('dbName')?.setValue(tenantDBConfig.name);
    this.form.get('dbEngine')?.setValue(tenantDBConfig.engine);
    this.form.get('dbHost')?.setValue(tenantDBConfig.host);
    this.form.get('dbPort')?.setValue(tenantDBConfig.port);
    this.form.get('dbDatabase')?.setValue(tenantDBConfig.database);
    this.form.get('dbScheme')?.setValue(tenantDBConfig.schema);
    this.form.get('dbUsername')?.setValue(tenantDBConfig.username);
    this.form.get('dbPassword')?.setValue(tenantDBConfig.password);
  }

}

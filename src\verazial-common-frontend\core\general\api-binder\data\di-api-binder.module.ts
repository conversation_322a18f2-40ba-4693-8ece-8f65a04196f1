import { NgModule } from "@angular/core";
import { ApiBinderRepository } from "../domain/repository/api-binder.repository";
import { UnenrollIdentityUseCase } from "../domain/use-cases/unenroll-identity.use-case";
import { ApiBinderRepositoryImpl } from "./repository-impl/api-binder-impl.repository";
import { CommonModule } from "@angular/common";

const unenrollIdentityUseCaseFactory =
    (apiBinderRepository: ApiBinderRepository) => new UnenrollIdentityUseCase(apiBinderRepository);

export const unenrollIdentityUseCaseProvider = {
    provide: UnenrollIdentityUseCase,
    useFactory: unenrollIdentityUseCaseFactory,
    deps: [ApiBinderRepository]
};

@NgModule({
    providers: [
        unenrollIdentityUseCaseProvider,
        { provide: ApiBinderRepository, useClass: ApiBinderRepositoryImpl }
    ],
    imports: [
        CommonModule,
    ]
})
export class DiApiBinderModule { }
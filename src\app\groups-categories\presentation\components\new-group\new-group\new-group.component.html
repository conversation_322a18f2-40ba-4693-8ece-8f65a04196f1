<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<div [formGroup]="form" class="dialog-container">
    @if( operationType == opType.INSERT ){
        <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" (activeIndexChange)="onActiveIndexChange($event)"></p-steps>
    }@else{
        <div class="flex justify-content-center">
            <p-selectButton
                [options]="stepOptions"
                formControlName="stepOptions"
                severity="secondary"
                multiple="false"
                allowEmpty="false"
                optionLabel="key"
                optionValue="value"
                dataKey="value"
                (onChange)="onActiveTabIndexChange($event)">
            </p-selectButton>
        </div>
    }
    <div *ngIf="activeIndex==0; else elseBlock">
        <div class="form-div">
            <div class="flex justify-content-end requiredFieldsLabel">
                {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
            </div>
            <div class="col-12">
                <label for="groupName" class="label-form"> {{ 'content.name' | translate }} <span class="requiredStar">*</span></label>
                <input type="text" formControlName="groupName" id="groupName"
                [disabled]="!readAndWritePermissions"
                [ngClass]="!isValid('groupName') && form.controls['groupName'].touched? 'ng-invalid ng-dirty':'' "
                pInputText [(ngModel)]="value" />
                <small *ngIf="!isValid('groupName') && form.controls['groupName'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
            </div>
            <div class="col-12">
                <label for="groupType" class="label-form"> {{ 'content.type' | translate }} <span class="requiredStar">*</span> </label>
                <p-dropdown
                    appendTo="body"
                    [options]="groupTypes"
                    placeholder="{{'content.select' | translate}}"
                    optionLabel="value"
                    [(ngModel)]="selectedGroupType"
                    formControlName="groupType"
                    id="groupType"
                    dataKey="key"
                    (onChange)="onSelectType($event)"
                    [ngClass]="!isValid('groupType') && form.controls['groupType'].touched? 'ng-invalid ng-dirty':'' "
                    >
                    <ng-template pTemplate="selectedItem">
                        <div class="flex align-items-center gap-2" *ngIf="selectedGroupType">
                            <div>{{selectedGroupType.value}}</div>
                        </div>
                    </ng-template>
                    <ng-template let-groupType pTemplate="item">
                        <div class="flex align-items-center gap-2">
                            <div>{{ groupType.value }}</div>
                        </div>
                    </ng-template>
                </p-dropdown>
                <small *ngIf="!isValid('groupType') && form.controls['groupType'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
            </div>
            <div class="col-12">
                <label for="groupDescription" class="label-form">{{ 'content.description' | translate }}</label>
                <textarea rows="3" cols="12" pInputTextarea formControlName="groupDescription"></textarea>
            </div>
        </div>
    </div>
    <ng-template #elseBlock>
        @if (selectedType==groupCategoryType.USERS) {
            <p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh' }">
                <app-list-user-subject
                    [isLoading]="isLoading"
                    [type]="subjectType"
                    [showHeader]="false"
                    [showBioSearch]="false"
                    [showTableRecordButton]="false"
                    [showMainButton]="false"
                    [mainActionOnRowClick]="false"
                    [showDeleteButton]="false"
                    [lazyLoad]="useLazyLoad"
                    [readAndWritePermissions]="readAndWritePermissions"
                    [readOnly]="true"
                    [listOfUsersSubjects]="listUsers"
                    [selectedUsersSubjects]="selectedUsers"
                    [totalRecords]="listUsers.lenght"
                    [offset]="getSubjectsRequest.offset"
                    [limit]="getSubjectsRequest.limit"
                    [allRoles]="allRoles"
                    [managerSettings]="managerSettings"
                    [konektorProperties]="konektorProperties"
                    [recordManagementMode]="false"
                    (onSelectionChange)="setSelection($event)"
                ></app-list-user-subject>
            </p-scrollPanel>
            <!-- <div class="flex flex-column pt-4">
                <div class="flex justify-content-end">
                    <p-iconField iconPosition="right">
                        <input pInputText type="text"
                        [style]="{'max-width': '250px'}"
                            formControlName="search"
                            [(ngModel)]="searchValue"
                            (input)="dt.filterGlobal($event.target.value, 'contains')"
                            placeholder="{{ 'content.search' | translate }}"
                        />
                        <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                    </p-iconField>
                </div>
                <div>
                    <div class="flex mt-5">
                        <p-table
                        #dt
                        [loading]="loadingUsers"
                        [value]="listUsers"
                        (onFilter)="onFilter($event, dt)"
                        [(selection)]="selectedUsers"
                        dataKey="id"
                        [rowHover]="true"
                        [paginator]="true"
                        [rows]="5"
                        [scrollable]="true"
                        scrollHeight="flex"
                        scrollDirection="horizontal"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        styleClass="fixed-table"
                        [showCurrentPageReport]="true"
                        currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                        [globalFilterFields]="['numId', 'names', 'lastNames', 'roles', 'birthdate', 'gender', 'email']"
                        [sortField]="'numId'" [sortOrder]="1">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 4rem"></th>
                                <th class="fixed-column" pSortableColumn="numId">{{ 'table.num_id' | translate }} <p-sortIcon field="name"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="names">{{ 'table.names' | translate }} <p-sortIcon field="name"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="lastNames">{{ 'table.lastNames' | translate }} <p-sortIcon field="lastname"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="roles">{{ 'table.profile' | translate }} <p-sortIcon field="profile"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="birthdate">{{ 'table.birthdate' | translate }} <p-sortIcon field="birthdate"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="gender">{{ 'table.gender' | translate }} <p-sortIcon field="gender"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="email">{{ 'content.email' | translate }} <p-sortIcon field="email"></p-sortIcon></th>
                            </tr>
                            <tr>
                                <th style="width: 4rem">
                                        <p-tableHeaderCheckbox/>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="numId" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="names" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="lastName" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="roles" [showMenu]="false" matchMode="customStringArray">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <p-dropdown appendTo="body"
                                                    [ngModel]="value"
                                                    [options]="allRoles"
                                                    (onChange)="filter($event.value)"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    [showClear]="true"
                                                    optionLabel="name"
                                                    optionValue="name"
                                                >
                                                    <ng-template pTemplate="selectedItem">
                                                        {{ value == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : value }}
                                                    </ng-template>
                                                    <ng-template let-option pTemplate="item">
                                                        {{ option.name == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : option.name }}
                                                    </ng-template>
                                                </p-dropdown>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="date" field="birthdate" [showMenu]="false" matchMode="customDateRange">
                                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                                <p-calendar
                                                    formControlName="date"
                                                    selectionMode="range"
                                                    (onSelect)="applyDateRangeFilter(dt, 'birthdate')"
                                                    (onInput)="applyDateRangeFilter(dt, 'birthdate')"
                                                    (onClickOutside)="applyDateRangeFilter(dt, 'birthdate')"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    dateFormat="{{ 'dateFormat' | translate }}"
                                                    appendTo="body"
                                                ></p-calendar>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter field="gender" [showMenu]="false" matchMode="equals">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <p-dropdown appendTo="body"
                                                    [ngModel]="value"
                                                    [options]="[
                                                        {label: 'options.male', value: 'male'},
                                                        {label: 'options.female', value: 'female'}
                                                    ]"
                                                    (onChange)="filter($event.value)"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    [showClear]="true"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                >
                                                    <ng-template pTemplate="selectedItem">
                                                        {{ 'options.' + value | translate }}
                                                    </ng-template>
                                                    <ng-template let-option pTemplate="item">
                                                        {{ option.label | translate }}
                                                    </ng-template>
                                                </p-dropdown>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="email" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-user let-rowIndex="rowIndex">
                                <tr class="p-selectable-row" [pSelectableRow]="user" [pSelectableRowIndex]="rowIndex">
                                    <td>
                                        <p-tableCheckbox [value]="user"></p-tableCheckbox>
                                    </td>
                                    <td showDelay="1000" pTooltip="{{user.numId}}" tooltipPosition="top" class="ellipsis-cell">{{ user.numId }}</td>
                                    <td showDelay="1000" pTooltip="{{user.names}}" tooltipPosition="top" class="ellipsis-cell">{{ user.names }}</td>
                                    <td showDelay="1000" pTooltip="{{user.lastNames}}" tooltipPosition="top" class="ellipsis-cell">{{ user.lastNames }}</td>
                                    <td showDelay="1000" pTooltip="{{listOfRolesToString(user.roles)}}" tooltipPosition="top" class="ellipsis-cell">{{ listOfRolesToString(user.roles) }}</td>
                                    @if(isValidDate(user.birthdate)){
                                        <td showDelay="1000" pTooltip="{{user.birthdate | date:('dateFormatLong' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ user.birthdate | date:('dateFormatLong' | translate) }}</td>
                                    }@else{
                                        <td showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                                    }
                                    <td showDelay="1000" pTooltip="{{user.gender != '' && user.gender != undefined && user.gender != null ? ('options.' + user.gender | translate) : ''}}" tooltipPosition="top" class="ellipsis-cell">{{ user.gender != '' && user.gender != undefined && user.gender != null ? ('options.' + user.gender | translate) : '' }}</td>
                                    <td showDelay="1000" pTooltip="{{user.email}}" tooltipPosition="top" class="ellipsis-cell">{{ user.email }}</td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div> -->
        }
        @else if (selectedType==groupCategoryType.LOCATIONS) {
            <div class="flex flex-column pt-4">
                <div class="flex justify-content-end">
                    <p-iconField iconPosition="right">
                        <input pInputText type="text"
                            [style]="{'max-width': '250px'}"
                            formControlName="search"
                            [(ngModel)]="searchValue"
                            (input)="dtLocation.filterGlobal($event.target.value, 'contains')"
                            placeholder="{{ 'content.search' | translate }}"
                        />
                        <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                    </p-iconField>
                </div>
                <div>
                    <div class="flex mt-5">
                        <p-table
                            #dtLocation
                            [loading]="loadingLocations"
                            [value]="listDeviceLocation"
                            [(selection)]="selectedDevicesLocation"
                            dataKey="id"
                            [rowHover]="true"
                            [paginator]="true"
                            [rows]="5"
                            [tableStyle]="{ 'min-width': '75rem' }"
                            styleClass="fixed-table"
                            [showCurrentPageReport]="true"
                            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                            [globalFilterFields]="['location', 'segment', 'device']"
                            [sortField]="'location'" [sortOrder]="1">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th style="width: 4rem"></th>
                                    <th class="fixed-column-sm" pSortableColumn="location">{{ 'content.location' | translate }} <p-sortIcon field="profile"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="segment">{{ 'content.segment' | translate }} <p-sortIcon field="username"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="device">{{ 'content.device' | translate }} <p-sortIcon field="name"></p-sortIcon></th>
                                </tr>
                                <tr>
                                    <th style="width: 4rem">
                                            <p-tableHeaderCheckbox/>
                                        </th>
                                        <th>
                                            <p-columnFilter type="text" field="location" [showMenu]="false" matchMode="contains">
                                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                    <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                                </ng-template>
                                            </p-columnFilter>
                                        </th>
                                        <th>
                                            <p-columnFilter type="text" field="segment" [showMenu]="false" matchMode="contains">
                                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                    <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                                </ng-template>
                                            </p-columnFilter>
                                        </th>
                                        <th>
                                            <p-columnFilter type="text" field="device" [showMenu]="false" matchMode="contains">
                                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                    <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                                </ng-template>
                                            </p-columnFilter>
                                        </th>
                                    </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-device let-rowIndex="rowIndex">
                                <tr class="p-selectable-row" [pSelectableRow]="device" [pSelectableRowIndex]="rowIndex">
                                    <td>
                                        <p-tableCheckbox [value]="device"></p-tableCheckbox>
                                    </td>
                                    <td showDelay="1000" pTooltip="{{device.location}}" tooltipPosition="top" class="ellipsis-cell">{{ device.location }}</td>
                                    <td showDelay="1000" pTooltip="{{device.segment}}" tooltipPosition="top" class="ellipsis-cell">{{ device.segment }}</td>
                                    <td showDelay="1000" pTooltip="{{device.device}}" tooltipPosition="top" class="ellipsis-cell">{{ device.device }}</td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
        }
        @else if (selectedType==groupCategoryType.SCHEDULES) {
            <div class="form-div">
                <div class="form-component">
                    <label for="scheduletype" class="label-form">{{ 'content.type' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listScheduleTypes"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="value"
                        [(ngModel)]="selectedSchedule"
                        formControlName="scheduletype"
                        id="scheduletype"
                        dataKey="key"
                        [ngClass]="!isValid('scheduletype') && form.controls['scheduletype'].touched? 'ng-invalid ng-dirty':'' "
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedSchedule">
                                <div>{{selectedSchedule.value}}</div>
                            </div>
                        </ng-template>
                        <ng-template let-scheduleItem pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ scheduleItem.value }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <small *ngIf="!isValid('scheduletype') && form.controls['scheduletype'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
            @if (selectedScheduleType=='ALL' || selectedScheduleType=='WORKING_DAYS') {
                <div class="form-div">
                    <div class="flex flex-row flex-wrap">
                        <div class="grid">
                            <div class="col-6">
                                <label for="scheduleInit" class="label-form"> {{ 'content.start' | translate }}</label>
                                <p-calendar
                                [disabled]="!readAndWritePermissions" appendTo="body"
                                id="scheduleInit" formControlName="scheduleInit" timeOnly="true" dataType="string" [readonlyInput]="true"
                                [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay"
                                [ngClass]="!isValid('scheduleInit') && form.controls['scheduleInit'].touched? 'ng-invalid ng-dirty':'' "
                                >
                                    <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                        <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                    </ng-template>
                                </p-calendar>
                                <small *ngIf="!isValid('scheduleInit') && form.controls['scheduleInit'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                            </div>
                            <div class="col-6">
                                <label for="scheduleEnd" class="label-form"> {{ 'content.end' | translate }}</label>
                                <p-calendar
                                [disabled]="!readAndWritePermissions" appendTo="body"
                                id="scheduleEnd" formControlName="scheduleEnd"
                                timeOnly="true" dataType="string" [readonlyInput]="true"
                                [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay"
                                [ngClass]="!isValid('scheduleEnd') && form.controls['scheduleEnd'].touched? 'ng-invalid ng-dirty':'' ">
                                    <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                        <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                    </ng-template>
                                </p-calendar>
                                <small *ngIf="!isValid('scheduleEnd') && form.controls['scheduleEnd'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="grid mt-3 mb-1 gap-1">
                            <div class="flex col-2 justify-content-center align-content-center">
                                <p-inputSwitch [disabled]="!readAndWritePermissions" formControlName="showDateInterval" (onChange)="showDateInterval()" />
                            </div>
                            <div class="flex col-9 justify-content-start align-content-center align-items-center">
                                {{ "category.enable_date_range" | translate}}
                            </div>
                        </div>
                    </div>
                    <div>
                        <div [style]="{'display': showDateRange?'block':'none'}">
                            <div class="grid">
                                <div class="col-6">
                                    <label for="dateInit" class="label-form"> {{ 'content.start' | translate }}</label>
                                    <p-calendar
                                    appendTo="body"
                                    id="dateInit" formControlName="dateInit" [readonlyInput]="true"
                                    [iconDisplay]="'input'" [showIcon]="true" dateFormat="{{ 'dateFormat' | translate }}"
                                    [ngClass]="!isValid('dateInit') && form.controls['dateInit'].touched? 'ng-invalid ng-dirty':'' "
                                    ></p-calendar>
                                    <small *ngIf="!isValid('dateInit') && form.controls['dateInit'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                                <div class="col-6">
                                    <label for="dateEnd" class="label-form"> {{ 'content.end' | translate }}</label>
                                    <p-calendar
                                    appendTo="body"
                                    id="dateEnd" formControlName="dateEnd" [readonlyInput]="true"
                                    [iconDisplay]="'input'" [showIcon]="true" dateFormat="{{ 'dateFormat' | translate }}"
                                    [ngClass]="!isValid('dateEnd') && form.controls['dateEnd'].touched? 'ng-invalid ng-dirty':'' "
                                    ></p-calendar>
                                    <small *ngIf="!isValid('dateEnd') && form.controls['dateEnd'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
            @else if (selectedScheduleType=='CUSTOM') {
                <div class="grid">
                    <div class="col-6">
                        <div class="form-div">
                            @for (day of days; track $index) {
                                @if (day.key != 'SATURDAY' && day.key != 'SUNDAY') {
                                    <div class="grid flex-nowrap gap-0 flex align-items-center justify-content-center">
                                        <div class="col-3">
                                            <label class="font-days">{{ day.value }}</label>
                                        </div>
                                        <div class="col-4">
                                            <p-calendar appendTo="body" [disabled]="!readAndWritePermissions" id="scheduleInit" formControlName="{{day.key}}Init" id="{{day.key}}Init" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                                </ng-template>
                                            </p-calendar>
                                            <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                        <div class="col"><label class="font-days">{{ 'content.to' | translate }}</label></div>
                                        <div class="col-4">
                                            <p-calendar appendTo="body" [disabled]="!readAndWritePermissions" id="scheduleInit" formControlName="{{day.key}}End" id="{{day.key}}End" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                                </ng-template>
                                            </p-calendar>
                                            <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-div">
                            @for (day of days; track $index) {
                                @if (day.key == 'SATURDAY' || day.key == 'SUNDAY') {
                                    <div class="grid flex-nowrap gap-0 flex align-items-center justify-content-center">
                                        <div class="col-3">
                                            <label class="font-days">{{ day.value }}</label>
                                        </div>
                                        <div class="col-4">
                                            <p-calendar appendTo="body" [disabled]="!readAndWritePermissions" id="scheduleInit" formControlName="{{day.key}}Init" id="{{day.key}}Init" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                                </ng-template>
                                            </p-calendar>
                                            <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                        <div class="col"><label class="font-days">{{ 'content.to' | translate }}</label></div>
                                        <div class="col-4">
                                            <p-calendar appendTo="body" [disabled]="!readAndWritePermissions" id="scheduleInit" formControlName="{{day.key}}End" id="{{day.key}}End" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                                </ng-template>
                                            </p-calendar>
                                            <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="grid mt-3 mb-1 gap-1">
                    <div class="flex col-1 justify-content-center align-content-end align-items-center">
                        <p-inputSwitch formControlName="showDateIntervalCustom" (onChange)="showDateIntervalCustom()" />
                    </div>
                    <div class="flex col-9 justify-content-start align-items-center">
                        {{ "category.enable_date_range" | translate}}
                    </div>
                </div>
                <div [style]="{'display': showDateRangeCustom?'block':'none'}">
                    <div class="grid">
                        <div class="col-3">
                            <label for="dateInitCustom" class="label-form"> {{ 'content.start' | translate }}</label>
                            <p-calendar appendTo="body" [disabled]="!readAndWritePermissions" id="dateInitCustom" [disabled]="true" formControlName="dateInitCustom" dataType="string" [readonlyInput]="true"
                            [iconDisplay]="'input'" [showIcon]="true" [minDate]="minDate"
                            [ngClass]="!isValid('dateInitCustom') && form.controls['dateInitCustom'].touched? 'ng-invalid ng-dirty':'' "
                            ></p-calendar>
                            <small *ngIf="!isValid('dateInitCustom') && form.controls['dateInitCustom'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-3">
                            <label for="dateEndCustom" class="label-form"> {{ 'content.end' | translate }}</label>
                            <p-calendar appendTo="body" [disabled]="!readAndWritePermissions" id="dateEndCustom" [disabled]="false" formControlName="dateEndCustom" dataType="string" [readonlyInput]="true"
                            [iconDisplay]="'input'" [showIcon]="true" [minDate]="minDate"
                            [ngClass]="!isValid('dateEndCustom') && form.controls['dateEndCustom'].touched? 'ng-invalid ng-dirty':'' "
                            ></p-calendar>
                            <small *ngIf="!isValid('dateEndCustom') && form.controls['dateEndCustom'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>
                </div>
            }
        }
    </ng-template>
    <div>
        @if (activeIndex==0) {
            <div class="footer-buttons-container">
                <p-button label="{{ 'next' | translate }}" icon="pi pi-angle-right" iconPos="right"
                    (onClick)="onNext()"
                    [disabled]="!isValid('groupName') || !isValid('groupType')? true : false"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
            </div>
        }@else{
            <div class="footer-buttons-container">
                <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left"
                (onClick)="onBack()"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
                <p-button
                [disabled]="!readAndWritePermissions"
                [label]="createUpdateButtonTitle"  iconPos="right"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                (onClick)="saveGroup()">
                </p-button>
            </div>
        }
    </div>
</div>

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccessAssignmentComponent } from './access-assignment/access-assignment.component';
import { TranslateModule } from '@ngx-translate/core';
import { PickListModule } from 'primeng/picklist';
import { SelectButtonModule } from 'primeng/selectbutton';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { InputSwitchModule } from 'primeng/inputswitch';

@NgModule({
  declarations: [
    AccessAssignmentComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    PickListModule,
    SelectButtonModule,
    InputSwitchModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    AccessAssignmentComponent
  ]
})
export class AccessAssignmentModule { }

import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, FilterService, MessageService, TreeNode } from "primeng/api";
import { Table } from "primeng/table";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { SubjectLocationEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-location.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { DeleteSubjectLocationByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/delete-subject-location.use-case";
import { GetSubjectLocationsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-location-by-subject-id.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { SaveSubjectLocationUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject-location.use-case";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { UpdateSettingsByIdUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/update-settings-by-id.use-case";
import { GetSettingsByApplicationUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case";
import { environment } from "src/environments/environment";
import { ManagerSettingsEntity } from "src/verazial-common-frontend/core/general/manager/domain/entity/manager-settings.model";
import { UpdateSubjectLocationUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject-location.use-case";
import { EntryExitService } from "src/verazial-common-frontend/core/services/entry-exit.service";
import { UpdateSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject.use-case";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";

@Component({
    selector: 'app-user-locations',
    templateUrl: './user-locations.component.html',
    styleUrl: './user-locations.component.css',
})
export class UserLocationsComponent implements OnInit, OnChanges {

    // Inputs
    @Input() userSubject: SubjectEntity | undefined;
    @Input() userIsVerified: boolean = false;
    @Input() isPrisoner: boolean = false;
    @Output() updateSubjectLocation: EventEmitter<void> = new EventEmitter<void>();

    canReadAndWrite: boolean = false;
    canSeeAllLocations: boolean = false;
    readOnly: boolean = false;
    isDisableSaveButton: boolean = true;

    searchValue: string | undefined;
    showNewLocDialog: boolean = false;
    showLocDialog: boolean = false;

    listOfLocations: SubjectLocationEntity[] = [];
    selectedLocations: SubjectLocationEntity[] = [];

    filteredValues: any[] = [];

    access_identifier = AccessIdentifier.USER_LOCATION;
    all_locations_idenfitier = AccessIdentifier.GET_ALL_LOCATIONS;
    actualDate: Date = new Date();


    formGroup: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    rangeDates: Date[] | null = null;

    formGroupExit: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    rangeDatesExit: Date[] | null = null;


    public form: FormGroup = this.fb.group({
        entryDate: [this.actualDate, Validators.required],
        locationId: ['', Validators.required],
        actualLocation: [true],
        regime: [''],
        classification: [''],
        pending: [true],
        exitDate: [],
        comments: [''],
    });
    location: SubjectLocationEntity | undefined;
    isNew: boolean = true;
    // Options
    lEntryDate: GenericKeyValue | undefined;
    lLocationId: GenericKeyValue | undefined;
    lActualLocation: GenericKeyValue | undefined;
    lRegime: GenericKeyValue | undefined;
    lClassification: GenericKeyValue | undefined;
    lPending: GenericKeyValue | undefined;
    lExitDate: GenericKeyValue | undefined;
    lComments: GenericKeyValue | undefined;

    lRegimeOptions: GenericKeyValue[] = [];
    lRegimesParameter: string = 'regimes';
    lClassificationOptions: GenericKeyValue[] = [];
    lClassificationsParameter: string = 'classifications';
    lLocationOptions!: any[];
    defaultRegex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/

    settings: ManagerSettingsEntity | undefined;

    isLoading: boolean = false;

    private rejectTimeout: any;

    constructor(
        private checkPermissions: CheckPermissionsService,
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        private auditTrailService: AuditTrailService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private newLocationsService: NewLocationsService,
        private deleteSubjectLocationByIdUseCase: DeleteSubjectLocationByIdUseCase,
        private getSubjectLocationsBySubjectIdUseCase: GetSubjectLocationsBySubjectIdUseCase,
        private saveSubjectLocationUseCase: SaveSubjectLocationUseCase,
        private updateSubjectLocationUseCase: UpdateSubjectLocationUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private updateSettingsByIdUseCase: UpdateSettingsByIdUseCase,
        private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
        private entriesExitsService: EntryExitService,
        private updateSubjectUseCase: UpdateSubjectUseCase,
    ) {

        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
    }

    ngOnInit() {

        this.isLoading = true;

        this.entriesExitsService.clearSubjectLocations();

        this.entriesExitsService.subjectLocations$.subscribe(locations => {
            this.listOfLocations = locations;
        });

        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
        if (!this.canReadAndWrite) {
            this.readOnly = this.checkPermissions.hasReadPermissions(this.access_identifier);
        }

        this.canSeeAllLocations = this.checkPermissions.hasReadPermissions(this.all_locations_idenfitier);

        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
            (data) => {
                this.localStorageService.setSessionSettings(data.settings!);
                this.settings = data;
            },
            (e) => {
                this.loggerService.error('Error Getting Settings:');
                this.loggerService.error(e);
            }
        );

        let settings = this.localStorageService.getSessionSettings();
        if (settings) {
            let options = settings.continued1?.newLocations!;
            if (this.canSeeAllLocations)
                this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptions(options, this.isPrisoner);
            else {
                this.getKonektorPropertiesUseCase.execute().subscribe({
                    next: (data) => {
                        const konektorProperties = data;
                        var location = konektorProperties.locationId;
                        this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptionsForLocation(options, location!, this.isPrisoner);
                    },
                    error: (e) => {
                        this.loggerService.error('Error Getting Konektor Properties:');
                        this.loggerService.error(e);
                    }
                });
            }

            let regimeOptions = settings.catalogs?.find((catalog) => catalog.parameter === this.lRegimesParameter)?.options;
            this.lRegimeOptions = regimeOptions ? (JSON.parse(regimeOptions) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.lRegimesParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];

            let classOptions = settings.catalogs?.find((catalog) => catalog.parameter === this.lClassificationsParameter)?.options;
            this.lClassificationOptions = classOptions ? (JSON.parse(classOptions) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.lClassificationsParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }

        this.getSubjectLocationsBySubjectIdUseCase.execute({ id: this.userSubject?.id! }).then(
            (data) => {
                this.listOfLocations = data;
                this.entriesExitsService.setSubjectLocations(data);
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_LOCATION_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
            }
        ).finally(() => {
            this.isLoading = false;

        });
    }

    showNewLocDialogFunc() {
        console.log(this.isPrisoner);
        console.log(this.lLocationOptions);
        //console.log(this.listOfLocations);
        if (this.isNew) {
            if (this.isPrisoner)
                if (this.listOfLocations.length > 0 && this.listOfLocations[0].pending) {
                    this.messageService.add({
                        severity: 'warn',
                        summary: this.translateService.instant('messages.warning'),
                        detail: this.translateService.instant('messages.noCreateLocationBecausePending'),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    return;
                }
            this.showNewLocDialog = true;
            if (!this.location) {
                this.location = new SubjectLocationEntity();
                this.location.subjectId = this.userSubject?.id!;
            }
            this.location.actualLocation = false;
            this.location.pending = true;
        }

        this.showNewLocDialog = true;
        if (!this.location) {
            this.location = new SubjectLocationEntity();
            this.location.subjectId = this.userSubject?.id!;
        }
        this.form.enable();
        this.form.get('actualLocation')?.disable();
        this.form.get('pending')?.disable();

        if(this.isPrisoner)
        {
            this.form.get('entryDate')?.disable();
            this.form.get('exitDate')?.disable();
        }

    }



    ngOnChanges(changes: SimpleChanges): void {
        if (changes['userSubject'] && changes['userSubject'].currentValue) {
            this.ngOnInit();
        }
        else if (changes['isPrisoner']) {
          let settings = this.localStorageService.getSessionSettings();
          if (settings) {
            let options = settings.continued1?.newLocations!;
            if (this.canSeeAllLocations)
              this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptions(options, this.isPrisoner);
            else {
              this.getKonektorPropertiesUseCase.execute().subscribe({
                next: (data) => {
                  const konektorProperties = data;
                  var location = konektorProperties.locationId;
                  this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptionsForLocation(options, location!, this.isPrisoner);
                },
                error: (e) => {
                  this.loggerService.error('Error Getting Konektor Properties:');
                  this.loggerService.error(e);
                }
              });
            }
          }
        }
      }

    editLocation(location: SubjectLocationEntity) {

        if (!location) {
            if (this.selectedLocations.length === 1) {
                location = this.selectedLocations[0];
            }
            else {
                return;
            }
        }

        this.isNew = false;
        this.location = { ...location };

        this.showNewLocDialogFunc();
        if (this.readOnly || !this.userIsVerified) {
            this.form.disable();
        } else {
            this.form.enable();
            this.form.get('locationId')?.disable();
            this.form.get('actualLocation')?.disable();
            this.form.get('pending')?.disable();
        }

        this.form.get('entryDate')?.setValue(location.entryDate);
        this.form.get('locationId')?.setValue({ key: location.locationId, label: this.newLocationsService.getNodeWithKeyInSelectTree(location.locationId!, this.lLocationOptions)?.label });
        this.form.get('actualLocation')?.setValue(location.actualLocation);
        this.form.get('regime')?.setValue(this.lRegimeOptions.find((regime) => regime.key === location.regime));
        this.form.get('classification')?.setValue(this.lClassificationOptions.find((classification) => classification.key === location.classification));
        this.form.get('pending')?.setValue(location.pending);

        if (!isNaN(location.exitDate?.getTime()!)) {
            this.form.get('exitDate')?.setValue(location.exitDate);
        }

        this.form.get('comments')?.setValue(location.comments);

        if(this.isPrisoner)
        {
            this.form.get('entryDate')?.disable();
            this.form.get('exitDate')?.disable();
        }
    }

    deleteLocation(location?: SubjectLocationEntity) {
        let subjectLocationsToDelete: SubjectLocationEntity[] = [];
        if (this.selectedLocations.length >= 1 && !location) {
            subjectLocationsToDelete = this.selectedLocations;
        } else if (location) {
            subjectLocationsToDelete.push(location);
        }

        for(let i = 0; i < subjectLocationsToDelete.length; i++)
        {
            if(subjectLocationsToDelete[i].actualLocation)
            {
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.cant_delete_actual_location'),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                return;
            }
        }

        this.confirmDelete(subjectLocationsToDelete);

    }

    // Confirmation Dialogs
    confirmDelete(location: SubjectLocationEntity[]) {

        let message: string = ""
        if (location.length == 1) {
            message = `${this.translateService.instant('messages.delete_single_record')}?`;
        } else {
            message = this.translateService.instant('messages.delete_multitple_records') + "<br>(" + location.length + ")<br>";
        }

        this.confirmationService.confirm({
            message: message,
            header: this.translateService.instant('messages.delete_confirmation_header'),
            icon: 'pi pi-exclamation-triangle',
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptIcon: "none",
            rejectIcon: "none",
            acceptLabel: this.translateService.instant("delete"),
            rejectLabel: this.translateService.instant("no"),
            accept: () => {
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(location) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DELETE_USER_LOCATION, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete(location); }, at_attributes);
            },
            reject: () => {
                this.clearRejectTimeout();
            }
        });

        // Set a timeout to automatically trigger the reject action after 10 seconds
        this.rejectTimeout = setTimeout(() => {
            this.confirmationService.close(); // Close the dialog
        }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
    }


    onSubmitDelete(subjectLocationsToDelete: SubjectLocationEntity[]) {
        this.isLoading = true;
        let itemsProcessed = 0;

        subjectLocationsToDelete.forEach((location, index, array) => {
            this.deleteLocationById(location);
            itemsProcessed++;
            if (itemsProcessed === array.length) {
                this.isLoading = false;
                this.messageService.add({
                    severity: 'success',
                    summary: this.translateService.instant('content.successTitle'),
                    detail: this.translateService.instant('messages.success_general'),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.selectedLocations = [];
                this.isLoading = false;
            }
        });
    }

    deleteLocationById(subjectLocation: SubjectLocationEntity) {
        this.deleteSubjectLocationByIdUseCase.execute({ id: subjectLocation.id! }).then(
            async (data) => {
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(subjectLocation) },
                ];
                if (data.success) {
                    this.listOfLocations = [...this.listOfLocations.filter((location) => location.id !== subjectLocation.id)];

                    if (this.userSubject?.locationId === subjectLocation.locationId) {
                        if(this.listOfLocations.length > 0 && !this.isPrisoner)
                        {
                            await this.updateSubject(this.listOfLocations[0].locationId!);
                        }
                        else
                            await this.updateSubject('');
                    }
                    this.entriesExitsService.setSubjectLocations(this.listOfLocations);
                    if (this.newLocationsService.removeOccupiedByFromLocation(this.settings?.settings?.continued1?.newLocations, subjectLocation.locationId!, subjectLocation.subjectId!)) {
                        this.updateSettings(false);
                    }
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_USER_LOCATION, 0, 'SUCCESS', '', at_attributes);
                }
                else {
                    at_attributes.push({ name: AuditTrailFields.ERROR, value: JSON.stringify(data) });
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_USER_LOCATION, 0, 'ERROR', '', at_attributes);
                }
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_USER_LOCATION, 0, 'ERROR', '', at_attributes);
            }
        );
    }

    onSubjectLocationSelectionChange(event: any) {
        this.loggerService.debug(this.selectedLocations);
    }

    trackDataChange() {
        if (!this.location) {
            this.location = new SubjectLocationEntity();
            this.location.subjectId = this.userSubject?.id!;
        }

        if (this.isNew) this.location.id = undefined;
        this.location.entryDate = this.form.get('entryDate')?.value;
        this.location.locationId = this.form.get('locationId')?.value?.key;
        this.location.actualLocation = this.form.get('actualLocation')?.value;
        this.location.regime = this.form.get('regime')?.value?.key;
        this.location.classification = this.form.get('classification')?.value?.key;
        this.location.pending = this.form.get('pending')?.value;
        if (this.form.get('exitDate')?.value)
            this.location.exitDate = this.form.get('exitDate')?.value;
        else
            this.location.exitDate = undefined;

        this.location.comments = this.form.get('comments')?.value;

        this.isDisableSaveButton = !this.form.valid;
    }

    trackDataChangePending(event: any) {
        if (!this.location) {
            this.location = new SubjectLocationEntity();
            this.location.subjectId = this.userSubject?.id!;
        }
        this.location.pending = event;
        if (event) {
            this.location.actualLocation = false;
        }
        this.isDisableSaveButton = !this.form.valid;
    }

    trackDataChangeActualLocation(event: any) {
        if (!this.location) {
            this.location = new SubjectLocationEntity();
            this.location.subjectId = this.userSubject?.id!;
        }
        this.location.actualLocation = event;
        if (event) {
            this.location.pending = false;
        }
        this.isDisableSaveButton = !this.form.valid;
    }

    saveLocation() {
        if (this.form.valid && this.location) {
            this.isLoading = true;
            if (this.isNew) {
                this.saveSubjectLocationUseCase.execute({ subjectLocation: this.location }).then(
                    async (data) => {

                        if (this.listOfLocations.length == 0) {
                            await this.updateSubject(this.location?.locationId!);
                        }

                        this.listOfLocations = [...this.listOfLocations, data];
                        this.entriesExitsService.setSubjectLocations(this.listOfLocations);
                        this.onCancelDialog();
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_USER_LOCATION, 0, 'SUCCESS', '', at_attributes);

                        if (this.newLocationsService.addOccupiedByToLocation(this.settings?.settings?.continued1?.newLocations, data.locationId!, data.subjectId!)) {
                            this.updateSettings();
                        }
                    },
                    (e) => {
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_USER_LOCATION, 0, 'ERROR', '', at_attributes);
                    }
                )
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
            else {
                this.loggerService.debug(this.location);
                this.updateSubjectLocationUseCase.execute({ subjectLocation: this.location }).then(
                    (data) => {
                        const index = this.listOfLocations.findIndex((location) => location.id === data.id);
                        if (index !== -1) {
                            const updatedList = [...this.listOfLocations];
                            updatedList[index] = data;
                            this.listOfLocations = updatedList;
                            this.entriesExitsService.setSubjectLocations(this.listOfLocations);
                        }
                        this.onCancelDialog();
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.location) },
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.UPDATE_USER_LOCATION, 0, 'SUCCESS', '', at_attributes);
                    },
                    (e) => {
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.UPDATE_USER_LOCATION, 0, 'ERROR', '', at_attributes);
                    }
                )
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
            this.isLoading = false;
        }
    }

    updateSubject(locationId: string): Promise<void> {

        this.userSubject!.center = this.newLocationsService.getRootKeyPath(locationId, this.lLocationOptions);
        this.userSubject!.locationId = locationId;

        return this.updateSubjectUseCase.execute({ subject: this.userSubject! }).then(
            (data) => {
                if (data) {
                    this.updateSubjectLocation.emit();

                    this.isLoading = false;
                }
            },
            (e) => {
                this.loggerService.error('Error Updating Subject:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_updating_subject')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }
        );
    }


    updateSettings(showMessage: boolean = true) {
        this.updateSettingsByIdUseCase.execute({
            id: this.settings?.id!,
            settings: this.settings?.settings!,
        }).then(
            (data) => {
                this.localStorageService.setSessionSettings(data.settings!);
                if (showMessage)
                    this.messageService.add({
                        severity: 'success',
                        summary: this.translateService.instant('content.successTitle'),
                        detail: this.translateService.instant('messages.location_created'),
                    });
            },
            (e) => {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: this.translateService.instant('messages.location_created_error'),
                });
            },
        );
    }

    onCancelDialog() {
        this.location = undefined;
        this.isNew = true;

        this.showNewLocDialog = false;
        this.form.reset();
        this.lActualLocation = undefined;
        this.lClassification = undefined;
        this.lEntryDate = undefined;
        this.lLocationId = undefined;
        this.lPending = undefined;
        this.lRegime = undefined;
        this.lExitDate = undefined;
        this.lComments = undefined;
    }

    isRequiredField(field: string): boolean {
        return this.validatorService.isRequiredField(this.form, field);
    }

    isValid(field: string): boolean {
        return this.validatorService.isValidField(this.form, field);
    }

    /* Search */
    onFilter(event: any, dt: Table) {
        if (!event.filters['entryDate'].value) {
            this.rangeDates = null;
        }
        if (event.filters['exitDate'].value) {
            this.rangeDatesExit = null;
        }
        this.filteredValues = event.filteredValue;
    }

    isValidDate(dateString: string | null): boolean {
        if (!dateString) return false;
        const date = new Date(dateString);
        return !isNaN(date.getTime());
    }

    /* Date Range Filter */
    applyDateRangeFilter(dt: Table, field: string) {
        this.rangeDates = this.formGroup.get('date')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    applyDateRangeFilterExit(dt: Table, field: string) {
        this.rangeDates = this.form.get('dateExit')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    private clearRejectTimeout() {
        if (this.rejectTimeout) {
            clearTimeout(this.rejectTimeout);
        }
        this.rejectTimeout = null;
    }

    getLabel(key: string, catalogParameter: string): string {
        let reasonTranslations = this.settings?.settings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter)?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}
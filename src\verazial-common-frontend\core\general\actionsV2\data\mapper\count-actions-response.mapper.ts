import { CountActionsResponse } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { CountActionsResponseEntity, CountModeResult, Group } from "../../domain/entity/count-actions-response.entity";
import { StructMapper } from "./struct.mapper";

export class CountActionsResponseMapper extends Mapper<CountActionsResponse, CountActionsResponseEntity> {
    structMapper = new StructMapper();

    override mapFrom(param: CountActionsResponse): CountActionsResponseEntity {
        const entity = new CountActionsResponseEntity();

        entity.totalResults = Number(param.getTotalResults()); // Convert int64 to number
        entity.groupByResults = param.getGroupByResultsList().map(group => this.mapGroup(group));
        entity.extraCountResults = param.getExtraCountResultsList().map(result => this.mapCountModeResult(result));

        return entity;
    }

    override mapTo(param: CountActionsResponseEntity): CountActionsResponse {
        throw new Error("Method not implemented.");
    }

    private mapGroup(group: CountActionsResponse.Group): Group {
        return {
            groupTotal: Number(group.getGroupTotal()), // Convert int64 to number
            groupValue: group.getGroupValue(),
            subGroups: group.getSubGroupsList().map(subGroup => this.mapGroup(subGroup)), // Recursively map subGroups
            groupExtraCountResults: group.getGroupExtraCountResultsList().map(result => this.mapCountModeResult(result))
        };
    }

    private mapCountModeResult(result: CountActionsResponse.CountModeResult): CountModeResult {
        const mode = result.getAverageValue();
        return mode
            ? { mode: { attributePath: mode.getAttributePath(), value: mode.getValue() } }
            : {};
    }
}
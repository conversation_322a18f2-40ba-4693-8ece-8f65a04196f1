import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialEntity } from "../entity/credential.entity";
import { Observable } from "rxjs";
import { CredentialRepository } from "../repositories/credential.repository";

export class AddCredentialsUseCase implements UseCase<CredentialEntity, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: CredentialEntity): Observable<any> {
        return this.credentialsRepository.addCredentials(params);
    }

}
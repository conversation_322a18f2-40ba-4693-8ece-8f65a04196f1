import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { DataSourceEntity } from "../../domain/entities/data-source.entity";
import { DataSourceRepository } from "../../domain/repositories/data-source.repository";
import { DataSourceGrpcMapper } from "../mapper/data-source-grpc.mapper";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { AppDataSourceRequest, AppDataSourceGrpcModel } from "src/verazial-common-frontend/core/generated/datasource/datasource_pb";
import { CoreAppDataSourceClient } from "src/verazial-common-frontend/core/generated/datasource/DatasourceServiceClientPb";

@Injectable({
    providedIn: 'root',
})
export class DataSourceRepositoryGrpcImpl extends DataSourceRepository {

    dataSourceGrpcMapper = new DataSourceGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }

    override addAppDataSource(dataSource: DataSourceEntity): Promise<DataSourceEntity> {
        let request = this.dataSourceGrpcMapper.mapTo(dataSource);

        let coreAppDataSourceClient = new CoreAppDataSourceClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreAppDataSourceClient.addAppDataSource(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.dataSourceGrpcMapper.mapFrom(
                            response.getAppdatasourcemodel()!
                        ));
                    }
                }
            });
        });
    }
    override updateAppDataSourceById(dataSource: DataSourceEntity): Promise<SuccessResponse> {

        let request = this.dataSourceGrpcMapper.mapTo(dataSource);

        let success!: SuccessResponse;

        let coreAppDataSourceClient = new CoreAppDataSourceClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreAppDataSourceClient.updateAppDataSourceById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override getAppDataSourceById(params: { id: string; }): Promise<DataSourceEntity> {
        let request = new AppDataSourceRequest();

        request.setValue(params.id)

        let coreAppDataSourceClient = new CoreAppDataSourceClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreAppDataSourceClient.getAppDataSourceById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.dataSourceGrpcMapper.mapFrom(
                            response.getAppdatasourcemodel()!
                        ));
                    }
                }
            });
        });
    }

    override getAppDataSourceByName(params: { name: string; }): Promise<DataSourceEntity> {
        let request = new AppDataSourceRequest();

        request.setValue(params.name);

        let coreAppDataSourceClient = new CoreAppDataSourceClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreAppDataSourceClient.getAppDataSourceByName(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.dataSourceGrpcMapper.mapFrom(
                            response.getAppdatasourcemodel()!
                        ));
                    }
                }
            });
        });
    }

    override getAllDataSources(): Promise<DataSourceEntity[]> {
        let responseDatasource: DataSourceEntity[] = [];

        let coreAppDataSourceClient = new CoreAppDataSourceClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        let grpc = coreAppDataSourceClient.getAllDataSources(new Empty, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: AppDataSourceGrpcModel) => {
                responseDatasource.push(this.dataSourceGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseDatasource);
            });
        });
    }

    override deleteDataSourceById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new AppDataSourceRequest();

        request.setValue(params.id);

        let success!: SuccessResponse;

        let coreAppDataSourceClient = new CoreAppDataSourceClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreAppDataSourceClient.deleteDataSourceById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }
}
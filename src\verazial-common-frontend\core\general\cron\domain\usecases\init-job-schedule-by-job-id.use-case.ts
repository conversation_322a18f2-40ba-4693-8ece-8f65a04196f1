import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ScheduleJobRequestModel } from "../../common/models/schedule-job-request.model";
import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";
import { CronServiceRepository } from "../repository/cron-service.repository";

export class InitJobScheduleByJobIdUseCase implements UseCaseGrpc<ScheduleJobRequestModel, SuccessResponse> {
    constructor(private cronServiceRepository: CronServiceRepository) { }
    execute(params: ScheduleJobRequestModel): Promise<SuccessResponse> {
        return this.cronServiceRepository.initJobScheduleByJobId(params);
    }
}
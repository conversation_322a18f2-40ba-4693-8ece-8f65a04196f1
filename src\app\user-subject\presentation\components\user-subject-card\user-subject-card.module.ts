import { NgModule } from "@angular/core";
import { UserSubjectCardComponent } from "./user-subject-card/user-subject-card.component";
import { CommonModule } from "@angular/common";
import { TranslateModule } from "@ngx-translate/core";
import { AvatarModule } from "primeng/avatar";
import { ButtonModule } from "primeng/button";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { ProfilePicModule } from "src/verazial-common-frontend/modules/shared/components/profile-pic/profile-pic.module";
import { TooltipModule } from "primeng/tooltip";

@NgModule({
  declarations: [
    UserSubjectCardComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG */
    AvatarModule,
    ButtonModule,
    TooltipModule,
    ProgressSpinnerModule,
    /* Custom Modules */
    ProfilePicModule,
  ],
  exports: [
    UserSubjectCardComponent
  ]
})
export class UserSubjectCardModule { }
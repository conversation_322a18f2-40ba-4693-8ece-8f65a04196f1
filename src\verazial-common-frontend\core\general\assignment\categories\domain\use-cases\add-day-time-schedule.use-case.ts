import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { DayTimeScheduleEntity } from "../entity/day-time-schedule.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class AddDayTimeScheduleUseCase implements UseCaseGrpc<{ listOfDayTimeSchedule: DayTimeScheduleEntity[] }, DayTimeScheduleEntity[]> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { listOfDayTimeSchedule: DayTimeScheduleEntity[]; }): Promise<DayTimeScheduleEntity[]> {
        return this.groupCategoryRepository.addDayTimeSchedule(params);
    }
}
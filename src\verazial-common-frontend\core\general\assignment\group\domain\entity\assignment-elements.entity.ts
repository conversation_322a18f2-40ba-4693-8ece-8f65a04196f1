
import { GroupCategoryType } from "../../../categories/common/models/group-category-type.enum";
import { AssignmentType } from "../../common/enums/assignment-type.enum";

export class AssignmentElementEntity {
    id: string | undefined;
    assignmentId: string | undefined;
    type: AssignmentType | undefined;
    subtype: GroupCategoryType | undefined;
    elementId: string | undefined;
    createdAt: Date | undefined;
    updatedAt: Date | undefined;
}
import { CommonModule } from "@angular/common";
import { ApplicationWindowRepository } from "../domain/repositories/application-window.repository";
import { AddAppWindowUseCase } from "../domain/use-cases/add-app-window.use-case";
import { DeleteAppWindowByIdUseCase } from "../domain/use-cases/delete-app-window-by-id.use-case";
import { GetAppWindowByAppIdUseCase } from "../domain/use-cases/get-app-window-by-app-id.use-case";
import { GetAppWindowByIdUseCase } from "../domain/use-cases/get-app-window-by-id.use-case";
import { UpdateAppWindowByIdUseCase } from "../domain/use-cases/update-app-window-by-id.use-case";
import { ApplicationWindowRepositoryGrpcImp } from "./repository-impl/application-window-impl-grpc.repository";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { NgModule } from "@angular/core";

const addAppWindowUseCaseFactory =
    (applicationWindowRepository: ApplicationWindowRepository) => new AddAppWindowUseCase(applicationWindowRepository);

const updateAppWindowByIdUseCaseFactory =
    (applicationWindowRepository: ApplicationWindowRepository) => new UpdateAppWindowByIdUseCase(applicationWindowRepository);

const getAppWindowByIdUseCaseFactory =
    (applicationWindowRepository: ApplicationWindowRepository) => new GetAppWindowByIdUseCase(applicationWindowRepository);

const getAppWindowByAppIdUseCaseFactory =
    (applicationWindowRepository: ApplicationWindowRepository) => new GetAppWindowByAppIdUseCase(applicationWindowRepository);

const deleteAppWindowByIdUseCaseFactory =
    (applicationWindowRepository: ApplicationWindowRepository) => new DeleteAppWindowByIdUseCase(applicationWindowRepository);

export const addAppWindowUseCaseProvider = {
    provide: AddAppWindowUseCase,
    useFactory: addAppWindowUseCaseFactory,
    deps: [ApplicationWindowRepository]
}

export const updateAppWindowByIdUseProvider = {
    provide: UpdateAppWindowByIdUseCase,
    useFactory: updateAppWindowByIdUseCaseFactory,
    deps: [ApplicationWindowRepository]
}

export const getAppWindowByIdUseCaseProvider = {
    provide: GetAppWindowByIdUseCase,
    useFactory: getAppWindowByIdUseCaseFactory,
    deps: [ApplicationWindowRepository]
}

export const getAppWindowByAppIdUseCaseProvider = {
    provide: GetAppWindowByAppIdUseCase,
    useFactory: getAppWindowByAppIdUseCaseFactory,
    deps: [ApplicationWindowRepository]
}

export const deleteAppWindowByIdUseCaseProvider = {
    provide: DeleteAppWindowByIdUseCase,
    useFactory: deleteAppWindowByIdUseCaseFactory,
    deps: [ApplicationWindowRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        addAppWindowUseCaseProvider,
        updateAppWindowByIdUseProvider,
        getAppWindowByIdUseCaseProvider,
        getAppWindowByAppIdUseCaseProvider,
        deleteAppWindowByIdUseCaseProvider,
        { provide: ApplicationWindowRepository, useClass: ApplicationWindowRepositoryGrpcImp },
        provideHttpClient(withInterceptorsFromDi())
    ]
})

export class DiApplicationWindowModule { }
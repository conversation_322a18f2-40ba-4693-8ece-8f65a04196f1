<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-content-center w-full h-full mt-4">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" [ngClass]="{'spinner': isLoading}"></p-progressSpinner>
    </div>
</ng-template>
<div *ngIf="!isLoading else loadingSpinner" class="subcontainer">
    <div *ngIf="listOfBelongings.length != 0 else empty" class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.belongings' | translate}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedBelongings.length > 0){
                        <div>
                            {{ selectedBelongings.length + ' ' + ('content.selected' | translate) }}
                        </div>
                        <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedBelongings.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="editBelonging()"></button>
                        <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteBelonging()"></button>
                    }
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        ngClass="add-action-main-full"
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'content.new_record' | translate }}"
                        icon="pi pi-plus" iconPos="right"
                        [rounded]="true"
                        (onClick)="onNewBelonging()"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus"
                        [rounded]="true"
                        (onClick)="onNewBelonging()"
                    ></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfBelongings"
            (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedBelongings"
            (selectionChange)="onBelongingSelectionChange($event)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="[
                'id',
                'registrationDate',
                'subjectReceptionSignatureDate',
                'subjectReturnSignatureDate',
                'comments',
                'createdAt',
                'updatedAt',
            ]"
            [sortField]="'id'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="id"> {{ 'content.id' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="registrationDate">{{ 'content.registrationDate' | translate }} <p-sortIcon field="registrationDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="subjectReceptionSignatureDate">{{ 'content.subjectReceptionSignatureDate' | translate }} <p-sortIcon field="subjectReceptionSignatureDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="subjectReturnSignatureDate">{{ 'content.subjectReturnSignatureDate' | translate }} <p-sortIcon field="subjectReturnSignatureDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="status">{{ 'content.status' | translate }} <p-sortIcon field="status"></p-sortIcon></th>
                    <th class="fixed-column" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox/>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="registrationDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'registrationDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'registrationDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'registrationDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="subjectReceptionSignatureDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'subjectReceptionSignatureDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'subjectReceptionSignatureDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'subjectReceptionSignatureDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="subjectReturnSignatureDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'subjectReturnSignatureDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'subjectReturnSignatureDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'subjectReturnSignatureDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="status" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-belonging let-rowIndex="rowIndex">
                <tr class="p-selectable-row" [pSelectableRow]="belonging" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="belonging"></p-tableCheckbox>
                    </td>
                    <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.id}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.id }}</td>
                    @if(isValidDate(belonging.registrationDate)){
                        <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.registrationDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.registrationDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.registrationDate}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.registrationDate }}</td> -->
                    @if(isValidDate(belonging.subjectReceptionSignatureDate) && belonging.subjectReceptionSignatureTech != null && belonging.subjectReceptionSignatureTech != undefined && belonging.subjectReceptionSignatureTech != ''){
                        <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.subjectReceptionSignatureDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.subjectReceptionSignatureDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.subjectReceptionSignatureDate}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.subjectReceptionSignatureDate }}</td> -->
                    @if(isValidDate(belonging.subjectReturnSignatureDate) && belonging.receptionUserSignatureTech != null && belonging.receptionUserSignatureTech != undefined && belonging.receptionUserSignatureTech != ''){
                        <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.subjectReturnSignatureDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.subjectReturnSignatureDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{belonging.subjectReturnSignatureDate}}" tooltipPosition="top" class="ellipsis-cell">{{ belonging.subjectReturnSignatureDate }}</td> -->
                    <td (click)="editBelonging(belonging)" showDelay="1000" pTooltip="{{getBelongingsStatus(belonging) | translate }}" tooltipPosition="top" class="ellipsis-cell">{{ getBelongingsStatus(belonging) | translate }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editBelonging(belonging)"></button>
                            <button pButton pRipple [disabled]="!canReadAndWrite || managerSettings?.allowRemoveIdentity == false || !userIsVerified" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteBelonging(belonging)"></button>
                            <button pButton pRipple icon="pi pi-image" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="showBelongingPhotos(belonging)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>

        <!-- Create/Edit belonging -->
        <p-dialog #belongingDialog [(visible)]="showBelongingDialog" styleClass="p-fluid" [modal]="true" [closable]="true" [style]="{'height': (activeIndex != 0 ? '80vh' : '450px')}" (onHide)="onCancelDialog()">
            <ng-template pTemplate="header">
                <div >
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ isNew ? ('content.new_belonging_record' | translate) : ('headers.belonging_record' | translate) + ' ' + belonging.id }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                    <div class="flex justify-content-end pb-3 requiredFieldsLabel">
                        {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                    </div>
                    <form [formGroup]="form">
                        <div class="mb-3">
                            @if( operationType == opType.INSERT ){
                                <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" (activeIndexChange)="onActiveIndexChange($event)"></p-steps>
                            }@else{
                                <div class="flex justify-content-center">
                                    <p-selectButton
                                        [options]="stepOptions"
                                        formControlName="stepOptions"
                                        severity="secondary"
                                        multiple="false"
                                        allowEmpty="false"
                                        optionLabel="key"
                                        optionValue="value"
                                        dataKey="value"
                                        (onChange)="onActiveTabIndexChange($event)">
                                    </p-selectButton>
                                </div>
                            }
                        </div>
                        <p-scrollPanel [style]="{ maxWidth: '85vw', height: (activeIndex != 0 ? '55vh' : 'auto') }">
                            <div class="form-fields">
                                <div class="grid">
                                    <!-- General -->
                                    <div *ngIf="activeIndex==0">
                                        <div class="col-12" style="padding-bottom: 0rem;">
                                            <label class="label-form">{{ 'content.subject' | translate }}</label>
                                            <input type="text" pInputText value="{{ userSubject.names + ' ' + userSubject.lastNames }}" [disabled]="true"/>
                                        </div>
                                        <div class="col-12" style="padding-bottom: 0rem;">
                                            <div class="pb-1">
                                                <label class="label-form" for="comments">{{ 'content.comments' | translate }} <span
                                                        *ngIf="isRequiredField('comments', form)" class="requiredStar">*</span></label>
                                                <textarea pInputTextarea
                                                    rows="3" cols="30" formControlName="comments" [autoResize]="true"
                                                    (ngModelChange)="trackDataChange()"
                                                ></textarea>
                                                <small *ngIf="!isValid('comments', form) && form.controls['comments'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Belongings List -->
                                    <div *ngIf="activeIndex==1">
                                        <div class="flex justify-content-center align-items-center w-full">
                                            <p-accordion [activeIndex]="0">
                                                <p-accordionTab header="{{ 'titles.belongings' | translate }}">
                                                    <div class="lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                                                        <div *ngIf="canReadAndWrite && userIsVerified && editEnabled" class="grid" [formGroup]="belongingItemForm">
                                                            <div class="col-3 width100 p-1">
                                                                <label class="label-form" for="belongingType">{{ 'content.belongingType' | translate }} <span
                                                                    *ngIf="isRequiredField('belongingType', belongingItemForm)" class="requiredStar">*</span></label>
                                                                <p-dropdown formControlName="belongingType" [(ngModel)]="belongingTypeSelected" (ngModelChange)="trackDataChange()"
                                                                    appendTo="body" [options]="belongingTypeOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                                                                <small *ngIf="!isValid('belongingType', belongingItemForm) && belongingItemForm.controls['belongingType'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                                            </div>
                                                            <div class="col-7 width100 p-1">
                                                                <label class="label-form" for="belongingDescription">{{ 'content.belongingDescription' | translate }} <span
                                                                    *ngIf="isRequiredField('belongingDescription', belongingItemForm)" class="requiredStar">*</span></label>
                                                                <input type="text" pInputText formControlName="belongingDescription" (ngModelChange)="trackDataChange()" />
                                                                <small *ngIf="!isValid('belongingDescription', belongingItemForm) && belongingItemForm.controls['belongingDescription'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                                            </div>
                                                            <div class="col-2 width100 p-1"></div>
                                                            <div class="col-10 width100 p-1">
                                                                <label class="label-form" for="comments">{{ 'content.comments' | translate }} <span
                                                                    *ngIf="isRequiredField('comments', belongingItemForm)" class="requiredStar">*</span></label>
                                                                <input pInputText formControlName="comments" (ngModelChange)="trackDataChange()"/>
                                                                <small *ngIf="!isValid('comments', belongingItemForm) && belongingItemForm.controls['comments'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                                            </div>
                                                            <div class="col-2 flex justify-content-center align-items-end width100 p-1">
                                                                <p-button
                                                                    [disabled]="belongingItemForm.invalid"
                                                                    [style]="{'pointer-events': 'auto', 'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                                                    label="{{ 'add'| translate }}"
                                                                    (onClick)="addBelongingItem()"
                                                                ></p-button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                                                        <p-table
                                                            [value]="belongingsList"
                                                            dataKey="id"
                                                            editMode="row"
                                                            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                                                            [rows]="5"
                                                            [paginator]="true"
                                                            [styleClass]="'p-datatable-sm p-datatable-striped'"
                                                            [globalFilterFields]="['description', 'type', 'comments']">
                                                        >
                                                            <ng-template pTemplate="header">
                                                                <tr>
                                                                    <th class="header-small-list">{{ 'content.id' | translate }}</th>
                                                                    <th class="header-small-list">{{ 'content.description' | translate }}</th>
                                                                    <th class="header-small-list">{{ 'content.type' | translate }}</th>
                                                                    <th class="header-small-list">{{ 'content.comments' | translate }}</th>
                                                                    <th class="header-small-list"></th>
                                                                </tr>
                                                                <tr>
                                                                    <th>
                                                                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains" />
                                                                    </th>
                                                                    <th>
                                                                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains" />
                                                                    </th>
                                                                    <th>
                                                                        <p-columnFilter type="text" field="type" [showMenu]="false" matchMode="contains" />
                                                                    </th>
                                                                    <th>
                                                                        <p-columnFilter type="text" field="comments" [showMenu]="false" matchMode="contains" />
                                                                    </th>
                                                                    <th></th>
                                                                </tr>
                                                            </ng-template>
                                                            <ng-template pTemplate="body" let-rec let-editing="editing" let-ri="rowIndex">
                                                                <tr [pEditableRow]="rec" [formGroup]="rowForm">
                                                                    <td>
                                                                        {{rec.id}}
                                                                    </td>
                                                                    <td>
                                                                        <p-cellEditor>
                                                                            <ng-template pTemplate="input">
                                                                                <input
                                                                                    pInputText
                                                                                    type="text"
                                                                                    formControlName="description"
                                                                                    required
                                                                                />
                                                                            </ng-template>
                                                                            <ng-template pTemplate="output">
                                                                                {{rec.description}}
                                                                            </ng-template>
                                                                        </p-cellEditor>
                                                                    </td>
                                                                    <td>
                                                                        <p-cellEditor>
                                                                            <ng-template pTemplate="input">
                                                                                <p-dropdown
                                                                                    [options]="belongingTypeOptions"
                                                                                    appendTo="body"
                                                                                    formControlName="type"
                                                                                    optionLabel="value"
                                                                                    dataKey="key"
                                                                                    [filter]="true"
                                                                                    filterBy="value"
                                                                                    placeholder="{{ 'content.select' | translate }}"
                                                                                />
                                                                            </ng-template>
                                                                            <ng-template pTemplate="output">
                                                                                {{ rec.type }}
                                                                            </ng-template>
                                                                        </p-cellEditor>
                                                                    </td>
                                                                    <td>
                                                                        <p-cellEditor>
                                                                            <ng-template pTemplate="input">
                                                                                <input
                                                                                    pInputText
                                                                                    type="text"
                                                                                    formControlName="comments"
                                                                                    required
                                                                                />
                                                                            </ng-template>
                                                                            <ng-template pTemplate="output">
                                                                                {{rec.comments}}
                                                                            </ng-template>
                                                                        </p-cellEditor>
                                                                    </td>
                                                                    <td>
                                                                        <div class="flex align-items-center justify-content-center gap-2">
                                                                            <button
                                                                                *ngIf="!editing && canReadAndWrite && userIsVerified && editEnabled"
                                                                                pButton
                                                                                pRipple
                                                                                type="button"
                                                                                pInitEditableRow
                                                                                icon="pi pi-pencil"
                                                                                (click)="onRowEditInit(rec)"
                                                                                class="p-button-rounded p-button-text">
                                                                            </button>
                                                                            <button
                                                                                *ngIf="!editing && canReadAndWrite && userIsVerified && editEnabled"
                                                                                pButton
                                                                                pRipple
                                                                                type="button"
                                                                                icon="pi pi-trash"
                                                                                (click)="onRowRemoveItem(rec)"
                                                                                class="p-button-rounded p-button-text p-button-danger">
                                                                            </button>
                                                                            <div *ngIf="!editing">
                                                                                <button
                                                                                    *ngIf="!hasImage(rec) && editEnabled"
                                                                                    pButton
                                                                                    pRipple
                                                                                    type="button"
                                                                                    icon="pi pi-camera"
                                                                                    [disabled]="!(canReadAndWrite && userIsVerified)"
                                                                                    class="p-button-rounded p-button-text"
                                                                                    (click)="openCameraDialog(rec)">
                                                                                </button>
                                                                                <button
                                                                                    *ngIf="hasImage(rec)"
                                                                                    pButton
                                                                                    pRipple
                                                                                    type="button"
                                                                                    icon="pi pi-image"
                                                                                    [disabled]="!(canReadAndWrite && userIsVerified)"
                                                                                    class="p-button-rounded p-button-text"
                                                                                    (click)="openImageDialog(null, rec)">
                                                                                </button>
                                                                            </div>
                                                                            <button
                                                                                *ngIf="editing && canReadAndWrite && userIsVerified && editEnabled"
                                                                                pButton
                                                                                pRipple
                                                                                type="button"
                                                                                pSaveEditableRow
                                                                                icon="pi pi-check"
                                                                                (click)="onRowEditSave(rec, ri)"
                                                                                class="p-button-rounded p-button-text p-button-success mr-2">
                                                                            </button>
                                                                            <button
                                                                                *ngIf="editing && canReadAndWrite && userIsVerified && editEnabled"
                                                                                pButton pRipple
                                                                                type="button"
                                                                                pCancelEditableRow
                                                                                icon="pi pi-times"
                                                                                (click)="onRowEditCancel(rec, ri)"
                                                                                class="p-button-rounded p-button-text p-button-danger">
                                                                            </button>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </ng-template>
                                                            <ng-template pTemplate="emptymessage">
                                                                <tr>
                                                                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                                                                </tr>
                                                            </ng-template>
                                                        </p-table>
                                                    </div>
                                                    <!-- Belongings Photos -->
                                                    <div class="flex flex-row justify-content-center align-items-center w-full lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                                                        <p-accordion [style]="{minWidth: '35vw'}" [activeIndex]="0">
                                                            <p-accordionTab header="{{ 'titles.belongings_photos' | translate }}">
                                                                <ng-container *ngTemplateOutlet="belongingPhotos"></ng-container>
                                                            </p-accordionTab>
                                                        </p-accordion>
                                                    </div>
                                                </p-accordionTab>
                                            </p-accordion>
                                        </div>
                                    </div>
                                    <!-- Signatures -->
                                    <div *ngIf="activeIndex==2">
                                        <!-- Reception Signatures -->
                                        <div *ngIf="(showReceptionButton || showReceptionInfo)" class="flex flex-row justify-content-center align-items-center w-full">
                                            <p-accordion styleClass="minWidth" [activeIndex]="0">
                                                <p-accordionTab header="{{ 'titles.belongings_hand_over_signatures' | translate }}">
                                                    <div class="flex flex-row justify-content-center align-items-center gap-4 m-3">
                                                        <!-- Subject Reception Signature -->
                                                        <div *ngIf="showReceptionButton">
                                                            <!-- Subject Reception Signature Button -->
                                                            <div *ngIf="belonging.subjectReceptionSignatureTech == null || belonging.subjectReceptionSignatureTech == undefined || belonging.subjectReceptionSignatureTech == ''" class="flex flex-column align-items-start gap-2">
                                                                <label class="signature-title">{{ 'content.subject_reception_signature' | translate }} </label>
                                                                <div *ngIf="userIsVerified else mustVerifyUser" class="signature-container flex flex-column gap-2">
                                                                    <label class="label-form">{{ 'content.subject' | translate }}</label>
                                                                    <input type="text" pInputText value="{{ userSubject.names + ' ' + userSubject.lastNames }}" [disabled]="true"/>
                                                                    <div class="flex flex-column justify-content-center align-items-center">
                                                                        <p-button
                                                                            *ngIf="canReadAndWrite && userIsVerified"
                                                                            [disabled]="!(canReadAndWrite && userIsVerified)"
                                                                            severity="secondary"
                                                                            [outlined]="true"
                                                                            label="{{ 'content.sign' | translate }}"
                                                                            icon="pi pi-pencil"
                                                                            iconPos="right"
                                                                            (onClick)="openWidgetVerify()"
                                                                        ></p-button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!-- Subject Reception Signature Information -->
                                                            <div *ngIf="belonging.subjectReceptionSignatureTech != null && belonging.subjectReceptionSignatureTech != undefined && belonging.subjectReceptionSignatureTech != ''">
                                                                <ng-container *ngTemplateOutlet="subjectReceptionInfo" />
                                                            </div>
                                                        </div>
                                                        <!-- Subject Reception Signature Information -->
                                                        <div *ngIf="showReceptionInfo" class="flex flex-row justify-content-center align-items-center gap-4 m-3 w-full">
                                                            <div *ngIf="belonging.subjectReceptionSignatureTech != null && belonging.subjectReceptionSignatureTech != undefined && belonging.subjectReceptionSignatureTech != ''">
                                                                <ng-container *ngTemplateOutlet="subjectReceptionInfo" />
                                                            </div>
                                                        </div>
                                                        <!-- User Reception Signature -->
                                                        <app-bio-signatures
                                                            [showSignatureButton]="belonging.receptionUserSignatureTech == null || belonging.receptionUserSignatureTech == undefined || belonging.receptionUserSignatureTech == ''"
                                                            [showExtraFormFields]="false"
                                                            [showSignatureInfo]="(belonging.receptionUserSignatureTech != null && belonging.receptionUserSignatureTech != undefined && belonging.receptionUserSignatureTech != '') || showReceptionInfo"
                                                            [signatureTitle]="'content.user_reception_signature'"
                                                            [signatureInputLabel]="'content.user'"
                                                            [konektorProperties]="konektorProperties"
                                                            [managerSettings]="managerSettings"
                                                            [signatureData]="receptionSignatureData"
                                                            [restrictSubjectRoles]="restrictReceptionRoles"
                                                            [subjectRoleSegmentedSearch]="segmentedSearchReceptionRole"
                                                            [userIsVerified]="userIsVerified"
                                                            (outputResult)="userReceptionSignatureResult($event)"
                                                        ></app-bio-signatures>
                                                    </div>
                                                </p-accordionTab>
                                            </p-accordion>
                                        </div>
                                        <!-- Return Signatures -->
                                        <div *ngIf="(showReturnButton || showReturnInfo)" class="flex flex-row justify-content-center align-items-center w-full">
                                            <p-accordion styleClass="minWidth" [activeIndex]="0">
                                                <p-accordionTab header="{{ 'titles.belongings_return_signatures' | translate }}">
                                                    <div class="flex flex-row justify-content-center align-items-center gap-4 m-3">
                                                        <!-- Subject Return Signature Button -->
                                                        <div *ngIf="showReturnButton" class="flex flex-row justify-content-center align-items-center gap-4 m-3">
                                                            <!-- Subject Return Signature Button -->
                                                            <div *ngIf="belonging.subjectReturnSignatureTech == null || belonging.subjectReturnSignatureTech == undefined || belonging.subjectReturnSignatureTech == ''" class="flex flex-column align-items-center gap-2">
                                                                <label class="signature-title">{{ 'content.subject_return_signature' | translate }} </label>
                                                                <div *ngIf="userIsVerified else mustVerifyUser" class="signature-container flex flex-column gap-2">
                                                                    <label class="label-form">{{ 'content.subject' | translate }}</label>
                                                                    <input type="text" pInputText value="{{ userSubject.names + ' ' + userSubject.lastNames }}" [disabled]="true"/>
                                                                    <div class="flex flex-column justify-content-center align-items-center">
                                                                        <p-button
                                                                            *ngIf="canReadAndWrite && userIsVerified"
                                                                            [disabled]="!(canReadAndWrite && userIsVerified)"
                                                                            severity="secondary"
                                                                            [outlined]="true"
                                                                            label="{{ 'content.sign' | translate }}"
                                                                            icon="pi pi-pencil"
                                                                            iconPos="right"
                                                                            (onClick)="openWidgetVerify()"
                                                                        ></p-button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div *ngIf="belonging.subjectReturnSignatureTech != null && belonging.subjectReturnSignatureTech != undefined && belonging.subjectReturnSignatureTech != ''">
                                                                <ng-container *ngTemplateOutlet="subjectReturnInfo" />
                                                            </div>
                                                        </div>
                                                        <div *ngIf="showReturnInfo" class="flex flex-row justify-content-center align-items-center gap-4 m-3 w-full">
                                                            <div *ngIf="belonging.subjectReturnSignatureTech != null && belonging.subjectReturnSignatureTech != undefined && belonging.subjectReturnSignatureTech != ''">
                                                                <ng-container *ngTemplateOutlet="subjectReturnInfo" />
                                                            </div>
                                                        </div>
                                                        <!-- User Return Signature -->
                                                        <app-bio-signatures
                                                            [showSignatureButton]="belonging.returnUserSignatureTech == null || belonging.returnUserSignatureTech == undefined || belonging.returnUserSignatureTech == ''"
                                                            [showExtraFormFields]="false"
                                                            [showSignatureInfo]="(belonging.returnUserSignatureTech != null && belonging.returnUserSignatureTech != undefined && belonging.returnUserSignatureTech != '') || showReturnInfo"
                                                            [signatureTitle]="'content.user_return_signature'"
                                                            [signatureInputLabel]="'content.user'"
                                                            [konektorProperties]="konektorProperties"
                                                            [managerSettings]="managerSettings"
                                                            [signatureData]="returnSignatureData"
                                                            [restrictSubjectRoles]="restrictReturnRoles"
                                                            [subjectRoleSegmentedSearch]="segmentedSearchReturnRole"
                                                            [userIsVerified]="userIsVerified"
                                                            (outputResult)="userReturnSignatureResult($event)"
                                                        ></app-bio-signatures>
                                                    </div>
                                                </p-accordionTab>
                                            </p-accordion>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </p-scrollPanel>
                    </form>
            </ng-template>
            <ng-template pTemplate="footer">
                <!-- <div class="flex flex-row gap-1 mr-3 justify-content-end">
                    <p-button
                        *ngIf="canReadAndWrite && userIsVerified"
                        [disabled]="!(canReadAndWrite && userIsVerified) || isDisableSaveButton"
                        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'save'| translate }}"
                        (onClick)="saveBelonging()"
                    ></p-button>
                    <p-button
                        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ (canReadAndWrite && userIsVerified ? 'cancel' : 'close')| translate }}"
                        (onClick)="onCancelDialog()"
                    ></p-button>
                </div> -->
                <div>
                    <div class="footer-buttons-container">
                        @if (activeIndex==0) {
                                <p-button label="{{ 'cancel' | translate }}"
                                    (onClick)="onClose()"
                                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                                ></p-button>
                        }@else{
                                <p-button
                                    label="{{ 'back' | translate }}"
                                    icon="pi pi-angle-left"
                                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                                    (onClick)="onBack()"
                                ></p-button>
                        }
                        @if (activeIndex==2) {
                            <p-button
                                [disabled]="!(canReadAndWrite && userIsVerified) || isDisableSaveButton"
                                label="{{ 'save'| translate }}"
                                iconPos="right"
                                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                                (onClick)="saveBelonging()"
                            ></p-button>
                        }@else {
                            <p-button
                                label="{{ 'next' | translate }}"
                                icon="pi pi-angle-right"
                                iconPos="right"
                                (onClick)="onNext()"
                                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                            ></p-button>
                        }
                    </div>
                </div>
            </ng-template>
        </p-dialog>
    </div>
</div>

<ng-template #mustVerifyUser>
    <div style="width: 200px;" class="signature-container flex flex-column justify-content-center align-items-center">
        {{ 'messages.verification_required_data' | translate }}
    </div>
</ng-template>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.belongings' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <app-empty
        [readAndWritePermissions]="canReadAndWrite && userIsVerified"
        buttonLabel="content.new_record"
        titleLabel="titles.no_belongings_records_available"
        contentHeight="300px"
        (clicked)="onNewBelonging()">
    </app-empty>
</ng-template>

<ng-template #belongingPhotos>
    <div *ngIf="belongingsPhotoList.length > 0 else placeholder">
        <p-carousel
            [value]="belongingsPhotoList"
            [numVisible]="3"
            [numScroll]="1"
            [circular]="belongingsPhotoList.length > 1"
            [showIndicators]="true"
            [responsiveOptions]="responsiveOptions">
                <ng-template let-pDataItem pTemplate="item">
                    <div class="flex flex-column align-items-center justify-content-center">
                        <div>
                            <img
                                [src]="'data:image/jpeg;base64,' + pDataItem.content"
                                [alt]="picHistoryName + '-' + pDataItem.number"
                                class="pDataImage"
                                (click)="openImageDialog(pDataItem)">
                        </div>
                        <div class="pDataDateLabel flex justify-content-center">
                            <div>{{ pDataItem.createdAt | date:'dd/MM/yyyy' }}</div>
                        </div>
                    </div>
                </ng-template>
        </p-carousel>
    </div>
</ng-template>

<ng-template #placeholder>
    <p-card class="w-full">
        <div *ngIf="!isLoading else loadingSpinner" class="flex justify-content-center">
            {{ 'content.noPhotosAdded' | translate }}
        </div>
    </p-card>
    <ng-template #loadingSpinner>
        <div class="flex justify-content-center">
            <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
        </div>
    </ng-template>
</ng-template>

<ng-template #noData>
    <p-card *ngIf="!showReceptionButton && !showReturnButton" class="w-12 flex justify-content-center m-5">
        {{ 'content.noDataAvailable' | translate }}
    </p-card>
</ng-template>

<ng-template #subjectReceptionInfo>
    <div class="flex flex-column align-items-start gap-2">
        <label class="signature-title">{{ 'content.subject_reception_signature' | translate }} </label>
        <div class="signature-container flex flex-column gap-2">
            <div class="flex flex-column gap-4">
                <div class="flex justify-content-center align-items-center gap-3" style="padding-bottom: 0rem;">
                    <img class="imgUser" src={{image}} />
                    <div class="flex flex-column gap-1">
                        <div class="namesLabel mb-2"> {{ userSubject.names + ' ' + userSubject.lastNames }} </div>
                        <div>
                            <span class="dataKey">{{ 'table.numId' | translate }}: </span>
                            <span class="dataValue">{{ userSubject.numId }}</span>
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'content.technology' | translate }}: </span>
                            <img style="width: 20px;" pTooltip="{{ ('content.subjectReceptionSignatureTech' | translate) + ': ' + ('content.' + belonging.subjectReceptionSignatureTech | translate) }}" [src]="getTechIcon(belonging.subjectReceptionSignatureTech)"/>
                        </div>
                    </div>
                </div>
                <div style="padding-bottom: 0rem;">
                    <div class="signatureDateLabel">{{ ('content.signedOn' | translate) + ' ' + (belonging.subjectReceptionSignatureDate  | date: 'short':'':language) }}</div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #subjectReturnInfo>
    <div class="flex flex-column align-items-start gap-2">
        <label class="signature-title">{{ 'content.subject_reception_signature' | translate }} </label>
        <div class="signature-container flex flex-column gap-2">
            <div class="flex flex-column gap-4">
                <div class="flex justify-content-center align-items-center gap-3" style="padding-bottom: 0rem;">
                    <img class="imgUser" src={{image}} />
                    <div class="flex flex-column gap-1">
                        <div class="namesLabel mb-2"> {{ userSubject.names + ' ' + userSubject.lastNames }} </div>
                        <div>
                            <span class="dataKey">{{ 'table.numId' | translate }}: </span>
                            <span class="dataValue">{{ userSubject.numId }}</span>
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'content.technology' | translate }}: </span>
                            <img style="width: 20px;" pTooltip="{{ ('content.subjectReturnSignatureTech' | translate) + ': ' + ('content.' + belonging.subjectReturnSignatureTech | translate) }}" [src]="getTechIcon(belonging.subjectReturnSignatureTech)"/>
                        </div>
                    </div>
                </div>
                <div style="padding-bottom: 0rem;">
                    <div class="signatureDateLabel">{{ ('content.signedOn' | translate) + ' ' + (belonging.subjectReturnSignatureDate  | date: 'short':'':language) }}</div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<app-camera
    [canReadAndWrite]="canReadAndWrite && userIsVerified && editEnabled"
    [selectedCurrentResult]="selectedCurrentResult"
    [showCameraDialog]="openFullImageDialog"
    [aspectRatio]="1"
    (result)="onCameraResult($event)"
></app-camera>

<app-camera
    [canReadAndWrite]="canReadAndWrite && userIsVerified && editEnabled"
    [selectedCurrentResult]="selectedCurrentResult"
    [showCameraDialog]="showNewBelongingPhotoDialog"
    [aspectRatio]="7/5"
    (result)="onCameraResult($event)"
></app-camera>

<!-- Widget User Verify -->
<app-widget-match
    [numId]="subjectNumId"
    [subject]="userSubject"
    [widgetUrl]="widgetUrl"
    [verified]="false"
    [managerSettings]="managerSettings"
    [konektorProperties]="konektorProperties"
    [ready]="verifyReady"
    (result)="onWidgetMatchResult($event)"
></app-widget-match>

<!-- Widget Verify -->
<app-widget-search
    [numId]="userNumId"
    [widgetUrl]="widgetUrl"
    [managerSettings]="managerSettings"
    [ready]="searchReady"
    [technology]="tech"
    [segmentedSearchAttributes]="segmentedSearchAttributes"
    (result)="onWidgetSearchResult($event)"
></app-widget-search>

<p-dialog header="{{ 'titles.belongings_photos' | translate }}" [modal]="true" [(visible)]="showBelongingPhotosDialog" (onHide)="onCancelDialog()" [style]="{ minWidth: '35rem' }">
    <div class="flex flex-column align-items-center justify-content-center">
        <ng-container *ngTemplateOutlet="belongingPhotos"></ng-container>
    </div>
</p-dialog>

<p-dialog [(visible)]="showEnterNumId" [modal]="true" [style]="{ background: '#FFFFFF', 'border-radius': '6px', width: '25rem' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="dialogHeader mt-3 ml-3">{{ 'content.verification_1to1_enabled' | translate }}</div>
        <div class="flex flex-column align-items-center justify-content-center gap-3 my-3" [formGroup]="formNumId">
            <p-floatLabel style="width: 90%;" class="mt-6 mb-4">
                <input id="numId" type="text" pInputText formControlName="numId" class="flex-auto" autocomplete="off" style="width: 100%;"/>
                <label for="numId">{{ 'content.id_number' | translate}}</label>
            </p-floatLabel>
            <div class="flex justify-content-center gap-2">
                <button pButton label="{{ 'cancel' | translate }}" class="p-button-text" severity="secondary" (click)="closeNumIdDialog()"></button>
                <p-button label="{{ 'verify' | translate}}" severity="secondary" (onClick)="onNumIdDialogSubmit()" />
            </div>
        </div>
    </ng-template>
</p-dialog>
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CategoryScheduleEntity } from "../entity/category-schedule.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class UpdateCategoryScheduleUseCase implements UseCaseGrpc<{ schedule: CategoryScheduleEntity }, SuccessResponse> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { schedule: CategoryScheduleEntity; }): Promise<SuccessResponse> {
        return this.groupCategoryRepository.updateCategorySchedule(params);
    }
}
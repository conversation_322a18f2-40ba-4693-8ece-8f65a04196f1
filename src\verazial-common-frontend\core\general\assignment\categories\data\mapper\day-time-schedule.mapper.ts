import { DayTimeScheduleGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { DayTimeScheduleEntity } from "../../domain/entity/day-time-schedule.entity";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";

export class DayTimeScheduleMapper extends Mapper<DayTimeScheduleGrpc, DayTimeScheduleEntity> {
    override mapFrom(param: DayTimeScheduleGrpc): DayTimeScheduleEntity {
        let dayTime = new DayTimeScheduleEntity();

        dayTime.id = param.getId();
        dayTime.categoryScheduleId = param.getCategoryscheduleid();
        dayTime.day = param.getDay();
        dayTime.timeInit = new Date(param.getTimeinit()?.getSeconds()!! * 1000 + Math.round(param.getTimeinit()?.getNanos()!! / 1e6));
        dayTime.timeEnd = new Date(param.getTimeend()?.getSeconds()!! * 1000 + Math.round(param.getTimeend()?.getNanos()!! / 1e6));

        return dayTime;
    }
    override mapTo(param: DayTimeScheduleEntity): DayTimeScheduleGrpc {
        let dayTime = new DayTimeScheduleGrpc();

        dayTime.setId(param.id!);
        dayTime.setCategoryscheduleid(param.categoryScheduleId!);
        dayTime.setDay(param.day!);
        dayTime.setTimeinit(dateToTimestamp(param.timeInit!));
        dayTime.setTimeend(dateToTimestamp(param.timeEnd!));

        return dayTime;
    }
}
import { Component, Input, OnInit, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'ui-profile-pic',
  templateUrl: './ui-profile-pic.component.html',
  styleUrls: ['./ui-profile-pic.component.css']
})
export class UiProfilePicComponent implements OnInit {

  @Input() image: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
  @Input() editImage: string = "verazial-common-frontend/assets/images/all/EditPic.svg";
  @Input() edit: boolean = true;
  @Input() responsive: boolean = true;
  @Output() pic = new EventEmitter<string>();
  debugMode: boolean = false;

  constructor(private localStorage: LocalStorageService,
    private consoleLogger: ConsoleLoggerService) { }

  ngOnInit(): void {
    this.debugMode = this.localStorage.isDebugModeEnabled();
  }

  @ViewChild('profPic') profPic!: ElementRef;
  @ViewChild('video') video!: ElementRef;
  @ViewChild('canvas') canvas!: ElementRef;
  isPreviewRunning = false;

  async startUserPictureTake() {
    // Check if camera is available
    if (!('mediaDevices' in navigator) || !('getUserMedia' in navigator.mediaDevices)) {
      alert('Cámara no disponible');
      this.consoleLogger.consoleLog(this.debugMode, "Camera error (could not find camera) (2)");
      return;
    }

    // Get necessary HTML elements
    const profPic = this.profPic.nativeElement;
    const video = this.video.nativeElement;
    const canvas = this.canvas.nativeElement;

    // Check if all HTML elements are present
    if (!profPic || !canvas || !video) {
      this.consoleLogger.consoleLog(this.debugMode, "Camera error (missing html elements) (4)");
      return;
    }

    if (!this.isPreviewRunning) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        video.srcObject = stream;
        video.classList.remove('d-none');
        profPic.classList.add('d-none');
        this.isPreviewRunning = true;
      } catch (error) {
        this.consoleLogger.consoleLog(this.debugMode, "Camera error: ");
        this.consoleLogger.consoleLog(this.debugMode, error);
      }
    } else {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      canvas.getContext('2d').drawImage(video, 0, 0);
      profPic.src = canvas.toDataURL('image/jpeg');
      profPic.classList.remove('d-none');
      video.classList.add('d-none');
      video.pause();
      this.pic.emit(profPic.src);
      video.srcObject.getTracks()[0].stop();
      video.srcObject = null;
      this.isPreviewRunning = false;
    }
  }
}

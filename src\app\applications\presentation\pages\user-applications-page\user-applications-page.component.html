@if(isLoading){
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
}@else{
    <app-list-user-subject
        [isLoading]="isLoading"
        [type]="subjectType"
        [headerText]="'pass_assigment.application_assigment'"
        [showBioSearch]="false"
        [showTableRecordButton]="false"
        [mainButtonIcon]="'eye'"
        [secondaryButtonIcon]="'check-square'"
        [clearMultipleOnSecondaryAction]="false"
        [lazyLoad]="useLazyLoad"
        [readAndWritePermissions]="canReadAndWrite"
        [readOnly]="true"
        [listOfUsersSubjects]="listSubjectsResponse"
        [selectedUsersSubjects]="selectedSubjects"
        [totalRecords]="listSubjectsResponse.lenght"
        [offset]="getSubjectsRequest.offset"
        [limit]="getSubjectsRequest.limit"
        [allRoles]="allRoles"
        [managerSettings]="managerSettings"
        [konektorProperties]="konektorProperties"
        [recordManagementMode]="false"
        (onSecondaryAction)="onShowAddApplication($event)"
        (onMainAction)="onShowViewDetails($event)"
        (onSelectionChange)="setSelection($event)"
    ></app-list-user-subject>
    <div class="container-column gap-3">
        <!-- Application details -->
        <p-dialog
            header="Header"
            [(visible)]="showApplicationDetails"
            [modal]="true"
            [style]="{ width: '80rem' }"
            >

                <ng-template pTemplate="header">
                    <div></div>
                    <div class="text-center mt-3 mb-3 text-l font-semibold">
                        {{subject}}
                    </div>
                </ng-template>
                <div class="flex flex-column">
                    <label class="mt-4 mb-2" [style]="{ 'font-weight': '700', 'font-size': '12px', 'text-transform': 'uppercase', 'color': '#818EA1'}">{{ "pass_assigment.subject_application" | translate}}</label>
                    <p-table
                        [loading]="loading"
                        [value]="listSubjectApplications"
                        editMode="row"
                        dataKey="id"
                        [rowHover]="true"
                        [paginator]="true"
                        [rows]="3"
                        styleClass="p-datatable-sm"
                        [showCurrentPageReport]="true"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                        [globalFilterFields]="['subjectAppName', 'applicationId', 'connectionMethod', 'canUserUpdateCredentials', 'credentialsInitialised', 'host']"
                        [sortField]="'subjectAppName'" [sortOrder]="1">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 25rem" pSortableColumn="subjectAppName">{{ 'pass_assigment.authorization_id' | translate }}<p-sortIcon field="subjectAppName"></p-sortIcon></th>
                                <th style="width: 25rem">{{ 'pass_assigment.application' | translate }}</th>
                                <th style="width: 25rem" pSortableColumn="applicationId">{{ 'pass_assigment.application_flow' | translate }}<p-sortIcon field="applicationId"></p-sortIcon></th>
                                <th style="width: 25rem" pSortableColumn="connectionMethod">{{ 'pass_assigment.connection_type' | translate }}<p-sortIcon field="connectionMethod"></p-sortIcon></th>
                                <th style="width: 25rem" pSortableColumn="canUserUpdateCredentials">{{ 'pass_assigment.can_user_update' | translate }}<p-sortIcon field="canUserUpdateCredentials"></p-sortIcon></th>
                                <th style="width: 25rem" pSortableColumn="credentialsInitialised">{{ 'pass_assigment.initialised' | translate }}<p-sortIcon field="credentialsInitialised"></p-sortIcon></th>
                                <th style="width: 25rem" pSortableColumn="host">{{ 'pass_assigment.host' | translate }}<p-sortIcon field="host"></p-sortIcon></th>
                                <th style="width:10rem" pFrozenColumn [frozen]="true"></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-application let-editing="editing" let-ri="rowIndex">
                            <tr [pSelectableRow]="application" [pEditableRow]="application">
                                <td>{{application.subjectAppName}}</td>
                                <td>{{getAppRegistryNameByAppId(application.applicationId)}}</td>
                                <td>{{getAppNameByAppId(application.applicationId)}}</td>
                                <td>
                                    <p-cellEditor>
                                        <ng-template pTemplate="input">
                                            <p-dropdown
                                                [options]="listConnectionTypes"
                                                appendTo="body"
                                                [(ngModel)]="application.connectionMethod"
                                                [style]="{'width':'100%'}"
                                            >
                                            </p-dropdown>
                                        </ng-template>
                                        <ng-template pTemplate="output">
                                            {{application.connectionMethod}}
                                        </ng-template>
                                    </p-cellEditor>
                                </td>
                                <td>
                                    <p-cellEditor>
                                        <ng-template pTemplate="input">
                                            <p-inputSwitch [(ngModel)]="application.canUserUpdateCredentials"/>
                                        </ng-template>
                                        <ng-template pTemplate="output">
                                            <p-inputSwitch [(ngModel)]="application.canUserUpdateCredentials" [disabled]="true"/>
                                        </ng-template>
                                    </p-cellEditor>
                                </td>
                                <td>
                                    <p-cellEditor>
                                        <ng-template pTemplate="input">
                                            <p-inputSwitch [(ngModel)]="application.credentialsInitialised" />
                                        </ng-template>
                                        <ng-template pTemplate="output">
                                            <p-inputSwitch [(ngModel)]="application.credentialsInitialised" [disabled]="true"/>
                                        </ng-template>
                                    </p-cellEditor>
                                </td>
                                <td>
                                    <p-cellEditor>
                                        <ng-template pTemplate="input">
                                            <input pInputText type="text" [(ngModel)]="application.host">
                                        </ng-template>
                                        <ng-template pTemplate="output">
                                            {{application.host}}
                                        </ng-template>
                                    </p-cellEditor>
                                </td>
                                <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                                    <div class="flex align-items-center justify-content-center gap-2">
                                        <button *ngIf="!editing" pButton pRipple type="button" pInitEditableRow icon="pi pi-pencil" (click)="onApplicationRowEditInit(application)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                                        <button *ngIf="!editing" pButton pRipple type="button" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" severity="warning" (click)="deleteApplication(application)"></button>
                                        <button *ngIf="!editing && showDetailsAccess" pButton pRipple type="button" icon="pi pi-list" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="handleSelectApplication(application)"></button>
                                        <button *ngIf="editing" pButton pRipple type="button" pSaveEditableRow icon="pi pi-check" (click)="onApplicationRowEditSave(application)" class="p-button-rounded p-button-text p-button-success mr-2"></button>
                                        <button *ngIf="editing" pButton pRipple type="button" pCancelEditableRow icon="pi pi-times" (click)="onApplicationRowEditCancel(application, ri)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" severity="danger"></button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                            </tr>
                        </ng-template>
                    </p-table>

                    <label class="mt-5 mb-2" [style]="{ 'font-weight': '700', 'font-size': '12px', 'text-transform': 'uppercase', 'color': '#818EA1'}">{{ "pass_assigment.credentials" | translate}}</label>
                    <p-table
                        [loading]="loadingCredentials"
                        [value]="listSubjectCredentials"
                        editMode="row"
                        dataKey="id"
                        [rowHover]="true"
                        [paginator]="true"
                        [rows]="3"
                        [showCurrentPageReport]="true"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                        styleClass="p-datatable-sm"
                        [sortField]="'parameter'" [sortOrder]="1">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 25rem" pSortableColumn="parameter">{{ 'pass_assigment.parameter' | translate }}<p-sortIcon field="parameter"></p-sortIcon></th>
                                <th style="width: 25rem" pSortableColumn="value">{{ 'pass_assigment.value' | translate }}<p-sortIcon field="value"></p-sortIcon></th>
                                <th style="width:10rem" pFrozenColumn [frozen]="true"></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-credential let-editing="editing" let-ri="rowIndex">
                            <tr [pSelectableRow]="credential" [pEditableRow]="credential">
                                <td>{{credential.parameterName}}</td>
                                <td>
                                    <p-cellEditor>
                                        <ng-template pTemplate="input">
                                            <p-password [(ngModel)]="credential.value" [toggleMask]="true"></p-password>
                                        </ng-template>
                                        <ng-template pTemplate="output">
                                            ********
                                        </ng-template>
                                    </p-cellEditor>
                                </td>
                                <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                                    <div *ngIf="!passwordSubjectCredentials[ri] || this.subjectNumId == this.localStorageService.getUser().numId" class="flex align-items-center justify-content-center gap-2">
                                        <button *ngIf="!editing" pButton pRipple type="button" pInitEditableRow icon="pi pi-pencil" (click)="onCredentialRowEditInit(credential)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                                        <button *ngIf="editing" pButton pRipple type="button" pSaveEditableRow icon="pi pi-check" (click)="onCredentialRowEditSave(credential)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                                        <button *ngIf="editing" pButton pRipple type="button" pCancelEditableRow icon="pi pi-times" (click)="onCredentialRowEditCancel(credential, ri)" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"></button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            <ng-template pTemplate="footer">
            </ng-template>
        </p-dialog>

        <p-contextMenu #cm [model]="items" (onHide)="onHide()" />

        <!-- Sidebar -->
        <p-sidebar [(visible)]="sidebarVisible" position="right" styleClass="w-30rem">
            <ng-template pTemplate="header">
                <div class="flex align-items-center gap-2" [style]="{'color': '#495057', 'font-size': '14px', 'font-weight':'600'}">
                    <label for="">{{'pass_assigment.application_credential' | translate}}</label>
                </div>
            </ng-template>
            <form [formGroup]="dataForm">
                <div class="flex flex-column mt-3">
                    <p-accordion [multiple]="true" [activeIndex]="[0,1]">
                        <div class="flex flex-column gap-2">
                            <p-accordionTab class="custom-accordion-style" header="{{ 'pass_assigment.application' | translate }}">
                                <div class="field">
                                    <label class="label-text" for="connectionType">{{ 'pass_assigment.application' | translate }}</label>
                                    <p-dropdown
                                        appendTo="body"
                                        class="min-w-full mb-3"
                                        formControlName="appRegistry"
                                        [options]="listAppRegistries"
                                        optionLabel="name"
                                        placeholder="{{'pass_assigment.select_apps' | translate}}"
                                        [ngClass]="!isValid('appRegistry') && dataForm.controls['appRegistry'].touched? 'ng-invalid ng-dirty':'' "
                                        (onChange)="onAppRegistrySelect($event)">
                                    </p-dropdown>
                                    <small *ngIf="!isValid('appRegistry') && dataForm.controls['appRegistry'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                                <div class="field">
                                    <label class="label-text" for="connectionType">{{ 'pass_assigment.application_flow' | translate }}</label>
                                    <p-dropdown
                                        appendTo="body"
                                        class="min-w-full mb-3"
                                        formControlName="appSelectBox"
                                        [options]="listApplications"
                                        [(ngModel)]="selectedApp"
                                        placeholder="{{'pass_assigment.select_apps_flow' | translate}}"
                                        [ngClass]="!isValid('appSelectBox') && dataForm.controls['appSelectBox'].touched? 'ng-invalid ng-dirty':'' "
                                        (onChange)="onApplicationSelect($event)"
                                        id="appSelectBox"
                                        >
                                        <ng-template pTemplate="selectedItem">
                                            <div class="flex align-items-center gap-2" *ngIf="selectedApp">
                                                <div>{{ selectedApp.applicationName }} @if(selectedApp.flowType){ <p-tag value="{{ selectedApp.flowType }}" />}</div>
                                            </div>
                                        </ng-template>
                                        <ng-template let-selectedApp pTemplate="item">
                                            <div class="flex align-items-center gap-2">
                                                <div>{{ selectedApp.applicationName }} @if(selectedApp.flowType){ <p-tag value="{{ selectedApp.flowType }}" />}</div>
                                            </div>
                                        </ng-template>
                                    </p-dropdown>
                                    <small *ngIf="!isValid('appSelectBox') && dataForm.controls['appSelectBox'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                                <div class="field">
                                    <label class="label-text" for="connectionType">{{ 'pass_assigment.authorization_id' | translate }}</label>
                                    <input
                                    pInputText type="text"
                                    formControlName="subjectAppName"
                                    class="min-w-full"
                                    placeholder="Local Application"
                                    [ngClass]="!isValid('subjectAppName') && dataForm.controls['subjectAppName'].touched? 'ng-invalid ng-dirty':'' "/>
                                    <small *ngIf="!isValid('subjectAppName') && dataForm.controls['subjectAppName'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>

                                <div *ngIf="showDesktopOptions" class="field">
                                    <label class="label-text" for="connectionType">{{ 'pass_assigment.connection_type' | translate }}</label>
                                    <p-dropdown
                                    appendTo="body"
                                    class="min-w-full"
                                    formControlName="connectionType"
                                    [options]="listConnectionTypes"
                                    placeholder="Connection Method"
                                    [ngClass]="!isValid('connectionType') && dataForm.controls['connectionType'].touched? 'ng-invalid ng-dirty':'' "
                                    />
                                    <small *ngIf="!isValid('connectionType') && dataForm.controls['connectionType'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>

                                <div *ngIf="showDesktopOptions" class="field">
                                    <label class="label-text" for="connectionType">{{ 'pass_assigment.host_name' | translate }}</label>
                                    <input
                                    pInputText
                                    type="text"
                                    formControlName="hostName"
                                    class="min-w-full" placeholder="http://***********:12345"
                                    [ngClass]="!isValid('hostName') && dataForm.controls['hostName'].touched? 'ng-invalid ng-dirty':'' "/>
                                    <small *ngIf="!isValid('hostName') && dataForm.controls['hostName'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>

                                <div class="field-checkbox">
                                    <p-checkbox id="canUserUpdateCred" formControlName="mustUserUpdate" [binary]="true"
                                    (onChange)="onCanUserUpdateChange($event)"></p-checkbox>
                                    <label for="canUserUpdateCred">{{ 'pass_assigment.allow_user_update_password' | translate }}</label>
                                </div>

                                <div class="field-checkbox">
                                    <p-checkbox id="mustUserUpdate" formControlName="mustUserUpdate" [binary]="true"
                                    (onChange)="onMustUpdateChange($event)"></p-checkbox>
                                    <label for="mustUserUpdate">{{ 'pass_assigment.must_user_update_credentials' | translate }}</label>
                                </div>

                            </p-accordionTab>
                            <p-accordionTab class="custom-accordion-style" header="{{ 'pass_assigment.fill_credential' | translate }}">

                                <div class="flex flex-column min-w-full">
                                    <!--div class="line"></div-->
                                    @if(applicationDetailsReady){
                                        <div class="flex flex-column min-w-full">
                                            <ng-template ngFor let-item [ngForOf]="listWindows" let-i="index">
                                                <label style="font-size: 15px; font-weight: 700;  margin: 10px 5px 10px 5px; text-align: center;">{{'pass_assigment.window' | translate}}: {{ item.windowName }}</label>
                                                <ng-template ngFor let-parameter [ngForOf]="item.windowParameters" let-i="index">
                                                    <div *ngIf="parameter.uiComponentType=='CHECKBOX'" class="flex align-items-center gap-2">
                                                        <p-checkbox [value]="parameter.id" [inputId]="parameter.id" [name]="parameter.id" [binary]="true"></p-checkbox>
                                                        <label [for]="parameter.id">{{ parameter.name }}</label>
                                                    </div>

                                                    <div *ngIf="parameter.uiComponentType=='COMBOBOX'" class="field">
                                                        <label class="label-text" [for]="parameter.id!!">{{parameter.name }}</label>
                                                        <input pInputText type="text" [id]="parameter.id!!" [name]="parameter.id!!" class="min-w-full" [placeholder]="parameter.name" />
                                                        <small>{{'pass_assigment.option_dropdown' | translate}}</small>
                                                    </div>

                                                    <div *ngIf="parameter.uiComponentType=='LIST-CHECKBOX'" class="field">
                                                        <label class="label-text" [for]="parameter.id!!">{{parameter.name }}</label>
                                                        <input pInputText type="text" [id]="parameter.id!!" [name]="parameter.id!!" class="min-w-full" [placeholder]="parameter.name" />
                                                        <small>{{'pass_assigment.option_list' | translate}}</small>
                                                        <p></p>
                                                    </div>

                                                    <div *ngIf=" parameter.uiComponentType=='PASSWORD'" class="field">
                                                        <label class="label-text" [for]="parameter.id!!">{{parameter.name }}</label>
                                                        <input pInputText type="password" [id]="parameter.id!!" [name]="parameter.id!!" class="min-w-full" [placeholder]="parameter.name" />
                                                    </div>

                                                    <div *ngIf="parameter.uiComponentType=='TEXTBOX'" class="field">
                                                        <label class="label-text" [for]="parameter.id!!">{{parameter.name }}</label>
                                                        <input pInputText type="text" [id]="parameter.id!!" [name]="parameter.id!!" class="min-w-full" [placeholder]="parameter.name" />
                                                    </div>

                                                    <div *ngIf="parameter.uiComponentType=='DATAGRID'" class="field">
                                                        <label class="label-text" [for]="parameter.id!!">{{parameter.name }}</label>
                                                        <input pInputText type="text" [id]="parameter.id!!" [name]="parameter.id!!" class="min-w-full" [placeholder]="parameter.name" />
                                                        <small>{{'pass_assigment.datagrip_row' | translate}}</small>
                                                    </div>

                                                </ng-template>
                                            </ng-template>
                                        </div>
                                    }@else{
                                        <div class="flex justify-content-center align-content-center">
                                            <label for="">{{ "pass_assigment.select_apps_flow" | translate }}</label>
                                        </div>
                                    }

                                </div>
                                <p></p>
                                <div class="flex flex-column align-items-center justify-content-center min-w-full">
                                    <div class="m-1">
                                        <p-button
                                        [disabled]="!canReadAndWrite && !applicationDetailsReady"
                                        [style]="{'width':'250px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#162746', 'font-family': 'Open Sans'}"
                                        label="{{ 'save' | translate }}" [rounded]="true"
                                        (click)="saveUserAppData()"></p-button>
                                    </div>
                                </div>
                            </p-accordionTab>
                        </div>
                    </p-accordion>
                </div>
            </form>
        </p-sidebar>
    </div>
}

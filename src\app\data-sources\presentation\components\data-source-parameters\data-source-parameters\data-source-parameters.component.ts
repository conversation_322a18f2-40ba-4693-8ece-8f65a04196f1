import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { DataSourceParametersEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source-parameters.entity';
import { AttributeData } from 'src/verazial-common-frontend/core/models/attribute-data.model';
import { SourceParamTypes } from 'src/verazial-common-frontend/core/models/source-params-type.enum';
import {v4 as uuidv4} from 'uuid';

@Component({
  selector: 'app-data-source-parameters',
  templateUrl: './data-source-parameters.component.html',
  styleUrl: './data-source-parameters.component.css'
})
export class DataSourceParametersComponent implements OnInit, OnD<PERSON>roy{
  @Input() inputData: DataSourceParametersEntity | undefined;
  @Output() outputData = new EventEmitter<DataSourceParametersEntity>();
  
  selectedType: AttributeData | undefined;

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private validatorService: ValidatorService,
  ){ }

  listDataSourceParamTypes = [
    {key: SourceParamTypes.API_TOKEN, value: this.translateService.instant(`content.${SourceParamTypes.API_TOKEN}`)}, 
    {key: SourceParamTypes.API_ENDPOINT_PARAMETER, value: this.translateService.instant(`content.${SourceParamTypes.API_ENDPOINT_PARAMETER}`)}, 
    {key: SourceParamTypes.API_USERNAME, value: this.translateService.instant(`content.${SourceParamTypes.API_USERNAME}`)}, 
    {key: SourceParamTypes.API_PASSWORD, value: this.translateService.instant(`content.${SourceParamTypes.API_PASSWORD}`)}, 
    {key: SourceParamTypes.API_RESULT_PARAM, value: this.translateService.instant(`content.${SourceParamTypes.API_RESULT_PARAM}`)}, 
    {key: SourceParamTypes.API_SEARCH_FIELD, value: this.translateService.instant(`content.${SourceParamTypes.API_SEARCH_FIELD}`)},
    {key: SourceParamTypes.LDAP_PASSWORD, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_PASSWORD}`)},
    {key: SourceParamTypes.LDAP_USERNAME, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_USERNAME}`)},
    {key: SourceParamTypes.LDAP_DOMAIN, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_DOMAIN}`)},
    {key: SourceParamTypes.LDAP_BIND_DN, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_BIND_DN}`)},
    {key: SourceParamTypes.LDAP_SEARCH_BASE, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_SEARCH_BASE}`)},
    {key: SourceParamTypes.LDAP_PORT, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_PORT}`)},
    {key: SourceParamTypes.LDAP_SSL, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_SSL}`)},
    {key: SourceParamTypes.LOCAL_METHOD, value: this.translateService.instant(`content.${SourceParamTypes.LOCAL_METHOD}`)},
    {key: SourceParamTypes.LOGIN_USERNAME, value: this.translateService.instant(`content.${SourceParamTypes.LOGIN_USERNAME}`)},
    {key: SourceParamTypes.LOGIN_PASSWORD, value: this.translateService.instant(`content.${SourceParamTypes.LOGIN_PASSWORD}`)},
  ];
  
  ngOnDestroy(): void {
    this.cleanFields();
  }

  ngOnInit(): void {
  }

  public form: FormGroup = this.fb.group({
    parameter: ['', [Validators.required]],
    value: ['', [Validators.required]],
    type: ['', [Validators.required]],
  });

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  addParameter(){
    this.form.get('parameter')?.markAsTouched();
    this.form.get('value')?.markAsTouched();
    this.form.get('type')?.markAsTouched();

    if(!this.isValid('parameter') || !this.isValid('value') || !this.isValid('type')){
      return;
    }

    let dataSourceParameter: DataSourceParametersEntity = {
      id: uuidv4(),
      dataSourceId: '',
      parameter: this.form.get('parameter')?.value,
      value: this.form.get('value')?.value,
      type: this.form.get('type')?.value.key
    }
    // this.listDataSourceParameters.push(dataSourceParameter);

    this.outputData.emit(dataSourceParameter);
  }

  cleanFields(){
    this.form.get('parameter')?.reset();
    this.form.get('value')?.reset();
    this.form.get('type')?.reset();
  }
  
  trackDataChange(data: any){
    if(data.value.key == SourceParamTypes.LDAP_SSL){
      this.form.get('parameter')?.setValue("SSL");
      this.form.get('value')?.setValue("SSL");
    } else {
      this.form.get('parameter')?.reset();
      this.form.get('value')?.reset();
    }
  }

}

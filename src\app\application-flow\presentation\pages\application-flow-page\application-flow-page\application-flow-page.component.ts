import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { ProccessFlowInterface } from 'src/verazial-common-frontend/core/general/application-flow/common/interfaces/proccess-flow.interface';
import { ApplicationEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/application.entity';
import { GetAllApplicationsUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-all-applications.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-application-flow-page',
  templateUrl: './application-flow-page.component.html',
  styleUrl: './application-flow-page.component.css'
})
export class ApplicationFlowPageComponent implements OnInit, OnDestroy{
  showEmpty: boolean = false;
  showApplicationsFlow: boolean = false;
  showListApplications: boolean = false;

  selectedApplication!: ApplicationEntity | undefined;

  data: ProccessFlowInterface[] = [];

  applications: ApplicationEntity[] = [];

  isLoading: boolean = false;

  // Access code identifier
  access_identifier: string = AccessIdentifier.PASS_APP_FLOWS;
  canReadAndWrite: boolean = false;

  constructor(
    private checkPermissions: CheckPermissionsService,
    private messageService: MessageService,
    private translate: TranslateService,
    private getAllApplicationsUseCase: GetAllApplicationsUseCase,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
  ){}

  ngOnDestroy(): void {
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.getApplications();
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
  }

  getApplications(){
    this.getAllApplicationsUseCase.execute().then(
      (data)=>{
        this.applications = data;
        if(this.applications.length > 0){
          this.showComponent('LIST-APPS');
        }else{
          this.showComponent('EMPTY');
        }
        this.isLoading = false;
      },
      (e)=>{
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_APPLICATIONS, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  loadFlowData(){

  }

  createNewFlow(event: boolean){
    this.selectedApplication = undefined;
    this.showComponent('FLOW-APP');
  }

  onReturn(event: boolean){
    this.getApplications();
    if(this.applications.length > 0){
      this.showComponent('LIST-APPS');
    }else{
      this.showComponent('EMPTY');
    }
  }

  onEditApplication(event: ApplicationEntity){
    this.showComponent('FLOW-APP');
    this.selectedApplication = event
  }

  showComponent(component: string){
    switch(component){
      case 'EMPTY': {
        this.showListApplications = false;
        this.showEmpty = true;
        this.showApplicationsFlow = false;
        break;
      }
      case 'LIST-APPS': {
        this.showListApplications = true;
        this.showEmpty = false;
        this.showApplicationsFlow = false;
        break;
      }
      case 'FLOW-APP': {
        this.showListApplications = false;
        this.showEmpty = false;
        this.showApplicationsFlow = true;
        break;
      }
      default:{
        break;
      }
    }
  }
}

@if(isLoading){
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
}@else{
    <app-list-data-sources [readAndWritePermissions]="canReadAndWrite" [inputData]="listDataSource" (outputData)="getDataSource()"></app-list-data-sources>
}


<p-dialog [(visible)]="showNewDataSourceDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="text-center mt-3 mb-3 text-l font-semibold">
            {{ 'content.data_source' | translate }}
        </div>
    </ng-template>

    <ng-template pTemplate="content">
        <!-- Call DataSource component here -->
        <div class="mb-5">
            <app-data-sources (cancel)="cancelNewDataSource()" (operationStatus)="operationResult($event)"></app-data-sources>
        </div>

    </ng-template>
</p-dialog>
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { MenuItem, TreeNode } from "primeng/api";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { ScheduleCustom } from "src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-custom.interface";
import { ScheduleTypeEnum } from "src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-type.enum";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { AttributeData } from "src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model";
import { NewLocationModel } from "src/verazial-common-frontend/core/general/manager/common/models/new-location.model";
import { AuthScheduleDetailEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/auth-schedule-detail.entity";
import { AuthScheduleEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/auth-schedule.entity";
import { DayTimeAuthScheduleDetailEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/day-time-auth-schedule-detail.entity";
import { CreateAuthScheduleUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/create-auth-schedule.use-case";
import { GetAuthScheduleByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/get-auth-schedule-by-id.use-case";
import { UpdateAuthScheduleUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/update-auth-schedule-by-id.use-case";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { formatDate } from "src/verazial-common-frontend/core/util/date-to-timestamp";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-auth-schedule-edit',
    templateUrl: './auth-schedule-edit.component.html',
    styleUrl: './auth-schedule-edit.component.css'
})
export class AuthScheduleEditComponent implements OnInit, OnChanges {

    // Inputs
    @Input() canReadAndWrite: boolean = false;
    @Input() userIsVerified: boolean = false;
    @Input() operationType!: OperationType;
    @Input() authSchedule?: AuthScheduleEntity;
    @Input() listRoles: RoleEntity[] = [];
    @Input() listLocations: TreeNode[] = [];
    @Input() createUpdateButtonTitle: string = this.translateService.instant('save');
    // Outputs
    @Output() operationStatus = new EventEmitter<OperationStatus>();

    isLoading: boolean = false;

    items?: MenuItem[];
    activeIndex: number = 0;
    opType = OperationType;
    minDate: Date | undefined;

    selectedAuthSchedule?: AuthScheduleEntity;
    selectedRole?: RoleEntity;
    selectedLocation?: TreeNode[];
    selectedScheduleType: string = "";
    selectedSchedule?: AttributeData;
    showDateRange: boolean = false;
    showDateRangeCustom: boolean = false;
    listOfOriginalDayTime: DayTimeAuthScheduleDetailEntity[] = [];

    public form: FormGroup = this.fb.group({
        authScheduleName: ['', [Validators.required]],
        authScheduleDescription: [],
        authScheduleRoleId: ['', Validators.required],
        authScheduleLocationId: ['', Validators.required],
        /* Schedule */
        authScheduleType: ['', [Validators.required]],
        dateInit: [],
        dateEnd: [],
        dateInitCustom: [],
        dateEndCustom: [],
        showDateInterval: [],
        showDateIntervalCustom: [],
        scheduleInit: ['', [Validators.required]],
        scheduleEnd: ['', [Validators.required]],
        MONDAYInit: [],
        TUESDAYInit: [],
        WEDNESDAYInit: [],
        THURSDAYInit: [],
        FRIDAYInit: [],
        SATURDAYInit: [],
        SUNDAYInit: [],
        MONDAYEnd: [],
        TUESDAYEnd: [],
        WEDNESDAYEnd: [],
        THURSDAYEnd: [],
        FRIDAYEnd: [],
        SATURDAYEnd: [],
        SUNDAYEnd: [],
        stepOptions: ['0'],
        search: [],
        maxTime: [null, [Validators.required]],
    });
    // Options
    stepOptions: AttributeData[] = [
        { key: this.translateService.instant('category.details'), value: "0" },
        { key: this.translateService.instant('category.configuration'), value: "1" },
    ];
    listScheduleTypes: AttributeData[] = [
        { key: ScheduleTypeEnum.ALL, value: this.translateService.instant('content.everyday') },
        { key: ScheduleTypeEnum.WORKING_DAYS, value: this.translateService.instant('content.monday_to_friday') },
        { key: ScheduleTypeEnum.CUSTOM, value: this.translateService.instant('content.customised') }
    ];
    days: AttributeData[] = [
        { key: 'MONDAY', value: this.translateService.instant('content.monday') },
        { key: 'TUESDAY', value: this.translateService.instant('content.tuesday') },
        { key: 'WEDNESDAY', value: this.translateService.instant('content.wednesday') },
        { key: 'THURSDAY', value: this.translateService.instant('content.thursday') },
        { key: 'FRIDAY', value: this.translateService.instant('content.friday') },
        { key: 'SATURDAY', value: this.translateService.instant('content.saturday') },
        { key: 'SUNDAY', value: this.translateService.instant('content.sunday') },
    ];

    constructor(
        private translateService: TranslateService,
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private auditTrailService: AuditTrailService,
        private getAuthScheduleByIdUseCase: GetAuthScheduleByIdUseCase,
        private updateAuthScheduleUseCase: UpdateAuthScheduleUseCase,
        private createAuthScheduleUseCase: CreateAuthScheduleUseCase,
        private newLocationsService: NewLocationsService,
    ) { }

    ngOnInit(): void {
        this.form.reset();

        this.items = [
            {
                label: this.translateService.instant('content.details'),
            },
            {
                label: this.translateService.instant('content.configuration'),
            }
        ];
        this.stepOptions = [
            { key: this.translateService.instant('content.details'), value: "0" },
            { key: this.translateService.instant('content.configuration'), value: "1" },
        ];
        this.listScheduleTypes = [
            { key: ScheduleTypeEnum.ALL, value: this.translateService.instant('content.everyday') },
            { key: ScheduleTypeEnum.WORKING_DAYS, value: this.translateService.instant('content.monday_to_friday') },
            { key: ScheduleTypeEnum.CUSTOM, value: this.translateService.instant('content.customised') }
        ];
        this.days = [
            { key: 'MONDAY', value: this.translateService.instant('content.monday') },
            { key: 'TUESDAY', value: this.translateService.instant('content.tuesday') },
            { key: 'WEDNESDAY', value: this.translateService.instant('content.wednesday') },
            { key: 'THURSDAY', value: this.translateService.instant('content.thursday') },
            { key: 'FRIDAY', value: this.translateService.instant('content.friday') },
            { key: 'SATURDAY', value: this.translateService.instant('content.saturday') },
            { key: 'SUNDAY', value: this.translateService.instant('content.sunday') },
        ];
        this.onScheduleTypeChange();

        this.minDate = new Date();

        if (this.authSchedule){
            if (this.authSchedule.authScheduleDetail?.dayTime && this.authSchedule.authScheduleDetail.dayTime.length > 0) {
              this.listOfOriginalDayTime = this.authSchedule.authScheduleDetail.dayTime.map(item => ({ ...item }));
            }
        }

        this.isLoading = true;
        if (this.operationType == OperationType.UPDATE) {
            setTimeout(() => {
                this.fillFields();
            }, 300);
        } else {
            this.onCancel();
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        // ng on changes for operationType
        if (changes && changes['operationType'].currentValue) {
            if (changes['operationType'].currentValue == OperationType.UPDATE) {
                this.isLoading = true;
                this.fillFields();
            } else {
                this.onCancel();
            }
        }
    }

    fillFields() {
        if (!this.authSchedule) {
            this.onCancel();
            return;
        }
        console.log("eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee");
        console.log(this.authSchedule);
        console.log(this.listLocations);
        this.form.get('authScheduleName')?.setValue(this.authSchedule?.name);
        this.form.get('authScheduleDescription')?.setValue(this.authSchedule?.description);
        this.form.get('authScheduleRoleId')?.setValue(this.listRoles.filter(role => 
            role.id !== undefined && this.authSchedule?.roleId?.includes(role.id)
          ));
        this.form.get('authScheduleLocationId')?.setValue(this.newLocationsService.getNodesWithKeysInSelectTree(this.authSchedule?.locationId!, this.listLocations));
        this.selectedAuthSchedule = this.authSchedule;

        this.selectedScheduleType = this.authSchedule?.type!;

        let sch = this.authSchedule;
        this.selectedSchedule = this.listScheduleTypes.find((_v, _k) => _v.key == this.authSchedule?.type);

        let schType = this.authSchedule?.authScheduleDetail;

        let maxTimeNumber = schType?.maxTime;
        let hours = Math.floor(maxTimeNumber! / 60);
        let minutes = maxTimeNumber! % 60;
        let maxTime = new Date();
        maxTime.setHours(hours, minutes, 0, 0);
        this.loggerService.debug(maxTime);
        this.form.get('maxTime')?.setValue(maxTime);

        if (this.selectedSchedule?.key == ScheduleTypeEnum.ALL || this.selectedSchedule?.key == ScheduleTypeEnum.WORKING_DAYS) {

            this.form.get('showDateInterval')?.setValue(schType?.hasDateRange);
            if (schType?.hasDateRange) {
                this.showDateRange = true;
            }
            const dateInit = formatDate(`${schType?.dateInit?.month}/${schType?.dateInit?.day}/${schType?.dateInit?.year}`);
            const dateEnd = formatDate(`${schType?.dateEnd?.month}/${schType?.dateEnd?.day}/${schType?.dateEnd?.year}`);

            this.form.get('dateInit')?.setValue(dateInit);
            this.form.get('dateEnd')?.setValue(dateEnd);
            this.form.get('scheduleInit')?.setValue(schType?.timeInit);
            this.form.get('scheduleEnd')?.setValue(schType?.timeEnd);
        } else if (this.selectedSchedule?.key == ScheduleTypeEnum.CUSTOM) {
            this.form.get('showDateIntervalCustom')?.setValue(schType?.hasDateRange);
            if (schType?.hasDateRange) {
                this.showDateRange = true;
            }
            const dateInit = formatDate(`${schType?.dateInit?.month}/${schType?.dateInit?.day}/${schType?.dateInit?.year}`);
            const dateEnd = formatDate(`${schType?.dateEnd?.month}/${schType?.dateEnd?.day}/${schType?.dateEnd?.year}`);

            this.form.get('dateInitCustom')?.setValue(dateInit);
            this.form.get('dateEndCustom')?.setValue(dateEnd);
            if (schType) {
                let listOfDayTime = schType.dayTime

                for (const schedule of listOfDayTime!) {
                    this.form.get(`${schedule.day}Init`)?.setValue(schedule.timeInit);
                    this.form.get(`${schedule.day}End`)?.setValue(schedule.timeEnd);
                }
            }
        }
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
        if(!this.canReadAndWrite || !this.userIsVerified) {
            this.form.disable();
            this.form.get('stepOptions')?.enable();
        }
        else {
            this.form.enable();
        }
        this.isLoading = false;
    }

    mapLocations(nodes?: NewLocationModel[]): TreeNode[] {
        return nodes?.map((node) => ({
            key: node.id,
            label: node.name,
            children: this.mapLocations(node.children) // Recursive call
        })) || [];
    }

    onNext() {
        this.form.get('authScheduleName')?.markAsTouched();
        this.form.get('authScheduleLocationId')?.markAsTouched();
        if (this.isValid('authScheduleName') && this.isValid('authScheduleLocationId')) {
            this.activeIndex += 1;
            this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
        }
    }

    onBack() {
        this.activeIndex -= 1;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onCancel() {
        this.form.reset();
        this.selectedScheduleType = "";
        this.showDateRange = false;
        this.showDateRangeCustom = false;
        this.authSchedule = undefined;
        this.isLoading = false;

        let maxTime = new Date();
        maxTime.setHours(0, 0, 0, 0);
        this.form.get('maxTime')?.setValue(maxTime);
    }

    onClose() {
        this.onCancel();
        let result: OperationStatus = {
            status: Status.SUCCESS,
            message: 'CLOSE'
        }
        this.operationStatus.emit(result);
    }

    saveAuthSchedule() {
        this.form.get('authScheduleType')?.markAsTouched();

        if (!this.isValid('authScheduleType')) {
            let status: OperationStatus = {
                status: Status.ERROR,
                message: this.translateService.instant('messages.invalid_shedule_type')
            }
            this.operationStatus.emit(status);
            return;
        }

        let authSchedule = new AuthScheduleEntity();
        let authScheduleDetail = new AuthScheduleDetailEntity();

        let maxTime = this.form.get('maxTime')?.value;

        if(maxTime instanceof Date)
            authScheduleDetail.maxTime = maxTime ? (maxTime.getHours() * 60 + maxTime.getMinutes()) : 0;
        else if (typeof maxTime === 'string')
            authScheduleDetail.maxTime = maxTime ? (parseInt(maxTime.split(':')[0]) * 60 + parseInt(maxTime.split(':')[1])) : 0;

        if (this.selectedScheduleType == ScheduleTypeEnum.ALL || this.selectedScheduleType == ScheduleTypeEnum.WORKING_DAYS) {

            if (this.authSchedule) {
                authSchedule.id = this.authSchedule.id;
                authScheduleDetail.id = this.authSchedule.authScheduleDetail?.id;
            }

            this.form.get('scheduleInit')?.markAsTouched();
            this.form.get('scheduleEnd')?.markAsTouched();
            this.form.get('maxTime')?.markAsTouched();
            if (!this.isValid('scheduleInit') || !this.isValid('scheduleEnd') || !this.isValid('maxTime')) {
                return;
            }
            let timeInit = this.form.get('scheduleInit')?.value;
            let timeEnd = this.form.get('scheduleEnd')?.value;

            authScheduleDetail.timeInit = timeInit;
            authScheduleDetail.timeEnd = timeEnd;
            authScheduleDetail.hasDateRange = this.showDateRange;

            if (this.form.get('showDateInterval')?.value) {
                let dateInit = this.form.get('dateInit')?.value;
                let dateEnd = this.form.get('dateEnd')?.value;
                authScheduleDetail.dateInit = dateInit;
                authScheduleDetail.dateEnd = dateEnd;
            }

            authSchedule.name = this.form.get('authScheduleName')?.value;
            authSchedule.type = this.selectedScheduleType;
            authSchedule.description = this.form.get('authScheduleDescription')?.value == undefined ? '' : this.form.get('authScheduleDescription')?.value,
            authSchedule.roleId = this.form.get('authScheduleRoleId')?.value.map((role: RoleEntity) => role.id);
            authSchedule.locationId = this.form.get('authScheduleLocationId')?.value.map((loc: TreeNode) => loc.key);
            authSchedule.authScheduleDetail = authScheduleDetail;

            this.submitAuthSchedule(authSchedule);

        } else if (this.selectedScheduleType == ScheduleTypeEnum.CUSTOM) {
            let dateTimeSchedules: DayTimeAuthScheduleDetailEntity[] = [];

            let customSchedule: ScheduleCustom;
            if (this.authSchedule) {
                authSchedule.id = this.authSchedule.id;
                authScheduleDetail.id = this.authSchedule.authScheduleDetail?.id;
            }

            this.days.forEach((day) => {
                if (this.form.get(day.key + 'Init')?.value && this.form.get(day.key + 'End')?.value) {

                    if (new Date('1/1/1999 ' + this.form.get(day.key + 'Init')?.value) > new Date('1/1/1999 ' + this.form.get(day.key + 'End')?.value)) {
                        let status: OperationStatus = {
                            status: Status.ERROR,
                            message: this.translateService.instant('messages.error_range_dates')
                        }
                        this.operationStatus.emit(status);
                        throw "Error dates";
                    }

                    let dateTimeSchedule = new DayTimeAuthScheduleDetailEntity();

                    let searchDayTimeId = this.listOfOriginalDayTime.find(obj => obj.day === day.key);

                    if (searchDayTimeId) {
                        dateTimeSchedule.authScheduleDetailId = this.authSchedule?.authScheduleDetail?.id;
                        dateTimeSchedule.id = searchDayTimeId.id;
                    }

                    dateTimeSchedule.day = day.key as string;
                    dateTimeSchedule.timeInit = this.form.get(day.key + 'Init')?.value,
                    dateTimeSchedule.timeEnd = this.form.get(day.key + 'End')?.value,
                    dateTimeSchedules.push(dateTimeSchedule);
                }
            });

            authScheduleDetail.hasDateRange = this.form.get('showDateIntervalCustom')?.value;
            authScheduleDetail.dayTime = dateTimeSchedules;

            if (this.form.get('showDateIntervalCustom')?.value) {
                let dateInitCustom = this.form.get('dateInitCustom')?.value;
                let dateEndCustom = this.form.get('dateEndCustom')?.value;
                authScheduleDetail.dateInit = dateInitCustom;
                authScheduleDetail.dateEnd = dateEndCustom;
            }

            if (dateTimeSchedules.length == 0) {
                let status: OperationStatus = {
                    status: Status.ERROR,
                    message: this.translateService.instant('messages.no_days_selected')
                }
                this.operationStatus.emit(status);
                return;
            }

            authSchedule.name = this.form.get('authScheduleName')?.value;
            authSchedule.description = this.form.get('authScheduleDescription')?.value == undefined ? '' : this.form.get('authScheduleDescription')?.value;
            authSchedule.type = ScheduleTypeEnum.CUSTOM;
            authSchedule.roleId = this.form.get('authScheduleRoleId')?.value.map((role: RoleEntity) => role.id);
            authSchedule.locationId = this.form.get('authScheduleLocationId')?.value.map((loc: TreeNode) => loc.key);
            authSchedule.authScheduleDetail = authScheduleDetail;

            this.submitAuthSchedule(authSchedule);
        }
    }

    submitAuthSchedule(authSchedule: AuthScheduleEntity) {

        this.loggerService.debug('authSchedule');
        this.loggerService.debug(authSchedule);
        // this.loggerService.debug(authSchedule);
        let responseStatus: OperationStatus;
        if (this.authSchedule && this.operationType == OperationType.UPDATE) {
            // Update the auth schedule
            this.getAuthScheduleByIdUseCase.execute({ id: authSchedule.id ?? this.authSchedule.id! }).then(
                (data) => {
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(authSchedule) },
                    ];
                    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_AUTH_SCHEDULE, ReasonActionTypeEnum.UPDATE, () => {
                        this.updateAuthScheduleUseCase.execute({ authSchedule: authSchedule }).then(
                            (_) => {
                                this.onCancel();
                                responseStatus = {
                                    status: Status.SUCCESS,
                                    message: ''
                                }
                                this.operationStatus.emit(responseStatus);
                            },
                            (e) => {
                                this.loggerService.error(e);
                                let responseStatus: OperationStatus = {
                                    status: Status.ERROR,
                                    message: e.message
                                }
                                this.operationStatus.emit(responseStatus);
                                const at_attributes: ExtraData[] = [
                                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(authSchedule) },
                                ];
                                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_AUTH_SCHEDULE, 0, 'ERROR', '', at_attributes);
                            }
                        );
                    }, at_attributes);
                },
                (e) => {
                    this.loggerService.error(e);
                    let responseStatus: OperationStatus = {
                        status: Status.ERROR,
                        message: e.message
                    }
                    this.operationStatus.emit(responseStatus);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.RECORD_ID, value: authSchedule.id!.toString() },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_AUTH_SCHEDULE_BY_ID, 0, 'ERROR', '', at_attributes);
                }
            );
            return
        }

        // Create a new auth schedule
        const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(authSchedule) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_AUTH_SCHEDULE, ReasonActionTypeEnum.CREATE, () => {
            this.createAuthScheduleUseCase.execute({ authSchedule: authSchedule }).then(
                (_) => {
                    this.onCancel();
                    responseStatus = {
                        status: Status.SUCCESS,
                        message: ''
                    }
                    this.operationStatus.emit(responseStatus);
                },
                (e) => {
                    this.loggerService.error(e);
                    let responseStatus: OperationStatus = {
                        status: Status.ERROR,
                        message: e.message
                    }
                    this.operationStatus.emit(responseStatus);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(authSchedule) },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_AUTH_SCHEDULE, 0, 'ERROR', '', at_attributes);
                }
            );
        }, at_attributes);
    }

    onScheduleTypeChange() {
      this.form.get('authScheduleType')?.valueChanges
        .subscribe((type) => {
          if (type) {
            this.selectedScheduleType = type.key
          }
        });
    }

    isRequiredField(field: string): boolean {
      return this.validatorService.isRequiredField(this.form, field);
    }

    isValid(field: string): boolean {
      return this.validatorService.isValidField(this.form, field);
    }

    showDateInterval() {
        this.showDateRange = !this.showDateRange;
    }

    showDateIntervalCustom() {
      this.showDateRangeCustom = !this.showDateRangeCustom;
    }

    onActiveTabIndexChange(event: any){
      this.activeIndex = event.value;
      this.form.get('stepOptions')?.setValue(this.activeIndex);
    }

    onActiveIndexChange(event: number) {
      this.activeIndex = event;
    }
}
<div class="subcontainer">
    <div *ngIf="listAppRegistries.length != 0 else empty" class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <label class="subcontainer-title">{{ "pass_application.applications" | translate}}</label>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'pass_application.new_application'| translate }}"  icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNew()"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNew()"
                    ></p-button>
                </div>
            </div>
        </div>
        <div>

        </div>
        <p-table
            #dt
            [value]="listAppRegistries"
            (onFilter)="onFilter($event)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['id','name','description','baseIdentifier','type','createdAt','updatedAt']"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column-sm" pSortableColumn="name">{{'content.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="description">{{ 'content.description' | translate }}<p-sortIcon field="description"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="baseIdentifier">{{ 'content.baseIdentifier' | translate }}<p-sortIcon field="baseIdentifier"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="type">{{ 'content.type' | translate }}<p-sortIcon field="type"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="createdAt">{{ 'created_at' | translate }}<p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="baseIdentifier" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="type" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="typeOptions"
                                    (onChange)="filter($event.value);"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="value"
                                    optionValue="key"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.value | translate }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate2">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="max-width: 200px;" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td (click)="onEditData(data)" showDelay="1000" pTooltip="{{data.name}}" tooltipPosition="top" class="ellipsis-cell">{{ data.name }}</td>
                    <td (click)="onEditData(data)" showDelay="1000" pTooltip="{{data.description}}" tooltipPosition="top" class="ellipsis-cell">{{ data.description }}</td>
                    <td (click)="onEditData(data)" showDelay="1000" pTooltip="{{data.baseIdentifier}}" tooltipPosition="top" class="ellipsis-cell">{{ data.baseIdentifier }}</td>
                    <td (click)="onEditData(data)" showDelay="1000" pTooltip="{{data.type | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ data.type | translate }}</td>
                    <td (click)="onEditData(data)" showDelay="1000" pTooltip="{{data.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.createdAt | date:('dateTimeFormat' | translate) }}</td>
                    <td (click)="onEditData(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.updatedAt | date:('dateTimeFormat' | translate) }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEditData(data)"></button>
                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onDeleteData(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="6" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>

    <ng-template #empty>
        <label class="subcontainer-title">{{ "pass_application.applications" | translate}}</label>
        <app-empty
            [readAndWritePermissions]="canReadAndWrite"
            buttonLabel="pass_application.new_application"
            titleLabel="pass_application.no_applications_available"
            (clicked)="createNew($event)"
        ></app-empty>
    </ng-template>

    <p-dialog [(visible)]="showDialog" [style]="{ minWidth: '410px' }" styleClass="p-fluid" [modal]="true" [closable]="true">
        <ng-template pTemplate="header">
            <div></div>
            <div class="dialog-title">
                {{ 'pass_application.save_application' | translate }}
            </div>
        </ng-template>
        <ng-template pTemplate="content">
            <app-application-edit
                [appRegistry]="appRegistry"
                [typeOptions]="typeOptions"
                (outputData)="onOutputData($event)"
            ></app-application-edit>
        </ng-template>
        <ng-template pTemplate="footer">
            <div class="dialog-footer">
                <div class="flex justify-content-center flex-wrap gap-2">
                    <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="onCancelData()"></button>
                    <p-button [disabled]="isSaveDisabled" label="{{ 'save' | translate }}" class="p-button-text" (click)="onSubmitData()" [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
                    ></p-button>
                </div>
            </div>
        </ng-template>
    </p-dialog>
</div>
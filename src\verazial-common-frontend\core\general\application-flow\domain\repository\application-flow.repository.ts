
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationFlowEntity } from "../entity/application-flow.entity";

export abstract class ApplicationFlowRepository {
    abstract addApplicationFlow(applicationFlow: ApplicationFlowEntity): Promise<ApplicationFlowEntity>;
    abstract updateApplicationFlowByAppId(applicationFlow: ApplicationFlowEntity): Promise<SuccessResponse>;
    abstract getApplicationFlowByAppId(params: { id: string }): Promise<ApplicationFlowEntity>;
    abstract deleteApplicationFlowByAppId(params: { id: string }): Promise<SuccessResponse>
}
import { DOCUMENT, formatDate } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Chart, { ChartType } from 'chart.js/auto';
/*import { getActionsByUseCase } from 'src/app/config/domain/use-case/getActionsBy.use-case';
import { GetConfigUseCase } from 'src/app/config/domain/use-case/get-config.use-case';
import { GetAllSubjectsUseCase } from 'src/app/home/<USER>/use-cases/get-subjects.usecase';
import { GetAllUsersUseCase } from 'src/app/home/<USER>/use-cases/get-users.usecase';*/
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { GetAllLicensesUseCase } from 'src/verazial-common-frontend/core/general/license/domain/use-cases/get-all-licenses.use-case';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';

@Component({
    selector: 'app-main-page',
    templateUrl: './main-page.component.html',
    styleUrl: './main-page.component.css'
})
export class MainPageComponent implements OnInit {

    selectedApp: string = "";
    progressValue: string = "";
    progressEnabled: boolean = false;
    progressColor: string = "#0ab4ba";
    appOptions: Array<any> = [];
    //applications: Array<any> = environment.applicationNames;

    chart: any;
    pieChart: any;
    hChart: any;
    dChart: any;
    lChart: any;
    vChart: any;
    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";
    subjectComputedNumber: any = 0;
    userComputedNumber: any = 0;
    samplesComputedNumber: any = 0;
    samplesAverageComputedNumber: any = 0;
    picturesComputedNumber: any = 0;

    identificationsNumber: any = 0;
    identificationsNumberF: any = 0;
    identificationsNumberF_ok: any = 0;
    identificationsNumberF_failed: any = 0;
    identificationsNumberI: any = 0;
    identificationsNumberI_ok: any = 0;
    identificationsNumberI_failed: any = 0;
    identificationsNumberH: any = 0;
    identificationsNumberH_ok: any = 0;
    identificationsNumberH_failed: any = 0;
    identificationsNumberO: any = 0;
    identificationsNumberO_ok: any = 0;
    identificationsNumberO_failed: any = 0;

    verificationsNumber: any = 0;
    verificationsNumberF: any = 0;
    verificationsNumberF_ok: any = 0;
    verificationsNumberF_failed: any = 0;
    verificationsNumberI: any = 0;
    verificationsNumberI_ok: any = 0;
    verificationsNumberI_failed: any = 0;
    verificationsNumberH: any = 0;
    verificationsNumberH_ok: any = 0;
    verificationsNumberH_failed: any = 0;
    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
    });

    subjectsData: any[] = [];
    subjectsDataPreFiltered: any[] = [];
    usersData: any[] = [];
    usersDataPreFiltered: any[] = [];
    actionsData: any[] = [];
    locations: any[] = [];
    showSpinners: boolean = true;

    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;
    showNoData5: boolean = false;
    showNoData6: boolean = false;
    showNoData7: boolean = false;
    showNoData8: boolean = false;

    constructor(
        @Inject(LOCALE_ID) private locale: string,
        @Inject(DOCUMENT) private document: Document,
        //private getActionByUseCase: getActionsByUseCase,
        //private getConfig: GetConfigUseCase, 
        //private getSubjects: GetAllSubjectsUseCase, 
        //private getUsers: GetAllUsersUseCase, 
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getAllTenantLicensesUseCase: GetAllLicensesUseCase,
        private reportsService: ReportsService,
        private translate: TranslateService,
        private cdr: ChangeDetectorRef,
        private fb: FormBuilder,
        private loggerService: ConsoleLoggerService,
    ) { }

    ngOnInit(): void {

        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);

        this.appOptions.push({ "name": "menu.all" });

        this.selectedApp = environment.applicationDefault;

        this.getConfigurationData();

    }

    getConfigurationData() {


        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                const currentTenant = data.tenantId;

                this.getAllTenantLicensesUseCase.execute({ tenantId: currentTenant }).then(
                    (data) => {
                        this.locations = Array.from(
                            new Set(
                                data.map((license) => license.location?.loc1).filter((loc1): loc1 is string => loc1 !== undefined)
                            )
                        );

                        this.locations.push(this.translate.instant("titles.unknown"));
                        //this.initializeVerificationArrays(this.locations.length);
                        this.getAllActions();
                    },
                    (error) => this.handleError(error)
                );
            },
            error: (e) => this.handleError(e)
        });
    }

    handleError(error?: any) {
        this.loggerService.error("Error get config from manager: " + error);
        this.locations = [this.translate.instant("titles.unknown")];
        //this.initializeVerificationArrays(1);
        setTimeout(() => this.getAllActions(), 1000);
    }

    getAllActions() {
        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";
        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = this.showNoData5 = this.showNoData6 = this.showNoData7 = this.showNoData8 = false;

        if (dateStartDate < dateEndDate) {

            /*this.getActionByUseCase.execute({ "from": startDate, "to": endDate }).subscribe({
                next: (actions) => {

                    this.loggerService.debug("Actions retrieved");
                    this.loggerService.debug(actions);
                    this.actionsData = actions.data;
                },
                complete: () => this.processData(),
                error: (e) => {
                    this.loggerService.error("Actions processing error");
                    this.loggerService.error(e);
                    setTimeout(() => {
                        this.progressEnabled = false;
                        this.progressValue = "0";
                    }, 2000);
                }
            });*/
            this.processData();
        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            setTimeout(() => {
                this.progressEnabled = false;
                this.progressValue = "0";
            }, 2000);
        }
    }

    processData() {

        //this.virtualActionsData = actions.data;

        var index = 0;
        this.identificationsNumber = 0;
        this.identificationsNumberF = 0;
        this.identificationsNumberF_ok = 0;
        this.identificationsNumberF_failed = 0;
        this.identificationsNumberI = 0;
        this.identificationsNumberI_ok = 0;
        this.identificationsNumberI_failed = 0;
        this.identificationsNumberH = 0;
        this.identificationsNumberH_ok = 0;
        this.identificationsNumberH_failed = 0;
        this.identificationsNumberO = 0;
        this.identificationsNumberO_ok = 0;
        this.identificationsNumberO_failed = 0;


        this.verificationsNumber = 0;
        this.verificationsNumberF = 0;
        this.verificationsNumberF_ok = 0;
        this.verificationsNumberF_failed = 0;
        this.verificationsNumberI = 0;
        this.verificationsNumberI_ok = 0;
        this.verificationsNumberI_failed = 0;
        this.verificationsNumberH = 0;
        this.verificationsNumberH_ok = 0;
        this.verificationsNumberH_failed = 0;


        var actionsNumber = 0;

        for (index = 0; index < this.actionsData.length; index++) {

            var processAction = false;

            if (this.actionsData[index].applicationId == this.selectedApp)
                processAction = true;

            //this.loggerService.debug(this.actionsData[index].attributes[0].name == "actionName" ? this.actionsData[index].attributes[0].value : "No action");

            //if(index<10)
            //this.loggerService.debug(this.actionsData[index]);

            if (this.actionsData[index].attributes.length > 0 && this.actionsData[index].attributes[0].name == "actionName" && processAction) {

                actionsNumber++;


                // Preprocess technologyID
                if (this.actionsData[index].technologyId == "") {

                    switch (this.actionsData[index].sensorBrand) {

                        case environment.sensorBrandsFinger[0].brand:
                        case environment.sensorBrandsFinger[1].brand:
                            this.actionsData[index].technologyId = "Huella";
                            break;

                        case environment.sensorBrandsIris[0].brand:
                        case environment.sensorBrandsIris[1].brand:
                            this.actionsData[index].technologyId = "Iris";
                            break;

                        case environment.sensorBrandsFacial[0].brand:
                        case environment.sensorBrandsFacial[1].brand:
                            this.actionsData[index].technologyId = "Facial";
                            break;

                        default:
                            this.actionsData[index].technologyId = "Facial";
                            break;
                    }

                }


                if (this.actionsData[index].attributes[0].value == "MCH_IDN" || this.actionsData[index].attributes[0].value == ("ID_SAM") || this.actionsData[index].attributes[0].value == ("ID_PIN")) {

                    if (this.actionsData[index].numericAttributes.length > 0) {

                        if (this.actionsData[index].numericAttributes[0].name == "totalTime") {


                            switch (this.actionsData[index].technologyId) {


                                case "Huella":
                                    this.identificationsNumberH++;
                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject identified successfully") {

                                        this.identificationsNumberH_ok++;
                                    }
                                    else {
                                        this.identificationsNumberH_failed++;
                                    }
                                    break;
                                case "Facial":
                                    this.identificationsNumberF++;

                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject identified successfully") {

                                        this.identificationsNumberF_ok++;
                                    }
                                    else {
                                        this.identificationsNumberF_failed++;
                                    }


                                    break;
                                case "Iris":
                                    this.identificationsNumberI++;
                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject identified successfully") {

                                        this.identificationsNumberI_ok++;
                                    }
                                    else {
                                        this.identificationsNumberI_failed++;
                                    }

                                    break;
                                case "PIN":
                                    this.identificationsNumberO++;
                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject identified successfully") {

                                        this.identificationsNumberO_ok++;
                                    }
                                    else {
                                        this.identificationsNumberO_failed++;
                                    }

                                    break;

                                default:
                                    break;


                            }

                        }

                        this.identificationsNumber++;

                    }
                }


                if (this.actionsData[index].attributes[0].value == "MCH_VRF" || this.actionsData[index].attributes[0].value == ("VER_INI") || this.actionsData[index].attributes[0].value == ("VER_COMP")) {


                    if (this.actionsData[index].numericAttributes.length > 0) {

                        if (this.actionsData[index].numericAttributes[0].name == "totalTime") {

                            switch (this.actionsData[index].technologyId) {


                                case "Huella":
                                    this.verificationsNumberH++;

                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject authenticated successfully") {

                                        this.verificationsNumberH_ok++;
                                    }
                                    else {
                                        this.verificationsNumberH_failed++;
                                    }
                                    break;
                                case "Facial":

                                    this.verificationsNumberF++;

                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject authenticated successfully") {

                                        this.verificationsNumberF_ok++;
                                    }
                                    else {
                                        this.verificationsNumberF_failed++;
                                    }

                                    break;
                                case "Iris":
                                    this.verificationsNumberI++;

                                    if (this.actionsData[index].actionResult == "SUCCESS" || this.actionsData[index].actionResult == "Success" || this.actionsData[index].actionResult == "Subject authenticated successfully") {

                                        this.verificationsNumberI_ok++;
                                    }
                                    else {
                                        this.verificationsNumberI_failed++;
                                    }
                                    break;
                                default:
                                    break;

                            }


                        }


                        this.verificationsNumber++;

                    }
                }

                this.updateProgress(index, this.actionsData.length);

                //window.requestAnimationFrame(() => (this.updateProgress(index, this.actionsData.length)))

            }
            else {
                //window.requestAnimationFrame(() => (this.updateProgress(index, this.actionsData.length)))

                this.updateProgress(index, this.actionsData.length);
            }
        }

        this.hideSpinners();
        this.progressValue = "100";

        setTimeout(() => {

            this.createBarChart1N();
            this.createBarChart11();
            this.progressEnabled = false;
            this.progressValue = "0";

        }, 2000);
    }

    updateProgress(index: any, total: any) {

        this.progressValue = (40 + (Math.ceil((60 * index) / total))).toString();
        this.loggerService.debug("New progress computed: " + this.progressValue);
        //this.cdr.detectChanges();

    }

    getAllSubjects() {

        this.loggerService.debug("getAllSubjects: 1");

        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";

        this.loggerService.debug("getAllSubjects: 2");

        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.loggerService.debug("Too much data to query");
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
        }
        else {


            if (dateStartDate < dateEndDate) {
                const startDate = dateStartDate.toISOString().split('T')[0] + "T00:00:00.000Z";
                const endDate = dateEndDate.toISOString().split('T')[0] + "T59:59:59.000Z";

                this.progressEnabled = true;
                this.progressValue = "5";


                /*this.getSubjects.execute().subscribe({
                    next: (subjectsReturn: any) => {


                        if (subjectsReturn.status == "SUCCESS") {

                            this.subjectsDataPreFiltered = subjectsReturn.data;
                           
                            this.loggerService.debug(this.subjectsDataPreFiltered);

                            var index = 0;
                            for (index = 0; index < this.subjectsDataPreFiltered.length; index++) {

                                var sub = this.subjectsDataPreFiltered[index];

                                if (new Date(sub.createdAt).toISOString() >= startDate && new Date(sub.createdAt).toISOString() <= endDate) {

                                    //this.loggerService.debug(sub);

                                    this.subjectsData.push(sub);

                                    if (sub.pic.indexOf("data:image") == 0)
                                        this.picturesComputedNumber++;

                                    this.subjectComputedNumber++;

                                }
                            }

                            this.progressValue = "20";

                            //this.loggerService.debug(subjectsReturn);
                            //this.loggerService.debug(subjectsReturn.data);
                            this.loggerService.debug("Subjects retrieved and processed ok.")
                        }
                        else
                        {
                            this.loggerService.error("Error retrieving subjects.")

                        }
                        //for (var userIndex = 0; userIndex < userList.data.lenght; )
                        //this.usersData= users;

                    },
                    complete: () => {
                        this.getAllUsers();
                    },
                    error: (e) => {
                        this.loggerService.error("Error retrieving subject: " + e);
                        this.progressEnabled = false;
                    }
                });*/
                this.getAllUsers();

            }
            else {
                this.progressEnabled = false;
                this.dateError = true;
                this.dateErrorMessage = "messages.error_dateRangeError";
                this.hideSpinners();
            }
        }
    }


    getAllUsers() {


        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);

        this.dateError = false;
        this.dateErrorMessage = "";

        if (dateStartDate < dateEndDate) {
            const startDate = dateStartDate.toISOString().split('T')[0] + "T00:00:00.000Z";
            const endDate = dateEndDate.toISOString().split('T')[0] + "T59:59:59.000Z";


            this.progressValue = "25";


            /*this.getUsers.execute().subscribe({
                next: (usersReturn: any) => {


                    if (usersReturn.status == "SUCCESS") {
                        this.usersDataPreFiltered = usersReturn.data;
                      
                        //this.loggerService.debug(usersReturn);
                        //this.loggerService.debug(usersReturn.data);

                        var index = 0;
                        for (index = 0; index < this.usersDataPreFiltered.length; index++) {

                            var usr = this.usersDataPreFiltered[index];
                            //this.loggerService.debug(usr.pic);

                            //if (usr.pic.indexOf("data:image") == 0)
                                //this.picturesComputedNumber++;

                            //this.subjectComputedNumber++;

                            if (new Date(usr.createdAt).toISOString() >= startDate && new Date(usr.createdAt).toISOString() <= endDate) {

                                this.usersData.push(usr);
                                this.userComputedNumber++;

                            }

                        }

                        this.loggerService.debug("Users retrieved and processed ok.");
                        this.progressValue = "40";
                    }
                    else
                    {
                        this.loggerService.error("Error retrieving users.")
                        this.progressValue = "40";
                    }
                    //for (var userIndex = 0; userIndex < userList.data.lenght; )
                    //this.usersData= users;

                },
                complete: () => { this.getAllActions() },
                error: (e) => {

                    this.progressEnabled = false;
                    this.loggerService.error("Error retrieving user: " + e);
                }
            });*/

            this.getAllActions();

        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
        }
    }




    createBarChart1N() {

        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");
        var option4 = this.translate.instant("titles.unknown");

        var optionA = this.translate.instant("titles.found");
        var optionB = this.translate.instant("titles.notFound");
        var optionC = this.translate.instant("messages.no_data_found");

        if (this.lChart == null) {


            if (this.identificationsNumberH == 0 && this.identificationsNumberF == 0 && this.identificationsNumberI == 0 && this.identificationsNumberO == 0) {

                this.lChart = new Chart("lChartMain", {
                    type: 'bar' as ChartType, //this denotes tha type of chart

                    data: {
                        labels: [option1, option2, option3, option4],
                        datasets: [{
                            data: [5, 10, 15, 20],
                            label: optionC,
                            backgroundColor: environment.colorDisabled,
                            fill: false,
                            barThickness: environment.barThickness
                        },
                        {
                            data: [3, 8, 13, 18],
                            backgroundColor: environment.colorDisabled,
                            fill: false,
                            barThickness: environment.barThickness
                        }
                        ]
                    }
                });
            }
            else {

                this.lChart = new Chart("lChartMain", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: {

                        labels: [option1, option2, option3, option4],
                        datasets: [{
                            data: [this.identificationsNumberH_ok, this.identificationsNumberF_ok, this.identificationsNumberI_ok, this.identificationsNumberO_ok],
                            label: optionA,
                            backgroundColor: environment.colorVerified,
                            fill: true,
                            barThickness: environment.barThickness
                        },
                        {
                            data: [this.identificationsNumberH_failed, this.identificationsNumberF_failed, this.identificationsNumberI_failed, this.identificationsNumberO_failed,],
                            label: optionB,
                            backgroundColor: environment.colorNotVerified,
                            fill: true,
                            barThickness: environment.barThickness
                        }
                        ]
                    }
                });
            }


        }
        else {


            if (this.identificationsNumberH == 0 && this.identificationsNumberF == 0 && this.identificationsNumberI == 0 && this.identificationsNumberO == 0) {
                // update graph
                this.lChart.options.plugins.legend.display = true;
                this.lChart.data.datasets[0].label = optionC;
                this.lChart.data.datasets[1].label = optionC;
                this.lChart.data.datasets[0].backgroundColor = environment.colorDisabled;
                this.lChart.data.datasets[1].backgroundColor = environment.colorDisabled;
                this.lChart.data.datasets[0].fill = false;
                this.lChart.data.datasets[1].fill = false;
                this.lChart.data.datasets[0].data = [5, 10, 15, 20];
                this.lChart.data.datasets[1].data = [3, 8, 13, 18];
                this.lChart.update();


            }
            else {
                // update graph
                this.lChart.options.plugins.legend.display = true;
                this.lChart.data.datasets[0].label = optionA;
                this.lChart.data.datasets[1].label = optionB;
                this.lChart.data.datasets[0].backgroundColor = environment.colorVerified;
                this.lChart.data.datasets[1].backgroundColor = environment.colorNotVerified;
                this.lChart.data.datasets[0].fill = true;
                this.lChart.data.datasets[1].fill = true;
                this.lChart.data.datasets[0].data = [this.identificationsNumberH_ok, this.identificationsNumberF_ok, this.identificationsNumberI_ok, this.identificationsNumberO_ok];
                this.lChart.data.datasets[1].data = [this.identificationsNumberH_failed, this.identificationsNumberF_failed, this.identificationsNumberI_failed, this.identificationsNumberO_failed];
                this.lChart.update();


            }




        }


    }

    createBarChart11() {

        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");

        var optionA = this.translate.instant("titles.verified");
        var optionB = this.translate.instant("titles.notVerified");

        var optionC = this.translate.instant("messages.no_data_found");

        if (this.vChart == null) {


            if (this.verificationsNumberH == 0 && this.verificationsNumberF == 0 && this.verificationsNumberI == 0) {

                this.vChart = new Chart("vChartMain", {
                    type: 'bar' as ChartType, //this denotes tha type of chart

                    data: {
                        labels: [option1, option2, option3],
                        datasets: [{
                            data: [5, 10, 15],
                            label: optionC,
                            backgroundColor: environment.colorDisabled,
                            fill: false,
                            barThickness: environment.barThickness
                        },
                        {
                            data: [3, 8, 13],
                            backgroundColor: environment.colorDisabled,
                            fill: false,
                            barThickness: environment.barThickness
                        }
                        ]
                    }
                });
            }
            else {

                this.vChart = new Chart("vChartMain", {
                    type: 'bar' as ChartType, //this denotes tha type of chart

                    data: {
                        labels: [option1, option2, option3],
                        datasets: [{
                            data: [this.verificationsNumberH_ok, this.verificationsNumberF_ok, this.verificationsNumberI_ok],
                            label: optionA,
                            backgroundColor: environment.colorVerified,
                            fill: true,
                            barThickness: environment.barThickness
                        },
                        {
                            data: [this.verificationsNumberH_failed, this.verificationsNumberF_failed, this.verificationsNumberI_failed],
                            label: optionB,
                            backgroundColor: environment.colorNotVerified,
                            fill: true,
                            barThickness: environment.barThickness
                        }
                        ]
                    }
                });
            }





        }
        else {

            // update graph

            if (this.verificationsNumberH == 0 && this.verificationsNumberF == 0 && this.verificationsNumberI == 0) {
                // update graph
                this.vChart.options.plugins.legend.display = true;
                this.vChart.data.datasets[0].label = optionC;
                this.vChart.data.datasets[1].label = optionC;
                this.vChart.data.datasets[0].backgroundColor = environment.colorDisabled,
                    this.vChart.data.datasets[1].backgroundColor = environment.colorDisabled,
                    this.vChart.data.datasets[0].fill = false;
                this.vChart.data.datasets[1].fill = false;
                this.vChart.data.datasets[0].data = [5, 10, 15];
                this.vChart.data.datasets[1].data = [3, 8, 13];
                this.vChart.update();


            }
            else {


                // update graph
                this.vChart.options.plugins.legend.display = true;
                this.vChart.data.datasets[0].label = optionA;
                this.vChart.data.datasets[1].label = optionB;
                this.vChart.data.datasets[0].backgroundColor = environment.colorVerified;
                this.vChart.data.datasets[1].backgroundColor = environment.colorNotVerified;
                this.vChart.data.datasets[0].fill = true;
                this.vChart.data.datasets[1].fill = true;
                this.vChart.data.datasets[0].data = [this.verificationsNumberH_ok, this.verificationsNumberF_ok, this.verificationsNumberI_ok];
                this.vChart.data.datasets[1].data = [this.verificationsNumberH_failed, this.verificationsNumberF_failed, this.verificationsNumberI_failed];
                this.vChart.update();


            }

        }


    }

    hideSpinners() {

        this.userComputedNumber == 0 ? this.showNoData1 = true : this.showNoData1 = false;
        this.subjectComputedNumber == 0 ? this.showNoData2 = true : this.showNoData2 = false;
        this.samplesComputedNumber == 0 ? this.showNoData3 = true : this.showNoData3 = false;
        this.samplesAverageComputedNumber == 0 ? this.showNoData4 = true : this.showNoData4 = false;
        this.picturesComputedNumber == 0 ? this.showNoData5 = true : this.showNoData5 = false;
        this.subjectsData.length == 0 ? this.showNoData6 = true : this.showNoData6 = false;
        this.showSpinners = false;
    }

    enableSpinners() {


        if (this.lChart != null) {

            this.lChart.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.lChart.data.datasets.length; i++) {

                this.lChart.data.datasets[i].data = [0, 0, 0, 0];

            }
            this.lChart.update();


        }

        if (this.vChart != null) {

            this.vChart.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.vChart.data.datasets.length; i++) {

                this.vChart.data.datasets[i].data = [0, 0, 0];

            }
            this.vChart.update();

        }

        this.subjectComputedNumber = 0;
        this.userComputedNumber = 0;
        this.samplesComputedNumber = 0;
        this.samplesAverageComputedNumber = 0;
        this.picturesComputedNumber = 0;
        this.showSpinners = true;
        this.subjectsData = [];
        this.subjectsDataPreFiltered = [];
        this.usersData = [];
        this.usersDataPreFiltered = [];
    }

}

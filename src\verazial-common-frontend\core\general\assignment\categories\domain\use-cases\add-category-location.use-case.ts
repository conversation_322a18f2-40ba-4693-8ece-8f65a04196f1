import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CategoryLocationEntity } from "../entity/category-location.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class AddCategoryLocationUseCase implements UseCaseGrpc<{ listOfLocations: CategoryLocationEntity[] }, CategoryLocationEntity[]> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { listOfLocations: CategoryLocationEntity[]; }): Promise<CategoryLocationEntity[]> {
        return this.groupCategoryRepository.addCategoryLocation(params);
    }
}
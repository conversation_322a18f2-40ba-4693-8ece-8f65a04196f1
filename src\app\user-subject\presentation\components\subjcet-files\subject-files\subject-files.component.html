<p-confirmDialog />
<p-toast/>

<div *ngIf="isLoading || isLoadingNewSubjectFile">
  <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<div *ngIf="!isLoading else loadingSpinner">
    <div *ngIf="subjectFiles.length > 0 else noData">
        <div *ngFor="let file of subjectFiles" class="my-3">
            <p-accordion>
                <p-accordionTab header="{{ getLabel(file.type) }}">
                    @if(file.files.length > 0){
                        <div class="flex flex-row justify-content-between flex-wrap gap-2 mb-3">
                            <div class="flex flex-row align-items-center">
                                <div class="pr-3">
                                    <label class="subcontainer-title">{{ getLabel(file.type) }}</label>
                                </div>
                            </div>
                            <div class="flex flex-row align-items-center">
                                <div class="pr-3">
                                    <p-button
                                        [disabled]="!readAndWritePermissions"
                                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                        label="{{ 'content.uploadNewFile' | translate }}"
                                        icon="pi pi-plus"
                                        [rounded]="true"
                                        (onClick)="uploadNew(file.type)"
                                    ></p-button>
                                </div>
                            </div>
                        </div>

                        <p-table
                            [value]="file.files"
                            (onFilter)="onFilter($event)"
                            dataKey="id"
                            [rowHover]="true"
                            [paginator]="true"
                            [rows]="10"
                            [rowsPerPageOptions]="[5, 10, 20]"
                            [scrollable]="true"
                            scrollHeight="flex"
                            scrollDirection="horizontal"
                            [tableStyle]="{ 'min-width': '75rem' }"
                            styleClass="fixed-table"
                            [showCurrentPageReport]="true"
                            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                            [sortField]="'number'" [sortOrder]="1">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th class="fixed-column-sm" pSortableColumn="name">{{'content.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="number">{{ 'content.number' | translate }}<p-sortIcon field="number"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="alias">{{ 'content.alias' | translate }}<p-sortIcon field="alias"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="description">{{ 'content.description' | translate }}<p-sortIcon field="description"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="mimeType">{{ 'content.mimeType' | translate }}<p-sortIcon field="mimeType"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="preview">{{ 'content.preview' | translate }}<p-sortIcon field="content"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="download">{{ 'content.download' | translate }}<p-sortIcon field="content"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="createdAt">{{ 'created_at' | translate }}<p-sortIcon field="createdAt"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                                    <th pFrozenColumn [frozen]="true"></th>
                                </tr>
                                <tr>
                                    <th>
                                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="number" field="number" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="number" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="alias" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="mimeType" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th></th>
                                    <th></th>
                                    <th>
                                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate2">
                                                <p-calendar
                                                    formControlName="date"
                                                    selectionMode="range"
                                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    dateFormat="{{ 'dateFormat' | translate }}"
                                                    appendTo="body"
                                                ></p-calendar>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                                <p-calendar
                                                    formControlName="date"
                                                    selectionMode="range"
                                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    dateFormat="{{ 'dateFormat' | translate }}"
                                                    appendTo="body"
                                                ></p-calendar>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-data>
                                <tr [pSelectableRow]="data">
                                    <td showDelay="1000" pTooltip="{{getLabel(data.name)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLabel(data.name) }}</td>
                                    <td showDelay="1000" pTooltip="{{data.number}}" tooltipPosition="top" class="ellipsis-cell">{{ data.number }}</td>
                                    <td showDelay="1000" pTooltip="{{data.alias}}" tooltipPosition="top" class="ellipsis-cell">{{ data.alias }}</td>
                                    <td showDelay="1000" pTooltip="{{data.description}}" tooltipPosition="top" class="ellipsis-cell">{{ data.description }}</td>
                                    <td showDelay="1000" pTooltip="{{data.mimeType}}" tooltipPosition="top" class="ellipsis-cell">{{ data.mimeType }}</td>
                                    <td showDelay="500" pTooltip="{{'content.preview' | translate}}" tooltipPosition="top" class="ellipsis-cell">
                                        <div  class="flex justify-content-center">
                                            <button pButton pRipple icon="pi pi-folder-open" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onPreview(data)" [disabled]="!readAndWritePermissions"></button>
                                        </div>
                                    </td>
                                    <td showDelay="500" pTooltip="{{'content.download' | translate}}" tooltipPosition="top" class="ellipsis-cell">
                                        <div class="flex justify-content-center">
                                            <button pButton pRipple icon="pi pi-cloud-download" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onDownload(data)" [disabled]="!readAndWritePermissions"></button>
                                        </div>
                                    </td>
                                    <td showDelay="1000" pTooltip="{{data.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.createdAt | date:('dateTimeFormat' | translate) }}</td>
                                    <td showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.updatedAt | date:('dateTimeFormat' | translate) }}</td>
                                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                                        <div *ngIf="readAndWritePermissions" class="flex flex-row">
                                            <!-- <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEdit(data)"></button> -->
                                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onDelete(data)"></button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    }
                    @else {
                        <div>
                            <app-empty
                                contentHeight="25vh"
                                [readAndWritePermissions]="readAndWritePermissions"
                                buttonLabel="content.uploadNewFile"
                                titleLabel="content.no_files_available"
                                (clicked)="uploadNew(file.type)"
                            ></app-empty>
                        </div>
                    }

                </p-accordionTab>
            </p-accordion>
        </div>
    </div>
</div>

<!-- Upload New -->
<p-dialog [(visible)]="showUploadDialog" styleClass="p-fluid" [modal]="true" [closable]="true" [style]="{'width': '40vw'}" (onHide)="onCancelDialog()">
    <ng-template pTemplate="header">
        <div>
            <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                {{ 'content.uploadNewFile' | translate }}
            </label>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <div>
            <form [formGroup]="form">
                <div class="grid form-fields">
                    <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form">{{ 'content.alias' | translate }}</label>
                        <input type="text" pInputText class="w-full" formControlName="alias" />
                    </div>
                    <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form">{{ 'content.description' | translate }}</label>
                        <input type="text" pInputText class="w-full" formControlName="description" />
                    </div>
                    <div class="col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form">{{ 'content.file' | translate }}</label>
                        <p-fileUpload
                            name="myfile[]"
                            accept="{{ acceptedFiles }}"
                            maxFileSize="{{ maxFileSize }}"
                            invalidFileSizeMessageSummary="{{ 'messages.file_size_exceeded' | translate }}"
                            invalidFileSizeMessageDetail="{{ 'messages.file_size_exceeded_detail' | translate }}"
                            invalidFileTypeMessageSummary="{{ 'messages.invalid_file_type' | translate }}"
                            invalidFileTypeMessageDetail="{{ 'messages.invalid_file_type_detail' | translate }}"
                            invalidFileLimitMessageDetail="{{ 'messages.invalid_file_limit_detail' | translate }}"
                            invalidFileLimitMessageSummary="{{ 'messages.invalid_file_limit' | translate }}"
                            uploadStyleClass="p-button-success"
                            cancelStyleClass="p-button-danger"
                            cancelLabel="{{ 'content.remove' | translate }}"
                            [customUpload]="true"
                            (uploadHandler)="onUpload($event)"
                            (onSelect)="onSelectedFiles($event)"
                        >
                            <ng-template pTemplate="content" let-files let-removeFileCallback="removeFileCallback">
                                <div *ngIf="files?.length > 0" class="flex flex-column flex-justify-content-center align-items-center">
                                    <div class="flex flex-wrap p-0 sm:p-5 gap-5">
                                        <div *ngFor="let file of files; let i = index" class="card m-0 p-3 border-1 surface-border">
                                            <div class="flex column justify-content-between w-full">
                                                <span class="font-semibold">{{ file.name }}</span>
                                                <i class="pi pi-times ml-3" style="font-size: 1rem; color: var(--danger); cursor: pointer;" pTooltip="{{ 'content.remove' | translate }}" tooltipPosition="top" (click)="removeFileCallback($event, index)"></i>
                                            </div>
                                            <div class="flex mt-3 flex-column align-items-center gap-3 px-6">
                                                <img *ngIf="file.type.includes('image') else noImage" role="presentation" [alt]="file.name" [src]="file.objectURL" width="100" />
                                                <ng-template #noImage>
                                                    <i class="pi pi-file" style="font-size: 2rem;"></i>
                                                </ng-template>
                                                <div>{{ formatSize(file.size) }}</div>
                                                <p-badge value="{{ 'content.pending' | translate }}" severity="warning" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>
                            <ng-template pTemplate="file"></ng-template>
                            <ng-template pTemplate="empty">
                                <div class="flex align-items-center justify-content-center flex-column">
                                    <i class="pi pi-cloud-upload border-2 border-circle p-5 text-8xl text-400 border-400"></i>
                                    <p class="mt-4 mb-0">{{ 'content.dragAndDropFilesHereToUpload' | translate }}</p>
                                </div>
                            </ng-template>
                        </p-fileUpload>
                    </div>
                </div>
            </form>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <div class="flex flex-row justify-content-end">
            <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="onCancelDialog()"></button>
        </div>
    </ng-template>
</p-dialog>

<!-- Preview -->

<ng-template #noData>
    <div class="flex justify-content-center align-items-center" style="height: 50vh;">
        {{ 'content.noSubjectFileGroupsConfigured' | translate }}
    </div>
</ng-template>

<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-items-center" style="height: 50vh;">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
    </div>
</ng-template>
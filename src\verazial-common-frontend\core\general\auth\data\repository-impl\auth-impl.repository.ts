import { AuthEntity } from "../../domain/entity/auth.entity";
import { AuthRepository } from "../../domain/repository/auth.repository";
import { AuthMapper } from "../mapper/auth.mapper";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Injectable } from "@angular/core";
import { CoreAuthServiceClient } from "src/verazial-common-frontend/core/generated/auth/AuthServiceClientPb";
import { AuthenticateByLicenseRequest, AuthenticateByUserRequest, ChangeTenantRequest, CheckTokenRequest, RefreshByUserRequest } from "src/verazial-common-frontend/core/generated/auth/auth_pb";
import { convertEmailPassToEmailPassCredentials, convertNumIdPassToNidPassCredentials } from "../common/converter/credentials-converter";
import { isValidEmail } from "src/verazial-common-frontend/core/util/supporting-functions";
import { AuthByLicenseRequestEntity } from "../../domain/entity/auth-by-license-request.entity";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";

@Injectable({
    providedIn: 'root',
})
export class AuthRepositoryImpl extends AuthRepository {

    authMapper = new AuthMapper()

    override authenticateByUser(params: { tenantId: string; username: string; password: string; }): Promise<AuthEntity> {
        let request = new AuthenticateByUserRequest();

        request.setTenantid(params.tenantId);
        // Auth By Email
        request.setEmailpass(convertEmailPassToEmailPassCredentials({ email: params.username, password: params.password }));
        // if (isValidEmail(params.username)) {
        //     // Auth By Email
        //     request.setEmailpass(convertEmailPassToEmailPassCredentials({ email: params.username, password: params.password }));
        // }
        // else {
        //     // Auth By NumId
        //     request.setNidpass(convertNumIdPassToNidPassCredentials({ numId: params.username, password: params.password }));
        // }

        let coreAccessServiceClient = new CoreAuthServiceClient(`${environment.grpcApiGateway}`);

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.authenticateByUser(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.authMapper.mapFrom(response));
                }
            });
        });
    }

    override checkToken(params: { token: string; }): Promise<boolean> {
        let request = new CheckTokenRequest();
        request.setToken(params.token);

        let coreAccessServiceClient = new CoreAuthServiceClient(`${environment.grpcApiGateway}`);

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.checkToken(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(response.getIsexpired());
                }
            });
        });
    }

    override refreshByUser(params: { oldToken: string; }): Promise<AuthEntity> {

        let request = new RefreshByUserRequest();
        request.setOldtoken(params.oldToken);

        let coreAccessServiceClient = new CoreAuthServiceClient(`${environment.grpcApiGateway}`);

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.refreshByUser(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.authMapper.mapFrom(response));
                }
            });
        });
    }

    override authenticateByLicense(requestParams: AuthByLicenseRequestEntity): Promise<AuthEntity> {
        let request = new AuthenticateByLicenseRequest();

        request.setLicense(requestParams.license!);
        request.setDevicehash(requestParams.deviceHash!);
        request.setApplicationname(requestParams.applicationName!);

        let coreAccessServiceClient = new CoreAuthServiceClient(`${environment.grpcApiGateway}`);

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.authenticateByLicense(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.authMapper.mapFrom(response));
                }
            });
        });
    }

    override changeTenant(params: { newTenantId: string; token: string; }): Promise<AuthEntity> {
        let request = new ChangeTenantRequest();
        request.setNewtenantid(params.newTenantId);
        request.setToken(params.token);

        let coreAccessServiceClient = new CoreAuthServiceClient(`${environment.grpcApiGateway}`);

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.changeTenant(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.authMapper.mapFrom(response));
                }
            });
        });
    }
}
import { DrawFlowRepository } from "../../repository/draw-flow.repository";
import { DrawFlowEntity } from "../../entity/draw-flow.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateDrawFlowUseCase implements UseCaseGrpc<{ drawFlow: DrawFlowEntity }, DrawFlowEntity> {
    constructor(private drawFlowRepository: DrawFlowRepository) { }
    execute(params: { drawFlow: DrawFlowEntity; }): Promise<DrawFlowEntity> {
        return this.drawFlowRepository.updateDrawFlow(params)
    }
}
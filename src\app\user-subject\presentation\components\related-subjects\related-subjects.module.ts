import { NgModule } from "@angular/core";
import { RelatedSubjectsComponent } from "./related-subjects/related-subjects.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputTextModule } from "primeng/inputtext";
import { InputIconModule } from "primeng/inputicon";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { OrganizationChartModule } from 'primeng/organizationchart';
import { TreeSelectModule } from "primeng/treeselect";

@NgModule({
    declarations: [
      RelatedSubjectsComponent
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      ProgressSpinnerModule,
      OrganizationChartModule,
      TreeSelectModule,
      /* Custom Modules */
    ],
    exports: [
      RelatedSubjectsComponent
    ]
  })
  export class RelatedSubjectsModule { }
<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>
<div *ngIf="!isLoading">
    <div *ngIf="showFlows; then editFlows else flows"></div>
    <ng-template #flows>
        <div *ngIf="hasFlows; then start else empty"></div>
        <ng-template #start>
            <app-list-flows
            [data]="data"
            [readAndWritePermissions]="canReadAndWrite"
            (onNewDataFlow)="createNewFlow($event)"
            (onEdit)="onEditFlow($event)"></app-list-flows>
        </ng-template>
        <ng-template #empty>
            <div class="container">
                <div class="subcontainer">
                    <label class="subcontainer-title">{{ "titles.flows" | translate}}</label>
                    <app-empty
                        [readAndWritePermissions]="canReadAndWrite"
                        (clicked)="createNewFlow($event)"
                        buttonLabel="flow.new_flow"
                        titleLabel="flow.no_flows_available"
                    ></app-empty>
                </div>
            </div>
        </ng-template>
    </ng-template>
    <ng-template #editFlows>
        <app-edit-flows
        (return)="onReturn($event)"
        [readAndWritePermissions]="canReadAndWrite"
        [taskFlow]="selectedFlow"></app-edit-flows>
    </ng-template>
</div>
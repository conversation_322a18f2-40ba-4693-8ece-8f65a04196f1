import { TaskFlowRepository } from "../../repository/task-flow.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { TaskFlowEntity } from "../../entity/task-flow.entity";

export class GetTaskFlowByIdUseCase implements UseCaseGrpc<{ id: string }, TaskFlowEntity> {
    constructor(private taskFlowRepository: TaskFlowRepository) { }
    execute(params: { id: string; }): Promise<TaskFlowEntity> {
        return this.taskFlowRepository.getTaskFlowById(params);
    }
}
import { Observable } from "rxjs";
import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialRepository } from "../repositories/credential.repository";

export class DeleteCredentialByNumIdAndSubjectAppIdUseCase implements UseCase<{ numId: string, subjectAppId: string }, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: { numId: string; subjectAppId: string; }): Observable<any> {
        return this.credentialsRepository.deleteCredentialByNumIdAndSubjectAppId(params);
    }
}
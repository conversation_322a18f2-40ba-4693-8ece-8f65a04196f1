import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { VerificationPageComponent } from './verification-page/verification-page.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';

const routes: Routes = [
  {
    path: '', 
    component: VerificationPageComponent,
    canActivate: [AuthGuard, NavigationGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class VerificationPageRoutingModule { }

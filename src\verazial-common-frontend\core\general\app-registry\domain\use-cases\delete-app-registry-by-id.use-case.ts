import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AppRegistryRepository } from "../repositories/app-registry.repository";

export class DeleteAppRegistryByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(
        private appRegistryRepository: AppRegistryRepository
    ) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.appRegistryRepository.deleteAppRegistryById(params)
    }
}
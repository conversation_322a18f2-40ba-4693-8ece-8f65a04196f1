import { GroupCategoryRepository } from "../repository/group-category.repository";
import { GroupCategoryEntity } from "../entity/group-category.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetAllGroupsCategoriesUseCase implements UseCaseGrpc<void, GroupCategoryEntity[]> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: void): Promise<GroupCategoryEntity[]> {
        return this.groupCategoryRepository.getAllGroupsCategories();
    }
}

import { WindowParametersEntity } from "../entities/window-parameters.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export abstract class WindowParametersRepository {
    abstract addWindowParam(windowParams: WindowParametersEntity[]): Promise<WindowParametersEntity[]>;
    abstract updateWindowParamById(windowParams: WindowParametersEntity): Promise<WindowParametersEntity>;
    abstract getWindowParamById(params: { id: string }): Promise<WindowParametersEntity>;
    abstract getWindowParamsByWindowId(params: { windowId: string }): Promise<WindowParametersEntity[]>;
    abstract getWindowParamsByAppId(params: { appId: string }): Promise<WindowParametersEntity[]>;
    abstract deleteWindowParamById(params: { id: string }): Promise<SuccessResponse>;
    abstract deleteWindowParamsByWindowId(params: { windowId: string, order: number }): Promise<SuccessResponse>;
}
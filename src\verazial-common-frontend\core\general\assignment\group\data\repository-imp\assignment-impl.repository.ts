import { AssignmentRespository } from "../../domain/repository/assignment.repository";
import { AssignmentMapper } from "../mapper/assignment.mapper";
import { Injectable } from "@angular/core";
import { AssignmentEntity } from "../../domain/entity/assignment.entity";
import { AssignmentElementMapper } from "../mapper/assignment-element.mapper";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { AssignmentElementEntity } from "../../domain/entity/assignment-elements.entity";
import { CoreAssignmentServiceClient } from "src/verazial-common-frontend/core/generated/assignment/AssignmentServiceClientPb";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { AssignmentElementGrpcModel, AssignmentGrpcModel, AssignmentResponseGrpModel } from "src/verazial-common-frontend/core/generated/assignment/assignment_pb";
import { Date, SearchParameters, StringParam } from "src/verazial-common-frontend/core/generated/util_pb";
import { toArrayOfAssignmentElements } from "../../common/converter/assignment.converter";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { HttpClient } from "@angular/common/http";
import { AssignmentSearchParametersEntity } from "../../domain/entity/search-parameters.entity";
import { AssignmentResponseEntity } from "../../domain/entity/assignment-response.entity";
import { AssignmentResponseMapper } from "../mapper/assignmnent-response.mapper";

@Injectable({
    providedIn: 'root',
})
export class AssignmentRepositoryImpl extends AssignmentRespository {

    assignmentMapper = new AssignmentMapper();
    assignmentElementMapper = new AssignmentElementMapper();
    assignmentResponseMapper = new AssignmentResponseMapper();

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override createAssignment(params: { assignment: AssignmentEntity; }): Promise<AssignmentEntity> {
        let request = this.assignmentMapper.mapTo(params.assignment);

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAssignmentServiceClient.createAssignment(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.assignmentMapper.mapFrom(response.getAssignment()!));
                    }
                }
            });
        });
    }

    override getAllAssignments(): Promise<AssignmentEntity[]> {
        let assignmentResponse: AssignmentEntity[] = [];

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreAssignmentServiceClient.getAllAssignments(new Empty);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: AssignmentGrpcModel) => {
                assignmentResponse.push(this.assignmentMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(assignmentResponse);
            });
        });
    }

    override getAssignmentById(params: { id: string; }): Promise<AssignmentEntity> {
        let request = new StringParam();
        request.setParameter(params.id);

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAssignmentServiceClient.getAssignmentById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.assignmentMapper.mapFrom(response.getAssignment()!));
                    }
                }
            });
        });
    }

    override deleteAssignmentById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new StringParam();
        request.setParameter(params.id);

        let success!: SuccessResponse;

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreAssignmentServiceClient.deleteAssignmentById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override updateAssignment(params: { assignment: AssignmentEntity; }): Promise<AssignmentEntity> {
        let request = this.assignmentMapper.mapTo(params.assignment);

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAssignmentServiceClient.updateAssignment(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.assignmentMapper.mapFrom(response.getAssignment()!));
                    }
                }
            });
        });
    }

    override addAssignmentElement(params: { elements: AssignmentElementEntity[]; }): Promise<AssignmentElementEntity[]> {
        let elements: AssignmentElementEntity[] = [];

        let request = toArrayOfAssignmentElements(params.elements);

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreAssignmentServiceClient.addAssignmentElement(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: AssignmentElementGrpcModel) => {
                elements.push(this.assignmentElementMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(elements);
            });
        });
    }

    override deleteAssignmentElementById(params: { elements: AssignmentElementEntity[]; }): Promise<SuccessResponse> {
        let request = toArrayOfAssignmentElements(params.elements);

        let success!: SuccessResponse;

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreAssignmentServiceClient.deleteAssignmentElementById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override searchAssignmentBy(params: AssignmentSearchParametersEntity): Promise<AssignmentResponseEntity[]> {
        let request = new SearchParameters();

        // subjectid
        request.setSubjectid(params.subjectId!);
        // locationid
        request.setLocationid(params.locationId!);
        // time
        request.setTime(undefined);
        // date
        if (params.date) {
            let date_: Date = new Date();
            date_.setYear(params.date.getFullYear()!);
            date_.setMonth(params.date.getMonth()!);
            date_.setDay(params.date.getDate()!);
            request.setDate(date_!);
        }
        // day
        request.setDay(params.day!);

        let actionResponse: AssignmentResponseEntity[] = [];

        let coreAssignmentServiceClient = new CoreAssignmentServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        let grpc = coreAssignmentServiceClient.searchAssignmentBy(request); //);

        return new Promise((resolve, reject) => {
            grpc.on('data', (response: AssignmentResponseGrpModel) => {
                actionResponse.push(this.assignmentResponseMapper.mapFrom(response));
            });
            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });
            grpc.on('end', () => {
                resolve(actionResponse);
            });
        });
    }
}
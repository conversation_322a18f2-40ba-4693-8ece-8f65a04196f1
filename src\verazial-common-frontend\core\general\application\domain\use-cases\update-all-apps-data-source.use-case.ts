import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationRepository } from "../repositories/application.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateAllAppsDataSourceUseCase implements UseCaseGrpc<{ dataSourceId: string, newDataSource: string }, SuccessResponse> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: { dataSourceId: string, newDataSource: string }): Promise<SuccessResponse> {
        return this.applicationRepository.updateAllAppsDataSource(params);
    }
}
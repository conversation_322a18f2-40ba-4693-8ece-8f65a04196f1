import { Component, HostListener, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { AuthService } from 'src/verazial-common-frontend/core/services/auth-service.service';
import { EventBusService } from 'src/verazial-common-frontend/core/services/event-bus.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit{
  title = 'verazial-app';

  isLoggedIn = false;
  eventBusSub?: Subscription;

  // Interval to start checking inactivity
  intervalStart = 5000;
  // Interval to check inactivity
  interval = 1000;
  // Inactivity timeout in milliseconds
  lockTime = 0;
  // Elapsed time in milliseconds
  elapsedTime = 0;

  // Count to check if the user is active
  countRefresh = 0;
  // Flag to check if the user is active
  isActivity = false;
  // Interval to start checking inactivity
  countRefreshStart: any;
  // Interval to check if the user is inactive
  isUserActivity: any;

  managerConfig: any;

  constructor(
    private storageService: LocalStorageService,
    private authService: AuthService,
    private eventBusService: EventBusService,
    /*
                    ** DO NOT REMOVE **
      Audit Trail Service used for the select reason dialog
    */
    private auditTrailService: AuditTrailService,
  ) {
    this.loadManagerConfig();
    this.startCheckingInactivity();
  }

  ngOnInit() {}

  startCheckingInactivity() {
    this.isActivity = true;
    clearInterval(this.isUserActivity);
    this.countRefreshStart = null;
    this.countRefreshStart = setTimeout(() => {
      this.countDown();
    }, this.intervalStart);
  }


  logout(): void {
    this.authService.logout();
  }

  countDown() {
    this.isActivity = true;
    clearInterval(this.isUserActivity);

    this.isUserActivity = setInterval(() => {
      if (this.lockTime > 0 && !this.isEmpty(this.storageService.getUser())) {
        this.countRefresh = 0;
        this.isActivity = false;

        this.elapsedTime = this.elapsedTime + this.interval;
        // console.log("Elapsed time: " + this.elapsedTime);
        // console.log("Lock time: " + this.lockTime);
        if (this.elapsedTime == this.lockTime) {
          this.logout();
        }
      }
    }, this.interval);

    if (this.isActivity && this.countRefresh === 1) {
      // Reset elapsed time if user is active
      this.elapsedTime = 0;
    }

    this.countRefresh++;
  }

  loadManagerConfig() {
    setInterval(() => {
      if (!this.isEmpty(this.storageService.getUser()) && this.lockTime == 0) {
        this.managerConfig = this.storageService.getSessionSettings();
        if (this.managerConfig.timeoutInactivity > 0) {
          this.lockTime = this.managerConfig.timeoutInactivity * 1000;
        }
        else {
          this.lockTime = 0;
        }
      }
    }, this.interval)
  }

  isEmpty(obj: Object) {
    if (obj == null) {
      return true;
    }
    return Object.keys(obj).length === 0;
  }

  @HostListener('document:mousemove', ['$event']) onMosusemove(e: MouseEvent) {
    // console.log('Mouse move event detected');
    this.startCheckingInactivity();
  }

  @HostListener('document:keydown', ['$event']) onKeydown(e: KeyboardEvent) {
    // console.log('Keydown event detected');
    this.startCheckingInactivity();
  }

  @HostListener('document:scroll', ['$event']) onScroll(e: Event) {
    // console.log('Scroll event detected');
    this.startCheckingInactivity();
  }

  @HostListener('document:click', ['$event']) onClick(e: MouseEvent) {
    // console.log('Click event detected');
    this.startCheckingInactivity();
  }

  @HostListener('document:touchstart', ['$event']) onTouchStart(e: TouchEvent) {
    // console.log('Touch start event detected');
    this.startCheckingInactivity();
  }

  @HostListener('document:touchmove', ['$event']) onTouchMove(e: TouchEvent) {
    // console.log('Touch move event detected');
    this.startCheckingInactivity();
  }

  @HostListener('document:touchend', ['$event']) onTouchEnd(e: TouchEvent) {
    // console.log('Touch end event detected');
    this.startCheckingInactivity();
  }
}

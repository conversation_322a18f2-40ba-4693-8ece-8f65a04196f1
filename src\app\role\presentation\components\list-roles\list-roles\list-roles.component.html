<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<div class="subcontainer">

    <ng-template #empty>
        <label class="subcontainer-title">{{ "titles.profile_role" | translate}}</label>
        <app-empty [readAndWritePermissions]="readAndWritePermissions" (clicked)="createNewRole()" buttonLabel="role.new_role" titleLabel="role.no_roles_available"></app-empty>
    </ng-template>

    <div *ngIf="listRoles.length != 0 else empty" class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <label class="subcontainer-title">{{ "titles.profile_role" | translate}}</label>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'role.new_profile_role'| translate }}"  icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNewRole()"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNewRole()"
                    ></p-button>
                </div>
            </div>
        </div>
        <div>

        </div>
        <p-table
            #dt
            [value]="listRoles"
            (onFilter)="onFilter($event, dt)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="[ 'name','level','type','description','createdAt','updatedAt' ]"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column-sm" pSortableColumn="name">{{'content.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="level">{{ 'content.level' | translate }}<p-sortIcon field="level"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="type">{{ 'content.type' | translate }}<p-sortIcon field="type"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="description">{{ 'content.description' | translate }}<p-sortIcon field="description"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="showInMenu">{{ 'content.showInMenu' | translate }}<p-sortIcon field="showInMenu"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="createdAt">{{ 'created_at' | translate }}<p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="level" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="type" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {label: 'content.subject_profile', value: '0'},
                                        {label: 'content.user_role', value: '1'},
                                    ]"
                                    (onChange)="filter($event.value);"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                    optionValue="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ (value == '0' ? 'content.subject_profile' : 'content.user_role') | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label | translate }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="showInMenu" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate2">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{data.name}}" tooltipPosition="top" class="ellipsis-cell">{{ data.name }}</td>
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{data.level}}" tooltipPosition="top" class="ellipsis-cell">{{ data.level }}</td>
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{getRoleType(data.type) | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ getRoleType(data.type) | translate }}</td>
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{data.description}}" tooltipPosition="top" class="ellipsis-cell">{{ data.description }}</td>
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{'options.' + data.showInMenu | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ 'options.' + data.showInMenu | translate }}</td>
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{data.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.createdAt | date:('dateTimeFormat' | translate) }}</td>
                    <td (click)="onEditRole(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.updatedAt | date:('dateTimeFormat' | translate) }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEditRole(data)"></button>
                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onDeleteRole(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>

    <!-- Create new role -->
    <p-dialog [(visible)]="showNewRoleDialog" styleClass="p-fluid" [modal]="true" [closable]="true" (onHide)="activeIndex = 0">
        <ng-template pTemplate="header">
            <div></div>
            <div class="dialog-title">
                {{ 'role.new_profile_role' | translate }}
            </div>
        </ng-template>
        <ng-template pTemplate="content">
            <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" (activeIndexChange)="onActiveIndexChange($event)"></p-steps>
            <div class="flex flex-column m-4">
                <div *ngIf="activeIndex==0; else elseBlock">
                    <app-role (outputData)="getRoleDetails($event)" [roleData]="roleDetails"></app-role>
                    <div class="flex justify-content-center gap-2">
                        <p-button
                            [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            label="{{ 'cancel'| translate }}"
                            (onClick)="showNewRoleDialog = false">
                        </p-button>
                        <div *ngIf="showAccessAssignment else saveButton">
                            <p-button
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': '1px solid #204887', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="!roleDetails?true:false"
                                label="{{ 'next'| translate }}"
                                (onClick)="next()">
                            </p-button>
                        </div>
                        <ng-template #saveButton>
                            <p-button
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                label="{{ 'save'| translate }}"
                                (onClick)="saveRoles()">
                            </p-button>
                        </ng-template>
                    </div>
                </div>

                <ng-template #elseBlock>
                    <div class="flex flex-row">
                        <app-access-assignment
                        [roleData]="roleDetails"
                        [listOfAccesses]="listOfAccesses"
                        [listOfSelectedRoleAccesses]="selectedRoleAccesses"
                        (roleAccesses)="getSelectedRoleAccesses($event)"
                        ></app-access-assignment>
                    </div>
                    <div class="flex flex-row justify-content-center gap-2 mt-4">
                        <p-button
                            [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            label="{{ 'back'| translate }}"
                            (onClick)="back()">
                        </p-button>
                        <p-button
                            [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            label="{{ 'save'| translate }}"
                            (onClick)="saveRoles()">
                        </p-button>
                    </div>
                </ng-template>
            </div>
        </ng-template>
    </p-dialog>

    <!-- Edit roles -->
    <p-dialog [(visible)]="showEditRoleDialog" styleClass="p-fluid" [modal]="true" [closable]="true" [style]="{'min-width': '400px'}" (onHide)="activeIndex = 0">
        <ng-template pTemplate="header">
            <div></div>
            <div class="dialog-title">
                {{ ('role.update_profile_role' | translate) + ' - ' + roleDetails.name }}
            </div>
        </ng-template>
        <ng-template pTemplate="content">
            <div class="flex justify-content-center mb-3" [formGroup]="formGroup">
                <p-selectButton
                    [options]="stepOptions"
                    formControlName="value"
                    severity="secondary"
                    multiple="false"
                    allowEmpty="false"
                    optionLabel="key"
                    optionValue="value"
                    dataKey="value"
                    [style]="{'max-width':'185px'}"
                    (onChange)="onActiveIndexChange($event)">
                </p-selectButton>
            </div>
            @if (activeIndex==0) {
                <app-role (outputData)="getRoleDetails($event)" [roleData]="roleDetails"></app-role>

                <div class="flex flex-row justify-content-center gap-2 mt-3">
                    <p-button
                        [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'cancel'| translate }}"
                        (onClick)="showEditRoleDialog = false">
                    </p-button>
                    <div *ngIf="showAccessAssignment else updateButton">
                        <p-button
                            [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': '1px solid #204887', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            [disabled]="!roleDetails?true:false"
                            label="{{ 'next'| translate }}"
                            (onClick)="next()">
                        </p-button>
                    </div>
                    <ng-template #updateButton>
                        <p-button
                            [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            label="{{ 'save'| translate }}"
                            (onClick)="onUpdateRoleOutput()">
                        </p-button>
                    </ng-template>
                </div>
            }@else {
                <div class="flex flex-row">
                    <app-access-assignment
                    [roleData]="roleDetails"
                    [listOfAccesses]="listOfAccesses"
                    [listOfSelectedRoleAccesses]="selectedRoleAccesses"
                    (roleAccesses)="getSelectedRoleAccesses($event)"
                    (remove)="accessesToBeRemoved($event)"
                    ></app-access-assignment>
                </div>
                <div class="flex flex-row justify-content-center gap-2 mt-4">
                    <p-button
                        [style]="{'width':'100px','height':'36px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'back'| translate }}"
                        (onClick)="back()">
                    </p-button>
                    <p-button
                        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'save'| translate }}"
                        (onClick)="onUpdateRoleOutput()">
                    </p-button>
                </div>
            }
        </ng-template>
    </p-dialog>
</div>
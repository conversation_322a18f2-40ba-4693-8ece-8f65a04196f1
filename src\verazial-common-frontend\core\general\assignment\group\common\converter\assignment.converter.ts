import { ArrayOfAssignmentsElements } from "src/verazial-common-frontend/core/generated/assignment/assignment_pb";
import { AssignmentElementEntity } from "../../domain/entity/assignment-elements.entity";
import { AssignmentElementMapper } from "../../data/mapper/assignment-element.mapper";

export function toArrayOfAssignmentElements(listOfElements?: AssignmentElementEntity[]): ArrayOfAssignmentsElements {
    let assignmentElementMapper = new AssignmentElementMapper();
    let arrayOfAssignmentsElements = new ArrayOfAssignmentsElements();

    listOfElements?.forEach((element) => {
        arrayOfAssignmentsElements.addElement(assignmentElementMapper.mapTo(element));
    })
    return arrayOfAssignmentsElements;
}

export function toListOfAssignmentElements(arrayOfAssignmentsElements?: ArrayOfAssignmentsElements): AssignmentElementEntity[] {
    let assignmentElementMapper = new AssignmentElementMapper();
    let listOfElements: AssignmentElementEntity[] = [];

    arrayOfAssignmentsElements?.getElementList().forEach(element => {
        listOfElements.push(assignmentElementMapper.mapFrom(element))
    })

    return listOfElements
}
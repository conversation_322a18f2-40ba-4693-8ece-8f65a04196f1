import { DOCUMENT, formatDate } from '@angular/common';
import { AfterViewInit, Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Chart, { ChartType } from 'chart.js/auto';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { CountActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-request.entity';
import { FilterEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/filter.entity';
import { CountActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/count-actions.use-case';
import { CountActionsResponseEntity, Group } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-response.entity';
import { MessageService } from 'primeng/api';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';

@Component({
    selector: 'app-performance-page',
    templateUrl: './performance-page.component.html',
    styleUrl: './performance-page.component.css',
    providers: [MessageService]
})
export class PerformancePageComponent implements OnInit, AfterViewInit {

    [key: string]: any;
    selectedTech: string = "";
    techOptions: Array<any> = [];
    technologies = environment.techNames;
    selectedApp: string = "";
    appOptions: Array<any> = [];
    progressValue: string = "";
    progressEnabled: boolean = false;
    progressColor: string = "#0ab4ba";
    chart: any;
    pieChart: any;
    hChart: any;
    dChart: any;
    lChart1: any;
    lChart2: any;
    lChart3: any;
    lChart4: any;
    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";

    averageVerifications: any[] = [];
    averageIdentifications: any[] = [];
    averageNewSamples: any[] = [];
    averageDelSamples: any[] = [];

    averageVerificationUserTime: number = 0;
    averageVerificationServerTime: number = 0;
    averageVerificationConnectionTime: number = 0;

    averageIdentificationUserTime: number = 0;
    averageIdentificationServerTime: number = 0;
    averageIdentificationConnectionTime: number = 0;

    averageNewSubjectUserTime: number = 0;
    averageNewSubjectServerTime: number = 0;
    averageNewSubjectConnectionTime: number = 0;

    averageDeleteSubjectUserTime: number = 0;
    averageDeleteSubjectServerTime: number = 0;
    averageDeleteSubjectConnectionTime: number = 0;

    averageNewSampleUserTime: number = 0;
    averageNewSampleServerTime: number = 0;
    averageNewSampleConnectionTime: number = 0;

    averageDeleteSampleUserTime: number = 0;
    averageDeleteSampleServerTime: number = 0;
    averageDeleteSampleConnectionTime: number = 0;

    averageNewPicUserTime: number = 0;
    averageNewPicServerTime: number = 0;
    averageNewPicConnectionTime: number = 0;

    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
        technology: [],
        subject: '',
    });

    actionsData: any[] = [];
    showSpinners: boolean = true;


    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;


    constructor(
        private translate: TranslateService,
        private fb: FormBuilder,
        private reportsService: ReportsService,
        private countActionsUseCase: CountActionsUseCase,
        private localStorageService: LocalStorageService,
        private messageService: MessageService,
        private loggerService: ConsoleLoggerService,
    ) { }


    ngOnInit(): void {

        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);

        this.appOptions.push({ "name": "menu.all" });

        this.selectedApp = environment.applicationDefault;

        this.technologies.forEach(t => {

            this.techOptions.push({ "name": t.name });

        });

        this.selectedTech = environment.techDefault;

        /*setTimeout(() => {
            this.getAllActions();
        }, 1000);*/
    }

    ngAfterViewInit(): void {
        this.getAllActions();
    }

    reset() {
        this.averageVerifications = [];
        this.averageIdentifications = [];
        this.averageNewSamples = [];
        this.averageDelSamples = [];

        this.averageVerificationUserTime = 0;
        this.averageVerificationServerTime = 0;
        this.averageVerificationConnectionTime = 0;

        this.averageIdentificationUserTime = 0;
        this.averageIdentificationServerTime = 0;
        this.averageIdentificationConnectionTime = 0;

        this.averageNewSubjectUserTime = 0;
        this.averageNewSubjectServerTime = 0;
        this.averageNewSubjectConnectionTime = 0;

        this.averageDeleteSubjectUserTime = 0;
        this.averageDeleteSubjectServerTime = 0;
        this.averageDeleteSubjectConnectionTime = 0;

        this.averageNewSampleUserTime = 0;
        this.averageNewSampleServerTime = 0;
        this.averageNewSampleConnectionTime = 0;

        this.averageDeleteSampleUserTime = 0;
        this.averageDeleteSampleServerTime = 0;
        this.averageDeleteSampleConnectionTime = 0;

        this.averageNewPicUserTime = 0;
        this.averageNewPicServerTime = 0;
        this.averageNewPicConnectionTime = 0;
    }

    async getResultForTechnology(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        const actionNames = ["MCH_VRF", "MCH_IDN", "NEW_SAM", "REM_SAM"] as const;

        const resultMapping: Record<typeof actionNames[number], any[]> = {
            MCH_VRF: this.averageVerifications,
            MCH_IDN: this.averageIdentifications,
            NEW_SAM: this.averageNewSamples,
            REM_SAM: this.averageDelSamples,
        };

        const createRequest = (actionName: typeof actionNames[number]): CountActionsRequestEntity => {
            const param = new CountActionsRequestEntity();
            param.startTime = dateStartDate;
            param.endTime = dateEndDate;
            param.filters.push({
                condition: { path: "actionName", value: actionName },
            });

            if (this.selectedApp != "menu.all") {
                const appFilterCount: FilterEntity = {
                    condition: {
                        path: "applicationId",
                        value: this.selectedApp,
                    },
                }
                param.filters.push(appFilterCount);
            }

            if (this.datesForm.controls['subject'].value != "") {
                const actionFilterCount: FilterEntity = {
                    condition: {
                        path: "commonAttributes.executorId",
                        value: this.datesForm.controls['subject'].value,
                    },
                }
                param.filters.push(actionFilterCount);
            }
    

            param.groupByAttributePath.push("commonAttributes.technologyId");
            param.extraCountModes.push({ mode: { attributePath: "timmingAttributes.totalTime" } });
            return param;
        };

        const processResult = (actionName: typeof actionNames[number], result: CountActionsResponseEntity) => {
            const averages = resultMapping[actionName];
            if (this.selectedTech === "menu.all" || this.selectedTech === "menu.fingerPrint")
                averages.push((result.groupByResults.find((e) => e.groupValue === "FINGER")?.groupExtraCountResults[0].mode?.value || 0) / 1000);
            else
                averages.push(0);
            if (this.selectedTech === "menu.all" || this.selectedTech === "menu.facial")
                averages.push((result.groupByResults.find((e) => e.groupValue === "FACE")?.groupExtraCountResults[0].mode?.value || 0) / 1000);
            else
                averages.push(0);
            if (this.selectedTech === "menu.all" || this.selectedTech === "menu.iris")
                averages.push((result.groupByResults.find((e) => e.groupValue === "IRIS")?.groupExtraCountResults[0].mode?.value || 0) / 1000);
            else
                averages.push(0);
        };

        const promises = actionNames.map(async (actionName) => {
            const request = createRequest(actionName);
            try {
                const result = await this.countActionsUseCase.execute(request);
                processResult(actionName, result);
            } catch (error) {
                this.loggerService.error(`Error processing ${actionName}: ` + error);
            }
        });

        await Promise.all(promises);
    }

    async getResultForAction(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        const actions = [
            { name: "MCH_VRF", keys: ["averageVerificationUserTime", "averageVerificationServerTime", "averageVerificationConnectionTime"] },
            { name: "MCH_IDN", keys: ["averageIdentificationUserTime", "averageIdentificationServerTime", "averageIdentificationConnectionTime"] },
            { name: "NEW_SUB", keys: ["averageNewSubjectUserTime", "averageNewSubjectServerTime", "averageNewSubjectConnectionTime"] },
            { name: "REM_SUB_BT", keys: ["averageDeleteSubjectUserTime", "averageDeleteSubjectServerTime", "averageDeleteSubjectConnectionTime"] },
            { name: "NEW_SAM", keys: ["averageNewSampleUserTime", "averageNewSampleServerTime", "averageNewSampleConnectionTime"] },
            { name: "REM_SAM", keys: ["averageDeleteSampleUserTime", "averageDeleteSampleServerTime", "averageDeleteSampleConnectionTime"] },
            { name: "ADD_PIC", keys: ["averageNewPicUserTime", "averageNewPicServerTime", "averageNewPicConnectionTime"] },
        ];

        const createRequest = (actionName: string): CountActionsRequestEntity => {
            let filters: FilterEntity[] = [{ condition: { path: "actionName", value: actionName } }];

            if (this.selectedTech === "menu.fingerprint") {
                filters.push({ condition: { path: "commonAttributes.technologyId", value: "FINGER" } });
            } else if (this.selectedTech === "menu.facial") {
                filters.push({ condition: { path: "commonAttributes.technologyId", value: "FACE" } });
            } else if (this.selectedTech === "menu.iris") {
                filters.push({ condition: { path: "commonAttributes.technologyId", value: "IRIS" } });
            }

            if (this.datesForm.controls['subject'].value != "") {
                const actionFilterCount: FilterEntity = {
                    condition: {
                        path: "commonAttributes.executorId",
                        value: this.datesForm.controls['subject'].value,
                    },
                }
                filters.push(actionFilterCount);
            }
    

            if (this.selectedApp != "menu.all") {
                filters.push({ condition: { path: "applicationId", value: this.selectedApp } });
            }

            return {
                startTime: dateStartDate,
                endTime: dateEndDate,
                filters,
                groupByAttributePath: [],
                extraCountModes: [
                    { mode: { attributePath: "timmingAttributes.userTime" } },
                    { mode: { attributePath: "timmingAttributes.serverTime" } },
                    { mode: { attributePath: "timmingAttributes.serverTimeCS" } },
                ],
            };
        };

        const processResult = (keys: string[], results: any) => {
            if (results.extraCountResults.length === 3) {
                keys.forEach((key, index) => {
                    this[key] = (results.extraCountResults[index].mode?.value || 0) / 1000;
                });
            }
        };

        await Promise.all(
            actions.map(async ({ name, keys }) => {
                try {
                    const results = await this.countActionsUseCase.execute(createRequest(name));
                    processResult(keys, results);
                } catch (error) {
                    this.loggerService.error(`Error processing action ${name}: ` + error);
                }
            })
        );
    }

    async getActionApplicationOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }



        paramCount.groupByAttributePath.push("applicationId");

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.appOptions = [];
                this.appOptions.push({ "name": "menu.all" });
                countActions.groupByResults.forEach((action: Group) => {
                    this.appOptions.push({ "name": action.groupValue });
                });
            }
        );
    }

    async getAllActions() {

        this.reset();

        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";

        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            return;
        }

        if (dateStartDate < dateEndDate) {

            try {
                await Promise.all([
                    this.getActionApplicationOptions(dateStartDate, dateEndDate),
                    this.getResultForTechnology(dateStartDate, dateEndDate),
                    this.getResultForAction(dateStartDate, dateEndDate),
                ]);
            } catch (error) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translate.instant("titles.error_operation"),
                    detail: this.translate.instant("messages.error_getting_actions"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }

            this.hideSpinners();

            setTimeout(() => {

                this.createLineChart1();
                this.createLineChart2();
            }, 100);
        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
        }
    }

    enableSpinners() {

        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = false;

        this.averageVerifications = [];
        this.averageIdentifications = [];
        this.averageNewSamples = [];
        this.averageDelSamples = [];
        this.actionsData = [];

        this.showSpinners = true;

        if (this.lChart1 != null) {

            this.lChart1.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.lChart1.data.datasets.length; i++) {

                this.lChart1.data.datasets[i].data = 0;

            }
            this.lChart1.update();

        }

        if (this.lChart2 != null) {

            this.lChart2.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.lChart2.data.datasets.length; i++) {

                this.lChart2.data.datasets[i].data = [0, 0, 0, 0, 0, 0, 0];

            }
            this.lChart2.update();

        }




    }

    hideSpinners() {

        this.showSpinners = false;

        this.actionsData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
        this.actionsData.length == 0 ? this.showNoData2 = true : this.showNoData2 = false;
        this.actionsData.length == 0 ? this.showNoData3 = true : this.showNoData3 = false;
        this.actionsData.length == 0 ? this.showNoData4 = true : this.showNoData4 = false;

    }

    createLineChart1() {


        var optionV = this.translate.instant("titles.actionV");
        var optionI = this.translate.instant("titles.actionI");
        var optionA = this.translate.instant("titles.actionA");
        var optionD = this.translate.instant("titles.actionD");

        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");
        var option4 = this.translate.instant("titles.unknown");
        var noDataFound = this.translate.instant("messages.no_data_found");


        const labels = [option1, option2, option3, option4];

        const chartData = {
            labels: labels,
            datasets: [{
                data: this.averageVerifications,
                label: optionV,
                backgroundColor: environment.colorActionVerification,
                barThickness: environment.barThickness,
                fill: false
            }, {
                data: this.averageIdentifications,
                label: optionI,
                backgroundColor: environment.colorActionIdentification,
                barThickness: environment.barThickness,
                fill: false
            }, {
                data: this.averageNewSamples,
                label: optionA,
                backgroundColor: environment.colorActionAddSample,
                barThickness: environment.barThickness,
                fill: false
            }, {
                data: this.averageDelSamples,
                label: optionD,
                backgroundColor: environment.colorActionDeleteSample,
                barThickness: environment.barThickness,
                fill: false
            }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled
                    }
                }
            },
        };

        var noDataAvailable = true;
        var index = 0;

        var datasetIndex = 0;

        for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

            for (index = 0; index < config.data.datasets[datasetIndex].data.length; index++) {

                if (config.data.datasets[datasetIndex].data[index] > 0)
                    noDataAvailable = false;

            }

        }

        if (noDataAvailable) {

            config.options.plugins.title.display = true;
            config.options.plugins.title.text = noDataFound;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

                config.data.datasets[datasetIndex].label = "";
                config.data.datasets[datasetIndex].backgroundColor = environment.colorDisabled,
                    config.data.datasets[datasetIndex].data = this.averageVerifications.fill(0);

                for (index = 0; index < this.averageVerifications.length; index++) {

                    config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                }

            }
        }
        else {

            config.options.plugins.title.display = false;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionV : (datasetIndex == 1 ? optionI : (datasetIndex == 2 ? optionA : (datasetIndex == 3 ? optionD : "")));
                config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorActionVerification : (datasetIndex == 1 ? environment.colorActionIdentification : (datasetIndex == 2 ? environment.colorActionAddSample : (datasetIndex == 3 ? environment.colorActionDeleteSample : "")));
            }
        }


        if (this.lChart1 == null) {


            this.lChart1 = new Chart("lChartPerformance1", {
                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options
            });

        }

        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }

        else {
            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;

        }

        this.lChart1.data = config.data;
        this.lChart1.options = config.options;
        this.lChart1.update();



    }



    createLineChart2() {


        var optionV = this.translate.instant("titles.actionV");
        var optionI = this.translate.instant("titles.actionI");
        var optionA = this.translate.instant("titles.actionA");
        var optionD = this.translate.instant("titles.actionD");

        var optionP = this.translate.instant("titles.actionP");
        var optionS = this.translate.instant("titles.actionS");
        var optionR = this.translate.instant("titles.actionR");

        var optionT1 = this.translate.instant("titles.userTime");
        var optionT2 = this.translate.instant("titles.connTime");
        var optionT3 = this.translate.instant("titles.serverTime");

        var noDataFound = this.translate.instant("messages.no_data_found");

        const labels = [optionV, optionI, optionS, optionR, optionA, optionD];//, optionP];

        const chartData = {
            labels: labels,
            datasets: [{
                data: [this.averageVerificationUserTime, this.averageIdentificationUserTime, this.averageNewSubjectUserTime, this.averageDeleteSubjectUserTime, this.averageNewSampleUserTime, this.averageDeleteSampleUserTime],//, this.averageNewPicUserTime],
                label: optionT1,
                backgroundColor: environment.colorProfile1,
                barThickness: environment.barThickness,
                fill: false
            }, {
                data: [this.averageVerificationConnectionTime, this.averageIdentificationConnectionTime, this.averageNewSubjectConnectionTime, this.averageDeleteSubjectConnectionTime, this.averageNewSampleConnectionTime, this.averageDeleteSampleConnectionTime],//, this.averageNewPicConnectionTime],
                label: optionT2,
                backgroundColor: environment.colorProfile2,
                barThickness: environment.barThickness,
                fill: false
            }, {
                data: [this.averageVerificationServerTime, this.averageIdentificationServerTime, this.averageNewSubjectServerTime, this.averageDeleteSubjectServerTime, this.averageNewSampleServerTime, this.averageDeleteSampleServerTime],//, this.averageNewPicServerTime],
                label: optionT3,
                backgroundColor: environment.colorProfile3,
                barThickness: environment.barThickness,
                fill: false
            }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled
                    }
                }
            },
        };

        var noDataAvailable = true;
        var index = 0;

        var datasetIndex = 0;

        for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

            for (index = 0; index < config.data.datasets[datasetIndex].data.length; index++) {

                if (config.data.datasets[datasetIndex].data[index] > 0)
                    noDataAvailable = false;

            }

        }

        if (noDataAvailable) {

            config.options.plugins.title.display = true;
            config.options.plugins.title.text = noDataFound;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

                config.data.datasets[datasetIndex].label = "";
                config.data.datasets[datasetIndex].backgroundColor = environment.colorDisabled;
                config.data.datasets[datasetIndex].data = [5, 10, 15, 20, 25, 30, 35];
            }
        }
        else {

            config.options.plugins.title.display = false;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionT1 : (datasetIndex == 1 ? optionT2 : (datasetIndex == 2 ? optionT3 : ""));
                config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorProfile1 : (datasetIndex == 1 ? environment.colorProfile2 : (datasetIndex == 2 ? environment.colorProfile3 : ""));
            }
        }


        if (this.lChart2 == null) {

            this.lChart2 = new Chart("lChartPerformance2", {

                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options
            });

        }


        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }

        else {
            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;

        }

        this.lChart2.data = config.data;
        this.lChart2.options = config.options;
        this.lChart2.update();

    }



}

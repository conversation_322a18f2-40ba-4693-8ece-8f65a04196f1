<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<!-- Audit Trail Select Reason -->
<p-confirmDialog #auditTrailSelectReasonDialog [key]="'auditTrailSelectReasonDialog'">
    <ng-template pTemplate="headless" let-message>
        <div class="flex flex-column align-items-center p-5 surface-overlay border-round">
            <div class="border-circle bg-primary inline-flex justify-content-center align-items-center h-6rem w-6rem">
                <i class="pi pi-save text-5xl"></i>
            </div>
            <span class="font-bold text-2xl block mb-2 mt-4">
                {{ message.header }}
            </span>
            <p class="mb-2">{{ message.message }}</p>
            <form [formGroup]="auditTrailService.reasonsForm">
                <p-dropdown
                    appendTo="body"
                    [options]="auditTrailService.reasonOptions"
                    formControlName="reason"
                    optionLabel="name"
                    optionValue="code"
                    placeholder="{{ 'content.reason' | translate }}" />
            </form>
            <div class="flex align-items-center gap-2 mt-4">
                <button
                    pButton
                    label="Save"
                    (click)="auditTrailSelectReasonDialog.accept()"
                    class="w-8rem">
                </button>
                <button
                    pButton
                    label="Cancel"
                    (click)="auditTrailSelectReasonDialog.reject()"
                    class="p-button-outlined w-8rem ">
                </button>
            </div>
        </div>
    </ng-template>
</p-confirmDialog>
<router-outlet></router-outlet>
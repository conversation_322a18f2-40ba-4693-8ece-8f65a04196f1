<form class="sub-container" [formGroup]="form">
    <ngx-verazial-ui-accordion title="{{ 'Applications'}}">
        <div class="accordion-content-individual" content>
            
            <table>
                <tr>
                    <div class="checkbox-div">
                        <input type="checkbox" id="cbox2" value="second_checkbox" />
                        <label for="cbox2">Running locally</label>
                    </div>
                </tr>
                <tr>
                    <div class="checkbox-div">
                        <input type="checkbox" id="cbox2" value="second_checkbox" />
                        <label for="cbox2">Update all applications</label>
                    </div>
                </tr>
                <div class="content-column" content>
                    <div class="accordion-content-individual" content>
                        <div class="sub-container-column">
                            <ngx-verazial-ui-accordion title="Applications" backgroundColor="white" borderColor="black" borderStyle="solid">
                                <div class="accordion-content" content>
                                    <div class="div-dropdown">
                                        <ng-multiselect-dropdown
                                            name="applications"
                                            [placeholder]="'Select Applications'"
                                            [settings]="dropdownSettings"
                                            [data]="dropdownList"
                                            formControlName="applications"
                                            [disabled]="false"
                                            (onSelect)="onItemSelect($event)"
                                            >
                                        </ng-multiselect-dropdown>
                                    </div>
                                </div>
                            </ngx-verazial-ui-accordion>
                            <br>
                            <ngx-verazial-ui-accordion title="Identifier Type" backgroundColor="white" borderColor="black" borderStyle="solid">
                                <div class="accordion-content" content>
                                    <div class="label-input-component" style="display: none;">
                                        <label>IP address/Hostname</label>
                                        <ngx-verazial-ui-text type="text" id="reason" formControlName="reason"></ngx-verazial-ui-text>
                                    </div>
                                    <div class="label-input-component">
                                        <label>Username</label>
                                        <ngx-verazial-ui-text type="text" id="reason" formControlName="reason"></ngx-verazial-ui-text>
                                    </div>
                                    <div class="label-input-component">
                                        <label>Password</label>
                                        <ngx-verazial-ui-text type="text" id="reason" formControlName="reason"></ngx-verazial-ui-text>
                                    </div>  
                                </div>
                            </ngx-verazial-ui-accordion>
                        </div>
                        
                        
                    </div>
                </div>
                <tr>
                    <td colspan="2">
                        <div class="button-wrapper">
                            <ngx-verazial-ui-button text="{{ 'buttons.save' | translate }}" type="submit" backgroundColor="#162746" fontColor="white" ></ngx-verazial-ui-button>
                            <!-- button class="button-component">Save</button -->
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </ngx-verazial-ui-accordion>
</form>
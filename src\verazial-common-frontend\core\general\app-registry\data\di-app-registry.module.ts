import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { GetAppRegistriesUseCase } from "../domain/use-cases/get-app-registries.use-case";
import { AppRegistryRepository } from "../domain/repositories/app-registry.repository";
import { GetAppRegistryByIdUseCase } from "../domain/use-cases/get-app-registry-by-id.use-case";
import { GetAppRegistryByNameUseCase } from "../domain/use-cases/get-app-registry-by-name.use-case";
import { AddAppRegistryUseCase } from "../domain/use-cases/add-app-registry.use-case";
import { UpdateAppRegistryUseCase } from "../domain/use-cases/update-app-registry.use-case";
import { DeleteAppRegistryByIdUseCase } from "../domain/use-cases/delete-app-registry-by-id.use-case";
import { AppRegistryRepositoryImpl } from "./repository-impl/app-registry-impl.repository";

const getAppRegistriesUseCaseFactory =
    (appRegistryRepository: AppRegistryRepository) => new GetAppRegistriesUseCase(appRegistryRepository);

const getAppRegistryByIdUseCaseFactory =
    (appRegistryRepository: AppRegistryRepository) => new GetAppRegistryByIdUseCase(appRegistryRepository);

const getAppRegistryByNameUseCaseFactory =
    (appRegistryRepository: AppRegistryRepository) => new GetAppRegistryByNameUseCase(appRegistryRepository);

const addAppRegistryUseCaseFactory =
    (appRegistryRepository: AppRegistryRepository) => new AddAppRegistryUseCase(appRegistryRepository);

const updateAppRegistryUseCaseFactory =
    (appRegistryRepository: AppRegistryRepository) => new UpdateAppRegistryUseCase(appRegistryRepository);

const deleteAppRegistryByIdUseCaseFactory =
    (appRegistryRepository: AppRegistryRepository) => new DeleteAppRegistryByIdUseCase(appRegistryRepository);

export const getAppRegistriesUseCaseProvider = {
    provide: GetAppRegistriesUseCase,
    useFactory: getAppRegistriesUseCaseFactory,
    deps: [AppRegistryRepository]
}

export const getAppRegistryByIdUseCaseProvider = {
    provide: GetAppRegistryByIdUseCase,
    useFactory: getAppRegistryByIdUseCaseFactory,
    deps: [AppRegistryRepository]
}

export const getAppRegistryByNameUseCaseProvider = {
    provide: GetAppRegistryByNameUseCase,
    useFactory: getAppRegistryByNameUseCaseFactory,
    deps: [AppRegistryRepository]
}

export const addAppRegistryUseCaseProvider = {
    provide: AddAppRegistryUseCase,
    useFactory: addAppRegistryUseCaseFactory,
    deps: [AppRegistryRepository]
}

export const updateAppRegistryUseCaseProvider = {
    provide: UpdateAppRegistryUseCase,
    useFactory: updateAppRegistryUseCaseFactory,
    deps: [AppRegistryRepository]
}

export const deleteAppRegistryByIdUseCaseProvider = {
    provide: DeleteAppRegistryByIdUseCase,
    useFactory: deleteAppRegistryByIdUseCaseFactory,
    deps: [AppRegistryRepository]
}

@NgModule({
    imports: [CommonModule],
    providers: [
        getAppRegistriesUseCaseProvider,
        getAppRegistryByIdUseCaseProvider,
        getAppRegistryByNameUseCaseProvider,
        addAppRegistryUseCaseProvider,
        updateAppRegistryUseCaseProvider,
        deleteAppRegistryByIdUseCaseProvider,
        { provide: AppRegistryRepository, useClass: AppRegistryRepositoryImpl },
    ]
})
export class DiAppRegistryModule { }
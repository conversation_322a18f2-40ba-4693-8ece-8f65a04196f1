<div class="flex flex-column">
    <div class="flex mb-4 ml-4">
        <label for="" [style]="{'color': '#757575', 'font-weight': '400', 'font-size':'16px'}">{{ assignmentData?.description }}</label>
    </div>
    
    <div class="flex flex-row gap-5 mr-4 ml-4 mt-3">
        <!-- List Flows -->
        <p-orderList 
            [value]="listSelectedFlows" 
            filterBy="name" 
            [listStyle]="{ height: '20rem', width: '20rem' }"
            [dragdrop]="false"
            header="{{ 'flow.selected_flows' | translate }}">
                <ng-template let-flow pTemplate="item">
                    <div class="flex flex-wrap p-2 align-items-center gap-3">
                        <div class="flex-1 flex flex-column gap-2">
                            <span>{{ flow.name }}</span>
                        </div>
                    </div>
                </ng-template>
        </p-orderList>
        <!-- List Categories -->
        <p-orderList 
            [value]="listSelectedCategories" 
            filterBy="name" 
            [contentEditable]="false"
            [dragdrop]="false"
            [listStyle]="{ height: '20rem', width: '20rem' }"
            header="{{ 'category.selected_categories' | translate }}">
                <ng-template let-category pTemplate="item">
                    <div class="flex flex-wrap p-2 align-items-center gap-3">
                        <div class="flex-1 flex flex-column gap-2">
                            <span class="font-bold">{{ category.name }}</span>
                            <div class="flex align-items-center gap-2 ">
                                @if (category.type=="USER") {
                                    <i class="pi pi-users"></i>
                                }@else if (category.type=="LOCATION") {
                                    <i class="pi pi-map-marker"></i>
                                }@else{
                                    <i class="pi pi-clock"></i>
                                }
                                
                                <span class="text-xs">
                                    {{getCategoryTranslation(category.type)}}
                                </span>
                            </div>
                        </div>
                    </div>
                </ng-template>
        </p-orderList>
    </div>

</div>
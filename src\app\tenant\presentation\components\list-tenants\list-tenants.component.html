<div class="subcontainer">
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row gap-4">
                <div>
                    <label class="subcontainer-title">{{ 'titles.tenants' | translate}}</label>
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                    [disabled]="!readAndWritePermissions"
                    [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    label="{{ 'tenant.new_tenant' | translate }}"
                    icon="pi pi-plus"
                    iconPos="right"
                    [rounded]="true"
                    (onClick)="showNewTenantDialog = true"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                    [disabled]="!readAndWritePermissions"
                    [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    icon="pi pi-plus"
                    [rounded]="true"
                    (onClick)="showNewTenantDialog = true"
                    ></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfTenants"
            (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedTenants"
            (selectionChange)="onTenantsSelectionChange($event)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['id', 'name', 'nif', 'email', 'country', 'city', 'status', 'isActive', 'createdAt', 'updatedAt']"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th *ngIf="showTenantId" class="fixed-column" pSortableColumn="id">{{ 'content.id' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="name">{{ 'tenant.name' | translate }} <p-sortIcon field="name"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="nif">{{ 'tenant.nif' | translate }} <p-sortIcon field="nif"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="email">{{ 'tenant.email' | translate }} <p-sortIcon field="email"></p-sortIcon></th>
                    <!-- <th class="fixed-column" pSortableColumn="country">{{ 'tenant.country' | translate }} <p-sortIcon field="country"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="city">{{ 'tenant.city' | translate }} <p-sortIcon field="city"></p-sortIcon></th> -->
                    <th class="fixed-column" pSortableColumn="status">{{ 'tenant.status' | translate }} <p-sortIcon field="status"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="isActive">{{ 'tenant.is_active' | translate }} <p-sortIcon field="isActive"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="createdAt">{{ 'tenant.created_at' | translate }} <p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedAt">{{ 'tenant.updated_at' | translate }} <p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th *ngIf="showTenantId">
                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="nif" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="email" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <!-- <th>
                        <p-columnFilter type="text" field="country" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="city" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th> -->
                    <th>
                        <p-columnFilter field="status" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {label: this.getTenantStatus(0), value: 0},
                                        {label: this.getTenantStatus(1), value: 1},
                                        {label: this.getTenantStatus(2), value: 2}
                                    ]"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                    optionValue="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ getTenantStatus(value) | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label | translate }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="isActive" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-tenant>
                <tr class="p-selectable-row">
                    <td *ngIf="showTenantId" (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.id}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.id }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.name}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.name }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.nif}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.nif }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.email}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.email }}</td>
                    <!-- <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.country}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.country }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.city}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.city }}</td> -->
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{getTenantStatus(tenant.status) | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ getTenantStatus(tenant.status) | translate }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{( tenant.isActive ? 'options.true' : 'options.false' ) | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ ( tenant.isActive ? 'options.true' : 'options.false' ) | translate }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.createdAt | date:('dateTimeFormat' | translate) }}</td>
                    <td (click)="editTenant(tenant)" showDelay="1000" pTooltip="{{tenant.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ tenant.updatedAt | date:('dateTimeFormat' | translate) }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!readAndWritePermissions" icon="pi pi-database" [text]="true" class="mr-2" style="width: 2rem; padding: 0;" (click)="editDatabase(tenant)"></button>
                            <button pButton pRipple [disabled]="!readAndWritePermissions" icon="pi pi-pencil" [text]="true" class="mr-2" style="width: 2rem; padding: 0;" (click)="editTenant(tenant)"></button>
                            <button pButton pRipple [disabled]="!readAndWritePermissions" icon="pi pi-trash" [text]="true" class="mr-2" style="width: 2rem; padding: 0;" (click)="onDeleteTenant(tenant)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>

        <!-- Create new User/Subject -->
        <p-dialog [(visible)]="showNewTenantDialog" [style]="{ width: '30rem' }" [modal]="true" [closable]="true" (onHide)="cancelTenant()">
            <ng-template pTemplate="header">
                <div >
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ 'tenant.tenant' | translate }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <app-edit-tenant [tenantData]="selectedTenant" [listOfTenants]="listOfTenants" (tenatDataOutput)="getTenantData($event)" (onSaveTenant)="saveTenant($event)" (onCancelTenant)="cancelTenant()"></app-edit-tenant>
            </ng-template>
            <!-- <ng-template pTemplate="footer">
                <div class="flex flex-row justify-content-center">
                    <p-button
                    [style]="{'width':'100px','height':'36px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    label="{{ 'cancel'| translate }}"
                    (onClick)="showNewTenantDialog = false"
                    ></p-button>
                    <p-button
                    [disabled]="!readAndWritePermissions || isDisableSaveButton"
                    [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    label="{{ 'save'| translate }}"
                    (onClick)="saveTenant()"
                    ></p-button>
                </div>
            </ng-template> -->
        </p-dialog>

        <!-- Show tenant database config -->
        <p-dialog [(visible)]="showTenantDBDialog" styleClass="p-fluid" [style]="{ width: '30rem' }" [modal]="true" [closable]="true">
            <ng-template pTemplate="header">
                <div >
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ 'tenant.database_config' | translate }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <app-tenant-database [tenantId]="tenantId"></app-tenant-database>
            </ng-template>
        </p-dialog>
    </div>
</div>

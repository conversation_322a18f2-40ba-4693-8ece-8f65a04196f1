import { DataSourceRepository } from "../repositories/data-source.repository";
import { DataSourceEntity } from "../entities/data-source.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetAllDataSourcesUseCase implements UseCaseGrpc<void, DataSourceEntity[]> {
    constructor(private dataSourceRepository: DataSourceRepository) { }
    execute(params: void): Promise<DataSourceEntity[]> {
        return this.dataSourceRepository.getAllDataSources();
    }
}
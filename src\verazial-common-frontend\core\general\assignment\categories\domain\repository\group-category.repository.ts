import { CategoryLocationEntity } from "../entity/category-location.entity";
import { CategoryScheduleEntity } from "../entity/category-schedule.entity";
import { CategorySubjectEntity } from "../entity/category-subject.entity";
import { DayTimeScheduleEntity } from "../entity/day-time-schedule.entity";
import { GroupCategoryEntity } from "../entity/group-category.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export abstract class GroupCategoryRepository {
    abstract createGroupCategory(params: { group: GroupCategoryEntity }): Promise<GroupCategoryEntity>;
    abstract getAllGroupsCategories(): Promise<GroupCategoryEntity[]>;
    abstract getGroupCategoryById(params: { id: string }): Promise<GroupCategoryEntity>;
    abstract updateGroupCategoryById(params: { group: GroupCategoryEntity }): Promise<GroupCategoryEntity>;
    abstract deleteGroupCategoryById(params: { id: string }): Promise<SuccessResponse>;

    abstract addCategorySubject(params: { listOfSubjects: CategorySubjectEntity[] }): Promise<CategorySubjectEntity[]>;
    abstract deleteCategorySubject(params: { listOfSubjects: CategorySubjectEntity[] }): Promise<SuccessResponse>;

    abstract addCategoryLocation(params: { listOfLocations: CategoryLocationEntity[] }): Promise<CategoryLocationEntity[]>;
    abstract deleteCategoryLocation(params: { listOfLocations: CategoryLocationEntity[] }): Promise<SuccessResponse>;

    abstract updateCategorySchedule(params: { schedule: CategoryScheduleEntity }): Promise<SuccessResponse>;

    abstract addDayTimeSchedule(params: { listOfDayTimeSchedule: DayTimeScheduleEntity[] }): Promise<DayTimeScheduleEntity[]>;
    abstract updateDayTimeSchedule(params: { dayTimeSchedule: DayTimeScheduleEntity }): Promise<DayTimeScheduleEntity>;
    abstract deleteDayTimeSchedule(params: { id: string }): Promise<SuccessResponse>
}

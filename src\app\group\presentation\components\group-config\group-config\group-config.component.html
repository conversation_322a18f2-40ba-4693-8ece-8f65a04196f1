<div [formGroup]="form">
    <div class="mt-4 ">
        <div class="flex justify-content-end requiredFieldsLabel">
            {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
        </div>
        <div class="grid">
            <div class="col-6 flex justify-content-between">
                <label class="label-form" for="isRequiredWithinSchedule">{{ 'content.isRequiredWithinSchedule' | translate }} <span *ngIf="isRequiredField('isRequiredWithinSchedule')" class="requiredStar">*</span></label>
                <div>
                    <p-inputSwitch formControlName="isRequiredWithinSchedule" (onChange)="trackChanges($event)"/>
                    <small *ngIf="!isValid('isRequiredWithinSchedule') && form.controls['isRequiredWithinSchedule'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
            <div class="pt-0 col-12">
                <p-accordion>
                    <p-accordionTab header="{{ 'content.requiredSchedule' | translate }}" [disabled]="!form.controls['isRequiredWithinSchedule'].value">
                        <app-category-assignment [canReadAndWrite]="canReadAndWrite && form.controls['isRequiredWithinSchedule'].value" [filterType]="[groupCategoryType.SCHEDULES]" (listCategories)="getSelectedConfigSchedules($event)" [inputData]="selectedConfigSchedules" [limit]="1"></app-category-assignment>
                    </p-accordionTab>
                </p-accordion>
            </div>
            <div *ngIf="form.controls['isRequiredWithinSchedule'].value" class="col-12 grid">
                <div class="col-6 flex justify-content-between">
                    <label class="label-form" for="alertIfAllPerformedWithinSchedule">{{ 'content.alertIfAllPerformedWithinSchedule' | translate }} <span *ngIf="isRequiredField('alertIfAllPerformedWithinSchedule')" class="requiredStar">*</span></label>
                    <div>
                        <p-inputSwitch formControlName="alertIfAllPerformedWithinSchedule" (onChange)="trackChanges($event)"/>
                        <small *ngIf="!isValid('alertIfAllPerformedWithinSchedule') && form.controls['alertIfAllPerformedWithinSchedule'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>
                <div class="col-6 flex justify-content-between">
                    <label class="label-form" for="alertIfNotPerformedWithinSchedule">{{ 'content.alertIfNotPerformedWithinSchedule' | translate }} <span *ngIf="isRequiredField('alertIfNotPerformedWithinSchedule')" class="requiredStar">*</span></label>
                    <div>
                        <p-inputSwitch formControlName="alertIfNotPerformedWithinSchedule" (onChange)="trackChanges($event)"/>
                        <small *ngIf="!isValid('alertIfNotPerformedWithinSchedule') && form.controls['alertIfNotPerformedWithinSchedule'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>
                <div class="col-6 flex justify-content-between">
                    <label class="label-form" for="alertIfPerformedOutsideSchedule">{{ 'content.alertIfPerformedOutsideSchedule' | translate }} <span *ngIf="isRequiredField('alertIfPerformedOutsideSchedule')" class="requiredStar">*</span></label>
                    <div>
                        <p-inputSwitch formControlName="alertIfPerformedOutsideSchedule" (onChange)="trackChanges($event)"/>
                        <small *ngIf="!isValid('alertIfPerformedOutsideSchedule') && form.controls['alertIfPerformedOutsideSchedule'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>
                <div class="pt-0 col-12">
                    <p-accordion>
                        <p-accordionTab header="{{ 'content.usersToAlert' | translate }}" [disabled]="!form.controls['alertIfAllPerformedWithinSchedule'].value && !form.controls['alertIfNotPerformedWithinSchedule'].value && !form.controls['alertIfPerformedOutsideSchedule'].value">
                            <app-category-assignment [canReadAndWrite]="canReadAndWrite && (form.controls['alertIfAllPerformedWithinSchedule'].value || form.controls['alertIfNotPerformedWithinSchedule'].value || form.controls['alertIfPerformedOutsideSchedule'].value)" [filterType]="[groupCategoryType.USERS]" (listCategories)="getSelectedConfigUsers($event)" [inputData]="selectedConfigUsers"></app-category-assignment>
                        </p-accordionTab>
                    </p-accordion>
                </div>
            </div>
        </div>
    </div>
</div>

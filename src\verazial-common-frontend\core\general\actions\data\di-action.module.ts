import { ActionRepository } from "../domain/repository/action.repository";
import { AddActionUseCase } from "../domain/use-cases/add-action.use-case";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ActionRepositoryImpl } from "./repository-impl/action-impl.repository";

const addActionUseCaseFactory = 
(actionRepository: ActionRepository) => new AddActionUseCase(actionRepository);

export const addActionUseCaseProvider = {
    provide: AddActionUseCase,
    useFactory: addActionUseCaseFactory,
    deps: [ActionRepository]
}

@NgModule({ imports: [CommonModule], providers: [
        addActionUseCaseProvider,
        { provide: ActionRepository, useClass: ActionRepositoryImpl },
        provideHttpClient(withInterceptorsFromDi())
    ] })
export class DiActionModule{  }
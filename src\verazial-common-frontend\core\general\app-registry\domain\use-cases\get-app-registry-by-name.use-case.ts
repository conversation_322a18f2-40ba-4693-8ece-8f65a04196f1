import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AppRegistryEntity } from "../entities/app-registry.entity";
import { AppRegistryRepository } from "../repositories/app-registry.repository";

export class GetAppRegistryByNameUseCase implements UseCaseGrpc<{ name: string }, AppRegistryEntity> {
    constructor(
        private appRegistryRepository: AppRegistryRepository
    ) { }
    execute(params: { name: string }): Promise<AppRegistryEntity> {
        return this.appRegistryRepository.getAppRegistryByName(params);
    }
}
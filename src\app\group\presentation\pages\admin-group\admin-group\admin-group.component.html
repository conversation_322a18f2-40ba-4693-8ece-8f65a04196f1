<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>
<app-list-assignments [readAndWritePermissions]="canReadAndWrite" [listAssignments]="listAssignments"></app-list-assignments>
<!-- @if(listAssignments.length>0){
}@else{
    <app-empty [readAndWritePermissions]="canReadAndWrite" buttonLabel="assignment.new_assignment" titleLabel="assignment.no_assignment_available" (clicked)="createNewAdminGroup($event)"></app-empty>
}

<p-dialog [(visible)]="showFlowCategoryDialog" [style]="{ width: '392px' }" header="{{ 'messages.warning' | translate }}" [modal]="true" styleClass="p-fluid">
    <ng-template pTemplate="content">
        <div class="flex flex-row align-content-center justify-content-start mt-3 gap-3">
            <i class="pi pi-exclamation-triangle" style="font-size: 28px"></i>
            {{ flowCategoryMessage | translate }}
        </div>
        
    </ng-template>
 
     <ng-template pTemplate="footer">
        <div class="flex flex-col justify-content-center">
            @if(showCreateFlowButton){
                <p-button label="{{ 'flow.flow' | translate }}" class="p-button-text" ic (click)="onAddFlow()" icon="pi pi-plus" iconPos="right"
                    [style]="{'color': '#FFFFFF', 'width': '130px', 'background': '#204887' }"
                ></p-button>
            }

            @if(showCreateCategoryButton){
                <p-button label="{{ 'category.category' | translate }}" class="p-button-text" ic (click)="onAddCategory()" icon="pi pi-plus" iconPos="right"
                    [style]="{'color': '#FFFFFF', 'width': '130px', 'background': '#204887' }"
                ></p-button>
            }
        </div>
         
     </ng-template>  
</p-dialog>

<p-dialog [(visible)]="showNewAdminGroupDialog" styleClass="p-fluid" [closable]="true" [modal]="true" (onHide)="resetStates()">
    <ng-template pTemplate="header">
        <div></div>
        <div class="dialog-title">
            {{ 'assignment.new_assignment' | translate }}    
        </div>
    </ng-template>
    
    <ng-template pTemplate="content">
        <p-steps 
        [model]="items" 
        [readonly]="false" 
        [activeIndex]="activeIndex"/>
        @if(activeIndex==0){
            <app-group-info (dataValid)="assignmentInfoDataValid($event)" (outputData)="getGroupData($event)" [inputData]="assignmentInfoData"></app-group-info>
            <div class="flex align-content-center justify-content-center mt-3 gap-3">
                <p-button label="{{ 'cancel' | translate }}"
                (onClick)="onCancel()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
                <p-button label="{{ 'next' | translate }}"   iconPos="right" 
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                    [disabled]="disableContinue? true : false" 
                    (onClick)="onNext()">
                </p-button>
            </div>
        }@else if (activeIndex==1) {
            <app-flow-assignment 
            (listFlows)="getSelectedFlows($event)" 
            [inputData]="selectedFlows" 
            [sourceListFlows]="listTaskFlows"></app-flow-assignment>
            <div class="flex align-content-center justify-content-center mt-3 gap-3">
                <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left" 
                (onClick)="onBack()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
                <p-button label="{{ 'next' | translate }}"  iconPos="right" 
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                    (onClick)="onNext()">
                </p-button>
            </div>
        }@else {
            <app-category-assignment 
            [inputData]="selectedCategories" 
            (listCategories)="getSelectedCategories($event)"></app-category-assignment>
            <div class="flex align-content-center justify-content-center mt-3 gap-3">
                <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left" 
                (onClick)="onBack()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
                <p-button label="{{ 'save' | translate }}"  iconPos="right" 
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                    (onClick)="onSave()">
                </p-button>
            </div>
        }
        <div>

        </div>
    </ng-template>
</p-dialog> -->
import { AssignmentElementGrpcModel, AssignmentTypeGrpc } from "src/verazial-common-frontend/core/generated/assignment/assignment_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { AssignmentElementEntity } from "../../domain/entity/assignment-elements.entity";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";
import { AssignmentType } from "../../common/enums/assignment-type.enum";
import { GroupCategoryTypeGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { GroupCategoryType } from "../../../categories/common/models/group-category-type.enum";

export class AssignmentElementMapper extends Mapper<AssignmentElementGrpcModel, AssignmentElementEntity> {
    override mapFrom(param: AssignmentElementGrpcModel): AssignmentElementEntity {
        let assignmentElement = new AssignmentElementEntity();

        assignmentElement.id = param.getId();
        assignmentElement.assignmentId = param.getAssignmentid();
        assignmentElement.type = toEnum(AssignmentType, param.getType());
        assignmentElement.subtype = toEnum(GroupCategoryType, param.getSubtype());
        assignmentElement.elementId = param.getElementid();
        assignmentElement.createdAt = new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6));
        assignmentElement.updatedAt = new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6));

        return assignmentElement;
    }
    override mapTo(param: AssignmentElementEntity): AssignmentElementGrpcModel {
        let assignmentElement = new AssignmentElementGrpcModel();

        assignmentElement.setId(param.id!);
        assignmentElement.setAssignmentid(param.assignmentId!);
        assignmentElement.setType(toEnum(AssignmentTypeGrpc, param.type!)!);
        assignmentElement.setSubtype(toEnum(GroupCategoryTypeGrpc, param.subtype!)!);
        assignmentElement.setElementid(param.elementId!);

        return assignmentElement;
    }

}
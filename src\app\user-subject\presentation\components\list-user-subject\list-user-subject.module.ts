import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListUserSubjectComponent } from './list-user-subject/list-user-subject.component';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { MessagesModule } from 'primeng/messages';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { EditUserSubjectModule } from 'src/verazial-common-frontend/modules/shared/components/edit-user-subject/edit-user-subject.module';
import { PasswordModule } from 'primeng/password';
import { CheckboxModule } from 'primeng/checkbox';
import { BioTechButtonsModule } from 'src/verazial-common-frontend/modules/shared/components/bio-tech-buttons/bio-tech-buttons.module';
import { WidgetSearchModule } from 'src/verazial-common-frontend/modules/shared/components/widget-search/widget-search.module';
import { ToastModule } from 'primeng/toast';
import { DropdownModule } from 'primeng/dropdown';
import { FloatLabelModule } from 'primeng/floatlabel';
import { CalendarModule } from 'primeng/calendar';
import { UserNotVerifiedModule } from 'src/verazial-common-frontend/modules/shared/components/user-not-verified/user-not-verified.module';
import { TooltipModule } from 'primeng/tooltip';
import { TreeSelectModule } from 'primeng/treeselect';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';

@NgModule({
  declarations: [
    ListUserSubjectComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    /* PrimeNG */
    ButtonModule,
    TableModule,
    TagModule,
    MessagesModule,
    DialogModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    PasswordModule,
    CheckboxModule,
    ToastModule,
    DropdownModule,
    FloatLabelModule,
    CalendarModule,
    TooltipModule,
    TreeSelectModule,
    /** Custom components */
    EditUserSubjectModule,
    BioTechButtonsModule,
    WidgetSearchModule,
    UserNotVerifiedModule,
    EmptyModule,
  ],
  exports:[
    ListUserSubjectComponent
  ]
})
export class ListUserSubjectModule { }

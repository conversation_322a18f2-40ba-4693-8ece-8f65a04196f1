import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { GetAllTaskFlowsUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-all-task-flows.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { TaskFlow } from 'src/verazial-common-frontend/core/models/task-flow.interface';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-flows-page',
  templateUrl: './flows-page.component.html',
  styleUrl: './flows-page.component.css'
})
export class FlowsPageComponent implements OnInit, OnDestroy{

  isLoading: boolean = true
  hasFlows: boolean = false;
  showFlows: boolean = false;
  selectedFlow!: TaskFlowEntity | undefined;

  data: TaskFlowEntity[] = [];

  // Access code identifier
  access_identifier: string = AccessIdentifier.FLOW;
  canReadAndWrite: boolean = false;

  constructor(
    private checkPermissions: CheckPermissionsService,
    private getAllTaskFlowsUseCase: GetAllTaskFlowsUseCase,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
    private router: Router, 
    private localStorageService: LocalStorageService,
  ){}

  ngOnDestroy(): void {
  }

  ngOnInit(): void {
    //Load List of flows
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
    this.loadFlowData();
  }

  loadFlowData(){
    let temp: TaskFlow[] = [];
    this.getAllTaskFlowsUseCase.execute().then(
      (data)=>{
        this.data = data
        if(this.data.length > 0){
          this.hasFlows = true;
          this.showFlows = false;
        }else{
          this.hasFlows = false;
        }
        this.isLoading = false;
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_TASK_FLOWS, 0, 'ERROR', '', at_attributes);
      })
  }

  createNewFlow(event: boolean){
    this.data = [];
    this.selectedFlow = undefined;
    this.showFlows = event;
    this.hasFlows = false;
  }

  onReturn(event: boolean){
    this.showFlows = !event;
    this.loadFlowData();
    if(this.data.length > 0){
      this.hasFlows = true;
    }
  }

  onEditFlow(event: TaskFlowEntity){
    this.selectedFlow = event;
    this.hasFlows = false;
    this.showFlows = true;
  }
}

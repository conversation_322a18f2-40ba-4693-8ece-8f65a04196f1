import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild, ViewContainerRef } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, MessageService } from "primeng/api";
import { WidgetResult } from "src/verazial-common-frontend/modules/shared/models/widget-response.model";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { SubjectTabsConfig } from "src/verazial-common-frontend/core/general/manager/common/models/subject-tabs-config.module";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { GetSubjectByNumIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService, ValidTenantResult } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { environment } from "src/environments/environment";
import { UserSubjectCardComponentResult } from "../../components/user-subject-card/user-subject-card/user-subject-card.component";
import { UserSubjectActionType } from "src/verazial-common-frontend/core/models/user-subject-action-type.enum";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";
import { DeleteSubjectByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/delete-subject-by-id.use-case";
import { DeleteUserByIdUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/delete-user-by-id.use-case";
import { GetUserByNumIdUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/get-user-by-num-id.use-case";
import { UpdateUserUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/update-user.use-case";
import { UpdateSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject.use-case";
import { GetAllRolesByUserIdUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/get-all-roles-by-user-id.use-case";
import { UserRoleEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user-role.entity";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { DeleteUserRoleByIdUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/delete-user-role-by-id.use-case";
import { AddUserRolesUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/add-user-roles.use-case";
import { CustomFieldModel } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field.model";
import { CustomFieldTypes } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field-type.enum";
import { DynamicFormAttributes, DynamicFormComponent } from "../../components/dynamic-form/dynamic-form/dynamic-form.component";
import { GetSubjectDetailsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-details-by-subject-id.use-case";
import { SubjectDetailEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-detail.entity";
import { SaveSubjectDetailUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject-detail.use-case";
import { TabViewChangeEvent } from "primeng/tabview";
import { GetStaticResourcesBySubjectIdAndNameUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case";
import { CreateStaticResourceUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case";
import { CreateStaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity";
import { UserDetailEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user-detail.entity";
import { SaveUserDetailUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/save-user-detail.use-case";
import { GetUserDetailsByUserIdUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/get-user-details-by-user-id.use-case";
import { GetAllRolesBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-roles-by-subject-id.use-case";
import { DeleteSubjectRoleByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/delete-subject-role-by-id.use-case";
import { AddSubjectRolesUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/add-subject-roles.use-case";
import { SubjectRoleEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-role.entity";
import { CheckTokenUseCase } from "src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { UnenrollIdentityService } from "src/verazial-common-frontend/core/services/unenroll-identity.service";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { GetAllRolesUseCase } from "src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case";
import { RoleType } from "src/verazial-common-frontend/core/general/role/common/enum/role-type.enum";
import { SaveUserUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/save-user.use-case";
import { SaveSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject.use-case";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetSettingsByApplicationUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { PasswordRecoveryRequestModel } from "src/verazial-common-frontend/core/general/user/domain/entity/password-recovery.entity";
import { Location } from "@angular/common";
import { PasswordRecoveryRequestUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/password-recovery-request.use-case";
import { PrisonsSettingsModel } from "src/verazial-common-frontend/core/general/manager/common/models/prisons-settings.model";
import { TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";

@Component({
  selector: 'app-user-subject-edit-page',
  templateUrl: './user-subject-edit-page.component.html',
  styleUrl: './user-subject-edit-page.component.css',
  providers: [MessageService, ConfirmationService]
})
export class UserSubjectEditPageComponent implements OnInit, OnDestroy {

  @Input() isChild: boolean = false;
  /* User in Session */
  @Input() userIsVerified: boolean = false;
  /* User/Subject */
  @Input() verified: boolean = false;
  @Input() numId: string = '';
  /* Page State */
  isLoading: boolean = false;
  imageUpdating: boolean = false;
  editMode: boolean = false;
  verifyBtnAvailable: boolean = false;
  disabledVerifyBtnToolTip: string = '';
  activeIndex: number = 0;
  subjectTabsConfig?: SubjectTabsConfig = new SubjectTabsConfig();
  prisonsConfig?: PrisonsSettingsModel = new PrisonsSettingsModel();
  allowEditAfterIdSearch: boolean = false;
  allowDeleteAfterIdSearch: boolean = false;
  showEditSubjectDialog: boolean = false;
  modified: boolean = false;
  pictureModified: boolean = false;
  updateUserSubjectActionType: UserSubjectActionType = UserSubjectActionType.UPDATE;
  viewOnlyUserSubjectActionType: UserSubjectActionType = UserSubjectActionType.VIEWONLY;
  userSubjectActionTypes = UserSubjectActionType;
  isDisableSaveButton: boolean = true;
  saveAllowed: boolean = true;
  deleteAllowed: boolean = true;
  newUserSaveAllowed: boolean = true;
  private rejectTimeout: any;
  /* Widget */
  widgetUrl: string = "";
  widgetReady: boolean = false;
  verifyReady: boolean = false;
  /* Settings */
  managerSettings?: GeneralSettings;
  konektorProperties?: KonektorPropertiesEntity;
  /* User-Subject */
  biometricSamples: boolean = false;
  userSubject?: SubjectEntity | UserEntity;
  subjectUserData?: UserEntity = undefined;
  userSubjectData?: SubjectEntity = undefined;
  userSubjectType: string = '';
  profilePicPlaceholder: string = 'verazial-common-frontend/assets/images/all/UserPic.svg';
  picHistoryName: string = 'profile-picture-history';
  userSubjectDataEdit?: SubjectEntity | UserEntity;
  isUser: boolean = false;
  userSubjectDetails: SubjectDetailEntity[] = [];
  updateAttributes: boolean = false;
  /* Access */
  access_identifier: string = "";
  canReadAndWrite: boolean = false;
  canReadAndWriteOther: boolean = false;
  /* Subject Details Form */
  @ViewChild('dynamicFormContent', { read: ViewContainerRef, static: true }) dynamicFormContent: ViewContainerRef;
  formAttributes: DynamicFormAttributes[] = [];
  showDynamicForm: boolean = false;
  componentRef: any;
  /* New User From Subject */
  showNewUserDialog: boolean = false;
  newUserData: UserEntity | undefined;
  roles: RoleEntity[] = [];
  listOfRoles: RoleEntity[] = [];
  currentUserRole: RoleEntity | undefined;
  newUserReadyToSave: boolean = false;
  newUserForm: FormGroup = this.fb.group({
    email: ['', [Validators.required, Validators.email]],
    roles: [[], Validators.required],
  });
  /* New Subject From User */

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private loggerService: ConsoleLoggerService,
    private localStorageService: LocalStorageService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private auditTrailService: AuditTrailService,
    private checkPermissions: CheckPermissionsService,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
    private getUserByNumIdUseCase: GetUserByNumIdUseCase,
    private deleteSubjectByIdUseCase: DeleteSubjectByIdUseCase,
    private deleteUserByIdUseCase: DeleteUserByIdUseCase,
    private unenrollIdentityService: UnenrollIdentityService,
    private updateSubjectUseCase: UpdateSubjectUseCase,
    private updateUserUseCase: UpdateUserUseCase,
    private getAllRolesBySubjectIdUseCase: GetAllRolesBySubjectIdUseCase,
    private getAllRolesByUserIdUseCase: GetAllRolesByUserIdUseCase,
    private deleteSubjectRoleByIdUseCase: DeleteSubjectRoleByIdUseCase,
    private deleteUserRoleByIdUseCase: DeleteUserRoleByIdUseCase,
    private addSubjectRolesUseCase: AddSubjectRolesUseCase,
    private addUserRolesUseCase: AddUserRolesUseCase,
    private getSubjectDetailsBySubjectIdUseCase: GetSubjectDetailsBySubjectIdUseCase,
    private getUserDetailsByUserIdUseCase: GetUserDetailsByUserIdUseCase,
    private saveSubjectDetailUseCase: SaveSubjectDetailUseCase,
    private saveUserDetailUseCase: SaveUserDetailUseCase,
    private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
    private createStaticResourceUseCase: CreateStaticResourceUseCase,
    private checkTokenUseCase: CheckTokenUseCase,
    private fb: FormBuilder,
    private validatorService: ValidatorService,
    private getAllRolesUseCase: GetAllRolesUseCase,
    private saveUserUseCase: SaveUserUseCase,
    private saveSubjectUseCase: SaveSubjectUseCase,
    private passwordRecoveryRequestUseCase: PasswordRecoveryRequestUseCase,
  ) {
    this.dynamicFormContent = {} as ViewContainerRef;
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /* Component Functions */

  async ngOnInit() {
    this.isLoading = true;
    if (!this.isChild) {
      if (this.localStorageService.isUserVerified()) {
        await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
          (response) => {
            this.userIsVerified = !response;
          },
          (e) => {
            this.userIsVerified = false;
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.CHECK_TOKEN, 0, 'ERROR', '', at_attributes);
          }
        );
      }
      else {
        this.userIsVerified = false;
      }
      this.route.params.subscribe(params => {
        this.isLoading = true;
        this.verified = false;
        if (history.state.verified != undefined) {
          this.loggerService.debug(history.state.verified)
          this.verified = history.state.verified
          if (this.verified) {
            this.enterEditMode();
          }
        }
        this.userSubjectType = params['userSubjectType'];
        switch (this.userSubjectType) {
          case 'subject':
            this.isUser = false;
            this.access_identifier = AccessIdentifier.SUBJECT;
            this.canReadAndWriteOther = this.checkPermissions.hasReadAndWritePermissions(AccessIdentifier.USER);
            break;
          case 'user':
            this.isUser = true;
            this.access_identifier = AccessIdentifier.USER;
            this.canReadAndWriteOther = this.checkPermissions.hasReadAndWritePermissions(AccessIdentifier.SUBJECT);
            break;
        }
        this.currentUserRole = this.localStorageService.getRole() as RoleEntity;
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
        this.numId = params['numId'];
        this.loggerService.debug(this.numId)
        this.getUserSubject(this.numId);
        this.getAllRoles();
      });
    }
    else {
      if (this.verified) {
        this.enterEditMode();
      }
      this.isUser = false;
      this.access_identifier = AccessIdentifier.SUBJECT;
      this.canReadAndWriteOther = this.checkPermissions.hasReadAndWritePermissions(AccessIdentifier.USER);
      this.currentUserRole = this.localStorageService.getRole() as RoleEntity;
      this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
      this.loggerService.debug(this.numId)
      this.getUserSubject(this.numId);
      this.getAllRoles();
    }
  }

  handleBeforeUnload(event: Event) {
    this.ngOnDestroy();
  }

  ngOnDestroy(): void {
    this.verifyReady = false;
    this.widgetReady = false;
    this.updateModified(false);
    // Clean up the timeout if the component is destroyed
    this.clearRejectTimeout();
  }

  /* User Subject Functions */

  getUserSubject(numId: string) {
    if (this.isUser) {
      this.loggerService.debug(numId);
      this.getUserByNumIdUseCase.execute({ numId: numId }).then(
        (data) => {
          if (data) {
            this.loggerService.debug('User Data Retrieved Successfully:');
            this.loggerService.debug(data);
            this.userSubject = data;
            this.numId = numId;
            this.subjectUserData = data;
            const userRole = this.localStorageService.getRole();
            if (this.subjectUserData.roles?.length! > 0) {
              const higherRoles = this.subjectUserData.roles?.filter(v => v.level! < userRole!.level!);
              if (higherRoles?.length! > 0) {
                this.canReadAndWrite = false;
              }
            }
            this.getSubjectByNumIdUseCase.execute({ numId: numId })
              .then(
                (data) => {
                  if (data) {
                    this.loggerService.debug('User Subject Data Retrieved Successfully:');
                    this.loggerService.debug(data);
                    this.userSubjectData = data;
                    if (this.userSubjectData.showPic && !this.userSubject?.showPic) {
                      if (this.userSubject && (this.userSubject.pic == null || this.userSubject.pic == undefined || this.userSubject.pic == '')) {
                        this.userSubject.pic = this.userSubjectData.pic;
                      }
                    }
                  }
                },
                (e) => {
                  this.loggerService.error('Error Retrieving Subject Data:');
                  this.loggerService.error(e);
                  const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.SUBJECT_NUM_ID, value: numId }
                  ];
                  this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_NUM_ID, 0, 'ERROR', '', at_attributes);
                },
              )
              .finally(() => {
                const managerSettings = this.localStorageService.getSessionSettings();
                const apiDataSources = managerSettings?.userSubjectApiExternalConnections?.map(v => {
                  return { key: v.connectionId, value: v.connectionName, data: v }
                });
                const ldapDataSources = managerSettings?.userSubjectLdapConnections?.map(v => {
                  return { key: v.connectionId, value: v.connectionName, data: v }
                });
                const dataSources = [ ... apiDataSources!, ... ldapDataSources!];
                if (dataSources.length > 0 && this.userSubject?.datasource != '' && this.userSubject?.datasource != undefined && this.userSubject?.datasource != null) {
                  const userSubjectDataSource = dataSources.find(v => v.key == this.userSubject?.datasource);
                  this.newUserSaveAllowed = userSubjectDataSource?.data.allowCreating ?? true;
                }
                else {
                  this.newUserSaveAllowed = true;
                }
                this.getManagerSettings();
              });;
          }
        },
        (e) => {
          this.loggerService.error('Error Retrieving User Data:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.USER_NUM_ID, value: numId }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_USER_BY_NUM_ID, 0, 'ERROR', '', at_attributes);
          if (e.code == 404) {
            this.router.navigate(['/error/404']);
          }
          else {
            this.navigateToErrorPage('timesError', 'error_general', e.code.toString(), false);
          }
        },
      );
    }
    else {
      this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
        (data) => {
          if (data) {
            this.loggerService.debug('Subject Data Retrieved Successfully:');
            this.loggerService.debug(data);
            this.userSubject = data;
            this.numId = numId;
            this.userSubjectData = data;
            this.getUserByNumIdUseCase.execute({ numId: numId })
              .then(
                (data) => {
                  if (data) {
                    this.loggerService.debug('Subject User Data Retrieved Successfully:');
                    this.loggerService.debug(data);
                    this.subjectUserData = data;
                    const userRole = this.localStorageService.getRole();
                    if (this.subjectUserData.roles?.length! > 0) {
                      const higherRoles = this.subjectUserData.roles?.filter(v => v.level! < userRole!.level!);
                      if (higherRoles?.length! > 0) {
                        this.canReadAndWrite = false;
                      }
                    }
                  }
                },
                (e) => {
                  this.subjectUserData = undefined;
                  this.loggerService.error('Error Retrieving User Data:');
                  this.loggerService.error(e);
                  const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.USER_NUM_ID, value: numId }
                  ];
                  this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_USER_BY_NUM_ID, 0, 'ERROR', '', at_attributes);
                },
              )
              .finally(() => {
                const managerSettings = this.localStorageService.getSessionSettings();
                const apiDataSources = managerSettings?.userSubjectApiExternalConnections?.map(v => {
                  return { key: v.connectionId, value: v.connectionName, data: v }
                });
                const ldapDataSources = managerSettings?.userSubjectLdapConnections?.map(v => {
                  return { key: v.connectionId, value: v.connectionName, data: v }
                });
                const dataSources = [ ... apiDataSources!, ... ldapDataSources!];
                if (dataSources.length > 0 && this.userSubject?.datasource != '' && this.userSubject?.datasource != undefined && this.userSubject?.datasource != null) {
                  const userSubjectDataSource = dataSources.find(v => v.key == this.userSubject?.datasource);
                  this.newUserSaveAllowed = userSubjectDataSource?.data.allowCreating ?? true;
                }
                else {
                  this.newUserSaveAllowed = true;
                }
                this.getManagerSettings();
              });;
          }
        },
        (e) => {
          this.loggerService.error('Error Retrieving Subject Data:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.SUBJECT_NUM_ID, value: numId }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_NUM_ID, 0, 'ERROR', '', at_attributes);
          if (e.code == 404) {
            this.router.navigate(['/error/404']);
          }
          else {
            this.navigateToErrorPage('timesError', 'error_general', e.code.toString(), false);
          }
        },
      )
    }
  }

  enterEditMode() {
    this.editMode = !this.editMode;
    this.toggleDynamicForm();
  }

  openEditSubjectDialog() {
    this.userSubjectDataEdit = { ...this.userSubject } as SubjectEntity | UserEntity;
    this.showEditSubjectDialog = true;
  }

  onEditUserSubject(data: SubjectEntity | UserEntity) {
    const formData = data as any;
    const userSubjectOriginal = this.userSubject as any;
    Object.keys(formData).forEach((key: string) => {
      const formValue = formData[key];
      const originalValue = userSubjectOriginal[key];
      // Check if both values are arrays
      if (Array.isArray(formValue) && Array.isArray(originalValue)) {
        if (JSON.stringify(formValue) !== JSON.stringify(originalValue)) {
          if (this.userIsVerified) {
            this.updateModified(true);
          }
        }
      } else if (formValue != originalValue) {
        if (this.userIsVerified) {
          this.updateModified(true);
        }
      }
    });
    if (this.modified) {
      this.userSubjectDataEdit = data;
    }
    if (data.numId && data.names && data.lastNames) {
      if (this.isUser) {
        if (data.email && data.roles && data.roles.length > 0) {
          this.isDisableSaveButton = false;
        }
        else {
          this.isDisableSaveButton = true;
        }
      }
      else {
        this.isDisableSaveButton = false;
      }
    }
    else {
      this.isDisableSaveButton = true;
    }
  }

  onEditUserSubjectPicture(pic: string) {
    if (this.userSubject?.pic != pic) {
      this.updateModified(true);
      this.pictureModified = true;
      this.onEditUserSubject(this.userSubjectDataEdit!);
    }
  }

  allowSave(allow: boolean) {
      this.saveAllowed = allow;
  }

  allowDelete(allow: boolean) {
      this.deleteAllowed = allow;
  }

  onSubmitEditSubject() {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.userSubjectDataEdit) }
    ];
    this.auditTrailService.auditTrailSelectReason(this.isUser ? ReasonTypeEnum.USER : ReasonTypeEnum.SUBJECT, this.isUser ? AuditTrailActions.MOD_USR : AuditTrailActions.MOD_SUB, ReasonActionTypeEnum.UPDATE, () => { this.updateUserSubject(); }, at_attributes, false);
  }

  updateUserSubject() {
    this.isLoading = true;
    this.userSubjectDataEdit!.showPic = this.userSubjectDataEdit?.pic != null && this.userSubjectDataEdit?.pic != undefined && this.userSubjectDataEdit?.pic != "" && this.userSubjectDataEdit?.pic != this.profilePicPlaceholder;
    const userSubject = this.userSubjectDataEdit;
    if (userSubject) {
      if (!this.isUser) {
        const systemUserRole = this.listOfRoles.find(v => v.name == 'SYSTEM_USER');
        const hasToHaveSystemUserRole: boolean = this.subjectUserData != undefined;

        if (hasToHaveSystemUserRole && systemUserRole != undefined) {
          if (userSubject.roles?.find(v => v.id == systemUserRole.id) == undefined) {
            userSubject.roles?.push(systemUserRole);
          }
        }
      }
      this.updateUserSubjectRoles(userSubject);
      if (this.isUser) {
        let updateSubject = userSubject.showPic != this.userSubjectData?.showPic;
        this.updateUserUseCase.execute({ user: userSubject }).then(
          (response) => {
            if (response) {
              this.userSubject = this.userSubjectDataEdit;
              if (userSubject.password != null && userSubject.password != undefined && userSubject.password != '') {
                const at_attributes = [
                  { name: AuditTrailFields.USER_NUM_ID, value: userSubject.numId },
                  { name: AuditTrailFields.USER_ID, value: userSubject.id },
                  { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(userSubject.roles) }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_PASS, 0, 'SUCCESS', '', at_attributes);
              }
              const password = userSubject.password;
              userSubject.password = '';
              const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
                { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) },
                { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_USR, 0, 'ERROR', '', at_attributes);
              userSubject.password = password;
              if (this.pictureModified) {
                this.pictureModified = false;
                this.updateUserSubjectPicHistory(this.userSubjectData!, userSubject?.pic!);
                if (updateSubject) {
                  this.userSubjectData!.showPic = userSubject.showPic;
                  const subjectUpdate = { ...this.userSubjectData! };
                  this.updateSubject(subjectUpdate);
                }
              }
              else {
                this.messageService.add({
                  severity: 'success',
                  summary: this.translateService.instant('content.successTitle'),
                  detail: this.translateService.instant('messages.success_general'),
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              }
            }
          },
          (e) => {
            this.loggerService.error('Error Updating User Data:');
            this.loggerService.error(e);
            if (userSubject.password != null && userSubject.password != undefined && userSubject.password != '') {
              const at_attributes = [
                { name: AuditTrailFields.USER_NUM_ID, value: userSubject.numId },
                { name: AuditTrailFields.USER_ID, value: userSubject.id },
                { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(userSubject.roles) }
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_PASS, 0, 'ERROR', '', at_attributes);
            }
            const password = userSubject.password;
            userSubject.password = '';
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) }
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_USR, 0, 'ERROR', '', at_attributes);
            userSubject.password = password;
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant('content.errorTitle'),
              detail: `${this.translateService.instant('messages.error_updating_user')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        )
        .finally(() => {
          this.showEditSubjectDialog = false;
          this.updateModified(false);
          this.isLoading = false;
          this.updateAttributes = true;
          this.widgetReady = false;
          if(!this.widgetReady){
            setTimeout(() => this.widgetReady = true, 0);
          }
        });
      }
      else {
        let updateUser = userSubject.showPic != this.subjectUserData?.showPic;
        this.updateSubjectUseCase.execute({ subject: userSubject }).then(
          (response) => {
            if (response) {
              this.userSubject = this.userSubjectDataEdit;
              if (this.pictureModified) {
                this.pictureModified = false;
                this.updateUserSubjectPicHistory(this.userSubject!, userSubject?.pic!);
                if (updateUser) {
                  this.subjectUserData!.showPic = userSubject.showPic;
                  const userUpdate = { ...this.subjectUserData! };
                  this.updateUser(userUpdate);
                }
              }
              else {
                this.messageService.add({
                  severity: 'success',
                  summary: this.translateService.instant('content.successTitle'),
                  detail: this.translateService.instant('messages.success_general'),
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              }
            }
          },
          (e) => {
            this.loggerService.error('Error Updating Subject Data:');
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) }
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'ERROR', '', at_attributes);
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant('content.errorTitle'),
              detail: `${this.translateService.instant('messages.error_updating_subject')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        )
        .finally(() => {
          this.showEditSubjectDialog = false;
          this.updateModified(false);
          this.isLoading = false;
          this.updateAttributes = true;
          this.widgetReady = false;
          if(!this.widgetReady){
            setTimeout(() => this.widgetReady = true, 0);
          }
        });
      }
    }
  }

  updateUser(user: UserEntity) {
    user.password = '';
    this.isLoading = true;
    this.updateUserUseCase.execute({ user: user }).then(
      (response) => {
        if (response) {
          const password = user.password;
          user.password = '';
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubjectData) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(user) }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_USR, 0, 'SUCCESS', '', at_attributes);
          user.password = password;
          this.userSubjectData = { ...user };
        }
      },
      (e) => {
        this.loggerService.error('Error Updating User Data:');
        this.loggerService.error(e);
        const password = user.password;
        user.password = '';
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubjectData) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(user) }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_USR, 0, 'ERROR', '', at_attributes);
        user.password = password;
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant('content.errorTitle'),
          detail: `${this.translateService.instant('messages.error_updating_user')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    )
    .finally(() => {
      this.isLoading = false;
      this.updateAttributes = true;
      this.widgetReady = false;
      if(!this.widgetReady){
        setTimeout(() => this.widgetReady = true, 0);
      }
    });
  }

  updateSubject(subject: SubjectEntity) {
    this.isLoading = true;
    this.updateSubjectUseCase.execute({ subject: subject }).then(
      (response) => {
        if (response) {
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubjectData) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(subject) }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'SUCCESS', '', at_attributes);
          this.userSubjectData = { ...subject };
        }
      },
      (e) => {
        this.loggerService.error('Error Updating Subject Data:');
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubjectData) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(subject) }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'ERROR', '', at_attributes);
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant('content.errorTitle'),
          detail: `${this.translateService.instant('messages.error_updating_subject')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    )
    .finally(() => {
      this.isLoading = false;
      this.updateAttributes = true;
      this.widgetReady = false;
      if(!this.widgetReady){
        setTimeout(() => this.widgetReady = true, 0);
      }
    });
  }

  updateUserSubjectRoles(userSubject: SubjectEntity | UserEntity) {
    let newRoleIds: number[] = [];
    userSubject.roles?.forEach((role: RoleEntity) => {
      newRoleIds.push(role.id!);
    });
    if (newRoleIds.length > 0) {
      if (this.isUser) {
        this.getAllRolesByUserIdUseCase.execute({ userId: userSubject.id! }).then(
          (data: UserRoleEntity[]) => {
            if (data.length > 0) {
              // this.loggerService.debug("Current Roles:");
              // this.loggerService.debug(data);
              let currentRoleIds: number[] = [];
              data.forEach((role: UserRoleEntity) => {
                currentRoleIds.push(role.roleId!);
              });
              // this.loggerService.debug("Current Role IDs:");
              // this.loggerService.debug(currentRoleIds);
              // this.loggerService.debug("New Role IDs:");
              // this.loggerService.debug(newRoleIds);

              // Sort arrays and compare
              const sortedArr1 = [...currentRoleIds].sort((a, b) => a - b);
              const sortedArr2 = [...newRoleIds].sort((a, b) => a - b);

              const areEqual = sortedArr1.every((value, index) => value === sortedArr2[index]);
              if (!areEqual) {
                // const removed = currentRoleIds.filter(value => !newRoleIds.includes(value));
                const rolesToRemove = sortedArr1.filter(role => !sortedArr2.includes(role));
                if (rolesToRemove.length > 0) {
                  let rolesToRemove: UserRoleEntity[] = [];
                  data.forEach((role: UserRoleEntity) => {
                    if (rolesToRemove.includes(role)) {
                      rolesToRemove.push(role);
                    }
                  });
                  // this.loggerService.debug('Roles to Remove Request:');
                  // this.loggerService.debug(rolesToRemove);
                  this.deleteUserRoleByIdUseCase.execute({ listUserRoles: rolesToRemove }).then(
                    (response) => {
                      if (response) {
                        this.loggerService.debug('Roles Deleted Successfully:');
                        this.loggerService.debug(response);
                        const at_attributes: ExtraData[] = [
                          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(rolesToRemove) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_USER_ROLES, 0, 'SUCCESS', '', at_attributes);
                      }
                    },
                    (e) => {
                      this.loggerService.error('Error Deleting Roles:');
                      this.loggerService.error(e);
                      const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(rolesToRemove) },
                      ];
                      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_USER_ROLES, 0, 'ERROR', '', at_attributes);
                      this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_deleting_roles')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                      });
                    }
                  );
                }
                // const added = newRoleIds.filter(value => !currentRoleIds.includes(value));
                const newRoles = sortedArr2.filter(role => !sortedArr1.includes(role));
                if (newRoles.length > 0) {
                  let rolesToAdd: UserRoleEntity[] = [];
                  newRoles.forEach((role: number) => {
                    rolesToAdd.push({ userId: userSubject.id!, roleId: role } as UserRoleEntity);
                  });
                  // this.loggerService.debug('Roles to Add Request:');
                  // this.loggerService.debug(rolesToAdd);
                  this.addUserRolesUseCase.execute({ listUserRoles: rolesToAdd }).then(
                    (response) => {
                      if (response) {
                        this.loggerService.debug('Roles Added Successfully:');
                        this.loggerService.debug(response);
                        const at_attributes: ExtraData[] = [
                          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USER_ROLES, 0, 'SUCCESS', '', at_attributes);
                      }
                    },
                    (e) => {
                      this.loggerService.error('Error Adding Roles:');
                      this.loggerService.error(e);
                      const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                      ];
                      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USER_ROLES, 0, 'ERROR', '', at_attributes);
                      this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_adding_roles')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                      });
                    }
                  );
                }
              }
            }
            else {
              // this.loggerService.debug("No Roles Found for the user");
              // this.loggerService.debug("New Role IDs:");
              // this.loggerService.debug(newRoleIds);
              let rolesToAdd: UserRoleEntity[] = [];
              newRoleIds.forEach((role: number) => {
                rolesToAdd.push({ userId: userSubject.id!, roleId: role } as UserRoleEntity);
              });
              // this.loggerService.debug('Roles to Add Request:');
              // this.loggerService.debug(rolesToAdd);
              this.addUserRolesUseCase.execute({ listUserRoles: rolesToAdd }).then(
                (response) => {
                  if (response) {
                    this.loggerService.debug('Roles Added Successfully:');
                    this.loggerService.debug(response);
                    const at_attributes: ExtraData[] = [
                      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USER_ROLES, 0, 'SUCCESS', '', at_attributes);
                  }
                },
                (e) => {
                  this.loggerService.error('Error Adding Roles:');
                  this.loggerService.error(e);
                  const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                  ];
                  this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USER_ROLES, 0, 'ERROR', '', at_attributes);
                  this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_adding_roles')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                  });
                }
              );
            }
          },
          (e) => {
            this.loggerService.error('Error Retrieving Roles:');
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.USER_ID, value: userSubject.id }
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES_BY_USER_ID, 0, 'ERROR', '', at_attributes);
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant('content.errorTitle'),
              detail: `${this.translateService.instant('messages.error_retrieving_roles')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        );
      }
      else {
        this.getAllRolesBySubjectIdUseCase.execute({ subjectId: userSubject.id! }).then(
          (data: SubjectRoleEntity[]) => {
            if (data.length > 0) {
              // this.loggerService.debug("Current Profiles:");
              // this.loggerService.debug(data);
              let currentRoleIds: number[] = [];
              data.forEach((role: SubjectRoleEntity) => {
                currentRoleIds.push(role.roleId!);
              });
              // this.loggerService.debug("Current Profile IDs:");
              // this.loggerService.debug(currentRoleIds);
              // this.loggerService.debug("New Profile IDs:");
              // this.loggerService.debug(newRoleIds);

              let same = (currentRoleIds.length !== newRoleIds.length);

              // Sort arrays and compare
              const sortedArr1 = [...currentRoleIds].sort((a, b) => a - b);
              const sortedArr2 = [...newRoleIds].sort((a, b) => a - b);

              same = sortedArr1.every((value, index) => value === sortedArr2[index]);
              if (!same) {
                const removed = currentRoleIds.filter(value => !newRoleIds.includes(value));
                if (removed.length > 0) {
                  let rolesToRemove: SubjectRoleEntity[] = [];
                  data.forEach((role: SubjectRoleEntity) => {
                    if (removed.includes(role.roleId!)) {
                      rolesToRemove.push(role);
                    }
                  });
                  // this.loggerService.debug('Profiles to Remove Request:');
                  // this.loggerService.debug(rolesToRemove);
                  this.deleteSubjectRoleByIdUseCase.execute({ listSubjectRoles: rolesToRemove }).then(
                    (response) => {
                      if (response) {
                        this.loggerService.debug('Profiles Deleted Successfully:');
                        this.loggerService.debug(response);
                        const at_attributes: ExtraData[] = [
                          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(rolesToRemove) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_SUBJECT_ROLES, 0, 'SUCCESS', '', at_attributes);
                      }
                    },
                    (e) => {
                      this.loggerService.error('Error Deleting Profiles:');
                      this.loggerService.error(e);
                      const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(rolesToRemove) },
                      ];
                      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_SUBJECT_ROLES, 0, 'ERROR', '', at_attributes);
                      this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_deleting_profiles')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                      });
                    }
                  );
                }
                const added = newRoleIds.filter(value => !currentRoleIds.includes(value));
                if (added.length > 0) {
                  let rolesToAdd: SubjectRoleEntity[] = [];
                  added.forEach((role: number) => {
                    rolesToAdd.push({ subjectId: userSubject.id!, roleId: role } as SubjectRoleEntity);
                  });
                  // this.loggerService.debug('Profiles to Add Request:');
                  // this.loggerService.debug(rolesToAdd);
                  this.addSubjectRolesUseCase.execute({ listSubjectRoles: rolesToAdd }).then(
                    (response) => {
                      if (response) {
                        this.loggerService.debug('Profiles Added Successfully:');
                        this.loggerService.debug(response);
                        const at_attributes: ExtraData[] = [
                          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'SUCCESS', '', at_attributes);
                      }
                    },
                    (e) => {
                      this.loggerService.error('Error Adding Profiles:');
                      this.loggerService.error(e);
                      const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                      ];
                      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'ERROR', '', at_attributes);
                      this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_adding_profiles')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                      });
                    }
                  );
                }
              }
            }
            else {
              // this.loggerService.debug("No Profiles Found for the subject");
              // this.loggerService.debug("New Profile IDs:");
              // this.loggerService.debug(newRoleIds);
              let rolesToAdd: SubjectRoleEntity[] = [];
              newRoleIds.forEach((role: number) => {
                rolesToAdd.push({ subjectId: userSubject.id!, roleId: role } as SubjectRoleEntity);
              });
              // this.loggerService.debug('Profiles to Add Request:');
              // this.loggerService.debug(rolesToAdd);
              this.addSubjectRolesUseCase.execute({ listSubjectRoles: rolesToAdd }).then(
                (response) => {
                  if (response) {
                    this.loggerService.debug('Profiles Added Successfully:');
                    this.loggerService.debug(response);
                    const at_attributes: ExtraData[] = [
                      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'SUCCESS', '', at_attributes);
                  }
                },
                (e) => {
                  this.loggerService.error('Error Adding Profiles:');
                  this.loggerService.error(e);
                  const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                  ];
                  this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'ERROR', '', at_attributes);
                  this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_adding_profiles')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                  });
                }
              );
            }
          },
          (e) => {
            this.loggerService.error('Error Retrieving Roles:');
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.SUBJECT_ID, value: userSubject.id }
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant('content.errorTitle'),
              detail: `${this.translateService.instant('messages.error_retrieving_profiles')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        );
      }
    }
  }

  updateUserSubjectPicHistory(userSubject: SubjectEntity, pic: string) {
    this.isLoading = true;
    if (pic != null && pic != undefined && pic != "" && pic != this.profilePicPlaceholder) {
      this.imageUpdating = true;
      this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: userSubject?.id!, name: this.picHistoryName }).then(
        (data) => {
          if (data) {
            let newStaticResource = new CreateStaticResourceEntity();
            // subjectId?: string;
            newStaticResource.subjectId = userSubject?.id!;
            // name?: string;
            newStaticResource.name = this.picHistoryName;
            // number?: number;
            const number = data.length;
            newStaticResource.number = number;
            // content?: string;
            newStaticResource.content = pic.replace('data:image/jpeg;base64,', '');
            // async?: boolean;
            newStaticResource.async = false;
            this.createStaticResourceUseCase.execute({ createStaticResourceRequest: newStaticResource }).then(
              () => {
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                  { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                  { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                this.messageService.add({
                  severity: 'success',
                  summary: this.translateService.instant('content.successTitle'),
                  detail: this.translateService.instant('messages.success_general'),
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.isLoading = false;
                this.imageUpdating = false;
              },
              (e) => {
                this.isLoading = false;
                this.imageUpdating = false;
                this.loggerService.error('Error Creating User/Subject Static Resource:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                  { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                  { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                  severity: 'error',
                  summary: this.translateService.instant('content.errorTitle'),
                  detail: `${this.translateService.instant('messages.error_uploading_image')}: ${e.message}`,
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              }
            );
          }
          else {
            this.isLoading = false;
            this.imageUpdating = false;
          }
        },
        (e) => {
          this.isLoading = false;
          this.imageUpdating = false;
          this.loggerService.error('Error Getting User/Subject Static Resources:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.SUBJECT_ID, value: userSubject?.id! },
            { name: AuditTrailFields.RECORD_NAME, value: this.picHistoryName }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('messages.error_downloading_image')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      );
    }
    else {
      this.isLoading = false;
    }
  }

  closeEditSubjectDialog() {
    if (this.modified && this.canReadAndWrite) {
      this.confirmationService.confirm({
        message: this.translateService.instant('messages.exitingWithoutSaving'),
        header: this.translateService.instant('titles.exitingWithoutSaving'),
        icon: 'pi pi-exclamation-triangle',
        acceptIcon: "none",
        acceptLabel: this.translateService.instant('options.true'),
        rejectIcon: "none",
        rejectLabel: this.translateService.instant('options.false'),
        rejectButtonStyleClass: "p-button-text",
        accept: () => {
          this.updateModified(false);
          this.showEditSubjectDialog = false;
          this.userSubjectDataEdit = { ...this.userSubject } as SubjectEntity | UserEntity;
        },
        reject: () => {
          this.clearRejectTimeout();
        }
      });

      // Set a timeout to automatically trigger the reject action after 10 seconds
      this.rejectTimeout = setTimeout(() => {
        this.confirmationService.close(); // Close the dialog
      }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
    }
    else {
      this.updateModified(false);
      this.userSubjectDataEdit = { ...this.userSubject } as SubjectEntity | UserEntity;
      this.showEditSubjectDialog = false;
    }
  }

  // Delete Confirmation Dialogs
  confirmDelete(userSubject: SubjectEntity | UserEntity) {
    if (!this.userIsVerified || (!this.isUser && this.subjectUserData != undefined) || !this.deleteAllowed) {
      return;
    }
    this.confirmationService.confirm({
      message: `${this.translateService.instant('messages.delete_single_record')} <b>${userSubject?.numId}</b>?`,
      header: this.translateService.instant('messages.delete_confirmation_header'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptIcon: "none",
      rejectIcon: "none",
      acceptLabel: this.translateService.instant("delete"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) },
          { name: this.isUser ? AuditTrailFields.USER_NUM_ID : AuditTrailFields.SUBJECT_NUM_ID, value: userSubject.numId },
          { name: this.isUser ? AuditTrailFields.USER_ROLES : AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(userSubject.roles) },
        ];
        this.auditTrailService.auditTrailSelectReason(this.isUser ? ReasonTypeEnum.USER : ReasonTypeEnum.SUBJECT, this.isUser ? AuditTrailActions.DEL_USR : AuditTrailActions.DEL_SUB, ReasonActionTypeEnum.DELETE, () => { this.unenrollIdentity(userSubject); }, at_attributes);
      },
      reject: () => {
        this.clearRejectTimeout();
      }
    });

    // Set a timeout to automatically trigger the reject action after 10 seconds
    this.rejectTimeout = setTimeout(() => {
      this.confirmationService.close(); // Close the dialog
    }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
  }

  // Delete user/subject from the DB
  onDeleteUserSubject(userSubject: SubjectEntity | UserEntity) {
    let success = false;
    if (this.isUser) {
      this.deleteUserByIdUseCase.execute({ id: userSubject.id! }).then(
        (data) => {
          success = true;
        },
        (e) => {
          this.loggerService.error('Error Retrieving User Data:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) },
            { name: AuditTrailFields.USER_NUM_ID, value: userSubject.numId },
            { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(userSubject.roles) },
            { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_USR, 0, 'ERROR', '', at_attributes);
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('messages.error_deleting_user')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      )
      .finally(() => {
        if (success) {
          // this.unenrollIdentity(userSubject);
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.success_general'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.updateModified(false);
          this.router.navigate(['/general', this.isUser ? 'user' : 'subject']);
        }
      });
    }
    else {
      this.deleteSubjectByIdUseCase.execute({ id: userSubject.id! }).then(
        (data) => {
          success = true;
        },
        (e) => {
          this.loggerService.error('Error Retrieving Subject Data:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) },
            { name: AuditTrailFields.SUBJECT_NUM_ID, value: userSubject.numId },
            { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(userSubject.roles) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_SUB, 0, 'ERROR', '', at_attributes);
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('messages.error_deleting_subject')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      )
      .finally(() => {
        if (success) {
          // this.unenrollIdentity(userSubject);
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.success_general'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.updateModified(false);
          this.router.navigate(['/general', this.isUser ? 'user' : 'subject']);
        }
      });
    }
  }

  async unenrollIdentity(userSubject: UserEntity | SubjectEntity) {
    if(this.isUser) {
      this.onDeleteUserSubject(userSubject);
    }
    else {
      await this.unenrollIdentityService.unenrollUserSubject(userSubject, this.isUser);
      this.onDeleteUserSubject(userSubject);
    }
  }

  /* Password Recovery */
  sendPasswordRecovery(isNewUser: boolean = false) {
    this.isLoading = true;
    const email = this.isUser ? this.userSubject?.email : this.subjectUserData?.email;
    const request: PasswordRecoveryRequestModel = {
        email: email,
        url: window.location.origin + this.location.prepareExternalUrl(''),
        isNewUser: isNewUser,
    };
    this.passwordRecoveryRequestUseCase.execute({ request: request }).then(
        (data) => {
            this.loggerService.debug(data);
            const at_attributes = [
                { name: AuditTrailFields.USER_EMAIL, value: email },
                { name: AuditTrailFields.USER_NUM_ID, value: this.userSubject?.numId },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.PW_RECOVERY_REQUEST, 0, 'SUCCESS', '', at_attributes);
            this.messageService.add(
              {
                severity: 'success',
                summary: this.translateService.instant("content.successTitle"),
                detail: `${this.translateService.instant("messages.password_reset_email_sent")}`,
                life: this.managerSettings?.timeoutMessages ? this.managerSettings.timeoutMessages * 1000 : 5000
              },
            );
        },
        (e) => {
            this.loggerService.error(e);
            const at_attributes = [
                { name: AuditTrailFields.USER_EMAIL, value: email },
                { name: AuditTrailFields.USER_NUM_ID, value: this.userSubject?.numId },
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.PW_RECOVERY_REQUEST, 0, 'ERROR', e.message, at_attributes);
            this.messageService.add(
              {
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: `${this.translateService.instant("messages.error_resetting_password")}`
              },
            );
        }
    )
    .finally(() => {
        this.isLoading = false;
    });
  }

  /* New User From Subject Functions */

  openNewUserDialog() {
    this.newUserForm.get('email')?.setValue(this.userSubject?.email);
    this.showNewUserDialog = true;
  }

  onSubmitNewUser() {
    if (this.newUserData) {
      // this.loggerService.debug(this.newUserData);
      this.saveUserUseCase.execute({ user: this.newUserData }).then(
        (data) => {
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
            { name: AuditTrailFields.USER_NUM_ID, value: data.numId },
            { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(data.roles) }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USR, 0, 'SUCCESS', '', at_attributes);
          this.subjectUserData = data;
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.success_general'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.sendPasswordRecovery(true);
        },
        (e) => {
          this.loggerService.error(e);
          const password = this.newUserData?.password;
          if (this.newUserData) this.newUserData.password = '';
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
            { name: AuditTrailFields.USER_NUM_ID, value: this.userSubject?.numId },
            { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(this.userSubject?.roles) }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USR, 0, 'ERROR', '', at_attributes);
          if (this.newUserData) this.newUserData.password = password;
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('messages.error_creating_user')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      )
      .finally(() => {
        if (this.userSubject?.email != this.newUserData?.email) {
          this.userSubjectDataEdit = this.userSubject!;
          this.userSubjectDataEdit.email = this.newUserData?.email;
          this.userSubjectDataEdit.roles?.push(this.listOfRoles.find((r) => r.name == 'SYSTEM_USER')!);
          this.updateUserSubject();
        }
        else {
          this.updateAttributes = true;
          this.widgetReady = false;
          if(!this.widgetReady){
            setTimeout(() => this.widgetReady = true, 0);
          }
        }
        this.showNewUserDialog = false;
      });
    }
  }

  closeNewUserDialog() {
    this.newUserData = undefined;
    this.newUserForm.reset();
    this.showNewUserDialog = false;
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.newUserForm, field);
  }

  async getAllRoles() {
    await this.getAllRolesUseCase.execute().then(
      (roles_data) => {
        this.listOfRoles = roles_data;
        const data = roles_data.filter(v => v.type == RoleType.USER);
        const higherRoles = data.filter(v => v.level! < this.currentUserRole!.level!);
        if (higherRoles.length > 0) {
          this.roles = [...data.filter(v => v.level! > this.currentUserRole!.level!)];
        }
        else {
          this.roles = [...data];
        }
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  trackDataChange() {
    if (!this.newUserData) {
      this.newUserData = new UserEntity();
    }

    this.newUserForm.get('email')?.markAsTouched;
    if (!this.isValid('email')) {
      return;
    }
    this.newUserData.password = this.newUserForm.get('password')?.value;

    this.newUserData.numId = this.userSubject?.numId;
    this.newUserData.names = this.userSubject?.names;
    this.newUserData.lastNames = this.userSubject?.lastNames;
    this.newUserData.email = this.newUserForm.get('email')?.value;
    this.newUserData.roles = this.newUserForm.get('roles')?.value;
    this.newUserData.birthdate = this.userSubject?.birthdate;
    this.newUserData.gender = this.userSubject?.gender;
    this.newUserData.datasource = this.userSubject?.datasource;
    this.newUserData.showPic = this.userSubject?.showPic;
    if (this.newUserForm.valid && this.newUserData && this.newUserData.numId && this.newUserData.names && this.newUserData.lastNames) {
      this.newUserReadyToSave = true;
    }
  }

  disableCopyPaste(event: ClipboardEvent): void {
    event.preventDefault();
  }

  /* User Subject Details */
  updateUserSubjectDetails(formData: any) {
    this.loggerService.debug("Updating User/Subject Details");
    Object.keys(formData).forEach((key: string) => {
      const dynamicFormField = this.formAttributes.find((attr: DynamicFormAttributes) => attr.key == key);
      const foundDetail = this.userSubjectDetails.find((detail: SubjectDetailEntity) => detail.id == dynamicFormField?.detailId);
      if (this.isUser) {
        let detail: UserDetailEntity = new UserDetailEntity();
        //id
        detail.id = foundDetail ? foundDetail.id : undefined;
        //userId
        detail.userId = this.userSubject?.id!;
        //name
        detail.name = dynamicFormField?.label;
        //parameter
        detail.parameter = key;
        //value
        detail.value = formData[key].toString();
        this.saveUserDetailUseCase.execute({ userDetail: detail }).then(
          (data) => {
            if (data) {
              this.loggerService.debug('User Detail Saved Successfully:');
              this.loggerService.debug(data);
            }
          },
          (e) => {
            this.loggerService.error('Error Saving User Detail:');
            this.loggerService.error(e);
            this.loggerService.debug(detail);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(detail) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_USER_DETAIL, 0, 'ERROR', '', at_attributes);
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant('content.errorTitle'),
              detail: `${this.translateService.instant('messages.error_saving_extended_biographic_data')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        );
      }
      else {
        let detail: SubjectDetailEntity = new SubjectDetailEntity();
        //id
        detail.id = foundDetail ? foundDetail.id : undefined;
        //tSubjectId
        detail.tSubjectId = this.userSubject?.id!;
        //name
        detail.name = dynamicFormField?.label;
        //parameter
        detail.parameter = key;
        //value
        detail.value = formData[key].toString();
        this.saveSubjectDetailUseCase.execute({ subjectDetail: detail }).then(
          (data) => {
            if (data) {
              this.loggerService.debug('Subject Detail Saved Successfully:');
              this.loggerService.debug(data);
            }
          },
          (e) => {
            this.loggerService.error('Error Saving Subject Detail:');
            this.loggerService.error(e);
            this.loggerService.debug(detail);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(detail) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_SUBJECT_DETAIL, 0, 'ERROR', '', at_attributes);
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant('content.errorTitle'),
              detail: `${this.translateService.instant('messages.error_saving_extended_biographic_data')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        );
      }
    });
    this.messageService.add({
      severity: 'success',
      summary: this.translateService.instant('content.successTitle'),
      detail: this.translateService.instant('messages.success_general'),
      life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
    });
  }

  /* Manager Functions */

  getManagerSettings() {
    this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
      (data) => {
        if (data) {
          this.loggerService.debug('Settings Data Retrieved Successfully:');
          this.loggerService.debug(data);
          this.managerSettings = data.settings;
          if (this.managerSettings) {
            this.localStorageService.setSessionSettings(this.managerSettings);
          }
          this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
              if (data) {
                this.konektorProperties = data;
              }
              this.updatePageState();
            },
            error: (e) => {
              this.loggerService.error('Error Retrieving Konektor Properties:');
              this.loggerService.error(e);
              const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_KONEKTOR_PROPERTIES, 0, 'ERROR', '', at_attributes);
            }
          });
        }
      },
      (e) => {
        this.loggerService.error('Error Retrieving Settings Data:');
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.APPLICATION_ID, value: environment.application }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
      },
    )
    .finally(() => {
      this.subjectTabsConfig = this.managerSettings?.subjectTabsConfig || new SubjectTabsConfig();
      if (this.userSubjectData?.biometricId != null && this.userSubjectData?.biometricId != undefined && this.userSubjectData?.biometricId != '') {
        this.activateEntriesExitsTab();
      }
    });
  }

  /* Page State Functions */

  updatePageState() {
    if (this.managerSettings) {
      this.subjectTabsConfig = this.managerSettings.subjectTabsConfig || new SubjectTabsConfig();
      this.prisonsConfig = this.managerSettings.continued1?.prisonsSettings || new PrisonsSettingsModel();
      // this.loggerService.log(this.prisonsConfig);
      this.widgetUrl = this.managerSettings.widgetConfig?.url || "";
      this.allowDeleteAfterIdSearch = this.managerSettings.allowRemoveIdentity!;
      this.allowEditAfterIdSearch = this.managerSettings.allowEditIdentity!;
      if (this.managerSettings.subjectTabsConfig?.showExtendedBioFieldsTab) {
        if (this.isUser) {
          this.getSubjectDetailsBySubjectIdUseCase.execute({ id: this.userSubjectData?.id! }).then(
            (data) => {
              if (data) {
                this.loggerService.debug('User Details Data Retrieved Successfully:');
                this.loggerService.debug(data);
                this.userSubjectDetails = data;
                this.buildExtendedBioForm();
              }
              else {
                this.loggerService.error('No User Details Data Retrieved');
              }
            },
            (e) => {
              this.loggerService.error('Error Retrieving User Details Data:');
              this.loggerService.error(e);
              const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_USER_DETAILS_BY_USER_ID, 0, 'ERROR', '', at_attributes);
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_obtaining_extended_biographic_data')}: ${e.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
            },
          )
          .finally(() => {
            this.isLoading = false;
          });
        }
        else {
          this.getSubjectDetailsBySubjectIdUseCase.execute({ id: this.userSubject?.id! }).then(
            (data) => {
              if (data) {
                this.loggerService.debug('Subject Details Data Retrieved Successfully:');
                this.loggerService.debug(data);
                this.userSubjectDetails = data;
                this.buildExtendedBioForm();
              }
              else {
                this.loggerService.error('No Subject Details Data Retrieved');
              }
            },
            (e) => {
              this.loggerService.error('Error Retrieving Subject Details Data:');
              this.loggerService.error(e);
              const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_DETAILS_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_obtaining_extended_biographic_data')}: ${e.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
            },
          )
          .finally(() => {
            this.isLoading = false;
          });
        }
      }
      else {
        this.isLoading = false;
      }

      /* Load Widget */
      this.widgetReady = true;
    }
    else {
      this.loggerService.error('No Settings Data Retrieved');
      const at_attributes: ExtraData[] = [{ name: 'error', value: "No Settings Data Retrieved" }];
      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
      this.isLoading = false;
    }
  }

  updateModified(modified: boolean) {
    this.modified = modified;
    this.localStorageService.setLockMenu(modified);
  }

  activateEntriesExitsTab() {
    this.activeIndex = 0;
    if (this.subjectTabsConfig?.showAdditionalTabs && this.subjectTabsConfig?.showEntriesExitsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictEntriesExitsTabToSpecificRoles!, this.subjectTabsConfig?.entriesExitsRoles!)) {
      let activeIndex = 1;
      if (this.subjectTabsConfig?.showExtendedBioFieldsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictExtendedBioFieldsTabToSpecificRoles!, this.subjectTabsConfig?.extendedBioFieldsRoles!)) {
        activeIndex++;
      }
      if (this.subjectTabsConfig?.showPhysicalDataTab && this.isTabEnabled(this.subjectTabsConfig?.restrictPhysicalDataTabToSpecificRoles!, this.subjectTabsConfig?.physicalDataRoles!)) {
        activeIndex++;
      }
      if (this.subjectTabsConfig?.showFilesTab && this.isTabEnabled(this.subjectTabsConfig?.restrictFilesTabToSpecificRoles!, this.subjectTabsConfig?.filesRoles!)) {
        activeIndex++;
      }
      if (this.subjectTabsConfig?.showProfilePictureTab && this.isTabEnabled(this.subjectTabsConfig?.restrictProfilePictureTabToSpecificRoles!, this.subjectTabsConfig?.profilePictureRoles!)) {
        activeIndex++;
      }
      if (this.subjectTabsConfig?.showRelationsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictRelationsTabToSpecificRoles!, this.subjectTabsConfig?.relationsRoles!)) {
        activeIndex++;
      }
      if (this.subjectTabsConfig?.showLocationsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictLocationsTabToSpecificRoles!, this.subjectTabsConfig?.locationsRoles!)) {
        activeIndex++;
      }
      this.activeIndex = activeIndex;
    }
  }

  /* Dynamic Form Functions */

  buildExtendedBioForm() {
    const extBioFields: CustomFieldModel[] = this.managerSettings?.extBioFields || [];
    if (extBioFields.length != 0) {
      this.loggerService.info('There are Extended Bio Fields to submit');
      this.formAttributes = [];
      // Classify the attributes of the action
      extBioFields.forEach((extBioField: CustomFieldModel) => {
        if (extBioField.type == CustomFieldTypes.INPUT || extBioField.type == CustomFieldTypes.DROPDOWN || extBioField.type == CustomFieldTypes.TOGGLE) {
          let type: CustomFieldTypes = extBioField.type;
          let required = false;
          let maxCharacters = 0;
          let options: string[] = [];
          extBioField.fieldData?.forEach((data: any) => {
            if (data.key.includes('required-checkbox')) {
              required = data.value == 'true';
            }
            if (data.key.includes('options-listbox')) {
              options = JSON.parse(data.value);
            }
            if (data.key.includes('max-characters-textbox')) {
              maxCharacters = data.value;
            }
          });
          if (type == CustomFieldTypes.INPUT && maxCharacters > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 25)) {
            type = CustomFieldTypes.TEXTAREA;
          }
          const formAttribute: DynamicFormAttributes = {
            type: type,
            label: extBioField.name,
            key: extBioField.parameter,
            value: this.userSubjectDetails.find((detail: SubjectDetailEntity) => detail.parameter == extBioField.parameter)?.value || '',
            detailId: this.userSubjectDetails.find((detail: SubjectDetailEntity) => detail.parameter == extBioField.parameter)?.id || '',
            required: required,
            options: options,
            disabled: !this.canReadAndWrite,
            group: extBioField.group?.name ?? '',
            groupRole: extBioField.group?.roleId ?? '',
            tooltip: extBioField.description ?? '',
            translations: this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'extBioFields')?.translations?.find((t: TranslationModel) => t.key === extBioField.parameter) || new TranslationModel(),
          };
          this.formAttributes.push(formAttribute);
        }
      });
      this.loggerService.debug(this.formAttributes);
      // If the action requires to show a form, render the dynamic form
      if (this.formAttributes.length > 0) {
        this.loggerService.info('There is a Form to Render');
        const formFields = this.convertFormControlArrayToObject(this.formAttributes);
        this.loggerService.debug(formFields);
        this.componentRef = this.dynamicFormContent.createComponent(DynamicFormComponent);
        this.componentRef.instance.controlsConfig = formFields;
        this.componentRef.instance.showForm = this.showDynamicForm;
        this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && (this.allowEditAfterIdSearch || this.verified) && this.userIsVerified;
        this.componentRef.instance.userSubjectRoles = this.userSubject?.roles;
        this.componentRef.instance.groupTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'extBioFieldGroups')?.translations;

        // Subscribe to the form submission event
        this.componentRef.instance.formSubmitted.subscribe((formData: any) => {
          this.loggerService.debug('Form Data:');
          this.loggerService.debug(formData);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(formData) }
          ];
          this.auditTrailService.auditTrailSelectReason(this.isUser ? ReasonTypeEnum.USER : ReasonTypeEnum.SUBJECT, this.isUser ? AuditTrailActions.UPDATE_USER_DETAILS : AuditTrailActions.UPDATE_SUBJECT_DETAILS, ReasonActionTypeEnum.UPDATE, () => { this.updateUserSubjectDetails(formData); }, at_attributes, false);
        });
        // Subscribe to the form modified event
        this.componentRef.instance.formModified.subscribe((modified: boolean) => {
          this.updateModified(modified);
        });
      }
    }
  }

  toggleDynamicForm() {
    if (this.componentRef) {
      this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && (this.allowEditAfterIdSearch || this.verified) && this.userIsVerified;
      this.componentRef.instance.toggleFormState(this.canReadAndWrite && (this.allowEditAfterIdSearch || this.verified) && this.userIsVerified);
    }
  }

  /* Subject Card Functions */

  UserSubjectCardResult(event: UserSubjectCardComponentResult) {
    switch (event.resultType) {
      case "verify":
        this.openVerifyDialog();
        break;
      case "edit":
        this.openEditSubjectDialog();
        break;
      case "delete":
        this.confirmDelete(this.userSubject!);
        break;
    }
  }

  /* Widget Functions */

  onWidgetMatchResult(event: WidgetResult) {
    if (!this.verifyReady) {
      return;
    }
    switch (event.action) {
      case "verify":
        if (event.result == "success") {
          if (event.data.isMatched) {
            this.verifyBtnAvailable = false;
            this.verified = true;
            this.enterEditMode();
          }
        }
        this.widgetReady = true;
        this.verifyReady = false;
        break;
      case "process":
        this.updateModified(event.result.toLowerCase().includes("started"))
        break;
      case "close_verify":
      case "error":
        this.updateModified(false);
        this.widgetReady = true;
        this.verifyReady = false;
        break;
    }
  }

  onWidgetEnrollResult(event: WidgetResult) {
    if (!this.widgetReady) {
      return;
    }

    switch (event.action) {
      case "server_biodata":
        if (event.result == "success") {
          this.verifyBtnAvailable = true;
          this.biometricSamples = true;
        }
        break;
      case "server_update_attributes":
        if (event.result == "SUCCESS") {
          this.updateAttributes = false;
        }
        break;
      case "process":
        this.updateModified(event.result.toLowerCase().includes("started"))
        break;
      case "enroll":
        this.biometricSamples = true;
        this.updateAttributes = false;
        /* A Subject gets created when a new user is created */
        // if (this.userSubjectData == undefined) {
        //   let userSubject = new UserEntity();
        //   userSubject.names = this.userSubject?.names;
        //   userSubject.lastNames = this.userSubject?.lastNames;
        //   userSubject.gender = this.userSubject?.gender;
        //   userSubject.pic = this.userSubject?.pic;
        //   userSubject.username = this.userSubject?.username;
        //   userSubject.email = this.userSubject?.email;
        //   userSubject.numId = this.userSubject?.numId;
        //   userSubject.birthdate = this.userSubject?.birthdate;
        //   this.onSubmitNewSubject(userSubject);
        // }
        break;
      case "close_verify":
      case "error":
        if(event.result == "NO_BIOMETRIC_IDENTITY") {
          // If no biometric Identity restart the widget to enable the user to enroll
          this.biometricSamples = false;
          this.enterEditMode();
          // this.widgetReady = false;
          // if(!this.widgetReady){
          //   setTimeout(() => this.widgetReady = true, 0);
          // }
        }
        if (event.result == "ERROR_IDENTITY_DOES_NOT_EXIST") {
          this.verified = true;
          this.biometricSamples = false;
          this.enterEditMode();
        }
        this.updateModified(false);
    }
  }

  /* Verify Fnctions */

  openVerifyDialog() {
    this.widgetReady = false;
    this.verifyReady = true;
  }

  async userVerifying(mod: boolean) {
    this.widgetReady = !mod;
    this.updateModified(mod);
    if (this.localStorageService.isUserVerified()) {
      await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
        (response) => {
          this.userIsVerified = !response;
        },
        (e) => {
          this.userIsVerified = false;
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.CHECK_TOKEN, 0, 'ERROR', '', at_attributes);
        }
      )
      .finally(() => {
        if(this.userIsVerified) {
          this.toggleDynamicForm();
        }
        this.updateModified(false);
        this.widgetReady = false;
        if(!this.widgetReady){
          setTimeout(() => this.widgetReady = true, 0);
        }
      });
      ;
    }
    else {
      this.userIsVerified = false;
    }
  }

  /* Support Functions */

  isPrisoner(): boolean {
    return this.isUser ? this.userSubjectData?.roles?.filter((role: RoleEntity) => role.id?.toString() == this.prisonsConfig?.prisonerProfileId).length != 0 : this.userSubject?.roles?.filter((role: RoleEntity) => role.id?.toString() == this.prisonsConfig?.prisonerProfileId).length != 0;
  }

  /**
   * Convert an array of form controls to an object
   * @param arr Array of form controls
   * @returns Object with the form controls
   */
  convertFormControlArrayToObject(arr: any[]) {
    const result: any = {};
    arr.forEach(item => {
      result[item.key] = item;
    });
    return result;
  }

  onTabChange(event: TabViewChangeEvent) {
    this.showDynamicForm = event.index == 1 && this.subjectTabsConfig?.showExtendedBioFieldsTab! && this.isTabEnabled(this.subjectTabsConfig?.restrictExtendedBioFieldsTabToSpecificRoles!, this.subjectTabsConfig?.extendedBioFieldsRoles!);
    this.componentRef.instance.showForm = this.showDynamicForm;
  }

  private clearRejectTimeout() {
    if (this.rejectTimeout) {
      clearTimeout(this.rejectTimeout);
    }
    this.rejectTimeout = null;
  }

  isTabEnabled(restricted: boolean, permittedRoles: string) {
    if (restricted && permittedRoles) {
      let isRoleIncluded = false;
      let roleIds = permittedRoles.split(',');
      let userSubject = this.isUser ? this.userSubjectData : this.userSubject;
      userSubject?.roles?.forEach((role: RoleEntity) => {
        if (roleIds.includes(role.id?.toString()!)) {
          isRoleIncluded = true;
        }
      });
      return isRoleIncluded;
    }
    return true;
  }

  updateSegmentedAttributes(){
    //console.log("updateSegmentedAttributes");
    this.updateAttributes = true;
  }

  isTenantValid(): boolean {
    const result: ValidTenantResult = this.checkPermissions.isTenantValid(this.konektorProperties);
    this.disabledVerifyBtnToolTip = result.message;
    return result.valid;
  }

  /**
     * Navigate to the Error Page
     * @param icon Icon to display in the error page
     * @param title Title to display in the error page
     * @param description Description to display in the error page
     */
  navigateToErrorPage(icon: string, title: string, description: string, hasTimeOut: boolean = true) {
    this.loggerService.error('Navigating to Error Page');
    let home = 'home';
    let translatePrefixMessage = 'ms_errors.';
    this.router.navigate(['/error/dynamic'], { queryParams: { icon, title, description, hasTimeOut, home, translatePrefixMessage} });
  }
}
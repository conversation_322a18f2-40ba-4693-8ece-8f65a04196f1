import { AppRegistryGrpcModel } from "src/verazial-common-frontend/core/generated/app-registry/app_registry_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { AppRegistryEntity } from "../../domain/entities/app-registry.entity";

export class AppRegistryMapper extends Mapper<AppRegistryGrpcModel, AppRegistryEntity> {
    override mapFrom(param: AppRegistryGrpcModel): AppRegistryEntity {
        return {
            id: param.getId(),
            name: param.getName(),
            description: param.getDescription(),
            baseIdentifier: param.getBaseidentifier(),
            type: param.getType(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }

    override mapTo(param: AppRegistryEntity): AppRegistryGrpcModel {
        let model = new AppRegistryGrpcModel();
        model.setId(param.id!);
        model.setName(param.name!);
        model.setDescription(param.description!);
        model.setBaseidentifier(param.baseIdentifier!);
        model.setType(param.type!);
        return model;
    }
}
import { Observable } from "rxjs";
import { ApplicationRepository } from "../repositories/application.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationEntity } from "../entities/application.entity";

export class GetApplicationByIdUseCase implements UseCaseGrpc<{ id: string }, ApplicationEntity> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: { id: string }): Promise<ApplicationEntity> {
        return this.applicationRepository.getApplicationById(params)
    }

}
<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<div [formGroup]="form" class="dialog-container">
    @if( operationType == opType.INSERT ){
        <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex"></p-steps>
    }@else{
        <div class="flex justify-content-center">
            <p-selectButton
                [options]="stepOptions"
                formControlName="stepOptions"
                severity="secondary"
                multiple="false"
                allowEmpty="false"
                optionLabel="key"
                optionValue="value"
                dataKey="value"
                (onChange)="onActiveTabIndexChange($event)">
            </p-selectButton>
        </div>
    }
    <div class="mt-5">
        <p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh' }">
            <!-- Dynamic Form -->
            <ng-template #dynamicFormContent></ng-template>
            @switch (activeIndex) {
                <!-- General -->
                @case (0) {
                    <div class="grid">
                        <div class="col-12 flex justify-content-end requiredFieldsLabel">
                            {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                        </div>
                        <!-- id/authCode -->
                        <div *ngIf="entryExitAuth?.id && operationType != opType.INSERT" class="col-4 col-offset-4">
                            <label for="authCode" class="label-form"> {{ 'content.authCode' | translate }}</label>
                            <input
                                type="text"
                                pInputText
                                [(ngModel)]="entryExitAuth!.id"
                                [ngModelOptions]="{standalone: true}"
                                [disabled]="true"
                            />
                        </div>
                        <!-- type -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.type' | translate }} <span *ngIf="isRequiredField('type')" class="requiredStar">*</span></label>
                            <p-dropdown formControlName="type" [(ngModel)]="selectAuthType" (onChange)="typeChanged($event)"
                                appendTo="body" [options]="authTypeOptions" optionLabel="key" optionValue="value" placeholder="{{ 'content.select' | translate }}"
                                [class.ng-dirty]="!isValid('type') && form.controls['type'].touched"
                            />
                            <small *ngIf="!isValid('type') && form.controls['type'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- authReason -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.reason' | translate }} <span *ngIf="isRequiredField('authReason')" class="requiredStar">*</span></label>
                            <p-dropdown formControlName="authReason" [(ngModel)]="selectAuthReason"
                                appendTo="body" [options]="authReasonOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}"
                                [class.ng-dirty]="!isValid('authReason') && form.controls['authReason'].touched"
                            />
                            <small *ngIf="!isValid('authReason') && form.controls['authReason'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- authStartDateTime -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.authStartDateTime' | translate }} <span *ngIf="isRequiredField('authStartDateTime')" class="requiredStar">*</span></label>
                            <p-calendar
                                inputId="calendar-24h"
                                formControlName="authStartDateTime"
                                [showTime]="true"
                                dateFormat="{{ 'dateFormat' | translate }}"
                                hourFormat="{{ 'hourFormat' | translate }}"
                                [showButtonBar]="true"
                                appendTo="body"
                                [class.ng-dirty]="!isValid('authStartDateTime') && form.controls['authStartDateTime'].touched"
                            />
                            <small *ngIf="!isValid('authStartDateTime') && form.controls['authStartDateTime'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- authEndDateTime -->
                        <div *ngIf="showEndDate else emptyCol" class="col-6">
                            <label class="label-form">{{ 'content.authEndDateTime' | translate }} <span *ngIf="isRequiredField('authEndDateTime')" class="requiredStar">*</span></label>
                            <p-calendar
                                inputId="calendar-24h"
                                formControlName="authEndDateTime"
                                [showTime]="true"
                                dateFormat="{{ 'dateFormat' | translate }}"
                                hourFormat="{{ 'hourFormat' | translate }}"
                                [showButtonBar]="true"
                                appendTo="body"
                                [onSelect]="onSelectEndDateTime($event)"
                                [class.ng-dirty]="!isValid('authEndDateTime') && form.controls['authEndDateTime'].touched"
                            />
                            <div class="flex flex-column">
                                <small *ngIf="!isValid('authEndDateTime') && form.controls['authEndDateTime'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                <small *ngIf="invalidDates" [style]="{'color': 'red'}">{{ 'messages.error_start_end_dates' | translate }}</small>
                            </div>
                        </div>
                        <ng-template #emptyCol>
                            <div class="col-6"></div>
                        </ng-template>
                        <!-- observations -->
                        <div class="col-12">
                            <label for="observations" class="label-form">{{ 'content.observation' | translate }} <span *ngIf="isRequiredField('observations')" class="requiredStar">*</span></label>
                            <textarea id="observations" rows="3" cols="12" pInputTextarea formControlName="observations"></textarea>
                        </div>
                    </div>
                }
                <!-- Details -->
                 @case (showDetails ? 1 : -1) {
                    <div class="flex justify-content-center mt-2">
                        <ng-container *ngTemplateOutlet="dynamicFormContent" />
                    </div>
                 }
                <!-- Signatures -->
                @case (showDetails ? 2 : 1) {
                    <div class="flex justify-content-center align-items-center gap-2">
                        <!-- User Authorization Signature -->
                        <div>
                            <app-bio-signatures
                                [showSignatureButton]="(!hasAuthSignature)"
                                [showExtraFormFields]="false"
                                [showSignatureInfo]="hasAuthSignature"
                                [signatureTitle]="'content.authSignature'"
                                [signatureInputLabel]="'content.subjectWhoAuthrizes'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="authSignatureData"
                                [restrictSubjectRoles]="restrictAuthRoles"
                                [subjectRoleSegmentedSearch]="segmentedSearchAuthRole"
                                [userIsVerified]="userIsVerified"
                                (outputResult)="userAuthSignatureResult($event)"
                            ></app-bio-signatures>
                        </div>
                    </div>
                }
            }
        </p-scrollPanel>
    </div>
</div>
<div>
    <div class="footer-buttons-container">
        @if (activeIndex==0) {
            <p-button label="{{ 'cancel' | translate }}"
                (onClick)="onClose()"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
        }
        @else {
            <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left"
                (onClick)="onBack()"
                [disabled]="modified"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
        }
        @if (activeIndex!=(items?.length-1)) {
            <p-button label="{{ 'next' | translate }}" icon="pi pi-angle-right" iconPos="right"
                (onClick)="onNext()"
                [disabled]="modified"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
        }@else{
            <p-button
                [disabled]="!canReadAndWrite || !userIsVerified || entryExitAuth?.isCompleted! || isInProgress"
                [label]="createUpdateButtonTitle"  iconPos="right"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                (onClick)="saveEntryExitAuth()">
            </p-button>
        }
    </div>
</div>
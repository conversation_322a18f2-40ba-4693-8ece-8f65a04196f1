import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { TaskFlowRepository } from "../../repository/task-flow.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class DeleteTaskFlowByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private taskFlowRepository: TaskFlowRepository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.taskFlowRepository.deleteTaskFlowById(params)
    }
}
import * as jspb from 'google-protobuf'

import * as manager_settings_common_custom_field_pb from '../../../manager/settings/common/custom_field_pb'; // proto import: "manager/settings/common/custom_field.proto"
import * as manager_settings_common_custom_field_group_pb from '../../../manager/settings/common/custom_field_group_pb'; // proto import: "manager/settings/common/custom_field_group.proto"
import * as util_pb from '../../../util_pb'; // proto import: "util.proto"


export class SubjectTabsGrpcModel extends jspb.Message {
  getShowadditionaltabs(): boolean;
  setShowadditionaltabs(value: boolean): SubjectTabsGrpcModel;
  hasShowadditionaltabs(): boolean;
  clearShowadditionaltabs(): SubjectTabsGrpcModel;

  getShowextendedbiofieldstab(): boolean;
  setShowextendedbiofieldstab(value: boolean): SubjectTabsGrpcModel;
  hasShowextendedbiofieldstab(): boolean;
  clearShowextendedbiofieldstab(): SubjectTabsGrpcModel;

  getRestrictextendedbiofieldstabtospecificroles(): boolean;
  setRestrictextendedbiofieldstabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictextendedbiofieldstabtospecificroles(): boolean;
  clearRestrictextendedbiofieldstabtospecificroles(): SubjectTabsGrpcModel;

  getExtendedbiofieldsroles(): string;
  setExtendedbiofieldsroles(value: string): SubjectTabsGrpcModel;
  hasExtendedbiofieldsroles(): boolean;
  clearExtendedbiofieldsroles(): SubjectTabsGrpcModel;

  getShowphysicaldatatab(): boolean;
  setShowphysicaldatatab(value: boolean): SubjectTabsGrpcModel;
  hasShowphysicaldatatab(): boolean;
  clearShowphysicaldatatab(): SubjectTabsGrpcModel;

  getRestrictphysicaldatatabtospecificroles(): boolean;
  setRestrictphysicaldatatabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictphysicaldatatabtospecificroles(): boolean;
  clearRestrictphysicaldatatabtospecificroles(): SubjectTabsGrpcModel;

  getPhysicaldataroles(): string;
  setPhysicaldataroles(value: string): SubjectTabsGrpcModel;
  hasPhysicaldataroles(): boolean;
  clearPhysicaldataroles(): SubjectTabsGrpcModel;

  getShowprofilepicturetab(): boolean;
  setShowprofilepicturetab(value: boolean): SubjectTabsGrpcModel;
  hasShowprofilepicturetab(): boolean;
  clearShowprofilepicturetab(): SubjectTabsGrpcModel;

  getRestrictprofilepicturetabtospecificroles(): boolean;
  setRestrictprofilepicturetabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictprofilepicturetabtospecificroles(): boolean;
  clearRestrictprofilepicturetabtospecificroles(): SubjectTabsGrpcModel;

  getProfilepictureroles(): string;
  setProfilepictureroles(value: string): SubjectTabsGrpcModel;
  hasProfilepictureroles(): boolean;
  clearProfilepictureroles(): SubjectTabsGrpcModel;

  getShowrelationstab(): boolean;
  setShowrelationstab(value: boolean): SubjectTabsGrpcModel;
  hasShowrelationstab(): boolean;
  clearShowrelationstab(): SubjectTabsGrpcModel;

  getRestrictrelationstabtospecificroles(): boolean;
  setRestrictrelationstabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictrelationstabtospecificroles(): boolean;
  clearRestrictrelationstabtospecificroles(): SubjectTabsGrpcModel;

  getRelationsroles(): string;
  setRelationsroles(value: string): SubjectTabsGrpcModel;
  hasRelationsroles(): boolean;
  clearRelationsroles(): SubjectTabsGrpcModel;

  getShowlocationstab(): boolean;
  setShowlocationstab(value: boolean): SubjectTabsGrpcModel;
  hasShowlocationstab(): boolean;
  clearShowlocationstab(): SubjectTabsGrpcModel;

  getRestrictlocationstabtospecificroles(): boolean;
  setRestrictlocationstabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictlocationstabtospecificroles(): boolean;
  clearRestrictlocationstabtospecificroles(): SubjectTabsGrpcModel;

  getLocationsroles(): string;
  setLocationsroles(value: string): SubjectTabsGrpcModel;
  hasLocationsroles(): boolean;
  clearLocationsroles(): SubjectTabsGrpcModel;

  getShowentriesexitstab(): boolean;
  setShowentriesexitstab(value: boolean): SubjectTabsGrpcModel;
  hasShowentriesexitstab(): boolean;
  clearShowentriesexitstab(): SubjectTabsGrpcModel;

  getRestrictentriesexitstabtospecificroles(): boolean;
  setRestrictentriesexitstabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictentriesexitstabtospecificroles(): boolean;
  clearRestrictentriesexitstabtospecificroles(): SubjectTabsGrpcModel;

  getEntriesexitsroles(): string;
  setEntriesexitsroles(value: string): SubjectTabsGrpcModel;
  hasEntriesexitsroles(): boolean;
  clearEntriesexitsroles(): SubjectTabsGrpcModel;

  getShowentryexitauthorizationstab(): boolean;
  setShowentryexitauthorizationstab(value: boolean): SubjectTabsGrpcModel;
  hasShowentryexitauthorizationstab(): boolean;
  clearShowentryexitauthorizationstab(): SubjectTabsGrpcModel;

  getRestrictentryexitauthorizationstabtospecificroles(): boolean;
  setRestrictentryexitauthorizationstabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictentryexitauthorizationstabtospecificroles(): boolean;
  clearRestrictentryexitauthorizationstabtospecificroles(): SubjectTabsGrpcModel;

  getEntryexitauthorizationsroles(): string;
  setEntryexitauthorizationsroles(value: string): SubjectTabsGrpcModel;
  hasEntryexitauthorizationsroles(): boolean;
  clearEntryexitauthorizationsroles(): SubjectTabsGrpcModel;

  getShowentryexitauthdetails(): boolean;
  setShowentryexitauthdetails(value: boolean): SubjectTabsGrpcModel;
  hasShowentryexitauthdetails(): boolean;
  clearShowentryexitauthdetails(): SubjectTabsGrpcModel;

  getEntryexitauthdetailfields(): manager_settings_common_custom_field_pb.ArrayOfCustomField | undefined;
  setEntryexitauthdetailfields(value?: manager_settings_common_custom_field_pb.ArrayOfCustomField): SubjectTabsGrpcModel;
  hasEntryexitauthdetailfields(): boolean;
  clearEntryexitauthdetailfields(): SubjectTabsGrpcModel;

  getEntryexitauthdetailfieldgroups(): manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup | undefined;
  setEntryexitauthdetailfieldgroups(value?: manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup): SubjectTabsGrpcModel;
  hasEntryexitauthdetailfieldgroups(): boolean;
  clearEntryexitauthdetailfieldgroups(): SubjectTabsGrpcModel;

  getShowfilestab(): boolean;
  setShowfilestab(value: boolean): SubjectTabsGrpcModel;
  hasShowfilestab(): boolean;
  clearShowfilestab(): SubjectTabsGrpcModel;

  getRestrictfilestabtospecificroles(): boolean;
  setRestrictfilestabtospecificroles(value: boolean): SubjectTabsGrpcModel;
  hasRestrictfilestabtospecificroles(): boolean;
  clearRestrictfilestabtospecificroles(): SubjectTabsGrpcModel;

  getFilesroles(): string;
  setFilesroles(value: string): SubjectTabsGrpcModel;
  hasFilesroles(): boolean;
  clearFilesroles(): SubjectTabsGrpcModel;

  getSubjectfiletypes(): util_pb.ArrayOfStrings | undefined;
  setSubjectfiletypes(value?: util_pb.ArrayOfStrings): SubjectTabsGrpcModel;
  hasSubjectfiletypes(): boolean;
  clearSubjectfiletypes(): SubjectTabsGrpcModel;

  getAcceptedfiles(): string;
  setAcceptedfiles(value: string): SubjectTabsGrpcModel;
  hasAcceptedfiles(): boolean;
  clearAcceptedfiles(): SubjectTabsGrpcModel;

  getMaxfilesize(): string;
  setMaxfilesize(value: string): SubjectTabsGrpcModel;
  hasMaxfilesize(): boolean;
  clearMaxfilesize(): SubjectTabsGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SubjectTabsGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: SubjectTabsGrpcModel): SubjectTabsGrpcModel.AsObject;
  static serializeBinaryToWriter(message: SubjectTabsGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SubjectTabsGrpcModel;
  static deserializeBinaryFromReader(message: SubjectTabsGrpcModel, reader: jspb.BinaryReader): SubjectTabsGrpcModel;
}

export namespace SubjectTabsGrpcModel {
  export type AsObject = {
    showadditionaltabs?: boolean,
    showextendedbiofieldstab?: boolean,
    restrictextendedbiofieldstabtospecificroles?: boolean,
    extendedbiofieldsroles?: string,
    showphysicaldatatab?: boolean,
    restrictphysicaldatatabtospecificroles?: boolean,
    physicaldataroles?: string,
    showprofilepicturetab?: boolean,
    restrictprofilepicturetabtospecificroles?: boolean,
    profilepictureroles?: string,
    showrelationstab?: boolean,
    restrictrelationstabtospecificroles?: boolean,
    relationsroles?: string,
    showlocationstab?: boolean,
    restrictlocationstabtospecificroles?: boolean,
    locationsroles?: string,
    showentriesexitstab?: boolean,
    restrictentriesexitstabtospecificroles?: boolean,
    entriesexitsroles?: string,
    showentryexitauthorizationstab?: boolean,
    restrictentryexitauthorizationstabtospecificroles?: boolean,
    entryexitauthorizationsroles?: string,
    showentryexitauthdetails?: boolean,
    entryexitauthdetailfields?: manager_settings_common_custom_field_pb.ArrayOfCustomField.AsObject,
    entryexitauthdetailfieldgroups?: manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup.AsObject,
    showfilestab?: boolean,
    restrictfilestabtospecificroles?: boolean,
    filesroles?: string,
    subjectfiletypes?: util_pb.ArrayOfStrings.AsObject,
    acceptedfiles?: string,
    maxfilesize?: string,
  }

  export enum ShowadditionaltabsCase { 
    _SHOWADDITIONALTABS_NOT_SET = 0,
    SHOWADDITIONALTABS = 1,
  }

  export enum ShowextendedbiofieldstabCase { 
    _SHOWEXTENDEDBIOFIELDSTAB_NOT_SET = 0,
    SHOWEXTENDEDBIOFIELDSTAB = 2,
  }

  export enum RestrictextendedbiofieldstabtospecificrolesCase { 
    _RESTRICTEXTENDEDBIOFIELDSTABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTEXTENDEDBIOFIELDSTABTOSPECIFICROLES = 3,
  }

  export enum ExtendedbiofieldsrolesCase { 
    _EXTENDEDBIOFIELDSROLES_NOT_SET = 0,
    EXTENDEDBIOFIELDSROLES = 4,
  }

  export enum ShowphysicaldatatabCase { 
    _SHOWPHYSICALDATATAB_NOT_SET = 0,
    SHOWPHYSICALDATATAB = 5,
  }

  export enum RestrictphysicaldatatabtospecificrolesCase { 
    _RESTRICTPHYSICALDATATABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTPHYSICALDATATABTOSPECIFICROLES = 6,
  }

  export enum PhysicaldatarolesCase { 
    _PHYSICALDATAROLES_NOT_SET = 0,
    PHYSICALDATAROLES = 7,
  }

  export enum ShowprofilepicturetabCase { 
    _SHOWPROFILEPICTURETAB_NOT_SET = 0,
    SHOWPROFILEPICTURETAB = 8,
  }

  export enum RestrictprofilepicturetabtospecificrolesCase { 
    _RESTRICTPROFILEPICTURETABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTPROFILEPICTURETABTOSPECIFICROLES = 9,
  }

  export enum ProfilepicturerolesCase { 
    _PROFILEPICTUREROLES_NOT_SET = 0,
    PROFILEPICTUREROLES = 10,
  }

  export enum ShowrelationstabCase { 
    _SHOWRELATIONSTAB_NOT_SET = 0,
    SHOWRELATIONSTAB = 11,
  }

  export enum RestrictrelationstabtospecificrolesCase { 
    _RESTRICTRELATIONSTABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTRELATIONSTABTOSPECIFICROLES = 12,
  }

  export enum RelationsrolesCase { 
    _RELATIONSROLES_NOT_SET = 0,
    RELATIONSROLES = 13,
  }

  export enum ShowlocationstabCase { 
    _SHOWLOCATIONSTAB_NOT_SET = 0,
    SHOWLOCATIONSTAB = 14,
  }

  export enum RestrictlocationstabtospecificrolesCase { 
    _RESTRICTLOCATIONSTABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTLOCATIONSTABTOSPECIFICROLES = 15,
  }

  export enum LocationsrolesCase { 
    _LOCATIONSROLES_NOT_SET = 0,
    LOCATIONSROLES = 16,
  }

  export enum ShowentriesexitstabCase { 
    _SHOWENTRIESEXITSTAB_NOT_SET = 0,
    SHOWENTRIESEXITSTAB = 17,
  }

  export enum RestrictentriesexitstabtospecificrolesCase { 
    _RESTRICTENTRIESEXITSTABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTENTRIESEXITSTABTOSPECIFICROLES = 18,
  }

  export enum EntriesexitsrolesCase { 
    _ENTRIESEXITSROLES_NOT_SET = 0,
    ENTRIESEXITSROLES = 19,
  }

  export enum ShowentryexitauthorizationstabCase { 
    _SHOWENTRYEXITAUTHORIZATIONSTAB_NOT_SET = 0,
    SHOWENTRYEXITAUTHORIZATIONSTAB = 20,
  }

  export enum RestrictentryexitauthorizationstabtospecificrolesCase { 
    _RESTRICTENTRYEXITAUTHORIZATIONSTABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTENTRYEXITAUTHORIZATIONSTABTOSPECIFICROLES = 21,
  }

  export enum EntryexitauthorizationsrolesCase { 
    _ENTRYEXITAUTHORIZATIONSROLES_NOT_SET = 0,
    ENTRYEXITAUTHORIZATIONSROLES = 22,
  }

  export enum ShowentryexitauthdetailsCase { 
    _SHOWENTRYEXITAUTHDETAILS_NOT_SET = 0,
    SHOWENTRYEXITAUTHDETAILS = 23,
  }

  export enum EntryexitauthdetailfieldsCase { 
    _ENTRYEXITAUTHDETAILFIELDS_NOT_SET = 0,
    ENTRYEXITAUTHDETAILFIELDS = 24,
  }

  export enum EntryexitauthdetailfieldgroupsCase { 
    _ENTRYEXITAUTHDETAILFIELDGROUPS_NOT_SET = 0,
    ENTRYEXITAUTHDETAILFIELDGROUPS = 25,
  }

  export enum ShowfilestabCase { 
    _SHOWFILESTAB_NOT_SET = 0,
    SHOWFILESTAB = 26,
  }

  export enum RestrictfilestabtospecificrolesCase { 
    _RESTRICTFILESTABTOSPECIFICROLES_NOT_SET = 0,
    RESTRICTFILESTABTOSPECIFICROLES = 27,
  }

  export enum FilesrolesCase { 
    _FILESROLES_NOT_SET = 0,
    FILESROLES = 28,
  }

  export enum SubjectfiletypesCase { 
    _SUBJECTFILETYPES_NOT_SET = 0,
    SUBJECTFILETYPES = 29,
  }

  export enum AcceptedfilesCase { 
    _ACCEPTEDFILES_NOT_SET = 0,
    ACCEPTEDFILES = 30,
  }

  export enum MaxfilesizeCase { 
    _MAXFILESIZE_NOT_SET = 0,
    MAXFILESIZE = 31,
  }
}


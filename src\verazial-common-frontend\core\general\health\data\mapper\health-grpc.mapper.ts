import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { HealthEntity } from "../../domain/entity/health.entity";
import { HealthStatus } from "src/verazial-common-frontend/core/models/health-status.enum";
import { HealthGrpcModel } from "src/verazial-common-frontend/core/generated/health/health_pb";

export class HealthGrpcMapper extends Mapper<HealthGrpcModel, HealthEntity> {
    override mapFrom(param: HealthGrpcModel): HealthEntity {
        return {
            status: HealthStatus[param.getStatus()],
            date: new Date(param.getDate()?.getSeconds()!! * 1000 + Math.round(param.getDate()?.getNanos()!! / 1e6)),
        }
    }
    override mapTo(param: HealthEntity): HealthGrpcModel {
        throw new Error("Method not implemented.");
    }

}
import { WindowParametersRepository } from "../repositories/window-parameters.repository";
import { WindowParametersEntity } from "../entities/window-parameters.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetWindowParamByIdUseCase implements UseCaseGrpc<{ id: string }, WindowParametersEntity> {
    constructor(private windowParametersRepository: WindowParametersRepository) { }
    execute(params: { id: string; }): Promise<WindowParametersEntity> {
        return this.windowParametersRepository.getWindowParamById(params);
    }
}
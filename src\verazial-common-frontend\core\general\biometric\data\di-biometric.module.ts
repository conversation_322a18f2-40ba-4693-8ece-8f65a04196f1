import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BiometricRepository } from "../domain/repository/biometric.repository";
import { ReplaceAttributesUseCase } from "../domain/use-cases/replace-attributes.use-case";
import { BiometricRepositoryImpl } from "./repository-impl/biometric-impl.repository";

const replaceAttributesCaseFactory =
    (biometricRepository: BiometricRepository) => new ReplaceAttributesUseCase(biometricRepository);

export const replaceAttributesUseCaseProvider = {
    provide: ReplaceAttributesUseCase,
    useFactory: replaceAttributesCaseFactory,
    deps: [BiometricRepository]
}

@NgModule({
    providers: [
        replaceAttributesUseCaseProvider,
        { provide: BiometricRepository, useClass: BiometricRepositoryImpl }
    ],
    imports: [
        CommonModule,
    ]
})
export class DiBiometricModule { }
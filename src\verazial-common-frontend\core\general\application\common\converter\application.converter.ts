import { ListWindowParametersGrpcModel, WindowParametersGrpcModel } from "src/verazial-common-frontend/core/generated/application/parameters_pb";
import { WindowParametersEntity } from "../../domain/entities/window-parameters.entity";
import { WindowParametersGrpcMapper } from "../../data/mapper/window-parameters-grpc.mapper";

export function convertToListOfWindowsParams(windowsParameter?: ListWindowParametersGrpcModel):
    WindowParametersEntity[] {
    const windowParamsMapper = new WindowParametersGrpcMapper();
    let listOfWindowsParameters: WindowParametersEntity[] = [];
    windowsParameter?.getApplicationparametersmodelList().forEach((params) => {
        listOfWindowsParameters.push(windowParamsMapper.mapFrom(params))
    });
    return listOfWindowsParameters;
}
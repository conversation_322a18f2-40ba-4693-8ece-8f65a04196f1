<div class="flex flex-column" [formGroup]="form">
    <div class="flex justify-content-end requiredFieldsLabel">
        {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
    </div>
    <div class="field">
        <label class="label-form" for="roleName">{{ 'content.name' | translate }} <span *ngIf="isRequiredField('roleName')" class="requiredStar">*</span></label>
        <input type="text" pInputText formControlName="roleName" (ngModelChange)="trackRoleDataChange()"/>
        <small *ngIf="!isValid('roleName') && form.controls['roleName'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    <div class="field">
        <label class="label-form" for="roleLevel">{{ 'content.level' | translate }} <span *ngIf="isRequiredField('roleLevel')" class="requiredStar">*</span></label>
        <p-inputNumber inputId="integeronly" formControlName="roleLevel" (ngModelChange)="trackRoleDataChange()" min="0" max="100"/>
        <small *ngIf="!isValid('roleLevel') && form.controls['roleLevel'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    <div class="field">
        <label class="label-form" for="roleType">{{ 'content.type' | translate }} <span *ngIf="isRequiredField('roleType')" class="requiredStar">*</span></label>
        <p-dropdown
            appendTo="body"
            formControlName="roleType"
            [options]="roleTypes"
            (ngModelChange)="trackRoleDataChange()"
            [style]="{'width': '100%'}"
            optionLabel="value"
            optionValue="key"
            placeholder="{{ 'content.select' | translate }}"
        ></p-dropdown>
        <small *ngIf="!isValid('roleType') && form.controls['roleType'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    <div class="field">
        <label class="label-form" for="roleDescription">{{ 'content.description' | translate }} <span *ngIf="isRequiredField('roleDescription')" class="requiredStar">*</span></label>
        <textarea
            formControlName="roleDescription"
            rows="5"
            cols="30"
            (ngModelChange)="trackRoleDataChange()"
            pInputTextarea
            >
        </textarea>
    </div>
    <div *ngIf="showRoleShowInMenu" class="field">
        <label class="label-form" for="roleShowInMenu">{{ 'content.showInMenu' | translate }} <span *ngIf="isRequiredField('roleShowInMenu')" class="requiredStar">*</span></label>
        <div class="w-full flex justify-content-center">
            <p-inputSwitch
                formControlName="roleShowInMenu"
                (ngModelChange)="trackRoleDataChange()"
            />
        </div>
    </div>
</div>
import { DateModel } from "../../common/models/date.model";
import { DayTimeScheduleEntity } from "./day-time-schedule.entity";


export class CategoryScheduleEntity {
    id: string | undefined;
    groupCategoryId: string | undefined;
    hasDateRange: boolean | undefined;
    timeInit: Date | undefined;
    timeEnd: Date | undefined;
    dateInit: DateModel | undefined;
    dateEnd: DateModel | undefined;
    dayTime: DayTimeScheduleEntity[] | undefined;
}
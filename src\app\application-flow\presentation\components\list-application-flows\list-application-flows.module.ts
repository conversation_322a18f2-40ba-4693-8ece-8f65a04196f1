import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListApplicationFlowsComponent } from './list-application-flows/list-application-flows.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { MessagesModule } from 'primeng/messages';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { TooltipModule } from 'primeng/tooltip';



@NgModule({
  declarations: [
    ListApplicationFlowsComponent
  ],
  imports: [
    CommonModule,
    TableModule,
    ButtonModule,
    TagModule,
    ConfirmDialogModule,
    TranslateModule,
    MessagesModule,
    ToastModule,
    DropdownModule,
    DialogModule,
    InputSwitchModule,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    TooltipModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    CalendarModule
  ],
  exports:[
    ListApplicationFlowsComponent
  ]
})
export class ListApplicationFlowsModule { }

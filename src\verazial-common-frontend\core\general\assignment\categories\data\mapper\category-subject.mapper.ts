import { CategorySubjectGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { CategorySubjectEntity } from "../../domain/entity/category-subject.entity";

export class CategorySubjectMapper extends Mapper<CategorySubjectGrpc, CategorySubjectEntity> {
    override mapFrom(param: CategorySubjectGrpc): CategorySubjectEntity {
        let categorySubject = new CategorySubjectEntity();

        categorySubject.id = param.getId();
        categorySubject.groupCategoryId = param.getGroupcategoryid();
        categorySubject.subjectId = param.getSubjectid();

        return categorySubject;
    }
    override mapTo(param: CategorySubjectEntity): CategorySubjectGrpc {
        let categorySubject = new CategorySubjectGrpc();

        categorySubject.setId(param.id!);
        categorySubject.setGroupcategoryid(param.groupCategoryId!);
        categorySubject.setSubjectid(param.subjectId!)

        return categorySubject;
    }

}
<div>
    @if(isLoading){
        <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
    }@else{
        @if (showEmpty) {
            <div class="subcontainer">
                <label class="subcontainer-title">{{ "pass_application.application_flows" | translate}}</label>
                <app-empty
                    [readAndWritePermissions]="canReadAndWrite"
                    buttonLabel="pass_application.new_application"
                    titleLabel="pass_application.no_applications_available"
                    (clicked)="createNewFlow($event)">
                </app-empty>
            </div>
        }@else if(showApplicationsFlow){
            <app-edit-application-flow (return)="onReturn($event)" [applicationData]="selectedApplication"></app-edit-application-flow>
        }@else if(showListApplications){
            <app-list-application-flows (onNewDataFlow)="createNewFlow($event)" (onEdit)="onEditApplication($event)"></app-list-application-flows>
        }
    }

</div>
import { Injectable } from "@angular/core";
import { ActionRepository } from "../../domain/repository/action.repository";
import { Observable, pipe, take } from "rxjs";
import { ActionEntity } from "../../domain/entity/action.entity";
import { ActionMapper } from "../mapper/action.mapper";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { GeneralSettings } from "../../../manager/common/models/general-settings.model";


@Injectable({
    providedIn: 'root',
})
export class ActionRepositoryImpl extends ActionRepository{

    actionMapper =  new ActionMapper();

    constructor(private http: HttpClient,
        private storageService: LocalStorageService){
        super();
    }

    override addAction(params: { action: ActionEntity; }): Observable<any> {
        let settings: GeneralSettings = this.storageService.getConfig();

        if (!settings.endPoints?.actionsConnection) {
            throw new Error('Actions connection settings not found');
        }

        let headers = new HttpHeaders({
            'Content-Type': 'application/json',
            'Authorization': 'Basic '+
                btoa(   settings.endPoints?.actionsConnection.credentials?.user + ':' +
                        settings.endPoints?.actionsConnection.credentials?.password)
        })

        return this.http.post<ActionEntity>(
            settings.endPoints.actionsConnection.url + '/actions/rest/v1/actions/actions',
            params.action,
            {headers: headers}
        ).pipe(take(1));
    }

}
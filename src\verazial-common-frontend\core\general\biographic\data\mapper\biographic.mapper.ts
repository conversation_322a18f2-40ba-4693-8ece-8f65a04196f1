
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { IdentityOperationResultEntity } from "../../domain/entity/identity-operation-result.entity";
import { IdentityOperationResult } from "src/verazial-common-frontend/core/generated/biographic/biographic_pb";

export class BiographicMapper extends Mapper<IdentityOperationResult, IdentityOperationResultEntity> {
    override mapFrom(param: IdentityOperationResult): IdentityOperationResultEntity {
        return {
            id: param.getId(),
            status: param.getStatus(),
            totalTime: param.getTotaltime(),
        }
    }
    override mapTo(param: IdentityOperationResultEntity): IdentityOperationResult {
        let biographic = new IdentityOperationResult()
        return biographic;
    }
}
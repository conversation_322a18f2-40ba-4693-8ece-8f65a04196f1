// source: manager/settings/common/license.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
goog.exportSymbol('proto.ArrayOfLicenses', null, global);
goog.exportSymbol('proto.LicenseGrpcModel', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.LicenseGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.LicenseGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.LicenseGrpcModel.displayName = 'proto.LicenseGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfLicenses = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfLicenses.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfLicenses, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfLicenses.displayName = 'proto.ArrayOfLicenses';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.LicenseGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.LicenseGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.LicenseGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.LicenseGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
guid: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
serialnumber: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
macstring: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
ipstring: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.LicenseGrpcModel}
 */
proto.LicenseGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.LicenseGrpcModel;
  return proto.LicenseGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.LicenseGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.LicenseGrpcModel}
 */
proto.LicenseGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGuid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSerialnumber(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMacstring(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setIpstring(value);
      break;
    case 9:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.LicenseGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.LicenseGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.LicenseGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.LicenseGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string guid = 1;
 * @return {string}
 */
proto.LicenseGrpcModel.prototype.getGuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.setGuid = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.clearGuid = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.LicenseGrpcModel.prototype.hasGuid = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string serialNumber = 2;
 * @return {string}
 */
proto.LicenseGrpcModel.prototype.getSerialnumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.setSerialnumber = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.clearSerialnumber = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.LicenseGrpcModel.prototype.hasSerialnumber = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string macString = 3;
 * @return {string}
 */
proto.LicenseGrpcModel.prototype.getMacstring = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.setMacstring = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.clearMacstring = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.LicenseGrpcModel.prototype.hasMacstring = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string ipString = 4;
 * @return {string}
 */
proto.LicenseGrpcModel.prototype.getIpstring = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.setIpstring = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.clearIpstring = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.LicenseGrpcModel.prototype.hasIpstring = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 9;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.LicenseGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 9));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.LicenseGrpcModel} returns this
*/
proto.LicenseGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.LicenseGrpcModel} returns this
 */
proto.LicenseGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.LicenseGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 9) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfLicenses.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfLicenses.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfLicenses.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfLicenses} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfLicenses.toObject = function(includeInstance, msg) {
  var f, obj = {
licensesList: jspb.Message.toObjectList(msg.getLicensesList(),
    proto.LicenseGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfLicenses}
 */
proto.ArrayOfLicenses.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfLicenses;
  return proto.ArrayOfLicenses.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfLicenses} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfLicenses}
 */
proto.ArrayOfLicenses.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.LicenseGrpcModel;
      reader.readMessage(value,proto.LicenseGrpcModel.deserializeBinaryFromReader);
      msg.addLicenses(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfLicenses.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfLicenses.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfLicenses} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfLicenses.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLicensesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.LicenseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated LicenseGrpcModel licenses = 1;
 * @return {!Array<!proto.LicenseGrpcModel>}
 */
proto.ArrayOfLicenses.prototype.getLicensesList = function() {
  return /** @type{!Array<!proto.LicenseGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.LicenseGrpcModel, 1));
};


/**
 * @param {!Array<!proto.LicenseGrpcModel>} value
 * @return {!proto.ArrayOfLicenses} returns this
*/
proto.ArrayOfLicenses.prototype.setLicensesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.LicenseGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.LicenseGrpcModel}
 */
proto.ArrayOfLicenses.prototype.addLicenses = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.LicenseGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfLicenses} returns this
 */
proto.ArrayOfLicenses.prototype.clearLicensesList = function() {
  return this.setLicensesList([]);
};


goog.object.extend(exports, proto);

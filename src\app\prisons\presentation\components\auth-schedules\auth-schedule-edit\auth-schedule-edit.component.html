<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<div [formGroup]="form" class="dialog-container">
    @if( operationType == opType.INSERT ){
        <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" (activeIndexChange)="onActiveIndexChange($event)"></p-steps>
    }@else{
        <div class="flex justify-content-center">
            <p-selectButton
                [options]="stepOptions"
                formControlName="stepOptions"
                severity="secondary"
                multiple="false"
                allowEmpty="false"
                optionLabel="key"
                optionValue="value"
                dataKey="value"
                (onChange)="onActiveTabIndexChange($event)">
            </p-selectButton>
        </div>
    }
    <div *ngIf="activeIndex==0; else elseBlock">
        <div class="form-div">
            <div class="flex justify-content-end requiredFieldsLabel">
                {{ 'content.requiredFields' | translate }} <span *ngIf="isRequiredField('authScheduleName')" class="requiredStar">*</span>
            </div>
            <div class="col-12">
                <label for="authScheduleName" class="label-form"> {{ 'content.name' | translate }} <span class="requiredStar">*</span></label>
                <input type="text" formControlName="authScheduleName" id="authScheduleName"
                [ngClass]="!isValid('authScheduleName') && form.controls['authScheduleName'].touched? 'ng-invalid ng-dirty':'' " 
                pInputText [(ngModel)]="value" />
                <small *ngIf="!isValid('authScheduleName') && form.controls['authScheduleName'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
            </div>
            <div class="col-12">
                <label for="authScheduleDescription" class="label-form">{{ 'content.description' | translate }} <span *ngIf="isRequiredField('authScheduleDescription')" class="requiredStar">*</span></label>
                <textarea rows="3" cols="12" pInputTextarea formControlName="authScheduleDescription"></textarea>
            </div>
            <div class="col-12">
                <label class="label-form">{{ 'content.role' | translate }} <span *ngIf="isRequiredField('authScheduleRoleId')" class="requiredStar">*</span></label>
                <p-multiSelect appendTo="body" [style]="{'width':'100%'}" formControlName="authScheduleRoleId" [options]="listRoles"
                    [(ngModel)]="selectedRole" dataKey="id" optionLabel="name"
                    [showClear]="true" [filter]="true" filterBy="name"
                    placeholder="{{ 'content.select' | translate }}"
                    [ngClass]="!isValid('authScheduleRoleId') && form.controls['authScheduleRoleId'].touched? 'ng-invalid ng-dirty':'' " />
                <small *ngIf="!isValid('authScheduleRoleId') && form.controls['authScheduleRoleId'].touched" class="mb-1"
                    style="color:red">{{ 'content.field_required' | translate }}</small>
            </div>
            <div class="col-12">
                <label class="label-form">{{ 'content.location' | translate }} <span *ngIf="isRequiredField('authScheduleLocationId')" class="requiredStar">*</span></label>
                <!-- <p-dropdown appendTo="body" [style]="{'width':'100%'}" formControlName="authScheduleLocationId" [options]="listLocations"
                    [(ngModel)]="selectedLocation" dataKey="id" optionLabel="name"
                    [showClear]="true" [filter]="true" filterBy="name"
                    placeholder="{{ 'content.select' | translate }}"
                    [ngClass]="!isValid('authScheduleLocationId') && form.controls['authScheduleLocationId'].touched? 'ng-invalid ng-dirty':'' " /> -->
                <p-treeSelect appendTo="body"
                    class="md:w-20rem w-full"
                    containerStyleClass="w-full"
                    formControlName="authScheduleLocationId"
                    [options]="listLocations"
                    [(ngModel)]="selectedLocation"
                    selectionMode="checkbox"
                    [filter]="true"
                    placeholder="{{ 'content.select' | translate }}"/>
                <small *ngIf="!isValid('authScheduleLocationId') && form.controls['authScheduleLocationId'].touched" class="mb-1"
                    style="color:red">{{ 'content.field_required' | translate }}</small>
            </div>
        </div>
    </div>
    <ng-template #elseBlock>
        <div class="form-div">
            <div class="form-component">
                <label for="authScheduleType" class="label-form">{{ 'content.type' | translate }}</label>
                <p-dropdown
                    appendTo="body"
                    [options]="listScheduleTypes"
                    placeholder="{{'content.select' | translate}}"
                    optionLabel="value"
                    [(ngModel)]="selectedSchedule"
                    formControlName="authScheduleType"
                    id="authScheduleType"
                    dataKey="key"
                    [ngClass]="!isValid('authScheduleType') && form.controls['authScheduleType'].touched? 'ng-invalid ng-dirty':'' "
                    >
                    <ng-template pTemplate="selectedItem">
                        <div class="flex align-items-center gap-2" *ngIf="selectedSchedule">
                            <div>{{selectedSchedule.value}}</div>
                        </div>
                    </ng-template>
                    <ng-template let-scheduleItem pTemplate="item">
                        <div class="flex align-items-center gap-2">
                            <div>{{ scheduleItem.value }}</div>
                        </div>
                    </ng-template>
                </p-dropdown>
                <small *ngIf="!isValid('authScheduleType') && form.controls['authScheduleType'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
            </div>
        </div>
        @if (selectedScheduleType=='ALL' || selectedScheduleType=='WORKING_DAYS') {
            <div class="form-div">
                <div class="flex flex-row flex-wrap">
                    <div class="grid">
                        <div class="col-6">
                            <label for="scheduleInit" class="label-form"> {{ 'content.start' | translate }}</label>
                            <p-calendar
                                appendTo="body"
                                id="scheduleInit" formControlName="scheduleInit" timeOnly="true" dataType="string" [readonlyInput]="true" 
                                [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay"
                                [ngClass]="!isValid('scheduleInit') && form.controls['scheduleInit'].touched? 'ng-invalid ng-dirty':'' "
                            >
                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                </ng-template>
                            </p-calendar>
                            <small *ngIf="!isValid('scheduleInit') && form.controls['scheduleInit'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-6">
                            <label for="scheduleEnd" class="label-form"> {{ 'content.end' | translate }}</label>
                            <p-calendar
                                appendTo="body"
                                id="scheduleEnd" formControlName="scheduleEnd"
                                timeOnly="true" dataType="string" [readonlyInput]="true"
                                [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay"
                                [ngClass]="!isValid('scheduleEnd') && form.controls['scheduleEnd'].touched? 'ng-invalid ng-dirty':'' "
                            >
                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                </ng-template>
                            </p-calendar>
                            <small *ngIf="!isValid('scheduleEnd') && form.controls['scheduleEnd'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="grid mt-3 mb-1 gap-1">
                        <div class="flex col-2 justify-content-center align-content-center">
                            <p-inputSwitch formControlName="showDateInterval" (onChange)="showDateInterval()" />
                        </div>
                        <div class="flex col-9 justify-content-start align-content-center align-items-center">
                            {{ "category.enable_date_range" | translate}}
                        </div>
                    </div>
                </div>
                <div>
                    <div [style]="{'display': showDateRange?'block':'none'}">
                        <div class="grid">
                            <div class="col-6">
                                <label for="dateInit" class="label-form"> {{ 'content.start' | translate }}</label>
                                    <p-calendar
                                    appendTo="body"
                                    id="dateInit" formControlName="dateInit" dataType="string" [readonlyInput]="true"
                                    [iconDisplay]="'input'" [showIcon]="true"
                                    [ngClass]="!isValid('dateInit') && form.controls['dateInit'].touched? 'ng-invalid ng-dirty':'' "
                                ></p-calendar>
                                <small *ngIf="!isValid('dateInit') && form.controls['dateInit'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                            </div>
                            <div class="col-6">
                                <label for="dateEnd" class="label-form"> {{ 'content.end' | translate }}</label>
                                <p-calendar
                                    appendTo="body"
                                    id="dateEnd" formControlName="dateEnd" dataType="string" [readonlyInput]="true"
                                    [iconDisplay]="'input'" [showIcon]="true"
                                    [ngClass]="!isValid('dateEnd') && form.controls['dateEnd'].touched? 'ng-invalid ng-dirty':'' "
                                ></p-calendar>
                                <small *ngIf="!isValid('dateEnd') && form.controls['dateEnd'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="grid">
                        <div class="col-6">
                            <label for="maxTime" class="label-form"> {{ 'content.max_inside_time' | translate }}</label>
                            <p-calendar appendTo="body" formControlName="maxTime" id="maxTime" timeOnly="true" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay"
                            [ngClass]="!isValid('maxTime') && form.controls['maxTime'].touched? 'ng-invalid ng-dirty':'' "
                            >
                                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                    <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                </ng-template>
                            </p-calendar>
                            <small *ngIf="!isValid('maxTime') && form.controls['maxTime'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>
                </div>
            </div>
        }
        @else if (selectedScheduleType=='CUSTOM') {
            <div class="grid">
                <div class="col-6">
                    <div class="form-div">
                        @for (day of days; track $index) {
                            @if (day.key != 'SATURDAY' && day.key != 'SUNDAY') {
                                <div class="grid flex-nowrap gap-0 flex align-items-center justify-content-center">
                                    <div class="col-3">
                                        <label class="font-days">{{ day.value }}</label>
                                    </div>
                                    <div class="col-4">
                                        <p-calendar appendTo="body" id="scheduleInit" formControlName="{{day.key}}Init" id="{{day.key}}Init" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                            <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                            </ng-template>
                                        </p-calendar>
                                        <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                    <div class="col"><label class="font-days">{{ 'content.to' | translate }}</label></div>
                                    <div class="col-4">
                                        <p-calendar appendTo="body" id="scheduleInit" formControlName="{{day.key}}End" id="{{day.key}}End" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                            <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                            </ng-template>
                                        </p-calendar>
                                        <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                </div>
                            }
                        }
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-div">
                        @for (day of days; track $index) {
                            @if (day.key == 'SATURDAY' || day.key == 'SUNDAY') {
                                <div class="grid flex-nowrap gap-0 flex align-items-center justify-content-center">
                                    <div class="col-3">
                                        <label class="font-days">{{ day.value }}</label>
                                    </div>
                                    <div class="col-4">
                                        <p-calendar appendTo="body" id="scheduleInit" formControlName="{{day.key}}Init" id="{{day.key}}Init" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                            <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                            </ng-template>
                                        </p-calendar>
                                        <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                    <div class="col"><label class="font-days">{{ 'content.to' | translate }}</label></div>
                                    <div class="col-4">
                                        <p-calendar appendTo="body" id="scheduleInit" formControlName="{{day.key}}End" id="{{day.key}}End" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                                            <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                                                <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                                            </ng-template>
                                        </p-calendar>
                                        <small *ngIf="!isValid( day.key + 'Init' ) && form.controls[day.key + 'Init'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
            <div class="grid mt-3 mb-1 gap-1">
                <div class="flex col-1 justify-content-center align-content-end align-items-center">
                    <p-inputSwitch formControlName="showDateIntervalCustom" (onChange)="showDateIntervalCustom()" />
                </div>
                <div class="flex col-9 justify-content-start align-items-center">
                    {{ "category.enable_date_range" | translate}}
                </div>
            </div>
            <div [style]="{'display': showDateRangeCustom?'block':'none'}">
                <div class="grid">
                    <div class="col-3">
                        <label for="dateInitCustom" class="label-form"> {{ 'content.start' | translate }}</label>
                        <p-calendar appendTo="body" id="dateInitCustom" formControlName="dateInitCustom" dataType="string" [readonlyInput]="true" 
                        [iconDisplay]="'input'" [showIcon]="true"
                        [ngClass]="!isValid('dateInitCustom') && form.controls['dateInitCustom'].touched? 'ng-invalid ng-dirty':'' "
                        ></p-calendar>
                        <small *ngIf="!isValid('dateInitCustom') && form.controls['dateInitCustom'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                    <div class="col-3">
                        <label for="dateEndCustom" class="label-form"> {{ 'content.end' | translate }}</label>
                        <p-calendar appendTo="body" id="dateEndCustom" formControlName="dateEndCustom" dataType="string" [readonlyInput]="true" 
                        [iconDisplay]="'input'" [showIcon]="true"
                        [ngClass]="!isValid('dateEndCustom') && form.controls['dateEndCustom'].touched? 'ng-invalid ng-dirty':'' "
                        ></p-calendar>
                        <small *ngIf="!isValid('dateEndCustom') && form.controls['dateEndCustom'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>
            </div>
            <div class="grid">
                <div class="col-6">
                    <label for="maxTime" class="label-form"> {{ 'content.max_inside_time' | translate }}</label>
                    <p-calendar appendTo="body" formControlName="maxTime" id="maxTime" timeOnly="true" dataType="string" [readonlyInput]="true" [iconDisplay]="'input'" [showIcon]="true" inputId="templatedisplay">
                        <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                            <i class="pi pi-angle-down pointer-events-none" (click)="clickCallBack($event)"></i>
                        </ng-template>
                    </p-calendar>
                </div>
            </div>
        }
    </ng-template>
</div>
<div>
    @if (activeIndex==0) {
        <div class="footer-buttons-container">
            <p-button label="{{ 'cancel' | translate }}"
                (onClick)="onClose()"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
            <p-button label="{{ 'next' | translate }}" icon="pi pi-angle-right" iconPos="right"
                (onClick)="onNext()"
                [disabled]="!isValid('authScheduleName') || !isValid('authScheduleName') || !isValid('authScheduleLocationId') || !isValid('authScheduleLocationId')? true : false" 
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
        </div>
    }@else{
        <div class="footer-buttons-container">
            <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left" 
            (onClick)="onBack()"
            [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
            <p-button 
            [disabled]="!canReadAndWrite || !userIsVerified"
            [label]="createUpdateButtonTitle"  iconPos="right" 
            [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
            (onClick)="saveAuthSchedule()">
            </p-button>
        </div>
    }
</div>
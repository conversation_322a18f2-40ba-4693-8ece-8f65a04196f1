
import { environment } from "src/environments/environment";
import { Injectable } from "@angular/core";
import { BiometricMapper } from "../mapper/biometric.mapper";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { HttpClient } from "@angular/common/http";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { BiometricRepository } from "../../domain/repository/biometric.repository";
import { ReplaceAttributesRequestEntity } from "../../domain/entity/replace-attributes-request.entity";
import { CoreBiometricServiceClient } from "src/verazial-common-frontend/core/generated/api-binder/ms-biometric/BiometricServiceClientPb";

@Injectable({
    providedIn: 'root',
})
export class BiometricRepositoryImpl extends BiometricRepository {

    biometricMapped = new BiometricMapper()

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override replaceAttributesRequest(request: ReplaceAttributesRequestEntity): Promise<void> {
        
        let coreBiographicServiceClient = new CoreBiometricServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        const requestGrpc = this.biometricMapped.mapTo(request);
        
        return new Promise<void>((resolve, reject) => {
            coreBiographicServiceClient.replaceAttributes(requestGrpc, null, (err, response) => {
                if (err) {
                    reject();
                } else {
                    resolve();
                }
            });
        });
    }
}
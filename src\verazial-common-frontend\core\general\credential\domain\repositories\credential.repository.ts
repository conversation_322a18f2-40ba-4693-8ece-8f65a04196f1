import { Observable } from "rxjs";
import { CredentialEntity } from "../entity/credential.entity";

export abstract class CredentialRepository {
    abstract addCredentials(credentials: CredentialEntity): Observable<any>;
    abstract getCredentialsByNumId(param: { numId: string }): Observable<any>;
    abstract getCredentialsByNumIdAndSubjectAppId(param: { numId: string, subjectAppId: string }): Observable<any>;
    abstract updateCredentialsByNumId(param: { numId: string, password: string }): Observable<any>;
    abstract deleteCredentialsByNumId(param: { numId: string }): Observable<any>;
    abstract deleteCredentialsById(param: { id: string }): Observable<any>;
    abstract deleteCredentialByNumIdAndSubjectAppId(param: { numId: string, subjectAppId: string }): Observable<any>;
    abstract updateCredentialPasswordByNumIdAndSubjectAppId(param: { numId: string, subjectAppId: string, password: string }): Observable<any>;
}
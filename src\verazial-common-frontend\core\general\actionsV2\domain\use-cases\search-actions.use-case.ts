import { ActionEntity } from "../entity/action.entity";
import { SearchActionsRequestEntity } from "../entity/search-actions-request.entity";
import { ActionsV2Repository } from "../repository/actionsV2.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";



export class SearchActionsUseCase implements UseCaseGrpc<SearchActionsRequestEntity,ActionEntity[]>{
    constructor(private actionsV2Repository: ActionsV2Repository){}
    execute(params: SearchActionsRequestEntity): Promise<ActionEntity[]> {
        return this.actionsV2Repository.searchActions(params);
    }
}
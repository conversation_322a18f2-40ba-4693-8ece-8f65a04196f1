import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GroupConfigComponent } from './group-config/group-config.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputSwitchModule } from 'primeng/inputswitch';
import { CategoryAssignmentModule } from '../category-assignment/category-assignment.module';
import { AccordionModule } from 'primeng/accordion';

@NgModule({
  declarations: [
    GroupConfigComponent
  ],
  imports: [
    CommonModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNg */
    InputSwitchModule,
    AccordionModule,
    /* Custom */
    CategoryAssignmentModule,
  ],
  exports: [
    GroupConfigComponent
  ]
})
export class GroupConfigModule { }

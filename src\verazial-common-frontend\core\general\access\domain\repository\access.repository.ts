import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { AccessEntity } from "../entity/access.entity";

export abstract class AccessRepository {
    abstract addAccess(params: { access: AccessEntity }): Promise<AccessEntity>;
    abstract getAllAccesses(): Promise<AccessEntity[]>;
    abstract getAccessById(params: { id: number }): Promise<AccessEntity>;
    abstract deleteAccessById(params: { id: number }): Promise<SuccessResponse>;
    abstract updateAccessById(params: { access: AccessEntity }): Promise<AccessEntity>;
}
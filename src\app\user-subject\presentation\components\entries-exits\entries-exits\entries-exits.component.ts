import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from "@angular/core";
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, FilterService, MenuItem, MessageService, TreeNode } from "primeng/api";
import { Table } from "primeng/table";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { SubjectLocationEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-location.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { AuditTrailService } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { environment } from "src/environments/environment";
import { UpdateSubjectLocationUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject-location.use-case";
import { EntriesExitsEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/entries-exits.entity";
import { SearchActionsRequestEntity } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/search-actions-request.entity";
import { SearchActionsUseCase } from "src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/search-actions.use-case";
import { ActionEntity } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/action.entity";
import { EntryExitService } from "src/verazial-common-frontend/core/services/entry-exit.service";
import { EntryExitControlRoleRelationRestrictionModel, PrisonsSettingsModel } from "src/verazial-common-frontend/core/general/manager/common/models/prisons-settings.model";
import { entriesExitsDescriptionArray, entriesExitsEnum } from "src/verazial-common-frontend/core/general/subject/domain/entity/entries-exits.enum";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { GetRelatedSubjectsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-related-subjects-by-subject-id.use-case";
import { GetRelatedSubjectsByRelatedSubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-related-subjects-by-related-subject-id.use-case";
import { GetSubjectByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-id.use-case";
import { RelatedSubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/related-subject.entity";
import { GetSubjectLocationsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-location-by-subject-id.use-case";
import { GetAllAuthSchedulesUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/get-all-auth-schedules.use-case";
import { AuthScheduleEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/auth-schedule.entity";
import { GetActualTimeUseCase } from "src/verazial-common-frontend/core/general/biographic/domain/use-cases/get-actual-time.use-case";
import { parseCustomDate } from "src/verazial-common-frontend/core/util/supporting-functions";
import { GetAllRolesUseCase } from "src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { AttributeData } from "src/verazial-common-frontend/core/models/attribute-data.model";
import { WidgetResult } from "src/verazial-common-frontend/core/models/widget-result.model";
import { SortOrderAction } from "src/verazial-common-frontend/core/general/actionsV2/common/enums/sort-order-action.enum";
import { BioSignatureResult } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures/bio-signatures.component";
import { InsideEnum } from "src/verazial-common-frontend/core/models/inside.enum";
import { UpdateSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject.use-case";
import { TransferAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth.entity";
import { AuthStatus } from "src/verazial-common-frontend/core/general/prisons/common/enums/transfer-auth-status.enum";
import { EntryExitAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/entry-exit-auth/entry-exit-auth.entity";
import { EntryExitAuthType } from "src/verazial-common-frontend/core/general/prisons/common/enums/entry-exit-auth-type.enum";
import { UpdateEntryExitAuthUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/entry-exit-auth/update-entry-exit-auth.use-case";
import { formatDate } from "@angular/common";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";


export class RelatedSubjectsRoleOptions {
    roleId: number = -1;
    relatedRoleId: number = -1;
    relatedRoleName: string = '';
    options: { entity: SubjectEntity; location: SubjectLocationEntity }[] = [];
}


@Component({
    selector: 'app-entries-exits',
    templateUrl: './entries-exits.component.html',
    styleUrl: './entries-exits.component.css'
})

export class EntriesExitsComponent implements OnInit, OnChanges {

    // Inputs
    @Input() userSubject: SubjectEntity | undefined;
    @Input() userIsVerified: boolean = false;
    @Input() isVerified: boolean = false;
    @Input() isPrisoner: boolean = false;
    //@Input() userSubjectDetails: SubjectDetailEntity[] = [];
    @Input() prisonsConfig: PrisonsSettingsModel = new PrisonsSettingsModel();
    @Input() managerSettings?: GeneralSettings;
    @Input() konektorProperties?: KonektorPropertiesEntity;
    @Output() updateSubjectStatus: EventEmitter<void> = new EventEmitter<void>();

    canReadAndWrite: boolean = false;
    readOnly: boolean = false;
    isDisableSaveButton: boolean = true;

    searchValue: string | undefined;
    showNewEntryDialog: boolean = false;
    showRoleDialog: boolean = false;
    showSelectLocationsDialog: boolean = false;
    showSelectRelatedSubjectsDialog: boolean = false;
    showSelectTransferDialog: boolean = false;
    showSelectEntryExitAuthDialog: boolean = false;

    entryExitAuth: EntryExitAuthEntity | undefined;

    listOfLocations: SubjectLocationEntity[] = [];
    locationsAvailableSoon: SubjectLocationEntity[] = [];
    listOfLocationsToEntry: SubjectLocationEntity[] = [];
    entryLocation: SubjectLocationEntity | undefined;

    listOfEntries: EntriesExitsEntity[] = [];
    selectedEntries: EntriesExitsEntity[] = [];

    listOfActions: ActionEntity[] = [];

    filteredValues: any[] = [];

    access_identifier = AccessIdentifier.ENTRIES_EXITS;
    actualDate: Date = new Date();

    formGroup: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    rangeDates: Date[] | null = null;

    formGroupExit: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    rangeDatesExit: Date[] | null = null;

    entriesExitsDescriptionArray = entriesExitsDescriptionArray;

    showTime: boolean = false;
    timeEnd: Date = new Date();

    public form: FormGroup = this.fb.group({
        entryDate: [this.actualDate, Validators.required],
        //exitDate: [],
        locationId: ['', Validators.required],
        reason: [''],
        description: [''],
        type: [''],
        relatedSubjectId: [''],
        stepOptions: ['0'],
        visitTimeMax: [null],
        visitTimeActual: [null],
        transferLimitDate: [null],
        transferId: [''],
        destinyLocationId: [''],

        entryExitAuthLimitDate: [null],
        entryExitAuthId: [''],
        authCode: [''],

    });

    public dataFormLocations = this.fb.group({
        location: ["", [Validators.required]],
    });

    public dataFormRelatedSubjects = this.fb.group({
        relatedSubject: ["", [Validators.required]],
    });

    public dataFormTransfer = this.fb.group({
        transfer: ["", [Validators.required]],
    });

    public dataFormEntryExitAuth = this.fb.group({
        entryExitAuth: ["", [Validators.required]],
    });

    entry: EntriesExitsEntity | undefined;
    isNew: boolean = true;
    // Options
    lEntryDate: GenericKeyValue | undefined;
    lLocationId: GenericKeyValue | undefined;
    lActualLocation: GenericKeyValue | undefined;
    lReason: GenericKeyValue | undefined;
    lClassification: GenericKeyValue | undefined;
    lPending: GenericKeyValue | undefined;
    //lExitDate: GenericKeyValue | undefined;
    lComments: GenericKeyValue | undefined;
    // Parameters
    reasonsEntryExit = 'entry-exit-reasons-';
    reasonsEntryExitGeneral = 'entry-exit-reasons-general';

    items?: MenuItem[];
    stepOptions: AttributeData[] = [];
    activeIndex: number = 0;
    visitTimeMax: number = 0;
    entryVisitTimeMax: number = 0;
    entryVisitTimeDate: Date | undefined = undefined;
    visitTimeActual: number = 0;


    lReasonOptions: GenericKeyValue[] = [];
    lReasonRoleOptions: GenericKeyValue[] = [];
    listOfAuthSchedules: AuthScheduleEntity[] = [];
    listOfEntryExitAuths: EntryExitAuthEntity[] = [];

    lClassificationOptions: GenericKeyValue[] = [];
    lLocationOptions: any[] = [];
    defaultRegex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/

    settings: GeneralSettings | undefined;

    isLoading: boolean = false;

    //isInside: InsideEnum = false;

    entryExitRole: number = -1;
    private rejectTimeout: any;

    entryRestrictions: EntryExitControlRoleRelationRestrictionModel[] = [];

    listOfRelatedSubjectsToThisSubject: RelatedSubjectEntity[] = [];
    listOfRelatedSubjects: RelatedSubjectEntity[] = [];

    relatedSubjectsOptions: RelatedSubjectsRoleOptions[] = [];

    listOfRelatedToEntry: { entity: SubjectEntity; location: SubjectLocationEntity }[] = [];

    entryRelatedSubject: SubjectEntity | undefined;

    allRoles: RoleEntity[] = [];


    // Widget Functions
    widgetUrl: string = '';
    segmentedSearchAttributes: { name: string, value: string, secondSearch?: string }[] = [];
    userNumId: string = '';
    subjectNumId: string = '';
    verifyReady: boolean = false;
    tech: string = '';


    // Subject Info
    image: string = '';
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
    userReturnImage: string = '';

    receptionSignatureData?: BioSignatureResult;

    authSignatureData?: BioSignatureResult;

    restrictSignatureRoles: string[] = [];
    segmentedSearchSignatureRole: string = '';

    multipleRoles: boolean = false;

    listOfTransferAuths: TransferAuthEntity[] = [];

    newEntryMade: boolean = false;

    loadRelationsFromServer = false;

    entriesExitsEnum = entriesExitsEnum;


    constructor(
        private checkPermissions: CheckPermissionsService,
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        private auditTrailService: AuditTrailService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private newLocationsService: NewLocationsService,
        private updateSubjectLocationUseCase: UpdateSubjectLocationUseCase,
        private updateSubjectUseCase: UpdateSubjectUseCase,
        private searchActionsUseCase: SearchActionsUseCase,
        private entryExitService: EntryExitService,
        private getRelatedSubjectsBySubjectIdUseCase: GetRelatedSubjectsBySubjectIdUseCase,
        private getRelatedSubjectsByRelatedSubjectIdUseCase: GetRelatedSubjectsByRelatedSubjectIdUseCase,
        private getSubjectByIdUseCase: GetSubjectByIdUseCase,
        private getSubjectLocationsBySubjectIdUseCase: GetSubjectLocationsBySubjectIdUseCase,
        private getAllAuthSchedulesUseCase: GetAllAuthSchedulesUseCase,
        private getActualTimeUseCase: GetActualTimeUseCase,
        private getAllRolesUseCase: GetAllRolesUseCase,
        //private getAllTransferAuthsUseCase: GetAllTransferAuthsUseCase,
        //private getTransferAuthByIdUseCase: GetTransferAuthByIdUseCase,
        //private updateTransferAuthUseCase: UpdateTransferAuthUseCase,
        //private updateTransferAuthUserSubjectUseCase: UpdateTransferAuthUserSubjectUseCase,
        private updateEntryExitAuthUseCase: UpdateEntryExitAuthUseCase,
    ) {

        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
    }

    ngOnInit() {
        this.initializeTranslations();
        this.subscribeToLocationsAndAuths();
        this.checkUserPermissions();
        this.loadSessionSettings();
        this.loadEntriesExits();
    }

    ngOnChanges(changes: SimpleChanges) {
        // console.log("Changes");
        // console.log(changes);

        if (changes['userSubject']) {

            if(changes['userSubject'].previousValue.id == changes['userSubject'].currentValue.id)
            {
                this.ngOnInit();
                if (this.userSubject?.roles && this.userSubject?.roles?.length > 1)
                    this.multipleRoles = true;
                else
                    this.multipleRoles = false;

                if (this.userSubject && (this.userSubject?.inside == undefined || this.userSubject?.inside == ""))
                    this.userSubject.inside = InsideEnum.OUTSIDE;

                this.settimeEnd();

            }
            else
            {
                this.restart();
            }
        }
    }

    restart() {
        this.loadEntriesExits(true);
    }


    private initializeTranslations() {
        this.items = [
            { label: this.translateService.instant('content.general') },
            { label: this.translateService.instant('content.signatures') }
        ];
        this.stepOptions = [
            { key: this.translateService.instant('content.general'), value: "0" },
            { key: this.translateService.instant('content.signatures'), value: "1" },
        ];
    }

    private subscribeToLocationsAndAuths() {
        this.entryExitService.subjectLocations$.subscribe(async locations => {
            const data = await this.getActualTimeUseCase.execute();
            const date = new Date(parseCustomDate(data.id!));
            this.listOfLocations = locations.filter(location =>
                location.entryDate && location.entryDate <= date &&
                (!location.exitDate || isNaN(location.exitDate.getTime()) || location.exitDate >= date)
            );
            if (this.listOfLocations.length == 0) {
                this.locationsAvailableSoon = [...locations.filter(location =>
                    location.entryDate && location.entryDate > date &&
                    (!location.exitDate || isNaN(location.exitDate.getTime()) || location.exitDate >= date)
                )];
            }
        });

        this.entryExitService.subjectEntryExitAuths$.subscribe(async authSchedules => {
            const data = await this.getActualTimeUseCase.execute();
            const date = new Date(parseCustomDate(data.id!));
            //this.listOfEntryExitAuths = authSchedules;
            this.listOfEntryExitAuths = authSchedules.filter(authSchedule =>
                authSchedule.authStartDateTime &&
                authSchedule.authStartDateTime <= date &&
                (!authSchedule.authEndDateTime || !this.isValidDate2(authSchedule.authEndDateTime) || authSchedule.authEndDateTime >= date) &&
                authSchedule.status === AuthStatus.AUTHORIZED || authSchedule.status === AuthStatus.IN_PROGRESS
            );
        });

        this.entryExitService.listOfRelatedSubjects$.subscribe((relatedSubjects) => {
            if (!this.loadRelationsFromServer) {
                return;
            }

            const previousMap = new Map(this.listOfRelatedSubjects.map(item => [item.id, item]));
            const currentMap = new Map(relatedSubjects.map(item => [item.id, item]));

            const added = relatedSubjects.filter(item => !previousMap.has(item.id));
            const removed = this.listOfRelatedSubjects.filter(item => !currentMap.has(item.id));

            added.forEach((item) => {
                this.getSubjectByIdUseCase.execute({ id: item.relatedSubjectId! }).then(
                    async (subject) => {

                        var roles = subject.roles?.map((role) => role.id?.toString());

                        roles = roles?.filter((role) => this.entryRestrictions.find((restriction) => restriction.relatedRole === role));

                        if (roles && roles.length > 0) {
                            var locations = await this.getSubjectLocationsBySubjectIdUseCase.execute({ id: subject?.id! });
                            var actualLocation = locations.find((location) => location.actualLocation === true);

                            if (actualLocation && this.newLocationsService.isLabelParentOfKey(actualLocation.locationId!, this.konektorProperties?.locationId!, this.lLocationOptions)) {
                                roles?.forEach((role) => {
                                    var option = this.relatedSubjectsOptions.find((option) => option.relatedRoleId === Number(role));
                                    if(option)
                                    {
                                        if(option.options.find((relatedSubject) => relatedSubject.entity.id === subject.id) == undefined)
                                        {
                                            option?.options.push({ entity: subject, location: actualLocation! });
                                        }
                                    }
                                });
                            }
                        }
                    },
                    (e) => {
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_RELATED_SUBJECTS_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                        return;
                    }
                );

            });
            removed.forEach((item) => {
                this.relatedSubjectsOptions.forEach((option) => {
                    option.options = option.options.filter((relatedSubject) => relatedSubject.entity.id !== item.relatedSubjectId);
                });
                this.entryRelatedSubject = undefined;
            });
            this.listOfRelatedSubjects = relatedSubjects;
        });
    }

    private checkUserPermissions() {
        this.isLoading = true;
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);

        if (!this.canReadAndWrite) {
            this.readOnly = this.checkPermissions.hasReadPermissions(this.access_identifier);
        }
    }

    private loadSessionSettings() {
        this.settings = this.localStorageService.getSessionSettings()!;
        if (this.settings) {
            this.restrictSignatureRoles = this.settings.continued1?.prisonsSettings?.bioSignAuthRoles?.signPrisonerEntryExitRoles ?? [];
            this.segmentedSearchSignatureRole = this.settings?.continued1?.prisonsSettings?.bioSignAuthRoles?.authorizePrisonerEntryExitMainRole ?? '';
            this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptions(this.settings.continued1?.newLocations!);

            let reasonOptions = this.settings.catalogs?.filter(catalog => catalog.parameter?.includes("reasonsEntryExit"))
                .flatMap(catalog => catalog.options ? JSON.parse(catalog.options) : []) ?? [];

            this.lReasonOptions = reasonOptions.length > 0
                ? reasonOptions.map(option => ({ key: option, value: option }))
                : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }
    }

    private async loadEntriesExits(restart: boolean = false) {
        try {
            if(!restart)
                await this.getAllRoles();
            await this.getEntriesExits();
            await this.loadAuthSchedules();
            await this.loadRelations();
            /*if (this.isPrisoner) {
                await this.getTransfer();
            }*/
        } catch (e) {
            this.loggerService.error(e!);
        } finally {
            this.isLoading = false;
        }
    }

    private async loadAuthSchedules() {
        try {
            this.listOfAuthSchedules = await this.getAllAuthSchedulesUseCase.execute({});
        } catch (e) {
            this.loggerService.error(e!);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(
                this.localStorageService.getUser().numId,
                AuditTrailActions.GET_ALL_AUTH_SCHEDULES,
                0,
                'ERROR',
                '',
                at_attributes
            );
        }
    }

    private async loadRelations() {
        await this.getRelations();
    }

    getEntriesExits(): Promise<boolean> {
        let paramSearch: SearchActionsRequestEntity = new SearchActionsRequestEntity();
        paramSearch.pageNumber = 0;
        paramSearch.pageSize = environment.maxTable;
        paramSearch.sortOrder = SortOrderAction.DESC;

        paramSearch.filters = [
            {
                condition: {
                    path: AuditTrailFields.ENTRYS_EXIT_GROUP + "." + AuditTrailFields.ACTION_TYPE,
                    value: 'EntryPrisons',
                }
            },
            {
                condition: {
                    path: AuditTrailFields.COMMON_GROUP + "." + AuditTrailFields.RECEIVER_ID,
                    value: this.userSubject?.id!,
                }
            },
        ];

        return new Promise(async (resolve) => {
            this.searchActionsUseCase.execute(paramSearch).then(
                async (searchActions) => {
                    this.listOfActions = searchActions;
                    this.listOfEntries = this.entryExitService.convertActionsToEntriesExits(this.listOfActions);
                    this.settimeEnd();
                    this.listOfEntries.forEach((entry) => {
                        this.transformEntry(entry);
                    });

                    this.listOfEntries = await this.transformSubjectsIdToNames(this.listOfEntries);

                    resolve(true);
                },
                (e) => {
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SEARCH_ACTIONS, 0, 'ERROR', '', at_attributes);
                    resolve(false);
                }

            );
        });
    }

    showNewEntryDialogFunc() {

        if (this.entryExitRole == -1) {
            if (this.userSubject?.roles?.length == 1) {
                this.entryExitRole = this.userSubject?.roles[0].id ?? -1;
            }
            else {
                this.showRoleDialog = true;
                return;
            }
        }

        if (this.entryExitRole != -1) {
            this.selectNewEntry();
        }
    }

    onAcceptSelectRole(roleId: string) {
        this.entryExitRole = Number(roleId);
        this.showRoleDialog = false;
        this.selectNewEntry();
    }

    onCancelSelectRole() {
        this.showRoleDialog = false;
    }

    selectNewEntry() {
        let entryRoleName = this.userSubject?.roles?.find(role => role.id === this.entryExitRole)?.name;

        if (this.settings) {
            let useProfileReasons = true;
            let reasonRoleOptions = this.settings.catalogs?.find((catalog) => catalog.parameter === this.reasonsEntryExit + entryRoleName)?.options;
            let reasonRoleFinalOptions: string[] = [];

            if (reasonRoleOptions)
                reasonRoleFinalOptions = JSON.parse(reasonRoleOptions) as string[];
            reasonRoleOptions = this.settings.catalogs?.find((catalog) => catalog.parameter === this.reasonsEntryExitGeneral)?.options;
            if (reasonRoleOptions)
                reasonRoleFinalOptions.push(...JSON.parse(reasonRoleOptions) as string[]);

            this.lReasonRoleOptions = reasonRoleOptions ? reasonRoleFinalOptions.map((option) => { return { key: option, value: this.getLabel(option, this.reasonsEntryExit + entryRoleName, this.reasonsEntryExitGeneral) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }

        if (this.entryExitRole.toString() == this.prisonsConfig?.prisonerProfileId) {
            this.prisonerNewEntry();
        }
        else {
            this.othersNewEntry();
        }
    }

    async othersNewEntry() {

        // Si esta dentro le ofrezco salir
        if (this.userSubject?.inside === InsideEnum.INSIDE) {
            var roleId = this.userSubject?.roles?.find(role => role.id === Number(this.entryExitRole))?.name;

            var entriesByRole = this.listOfEntries.filter((entry) => entry.roleId == roleId);
            var lastEntry = entriesByRole[0];

            var lastAction = this.listOfActions[0];
            var lastActionlocation = lastAction?.data?.findKeyValue(AuditTrailFields.LOCATION_ID, AuditTrailFields.ENTRYS_EXIT_GROUP);
            var location = this.listOfLocations.find((location) => location.locationId == lastActionlocation);

            if (lastEntry.relatedSubject) {
                var subjectId = lastAction?.data?.findKeyValue(AuditTrailFields.RELATED_SUBJECT_ID, AuditTrailFields.ENTRYS_EXIT_GROUP);
                if (subjectId) {
                    var relatedSubject = this.listOfRelatedSubjects.find((relatedSubject) => relatedSubject.relatedSubjectId == subjectId);
                    if (relatedSubject) {
                        this.entryRelatedSubject = await this.getSubjectByIdUseCase.execute({ id: relatedSubject.relatedSubjectId! });
                    }
                }
            }

            if (location)
                this.showEntryDialog(location, entriesExitsEnum.EXIT);
            else if (lastActionlocation != undefined) {

                var fakeLocation = new SubjectLocationEntity();
                fakeLocation.locationId = lastActionlocation;
                this.showEntryDialog(fakeLocation, entriesExitsEnum.EXIT);

            }
            else {
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.exitLocationNotFound')
                });
            }
            return;
        }
        else {
            //Si no esta dentro, ver si puede entrar

            //Comprobar si tiene alguna restriccion de relación

            var restriction = this.relatedSubjectsOptions.find((restriction) => restriction.roleId === this.entryExitRole);

            if (restriction) {

                if (restriction.options.length == 0) {
                    this.messageService.add({
                        severity: 'warn',
                        summary: this.translateService.instant('messages.warning'),
                        detail: this.translateService.instant('messages.noRelatedSubjectsOfEntryRole') + restriction.relatedRoleName
                    });
                    this.onCancelDialog();

                    return;
                }

                //Abrir ventana de seleccionar sujeto cuando tiene varias opciones
                if (restriction.options.length > 1) {
                    this.showSelectRelatedSubjectsDialog = true;
                    this.listOfRelatedToEntry = restriction.options;
                    return;
                }
                else {
                    //Si solo tiene una opción, mostrar pantalla de entrada
                    this.entryRelatedSubject = restriction.options[0].entity;
                    this.checkEntryToLocationNoPPLAndShow(restriction.options[0].location);
                }


            }
            else {

                var centerLocations = this.listOfLocations.filter(location => this.newLocationsService.isLabelParentOfKey(location.locationId!, this.konektorProperties?.locationId!, this.lLocationOptions));

                if (centerLocations.length == 0) {
                    if (this.locationsAvailableSoon.length > 0) {
                        this.messageService.add({
                            severity: 'warn',
                            summary: this.translateService.instant('messages.warning'),
                            detail: this.translateService.instant(this.locationsAvailableSoon.length == 1 ? 'messages.location_available_in_a_future_date' : 'messages.locations_available_in_a_future_date')
                                .replace('{0}', this.locationsAvailableSoon.length.toString())
                                .replace('{1}', this.locationsAvailableSoon.map(location => formatDate(location.entryDate!, this.translateService.instant('dateFormatLong'), this.translateService.instant('locale'))).join(', '))
                        });
                    }
                    else {
                        this.messageService.add({
                            severity: 'warn',
                            summary: this.translateService.instant('messages.warning'),
                            detail: this.translateService.instant('messages.noLocationToEnterHere')
                        });
                    }
                    return;
                }
                else if (centerLocations.length == 1) {
                    var pendingLocation = centerLocations[0];

                    this.checkEntryToLocationNoPPLAndShow(pendingLocation)
                }
                else {
                    this.listOfLocationsToEntry = centerLocations;
                    this.showSelectLocationsDialog = true;
                    //Abrir ventana de seleccionar localización
                }

            }
        }
    }

    private async validateEntryToLocation(location: SubjectLocationEntity): Promise<boolean> {
        const listFilterByRole = this.listOfAuthSchedules.filter((authSchedule) =>
            authSchedule.roleId?.includes(this.entryExitRole) ?? false
          );

        if (listFilterByRole.length == 0) {
            this.messageService.add({
                severity: 'warn',
                summary: this.translateService.instant('messages.warning'),
                detail: this.translateService.instant('messages.noAuthScheduleForRole')
            });

            this.onCancelDialog();
            return false;
        }

        try {
            const data = await this.getActualTimeUseCase.execute();
            const date = new Date(parseCustomDate(data.id!));
            const locationPath = this.newLocationsService.getKeysPath(location.locationId!, this.lLocationOptions);


            const listAuthFilterByLocation = locationPath!
            .map((locationId) =>
                listFilterByRole.find((auth) =>
                    auth.locationId?.includes(locationId) ?? false
                )
            )
            .filter((auth): auth is AuthScheduleEntity => !!auth);


            if (listAuthFilterByLocation.length == 0) {
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.noAuthScheduleForLocationAndRole')
                });

                this.onCancelDialog();
                return false;
            }

            for (const locationId of locationPath || []) {

                const auth = listAuthFilterByLocation.find((auth) => auth.locationId?.includes(locationId));

                if (auth && this.entryExitService.isValidEntry(date, auth)) {
                    this.visitTimeMax = auth.authScheduleDetail?.maxTime!;
                    return true;
                }
            }

            this.messageService.add({
                severity: 'warn',
                summary: this.translateService.instant('messages.warning'),
                detail: this.translateService.instant('messages.noValidAuthScheduleForLocationAndRole')
            });

            this.onCancelDialog();
            return false;
        } catch (e) {
            this.loggerService.error('Error Retrieving actual time:')
            this.loggerService.error(e!)
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction('NO_SUBJECT_ID', AuditTrailActions.GET_ACTUAL_TIME, 0, 'ERROR', '', at_attributes);
            return false;
        }
    }

    checkEntryToLocationNoPPLAndShow(location: SubjectLocationEntity) {
        this.validateEntryToLocation(location).then((isValid) => {
            if (isValid) {
                this.showEntryDialog(location, entriesExitsEnum.ENTRY);
            }
        });
    }


    async getAllRoles(): Promise<void> {
        await this.getAllRolesUseCase.execute().then(
            (data) => {
                data.forEach(role => {
                    this.allRoles.push({
                        id: role.id,
                        name: role.name,
                        level: role.level,
                        type: role.type,
                        description: role.description,
                        showInMenu: role.showInMenu,
                        createdAt: undefined,
                        updatedAt: undefined,
                    });
                });
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
            },
        );
    }

    /*async getTransfer(): Promise<void> {
        try {
            const data = await this.getAllTransferAuthsUseCase.execute({});

            const dataDate = await this.getActualTimeUseCase.execute();
            const date = new Date(parseCustomDate(dataDate.id!));

            for (const transfer of data) {
                const path = this.newLocationsService.getKeysPath(this.userSubject!.locationId!, this.lLocationOptions);
                if (path?.includes(transfer.originLocationId!) &&
                    transfer.authRegistrationDate &&
                    transfer.authExpirationDate &&
                    transfer.authRegistrationDate <= date &&
                    transfer.authExpirationDate >= date &&
                    transfer.authUserNumId &&
                    !transfer.isCancelled &&
                    !transfer.isCompleted
                ) {
                    try {

                        const transferAuth = await this.getTransferAuthByIdUseCase.execute({ id: transfer.id! });
                        if (transferAuth.listOfPrisoners?.some((prisoner) => prisoner.subjectId == this.userSubject?.id && prisoner.actualArrivalTech == "")) {
                            this.listOfTransferAuths.push(transferAuth);
                        }
                    } catch (error) {
                        console.error(error);
                    }
                }
            }
        } catch (error) {
            this.loggerService.error(error!);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
            this.auditTrailService.registerAuditTrailAction(
                this.localStorageService.getUser().numId,
                AuditTrailActions.GET_ALL_TRANSFER_AUTHORIZATIONS,
                0,
                'ERROR',
                '',
                at_attributes
            );
        }
    }*/

    async getRelations(): Promise<void> {

        var roles = this.userSubject?.roles?.map((role) => role.id?.toString());
        this.prisonsConfig.entryExitControlRoleRelationRestrictions?.forEach((restriction) => {

            if (roles?.includes(restriction.entryRole)) {
                this.entryRestrictions.push(restriction);
            }
        });

        if (this.entryRestrictions.length == 0) {
            return Promise.resolve();
        }
        else {

            this.entryRestrictions.forEach((restriction) => {
                var relatedRoleName = this.allRoles.find((role) => role.id == restriction.relatedRole)?.name;
                this.relatedSubjectsOptions.push({ roleId: Number(restriction.entryRole), relatedRoleId: Number(restriction.relatedRole), relatedRoleName: relatedRoleName!, options: [] });
            });
        }

        try {
            // Fetch related subjects by subject ID
            const relatedSubjects = await this.getRelatedSubjectsBySubjectIdUseCase.execute({ id: this.userSubject?.id! });
            this.listOfRelatedSubjects = relatedSubjects;

            // Fetch related subjects by related subject ID
            const relatedSubjectsToThisSubject = await this.getRelatedSubjectsByRelatedSubjectIdUseCase.execute({ id: this.userSubject?.id! });
            this.listOfRelatedSubjectsToThisSubject = relatedSubjectsToThisSubject;

            // Filter visitors
            this.listOfRelatedSubjects = this.listOfRelatedSubjects.filter((rs) => rs.isVisitor === true);
            this.listOfRelatedSubjectsToThisSubject = this.listOfRelatedSubjectsToThisSubject.filter((rs) => rs.isVisitor === true);

            this.listOfRelatedSubjectsToThisSubject = this.listOfRelatedSubjectsToThisSubject.map((rs) => ({
                ...rs,
                relatedSubjectId: rs.subjectId
            }));

            this.listOfRelatedSubjects.push(...this.listOfRelatedSubjectsToThisSubject);
            this.listOfRelatedSubjects.forEach((relatedSubject) => {
                this.getSubjectByIdUseCase.execute({ id: relatedSubject.relatedSubjectId! }).then(
                    async (subject) => {

                        var roles = subject.roles?.map((role) => role.id?.toString());

                        roles = roles?.filter((role) => this.entryRestrictions.find((restriction) => restriction.relatedRole === role));

                        if (roles && roles.length > 0) {
                            var locations = await this.getSubjectLocationsBySubjectIdUseCase.execute({ id: subject?.id! });
                            var actualLocation = locations.find((location) => location.actualLocation === true);

                            if (actualLocation && this.newLocationsService.isLabelParentOfKey(actualLocation.locationId!, this.konektorProperties?.locationId!, this.lLocationOptions)) {
                                roles?.forEach((role) => {
                                    this.relatedSubjectsOptions.find((option) => option.relatedRoleId === Number(role))?.options.push({ entity: subject, location: actualLocation! });
                                });
                            }
                        }
                    },
                    (e) => {
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_RELATED_SUBJECTS_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                        return;
                    }
                ).finally(() => {

                    this.loadRelationsFromServer = true;
                    //this.loggerService.debug(this.relatedSubjectsOptions);

                });
            });
        } catch (e) {
            // Handle errors
            this.loadRelationsFromServer = true;
            this.loggerService.error(e!);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_RELATED_SUBJECTS_BY_RELATED_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
        }
    }

    prisonerNewEntry() {

        const hasEntries = this.listOfEntries.length > 0;
        const lastMovement = hasEntries ? this.listOfEntries[0] : null;


        //VAN A HACERLE UN INGRESO DEFINITIVO PORQUE NO TIENE NINGUNA ENTRADA O LO ULTIMO FUE UNA SALIDA DEFINITIVA
        if (!hasEntries || lastMovement?.type === entriesExitsDescriptionArray.find(item => item.key === entriesExitsEnum.DEF_EXIT)?.value) {
            if (this.listOfLocations.length > 0) {
                const pendingLocation = this.listOfLocations.find(location => location.pending);
                if (!pendingLocation) {
                    this.messageService.add({
                        severity: 'warn',
                        summary: this.translateService.instant('messages.warning'),
                        detail: this.translateService.instant('messages.needLocationToEntry')
                    });
                    return;
                }

                if (!this.newLocationsService.isLabelParentOfKey(pendingLocation.locationId!, this.konektorProperties?.locationId!, this.lLocationOptions)) {
                    this.messageService.add({
                        severity: 'warn',
                        summary: this.translateService.instant('messages.warning'),
                        detail: this.translateService.instant('messages.noEntryOrExitUserLocation') + ": " + this.konektorProperties!.locationId
                    });
                    return;
                }

                this.showEntryDialog(pendingLocation, entriesExitsEnum.DEF_ENTRY, false);

            } else {
                if (this.locationsAvailableSoon.length > 0) {
                    this.messageService.add({
                        severity: 'warn',
                        summary: this.translateService.instant('messages.warning'),
                        detail: this.translateService.instant(this.locationsAvailableSoon.length == 1 ? 'messages.location_available_in_a_future_date' : 'messages.locations_available_in_a_future_date')
                        .replace('{0}', this.locationsAvailableSoon.length.toString())
                        .replace('{1}', this.locationsAvailableSoon.map(location => formatDate(location.entryDate!, this.translateService.instant('dateFormatLong'), this.translateService.instant('locale'))).join(', '))
                    });
                }
                else {
                    this.messageService.add({
                        severity: 'warn',
                        summary: this.translateService.instant('messages.warning'),
                        detail: this.translateService.instant('messages.needLocationToEntry')
                    });
                }
            }
        }
        //LO ULTIMO FUE UNA SALIDA DE TRASLADO POR LO QUE TIENE QUE HACER UNA ENTRADA DE TRASLADO
        /*else if (lastMovement?.type === entriesExitsDescriptionArray.find(item => item.key === entriesExitsEnum.TRANSFER_EXIT)?.value) {

            //Comprobar si tiene una localización válida para entrar

            //los PPL solo pueden tener una pending
            var pendingLocation = this.listOfLocations.find(location => location.pending);

            if(!pendingLocation)
            {
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.noLocationToEnterHere')
                });
                return;
            }

            var lastAction = this.listOfActions[0];
            var lastActionlocation = lastAction?.data?.findKeyValue(AuditTrailFields.DESTINY_LOCATION_ID, AuditTrailFields.ENTRYS_EXIT_GROUP);

            if( this.newLocationsService.isParentOrSame(this.lLocationOptions, lastActionlocation!, pendingLocation.locationId!))
            {
                this.showEntryDialog(pendingLocation, entriesExitsEnum.TRANSFER_ENTRY, false, lastMovement?.transferId, undefined,undefined);
            }
            else {
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.noLocationToEnterTransfer')
                });
            }


        }*/
        //ESTA FUERA POR SALIDA TEMPORAL POR LO QUE TIENE QUE HACER UNA ENTRADA
        else if (lastMovement?.type === entriesExitsDescriptionArray.find(item => item.key === entriesExitsEnum.EXIT)?.value){

            var lastAction = this.listOfActions[0];

            var lastActionlocation = lastAction?.data?.findKeyValue(AuditTrailFields.LOCATION_ID, AuditTrailFields.ENTRYS_EXIT_GROUP);

            var location = this.listOfLocations.find((location) => location.locationId == lastActionlocation);

            if(this.newLocationsService.isLabelParentOfKey(location?.locationId!, this.konektorProperties?.locationId!, this.lLocationOptions))
            {
                this.showEntryDialog(location!, entriesExitsEnum.ENTRY, true, undefined, lastMovement!.entryExitAuthLimitDate, lastMovement!.entryExitAuthId);
            }
            else {
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.subjectMustReturnToSameLocation')
                });
            }

        }
        else {
            //ESTA DENTRO POR LO QUE COMPROBAR TRASLADOS O PERMISOS DE SALIDA

            this.loggerService.debug(this.listOfEntryExitAuths);
            //Comprobando traspasos
            if(this.listOfTransferAuths.length > 0) {
                if(this.listOfTransferAuths.length == 1) {
                    this.showExitTransferDialog(this.listOfTransferAuths[0]);
                }
                else {
                    this.showSelectTransferDialog = true;

                }

            }
            else if (this.listOfEntryExitAuths.length > 0)
            {
                if(this.listOfEntryExitAuths.length == 1) {
                    this.entryExitAuth = this.listOfEntryExitAuths[0];
                    this.showEntryExitAuthDialog(this.entryExitAuth);
                }
                else {
                    this.showSelectEntryExitAuthDialog = true;
                }

            }
            else {


                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('messages.warning'),
                    detail: this.translateService.instant('messages.noEntryOrExitUser')
                });
            }
        }
    }

    showEntryExitAuthDialog(auth: EntryExitAuthEntity) {


        console.log(auth);

        this.authSignatureData = {
            id: auth.authUserId,
            numId: auth.authUserNumId,
            tech: auth.authUserSignatureTech,
            date: auth.authUserSignatureDate,
        }

        console.log(this.authSignatureData);

        this.showNewEntryDialog = true;
        this.activeIndex = 0;
        this.form.controls['stepOptions'].enable();
        this.form.controls['stepOptions'].setValue(this.activeIndex.toString());

        this.entry = new EntriesExitsEntity();
        this.entry.subjectId = this.userSubject?.id!;
        this.entryLocation = this.listOfLocations.find(location => location.actualLocation);
        this.entry.locationId = this.entryLocation?.locationId!;

        this.entry.roleId = this.entryExitRole.toString();

        if(auth.type == EntryExitAuthType.EXIT)
            this.entry.type = entriesExitsEnum.EXIT;
        else //if (auth.type == EntryExitAuthType.DFN_EXIT)
            this.entry.type = entriesExitsEnum.DEF_EXIT;

        this.entry.entryDate = new Date();
        this.entry.maxTimeVisit = auth.actualEndDateTime ? auth.actualEndDateTime.getTime() / 60000 : 0;
        this.entry.entryExitAuthId = auth.id;
        if(auth.type == EntryExitAuthType.EXIT)
            this.entry.entryExitAuthLimitDate = auth.authEndDateTime;

        this.form.get('entryDate')?.setValue(this.entry.entryDate);
        this.form.enable();
        this.form.get('entryDate')?.disable();
        this.form.get('locationId')?.setValue(this.newLocationsService.getNameAndTree(this.entry.locationId!, this.lLocationOptions));
        this.form.get('locationId')?.disable();
        this.form.get('type')?.setValue(entriesExitsDescriptionArray.find(e => e.key === this.entry!.type));
        this.form.get('type')?.disable();

        this.form.get('reason')?.setValue(auth.authReason);
        this.form.get('reason')?.disable();

        this.form.get('authCode')?.setValue(auth.id);
        this.form.get('authCode')?.disable();

        if(this.entry.type != entriesExitsEnum.DEF_EXIT)
        {
            this.form.get('entryExitAuthLimitDate')?.setValue(this.entry.entryExitAuthLimitDate);
            this.form.get('entryExitAuthLimitDate')?.disable();
        }

        this.isDisableSaveButton = !this.form.valid;
    }

    showExitTransferDialog(transfer: TransferAuthEntity) {

        this.showNewEntryDialog = true;
        this.activeIndex = 0;
        this.form.controls['stepOptions'].enable();
        this.form.controls['stepOptions'].setValue(this.activeIndex.toString());

        this.entry = new EntriesExitsEntity();
        this.entry.subjectId = this.userSubject?.id!;
        this.entry.destinyLocationId = transfer.destinyLocationId!;
        this.entry.locationId = transfer.originLocationId!;
        this.entry.roleId = this.entryExitRole.toString();
        this.entry.type = entriesExitsEnum.TRANSFER_EXIT;

        this.entry.entryDate = new Date();
        this.entry.maxTimeVisit = transfer.actualArrivalDateTime ? transfer.actualArrivalDateTime.getTime() / 60000 : 0;
        this.entry.transferId = transfer.id;
        this.entry.transferLimitDate = transfer.plannedArrivalDateTime;

        this.form.get('entryDate')?.setValue(this.entry.entryDate);
        this.form.enable();
        this.form.get('entryDate')?.disable();
        this.form.get('destinyLocationId')?.setValue(this.newLocationsService.getNameAndTree(transfer.destinyLocationId!, this.lLocationOptions));
        this.form.get('destinyLocationId')?.disable();
        this.form.get('locationId')?.setValue(this.newLocationsService.getNameAndTree(transfer.originLocationId!, this.lLocationOptions));
        this.form.get('locationId')?.disable();
        this.form.get('type')?.setValue(entriesExitsDescriptionArray.find(e => e.key === entriesExitsEnum.TRANSFER_EXIT));
        this.form.get('type')?.disable();
        this.form.get('transferLimitDate')?.setValue(this.entry.transferLimitDate);
        this.form.get('transferLimitDate')?.disable();


        this.isDisableSaveButton = !this.form.valid;

    }

    showEntryDialog(location: SubjectLocationEntity, type: entriesExitsEnum, blockEntry: boolean = true, transferId: string | undefined = undefined, entryExitAuthLimitDate: Date | undefined = undefined, entryExitAuthId: string | undefined = undefined) {
        this.entryLocation = location;
        this.showNewEntryDialog = true;

        this.activeIndex = 0;
        this.form.controls['stepOptions'].enable();
        this.form.controls['stepOptions'].setValue(this.activeIndex.toString());

        this.entry = new EntriesExitsEntity();
        this.entry.subjectId = this.userSubject?.id!;
        this.entry.locationId = location.locationId;
        this.entry.roleId = this.entryExitRole.toString();
        this.entry.type = type;
        this.entry.relatedSubject = this.entryRelatedSubject?.id;

        if(transferId && type == entriesExitsEnum.TRANSFER_ENTRY)
        {
            this.entry.transferId = transferId;
            this.entry.transferLimitDate = new Date(this.listOfEntries[0].transferLimitDate!);
            this.form.get('transferLimitDate')?.setValue(this.entry.transferLimitDate);
            this.form.get('transferLimitDate')?.disable();
        }

        this.entry.entryDate = new Date();
        this.entry.maxTimeVisit = this.visitTimeMax;

        this.form.get('entryDate')?.setValue(this.entry.entryDate);

        this.form.enable();
        this.form.get('locationId')?.setValue(this.newLocationsService.getNameAndTree(location.locationId!, this.lLocationOptions));
        this.form.get('locationId')?.disable();
        this.form.get('type')?.setValue(entriesExitsDescriptionArray.find(e => e.key === type));
        this.form.get('type')?.disable();

        if (this.entry.relatedSubject && this.entryRelatedSubject && this.entryRelatedSubject?.names && this.entryRelatedSubject?.lastNames) {
            this.form.get('relatedSubjectId')?.setValue(this.entryRelatedSubject?.names + " " + this.entryRelatedSubject?.lastNames);
            this.form.get('relatedSubjectId')?.disable();
        }

        if (this.visitTimeMax > 0) {
            const date = new Date(this.entry.entryDate!);
            date.setHours(this.visitTimeMax / 60, this.visitTimeMax % 60);
            this.form.get('visitTimeMax')?.setValue(date);
            this.form.get('visitTimeMax')?.disable();
        }

        if (this.showTime) {
            const differenceMs = new Date().getTime() - new Date(this.entryVisitTimeDate!).getTime();
            const differenceMinutes = Math.floor(differenceMs / (1000 * 60)); // Convert milliseconds to minutes
            const date = new Date(differenceMs);
            date.setHours(differenceMinutes / 60, differenceMinutes % 60);
            this.entry.actualTimeVisit = differenceMinutes;
            this.form.get('visitTimeActual')?.setValue(date);
            this.form.get('visitTimeActual')?.disable();


            const date2 = new Date(this.entry.entryDate!);
            date2.setHours(this.entryVisitTimeMax / 60, this.entryVisitTimeMax % 60);
            this.form.get('visitTimeMax')?.setValue(date2);
            this.form.get('visitTimeMax')?.disable();
            this.entry.maxTimeVisit = this.entryVisitTimeMax;
        }

        if(entryExitAuthId && entryExitAuthLimitDate)
        {
            this.entry.entryExitAuthId = entryExitAuthId;
            this.entry.entryExitAuthLimitDate = new Date(entryExitAuthLimitDate);
            this.form.get('entryExitAuthLimitDate')?.setValue(this.entry.entryExitAuthLimitDate);
            this.form.get('entryExitAuthLimitDate')?.disable();
            this.form.get('authCode')?.setValue(entryExitAuthId);
            this.form.get('authCode')?.disable();
        }

        if (blockEntry) {
            this.form.get('entryDate')?.disable();
        }

        this.isDisableSaveButton = !this.form.valid;
    }

    settimeEnd() {

        if (this.userSubject?.inside === InsideEnum.INSIDE && this.listOfEntries && this.listOfEntries.length > 0) {
            this.entryExitRole = Number(this.listOfEntries[0].roleId) ?? -1;

            var maxTimeVisit = this.listOfEntries[0].maxTimeVisit;

            if (maxTimeVisit && maxTimeVisit > 0) {

                this.entryVisitTimeMax = maxTimeVisit;
                this.entryVisitTimeDate = new Date(this.listOfEntries[0].entryDate!);

                this.timeEnd = new Date(this.entryVisitTimeDate.getTime() + this.entryVisitTimeMax * 60000);

                this.showTime = true;
            }
            else
                this.showTime = false;
        }
        else if((this.userSubject?.inside === InsideEnum.OUTSIDE || this.userSubject?.inside === InsideEnum.TRANSFER) && this.isPrisoner){
            this.entryExitRole = Number(this.prisonsConfig?.prisonerProfileId) ?? -1;

            try{
                if(this.listOfEntries.length > 0)
                {
                    if(this.listOfEntries[0].type == entriesExitsEnum.TRANSFER_EXIT)
                    {
                        this.showTime = true;

                        this.timeEnd = new Date(this.listOfEntries[0].transferLimitDate!);
                    }
                    else if (this.listOfEntries[0].entryExitAuthLimitDate && this.isValidDate2(new Date(this.listOfEntries[0].entryExitAuthLimitDate))) {
                        this.showTime = true;
                        this.timeEnd = new Date(this.listOfEntries[0].entryExitAuthLimitDate!);
                    }
                }
            }
            catch(e){
                this.loggerService.log(e);
            }
        }
    }

    seeEntry(entry: EntriesExitsEntity) {



        if (!entry) {
            if (this.selectedEntries.length === 1) {
                entry = this.selectedEntries[0];
            }
            else return;
        }

        this.isNew = false;
        this.entry = { ...entry };

        let entryRoleName = this.entry?.roleId;

        let reasonRoleOptions = this.settings!.catalogs?.find((catalog) => catalog.parameter === this.reasonsEntryExit + entryRoleName)?.options;
        let reasonRoleFinalOptions: string[] = [];

        if (reasonRoleOptions)
            reasonRoleFinalOptions = JSON.parse(reasonRoleOptions) as string[];
        reasonRoleOptions = this.settings!.catalogs?.find((catalog) => catalog.parameter === this.reasonsEntryExitGeneral)?.options;
        if (reasonRoleOptions)
            reasonRoleFinalOptions.push(...JSON.parse(reasonRoleOptions) as string[]);

        this.lReasonRoleOptions = reasonRoleFinalOptions.length > 0 ? reasonRoleFinalOptions.map((option) => { return { key: option, value: this.getLabel(option, this.reasonsEntryExit + entryRoleName, this.reasonsEntryExitGeneral) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];

        this.form.patchValue({
            entryDate: entry.entryDate ? new Date(entry.entryDate) : null,
            //exitDate: entry.exitDate ? new Date(entry.exitDate) : null,
            reason: this.lReasonRoleOptions.find(e => e.key === entry.reason) || null,
            description: entry.description || null,
            type: this.entriesExitsDescriptionArray.find(e => e.value === entry.type) || null,
            relatedSubjectId: this.entry.relatedSubject || null,
            locationId: entry.locationId || null,
            visitTimeMax: entry.maxTimeVisit ? new Date(0, 0, 0, entry.maxTimeVisit / 60, entry.maxTimeVisit % 60) : null,
            visitTimeActual: entry.actualTimeVisit ? new Date(0, 0, 0, entry.actualTimeVisit / 60, entry.actualTimeVisit % 60) : null,
            transferLimitDate: entry.transferLimitDate ? new Date(entry.transferLimitDate) : null,
            transferId: entry.transferId || null,
            destinyLocationId: entry.destinyLocationId || null,
            entryExitAuthLimitDate: entry.entryExitAuthLimitDate ? new Date(entry.entryExitAuthLimitDate) : null,
            entryExitAuthId: entry.entryExitAuthId || null,
        });

        this.showNewEntryDialog = true;
        this.form.disable();

        this.activeIndex = 0;
        this.form.controls['stepOptions'].enable();
        this.form.controls['stepOptions'].setValue(this.activeIndex.toString());

        if (this.entry.userSignatureTech && this.entry.userSignatureTech != "") {
            this.receptionSignatureData = {
                id: this.entry.userSignatureNumId,
                numId: this.entry.userSignatureNumId,
                tech: this.entry.userSignatureTech,
                date: this.entry.userSignatureDate,
                reason: undefined,
                observation: undefined,
            }
            this.updateImages();
        }
    }

    onEntrySelectionChange(event: any) {
        this.loggerService.error(this.selectedEntries);
    }

    trackDataChange() {

        if (!this.entry) {
            this.entry = new EntriesExitsEntity();
            this.entry.subjectId = this.userSubject?.id!;
            this.entry.executorId = this.localStorageService.getUser().numId;
        }

        if (this.isNew) this.entry.id = undefined;
        this.entry.entryDate = this.form.get('entryDate')?.value;
        this.entry.reason = this.form.get('reason')?.value?.key;
        this.entry.description = this.form.get('description')?.value;
        this.entry.type = this.form.get('type')?.value?.key;
        /*if (this.form.get('exitDate')?.value)
            this.entry.exitDate = this.form.get('exitDate')?.value;
        else
            this.entry.exitDate = undefined;*/

        this.isDisableSaveButton = !this.form.valid;
    }

    async saveEntry() {
        if (!this.form.valid || !this.entry) return;

        this.isLoading = true;

        try {
            if (this.isNew) {
                this.updateEntryDateIfNeeded();
                const data = await this.auditTrailService.registerEntryExitAuditTrailAction(this.entry);
                this.newEntryMade = true;

                this.loggerService.debug("DATA");
                this.loggerService.debug(data);

                if(data)
                    this.entry.id = data.id;

                await this.handleAuditTrailSuccess(data!);
            }
        } catch (error) {
            this.handleAuditTrailError(error);
        } finally {
            this.isLoading = false;
        }
    }

    private updateEntryDateIfNeeded() {
        const nowEntry = new Date().getTime() - this.entry!.entryDate!.getTime();
        if (nowEntry < 10 * 60 * 1000) {
            this.entry!.entryDate = new Date();
        }
    }

    private async handleAuditTrailSuccess(data: ActionEntity) {
        this.updatetimeEnd(this.entry!);
        const entry = { ...this.entry! };
        const entryType = this.entry!.type!;

        this.transformEntry(entry);
        if (entry.relatedSubject) {
            entry.relatedSubject = `${this.entryRelatedSubject?.names} ${this.entryRelatedSubject?.lastNames}`;
        }

        this.listOfActions.unshift(data);
        this.listOfEntries.unshift(entry);

        if (this.shouldUpdateSubject(entryType)) {
            await this.updateSubject(entryType, this.entry!);
        } else {
            await this.handleLocationUpdate(this.entry!, entryType);
        }

        this.onCancelDialog();
        this.messageService.add({
            severity: "success",
            summary: this.translateService.instant("content.successTitle"),
            detail: this.translateService.instant("messages.action_created"),
        });
    }

    private shouldUpdateSubject(entryType: string): boolean {
        return !!this.entry?.relatedSubject &&
               (entryType === entriesExitsEnum.ENTRY || entryType === entriesExitsEnum.EXIT);
    }

    private async handleLocationUpdate(entry: any, entryType: string) {
        let changedLocation = this.entryLocation;
        if (entryType === entriesExitsEnum.TRANSFER_EXIT) {
            changedLocation = this.listOfLocations.find((location) => location.actualLocation);
        }

        try {
            const data = await this.updateLocation(changedLocation!, entryType, entry);
            if (data) {
                await this.updateSubject(entryType, this.entry!);
                //if (entryType === entriesExitsEnum.TRANSFER_EXIT || entryType === entriesExitsEnum.TRANSFER_ENTRY) {
                    //await this.updateTransfer(this.entry!);
                //}
                //else
                if(this.entry?.entryExitAuthId)
                {
                    await this.updateEntryExitAuth(this.entry!);
                }
            }
        } catch (error) {
            this.handleLocationUpdateError(error);
        }
    }

    private handleAuditTrailError(error: any) {
        this.loggerService.error(error);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
        this.auditTrailService.registerAuditTrailAction(
            this.localStorageService.getUser().numId,
            AuditTrailActions.SAVE_RELATED_SUBJECT,
            0,
            "ERROR",
            "",
            at_attributes
        );
    }

    private handleLocationUpdateError(error: any) {
        this.loggerService.error("Error Updating Location:");
        this.loggerService.error(error);

        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
        this.auditTrailService.registerAuditTrailAction(
            this.localStorageService.getUser().numId,
            AuditTrailActions.UPDATE_USER_LOCATION,
            0,
            "ERROR",
            "",
            at_attributes
        );

        this.messageService.add({
            severity: "error",
            summary: this.translateService.instant("content.errorTitle"),
            detail: `${this.translateService.instant("messages.error_updating_location")}: ${error.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000,
        });
    }


    updatetimeEnd(entry: EntriesExitsEntity) {
        this.loggerService.debug("UPDATETIMEEND");
        this.loggerService.debug(entry);

        if (entry.type == entriesExitsEnum.ENTRY) {

            if(entry.roleId != this.settings?.continued1?.prisonsSettings?.prisonerProfileId)
            {
                if (entry.maxTimeVisit && entry.maxTimeVisit > 0) {
                    this.showTime = true;
                    this.timeEnd = new Date(entry.entryDate!.getTime() + entry.maxTimeVisit! * 60000);
                }
                else
                {
                    this.showTime = false;
                }
            }
            else
            {
                this.showTime = false;
            }
        }
        else if(entry.type == entriesExitsEnum.TRANSFER_EXIT)
        {
            if (entry.transferLimitDate) {
                this.showTime = true;
                this.timeEnd = new Date(entry.transferLimitDate!.getTime());
            }
            else
            {
                this.showTime = false;
            }
        }
        else if(entry.type == entriesExitsEnum.EXIT && this.isValidDate2(entry.entryExitAuthLimitDate))
        {
            this.showTime = true;
            this.timeEnd = new Date(entry.entryExitAuthLimitDate!.getTime());
        }
        else {
            this.showTime = false;
        }

    }

    updateEntryExitAuth(entry: EntriesExitsEntity): Promise<void> {

        const entryExit = this.listOfEntryExitAuths.find((entryExit) => entryExit.id == entry.entryExitAuthId);

        if(entryExit)
        {
            if(entry.type == entriesExitsEnum.EXIT)
            {
                entryExit.actualStartDateTime = entry.entryDate;
                entryExit.status = AuthStatus.IN_PROGRESS;
                entryExit.startActionId = entry.id;
            }
            else if(entry.type == entriesExitsEnum.DEF_EXIT)
            {
                entryExit.actualEndDateTime = entry.entryDate;
                entryExit.status = AuthStatus.COMPLETED;
                entryExit.isCompleted = true;
                entryExit.startActionId = entry.id;
            }
            else if(entry.type == entriesExitsEnum.ENTRY)
            {
                entryExit.endActionId = entry.id;
                entryExit.actualEndDateTime = entry.entryDate;
                entryExit.status = AuthStatus.COMPLETED;
                entryExit.isCompleted = true;
                entryExit.elapsedTime = this.formatTime((entryExit.actualEndDateTime?.getTime()! - entryExit.actualStartDateTime?.getTime()!)/1000);
            }

            this.loggerService.debug("UPDATE ENTRY EXIT AUTH");
            this.loggerService.debug(entryExit);

            return this.updateEntryExitAuthUseCase.execute({ entryExitAuth: entryExit }).then(
                (data) => {
                    return Promise.resolve();
                },
                (e) => {
                    console.error(e);
                    this.messageService.add({
                        severity: "error",
                        summary: this.translateService.instant("content.errorTitle"),
                        detail: `${this.translateService.instant("messages.errorUpdatingEntryExitAuth")}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000,
                    });
                    return Promise.resolve();
                }
            );
        }
        else
        {
            /*this.messageService.add({
                severity: "error",
                summary: this.translateService.instant("content.errorTitle"),
                detail: `${this.translateService.instant("messages.errorUpdatingEntryExitAuth")}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000,
            });*/
            console.error("Error updating entry exit auth, entry exit auth not found");
            return Promise.resolve();
        }

    }


    /*async updateTransfer(entry: EntriesExitsEntity): Promise<void> {

        const transfer = await this.getTransferAuthByIdUseCase.execute({id: entry.transferId!});
        //const transfer = this.listOfTransferAuths.find((transfer) => transfer.id == entry.transferId);
        const prisoner = transfer?.listOfPrisoners?.find((prisoner) => prisoner.subjectId == this.userSubject?.id);

        if(transfer && prisoner)
        {
            if(entry.type == entriesExitsEnum.TRANSFER_ENTRY)
            {
                prisoner.actualArrivalDateTime = entry.entryDate;
                prisoner.actualArrivalTech = entry.subjectSignatureTech;
                prisoner.elapsedTime = this.formatTime((prisoner.actualArrivalDateTime?.getTime()! - prisoner.actualDepartureDateTime?.getTime()!)/1000);

                //Check if all prisoners have arrived
                var allArrived = transfer.listOfPrisoners?.every((prisoner) => prisoner.actualArrivalDateTime != undefined);
                if(allArrived)
                {
                    transfer.status = AuthStatus.COMPLETED;
                    transfer.isCompleted = true;
                    transfer.actualArrivalDateTime = entry.entryDate;
                    transfer.elapsedTime = this.formatTime((transfer.actualArrivalDateTime?.getTime()! - transfer.actualDepartureDateTime?.getTime()!)/1000);
                }

            }
            else if(entry.type == entriesExitsEnum.TRANSFER_EXIT)
            {
                if(!this.isValidDate2(transfer.actualDepartureDateTime))
                {
                    transfer.actualDepartureDateTime = entry.entryDate;
                    transfer.status = AuthStatus.IN_PROGRESS;
                }

                prisoner.actualDepartureDateTime = entry.entryDate;
                prisoner.actualDepartureTech = entry.subjectSignatureTech;
            }

            return this.updateTransferAuthUseCase.execute({ transferAuth: transfer }).then(
                (data) => {
                    return this.updateTransferAuthUserSubjectUseCase.execute({ transferAuthUserSubject: prisoner }).then(
                        (data) => {
                            Promise.resolve();
                        },
                        (e) => {
                            console.error(e);
                            this.messageService.add({
                                severity: "error",
                                summary: this.translateService.instant("content.errorTitle"),
                                detail: `${this.translateService.instant("messages.errorUpdatingTransferUser")}`,
                                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000,
                            });
                            Promise.resolve();
                        }
                    );
                },
                (e) => {
                    this.messageService.add({
                        severity: "error",
                        summary: this.translateService.instant("content.errorTitle"),
                        detail: `${this.translateService.instant("messages.errorUpdatingTransferAuth")}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000,
                    });
                    Promise.resolve();
                }
            );

        }
        else
        {
            /*this.messageService.add({
                severity: "error",
                summary: this.translateService.instant("content.errorTitle"),
                detail: `${this.translateService.instant("messages.errorUpdatingTransferAuth")}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000,
            });
            console.error("Error updating transfer, transfer or prisoner not found");
            console.log(prisoner);
            console.log(transfer);
            return Promise.resolve();
        }

    }*/

    updateSubject(entryType: string, entry: EntriesExitsEntity): Promise<void> {

        if (entryType == entriesExitsEnum.ENTRY || entryType == entriesExitsEnum.DEF_ENTRY || entryType == entriesExitsEnum.TRANSFER_ENTRY) {
            this.userSubject!.inside = InsideEnum.INSIDE;
            if(entry.roleId != this.settings?.continued1?.prisonsSettings?.prisonerProfileId)
            {
                this.userSubject!.entryDate = entry.entryDate;
                this.userSubject!.exitDate = undefined;
                var dateTime = new Date(entry.entryDate!.getTime() + entry.maxTimeVisit! * 60000);
                this.userSubject!.visitTime = dateTime;
                //if (entry.relatedSubject != undefined && entry.relatedSubject != "") {
                    this.userSubject!.center = this.newLocationsService.getRootKeyPath(entry.locationId!, this.lLocationOptions);
                    this.userSubject!.locationId = entry.locationId;
                //}
            }
            else
            {
                this.userSubject!.entryDate = undefined;
                this.userSubject!.center = this.newLocationsService.getRootKeyPath(entry.locationId!, this.lLocationOptions);
                this.userSubject!.locationId = entry.locationId;
            }


        }
        else {
            this.userSubject!.inside = InsideEnum.OUTSIDE;
            if(entry.roleId == this.settings?.continued1?.prisonsSettings?.prisonerProfileId)
            {
                this.userSubject!.exitDate = entry.entryDate;
                this.userSubject!.entryDate = undefined;

                if(entry.type == entriesExitsEnum.TRANSFER_EXIT)
                {
                    var dateTime = new Date(entry.transferLimitDate!);
                    this.userSubject!.visitTime = dateTime;
                    this.userSubject!.center = this.newLocationsService.getRootKeyPath(entry.destinyLocationId!, this.lLocationOptions);
                    this.userSubject!.locationId = entry.destinyLocationId;
                }
                else if(entry.entryExitAuthId && this.isValidDate2(entry.entryExitAuthLimitDate))
                {
                    this.userSubject!.visitTime = new Date(entry.entryExitAuthLimitDate!);
                }
                else
                {
                    this.userSubject!.visitTime = undefined;
                }

                if(entry.type == entriesExitsEnum.DEF_EXIT)
                {
                    this.userSubject!.visitTime = undefined;
                    this.userSubject!.center = "";
                    this.userSubject!.locationId = "";
                }
            }
            else
            {
                this.userSubject!.exitDate = undefined;
                if (entry.relatedSubject != undefined && entry.relatedSubject != "") {
                    this.userSubject!.center = "";
                    this.userSubject!.locationId = "";
                }
                else if(this.listOfLocations.length > 0) //CUANDO SALE ALGUIEN QUE NO TIENE RELACIONADO (FUNCIONARIO) VOLVER A PONERLE SU UBICACION PRINCIPAL
                {
                    this.userSubject!.center = this.newLocationsService.getRootKeyPath(this.listOfLocations[this.listOfLocations.length -1].locationId!, this.lLocationOptions);
                    this.userSubject!.locationId = this.listOfLocations[this.listOfLocations.length -1].locationId!;
                }
            }
        }

        console.log("UPDATE SUBJECT");
        console.log(this.userSubject);

        return this.updateSubjectUseCase.execute({ subject: this.userSubject! }).then(
            (data) => {
                if (data) {
                    this.isLoading = false;
                    this.updateSubjectStatus.emit();
                }
            },
            (e) => {
                this.loggerService.error('Error Updating Subject:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.userSubject) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_updating_subject')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }
        );
    }

    onCancelDialog() {
        this.entry = undefined;
        this.isNew = true;

        this.showNewEntryDialog = false;
        this.form.reset();
        this.lActualLocation = undefined;
        this.lClassification = undefined;
        this.lEntryDate = undefined;
        this.lLocationId = undefined;
        this.lPending = undefined;
        this.lReason = undefined;
        //this.lExitDate = undefined;
        this.lComments = undefined;
        this.verifyReady = false;

        this.activeIndex = 0;
        this.visitTimeMax = 0;

        this.entryRelatedSubject = undefined;

        this.receptionSignatureData = undefined;


        if (this.entryExitRole != -1 && this.userSubject?.inside !== InsideEnum.INSIDE) {
            this.entryExitRole = -1;
        }
    }

    isRequiredField(field: string): boolean {
        return this.validatorService.isRequiredField(this.form, field);
    }

    isValid(field: string, form: FormGroup<any> = this.form): boolean {
        return this.validatorService.isValidField(form, field);
    }

    /* Search */
    onFilter(event: any, dt: Table) {
        if (!event.filters['entryDate'].value) {
            this.rangeDates = null;
        }
        /*if (event.filters['exitDate'].value) {
            this.rangeDatesExit = null;
        }*/
        this.filteredValues = event.filteredValue;
    }

    isValidDate(dateString: string | null): boolean {
        if (!dateString) return false;
        const date = new Date(dateString);
        return !isNaN(date.getTime());
    }

    isValidDate2(date: Date | undefined): boolean {
        if(!date) return false;
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
      }

    /* Date Range Filter */
    applyDateRangeFilter(dt: Table, field: string) {
        this.rangeDates = this.formGroup.get('date')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    applyDateRangeFilterExit(dt: Table, field: string) {
        this.rangeDates = this.form.get('dateExit')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    private clearRejectTimeout() {
        if (this.rejectTimeout) {
            clearTimeout(this.rejectTimeout);
        }
        this.rejectTimeout = null;
    }

    stringToDate(dateString: string): Date {
        return new Date(dateString);
    }

    transformEntry(entry: EntriesExitsEntity): EntriesExitsEntity {
        entry.locationId = this.newLocationsService.getNameAndTree(entry.locationId!, this.lLocationOptions) || entry.locationId;
        if(entry.destinyLocationId)
            entry.destinyLocationId = this.newLocationsService.getNameAndTree(entry.destinyLocationId!, this.lLocationOptions) || entry.destinyLocationId;
        var roleId = Number(entry.roleId)
        entry.roleId = this.allRoles?.find(role => role.id === roleId)?.name ?? entry.roleId;

        const result = entriesExitsDescriptionArray.find(e => e.key === entry.type as entriesExitsEnum);

        if (result) {
            entry.type = result.value;
        }
        return entry;
    }

    getTranslatedValue(entryType: string): string {
        const entry = this.entriesExitsDescriptionArray.find(e => e.key === entryType as entriesExitsEnum);
        return entry?.value || entryType;
    }

    updateLocation(location: SubjectLocationEntity, type: string, entry: EntriesExitsEntity): Promise<boolean> {

        this.isLoading = true;

        if (type == entriesExitsEnum.DEF_ENTRY || type == entriesExitsEnum.ENTRY || type == entriesExitsEnum.TRANSFER_ENTRY) {
            location.pending = false;
            location.actualLocation = true;
        }
        else if (type == entriesExitsEnum.DEF_EXIT || type == entriesExitsEnum.EXIT || type == entriesExitsEnum.TRANSFER_EXIT) {
            location.actualLocation = false;
            if(this.isPrisoner && type != entriesExitsEnum.EXIT)
                location.exitDate = entry.entryDate;
        }

        return this.updateSubjectLocationUseCase.execute({ subjectLocation: location })
            .then((data) => {
                // Handle success case here if needed
                return true; // Indicate success
            })
            .catch((e) => {
                this.isLoading = false;
                this.loggerService.error(e);

                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.UPDATE_USER_LOCATION, 0, 'ERROR', '', at_attributes);

                return false;
            });
    }


    resolveLocationLabel = (option: SubjectLocationEntity) => {
        return this.newLocationsService.getNameAndTree(option.locationId!, this.lLocationOptions);
    };

    resolveRelatedSubjectLabel = (option: { entity: SubjectEntity; location: SubjectLocationEntity }) => {
        return option.entity.names + " " + option.entity.lastNames;
    }

    resolveTransfertLabel = (transfer: TransferAuthEntity ) => {
        return transfer.id + ": " + this.newLocationsService.getNameAndTree(transfer.destinyLocationId!, this.lLocationOptions);
    }

    resolveEntryExitAuth = (entryExitAuth: EntryExitAuthEntity ) => {
        var name = "";
        if(entryExitAuth.type == EntryExitAuthType.ENTRY)
            name = this.entriesExitsDescriptionArray.find(e => e.key === entriesExitsEnum.ENTRY)?.value || "";
        else if(entryExitAuth.type == EntryExitAuthType.DFN_EXIT)
            name = this.entriesExitsDescriptionArray.find(e => e.key === entriesExitsEnum.DEF_EXIT)?.value || "";
        else if(entryExitAuth.type == EntryExitAuthType.EXIT)
            name = this.entriesExitsDescriptionArray.find(e => e.key === entriesExitsEnum.EXIT)?.value || "";

        return entryExitAuth.id + ": " + this.translateService.instant(name);
    }

    locationToEnterSelected() {
        this.showSelectLocationsDialog = false;
        const selectedLocation = this.listOfLocationsToEntry.find(
            loc => loc.id?.toString() == this.dataFormLocations.get("location")?.value
        );

        this.checkEntryToLocationNoPPLAndShow(selectedLocation!);
    }

    relatedSubjectToEnterSelected() {

        if(!this.dataFormRelatedSubjects.valid) {
            return;
        }

        this.showSelectRelatedSubjectsDialog = false;

        const selectedRelatedSubject = this.listOfRelatedToEntry.find(
            rs => rs.entity.id?.toString() == this.dataFormRelatedSubjects.get("relatedSubject")?.value
        );

        this.entryRelatedSubject = selectedRelatedSubject?.entity;

        this.checkEntryToLocationNoPPLAndShow(selectedRelatedSubject!.location);
    }

    transferToExitSelected() {

        if(!this.dataFormTransfer.valid) {
            return;
        }

        this.showSelectTransferDialog = false;

        var transfer = this.dataFormTransfer.get("transfer")?.value as TransferAuthEntity;

        this.showExitTransferDialog(transfer);
    }

    entryExitAuthToSelected() {
        if(!this.dataFormEntryExitAuth.valid) {
            return;
        }

        this.showSelectEntryExitAuthDialog = false;

        this.entryExitAuth = this.dataFormEntryExitAuth.get("entryExitAuth")?.value as EntryExitAuthEntity;

        this.showEntryExitAuthDialog(this.entryExitAuth);
    }

    async transformSubjectsIdToNames(entries: EntriesExitsEntity[]): Promise<EntriesExitsEntity[]> {
        const relatedSubjectsMap = new Map<string, string>();

        entries.forEach(entry => {
            if (entry.relatedSubject && !relatedSubjectsMap.has(entry.relatedSubject)) {
                relatedSubjectsMap.set(entry.relatedSubject, "");
            }
        });

        await Promise.all(
            Array.from(relatedSubjectsMap.keys()).map(async key => {
                try {
                    const subject = await this.getSubjectByIdUseCase.execute({ id: key });
                    relatedSubjectsMap.set(key, `${subject.names} ${subject.lastNames}`);
                } catch (e) {
                    this.loggerService.error(e!);
                    const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                    this.auditTrailService.registerAuditTrailAction(
                        this.localStorageService.getUser().numId,
                        AuditTrailActions.GET_SUBJECT_BY_ID,
                        0,
                        'ERROR',
                        '',
                        at_attributes
                    );
                }
            })
        );

        entries.forEach(entry => {
            if (entry.relatedSubject) {
                entry.relatedSubject = relatedSubjectsMap.get(entry.relatedSubject);
            }
        });

        return entries;
    }

    onActiveIndexChange(event: number) {
        this.activeIndex = event;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onActiveTabIndexChange(event: any) {
        this.activeIndex = Number(event.value);
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onNext() {
        this.activeIndex += 1;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onBack() {
        this.activeIndex -= 1;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    getTechIcon(tech: string): string {
        let fingerprintIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/fingerprint.svg";
        let facialIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/facial.svg";
        let irisIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/iris.svg";
        switch (tech) {
            case 'fingerprint':
                return fingerprintIcon;
            case 'facial':
                return facialIcon;
            case 'iris':
                return irisIcon;
            default:
                return '';
        }
    }

    openWidgetVerify() {
        this.widgetUrl = this.settings?.widgetConfig?.url || "";
        this.isLoading = false;
        this.subjectNumId = this.userSubject?.numId!;
        this.verifyReady = true;
    }

    onWidgetMatchResult(event: WidgetResult) {
        if (!this.verifyReady) {
            return;
        }
        switch (event.action) {
            case "verify":
                if (event.result == "success") {
                    if (event.data.isMatched && this.entry) {
                        this.entry.subjectSignatureDate = new Date();
                        this.entry.subjectSignatureTech = event.data.tech || this.tech;
                        this.updateImages();

                        const at_attributes = [
                            { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                        ]
                        this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.SUBJECT_VERIFY, 0, 'SUCCESS', event.data.tech, at_attributes);
                    }
                }
                this.subjectNumId = '';
                this.verifyReady = false;
                break;
            case "process":
                break;
            case "close_verify":
            case "error":
                this.subjectNumId = '';
                this.verifyReady = false;
                break;
        }
    }

    prueba(event: BioSignatureResult) {
        if (this.entry) {
            this.entry.userSignatureTech = event.tech;
            this.entry.userSignatureDate = event.date;
            this.entry.userSignatureNumId = event.numId;

            this.receptionSignatureData = {
                id: this.entry.userSignatureNumId,
                numId: this.entry.userSignatureNumId,
                tech: this.entry.userSignatureTech,
                date: this.entry.userSignatureDate,
                reason: undefined,
                observation: undefined,
            }
        }

    }

    updateImages() {
        this.image = (this.userSubject?.pic == this.imagePlaceholder || this.userSubject?.pic == "" || this.userSubject?.pic == undefined || this.userSubject?.pic == null) ? this.imagePlaceholder : this.userSubject.pic.includes('data:image/jpeg;base64,') ? this.userSubject?.pic! : 'data:image/jpeg;base64,' + this.userSubject?.pic!;
    }

    formatTime(seconds: number): string {
        const absSeconds = Math.abs(Math.floor(seconds));
        const hours = Math.floor(absSeconds / 3600);
        const minutes = Math.floor((absSeconds % 3600) / 60);
        const secs = absSeconds % 60;
        return `${seconds < 0 ? '-' : ''}${this.pad(hours)}:${this.pad(minutes)}:${this.pad(secs)}`;
    }

    pad(num: number): string {
        return num.toString().padStart(2, '0');
    }

    getLabel(key: string, catalogParameter1: string, catalogParameter2?: string): string {
        let reasonTranslationGroups = this.managerSettings?.continued1?.translations?.filter((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter1 || t.id === 'catalogs-' + catalogParameter2);
        let reasonTranslations: TranslationModel[] = [];
        reasonTranslationGroups?.forEach((group: TranslationGroup) => {
            reasonTranslations = reasonTranslations.concat(group.translations || []);
        });
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}

import { Component, Input, OnChanges, OnInit, SimpleChang<PERSON> } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { MessageService } from "primeng/api";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/delete-static-resource-by-subject-id-and-name-and-number.use-case";
import { GetStaticResourcesBySubjectIdAndNameUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
    selector: 'app-pic-history',
    templateUrl: './pic-history.component.html',
    styleUrl: './pic-history.component.css'
})
export class PicHistoryComponent implements OnInit, OnChanges {

    @Input() canReadAndWrite: boolean = false;
    @Input() userSubject: SubjectEntity | UserEntity | undefined;
    @Input() isUser: boolean = false;
    @Input() parentLoading: boolean = false;

    loading: boolean = false;

    userSubjectPicHistory: StaticResourceEntity[] = [];
    picHistoryName: string = 'profile-picture-history';
    assetRoute: string = 'verazial-common-frontend/assets/images/';

    selectedImage: StaticResourceEntity | undefined;
    openFullImageDialog: boolean = false;

    responsiveOptions: any[] | undefined;
    selectedCurrentResult: StaticResourceEntity | undefined;

    constructor(
        private loggerService: ConsoleLoggerService,
        private localStorageService: LocalStorageService,
        private auditTrailService: AuditTrailService,
        private translateService: TranslateService,
        private messageService: MessageService,
        private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
        private deleteStaticResourcesBySubjectIdAndNameAndNumberUseCase: DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase,
    ) { }

    ngOnInit() {
        if (this.userSubject) {
                this.userSubjectPicHistory = [];
            this.getUserSubjectPicHistory();
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['userSubject'] && changes['userSubject'].currentValue && !this.parentLoading) {
            this.userSubjectPicHistory = [];
            this.getUserSubjectPicHistory();
        }
        if (changes['parentLoading'].previousValue && !this.parentLoading) {
            this.userSubjectPicHistory = [];
            this.getUserSubjectPicHistory();
        }
    }

    getUserSubjectPicHistory() {
        this.loading = true;
        this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: this.userSubject?.id!, name: this.picHistoryName }).then(
            (data) => {
                if (data) {
                    this.userSubjectPicHistory = data.sort((a, b) => {
                        if (a.number === undefined) return -1;  // Push 'undefined' to the end
                        if (b.number === undefined) return 1; // Push 'undefined' to the end
                        return b.number - a.number;            // Regular comparison if both are defined
                    });
                    if (this.userSubjectPicHistory.length > 1) {

                    this.responsiveOptions = [
                        {
                            breakpoint: '1199px',
                            numVisible: 2,
                            numScroll: 1
                        },
                        {
                            breakpoint: '767px',
                            numVisible: 1,
                            numScroll: 1
                        }
                    ];
                }
                }
                this.loading = false;
            },
            (e) => {
                this.loggerService.error('Error Getting User/Subject Static Resources:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_downloading_image')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.userSubjectPicHistory = [];
                this.loading = false;
            }
        );
    }

    // openImageDialog(image: StaticResourceEntity) {
    //     this.selectedImage = { ... image};
    //     this.openFullImageDialog = true;
    // }

    // closeDialog() {
    //     this.selectedImage = undefined;
    //     this.openFullImageDialog = false;
    // }

    openImageDialog(staticResource: StaticResourceEntity) {
        this.selectedCurrentResult = { ...staticResource };
        if (this.selectedCurrentResult?.content) {
            this.openFullImageDialog = true;
        }
    }

    async closeDialog() {
        this.selectedCurrentResult = undefined;
        this.openFullImageDialog = false;
    }
    /* Camera Functions */

    onCameraResult(event: {action: string, staticResource: StaticResourceEntity}){
        switch (event.action) {
            case 'close':
                this.closeDialog();
                break;
            case 'delete':
                const at_attributes: ExtraData[] = [
                    { name: 'static_resource_name', value: event.staticResource.name! },
                    { name: 'static_resource_number', value: event.staticResource.number!.toString() }
                ];
                this.auditTrailService.auditTrailSelectReason(this.isUser ? ReasonTypeEnum.USER : ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_STA_RES, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete(event.staticResource); }, at_attributes);
                break;
            case 'create':
                break;
        }
    }

    onSubmitDelete(selectedCurrentResult: StaticResourceEntity) {
        if (selectedCurrentResult) {
            this.closeDialog();
            this.loading = true;
            const param = {
                subjectId: selectedCurrentResult.subjectId!,
                name: selectedCurrentResult.name!,
                number: selectedCurrentResult.number!
            }
            this.deleteStaticResourcesBySubjectIdAndNameAndNumberUseCase.execute(param).then(
                () => {
                    this.messageService.add({
                        severity: 'success',
                        summary: this.translateService.instant('content.successTitle'),
                        detail: this.translateService.instant('messages.success_general'),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.userSubjectPicHistory = [];
                    this.getUserSubjectPicHistory();
                },
                (e) => {
                    this.loggerService.error('Error Deleting User/Subject Static Resource:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: 'error', value: JSON.stringify(e) },
                        { name: 'static_resource_name', value: param.name },
                        { name: 'static_resource_number', value: param.number.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_STA_RES, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_removing_image')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.loading = false;
                }
            );
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_data_to_save"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }
}
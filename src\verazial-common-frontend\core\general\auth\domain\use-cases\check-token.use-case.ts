import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AuthRepository } from "../repository/auth.repository";

export class CheckTokenUseCase implements UseCaseGrpc<{ token: string }, boolean> {
    constructor(private authRepository: AuthRepository) { }
    execute(params: { token: string; }): Promise<boolean> {
        return this.authRepository.checkToken(params);
    }
}
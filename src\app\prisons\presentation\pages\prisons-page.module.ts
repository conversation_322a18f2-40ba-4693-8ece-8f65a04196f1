import { NgModule } from "@angular/core";
import { PrisonsVisitsPageComponent } from "./visits-page/visits-page.component";
import { PrisonsPageRoutingModule } from "./prisons-page-routing.module";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { DialogModule } from "primeng/dialog";
import { ToastModule } from "primeng/toast";
import { CardModule } from "primeng/card";
import { TabViewModule } from "primeng/tabview";
import { ButtonModule } from "primeng/button";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { CheckboxModule } from "primeng/checkbox";
import { MultiSelectModule } from "primeng/multiselect";
import { PasswordModule } from "primeng/password";
import { InputTextModule } from "primeng/inputtext";
import { TooltipModule } from "primeng/tooltip";
import { AccordionModule } from "primeng/accordion";
import { LoadingSpinnerModule } from "src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { WidgetEnrollModule } from "src/verazial-common-frontend/modules/shared/components/widget-enroll/widget-enroll.module";
import { WidgetMatchModule } from "src/verazial-common-frontend/modules/shared/components/widget-match/widget-match.module";
import { UserNotVerifiedModule } from "src/verazial-common-frontend/modules/shared/components/user-not-verified/user-not-verified.module";
import { AuthSchedulesModule } from "../components/auth-schedules/auth-schedules.module";
import { TransfersPageComponent } from "./transfers-page/transfers-page.component";
import { TransferAuthsModule } from "../components/transfer-auths/transfer-auths.module";

@NgModule({
  declarations: [
    PrisonsVisitsPageComponent,
    TransfersPageComponent,
  ],
  imports: [
    /* Routing */
    PrisonsPageRoutingModule,
    /* Angular Modules */
    CommonModule,
    /* Form Modules */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    /* Prime NG Modules */
    DialogModule,
    ToastModule,
    CardModule,
    TabViewModule,
    ButtonModule,
    ConfirmDialogModule,
    DropdownModule,
    ProgressSpinnerModule,
    CheckboxModule,
    MultiSelectModule,
    PasswordModule,
    InputTextModule,
    TooltipModule,
    AccordionModule,
    /* Custom Modules */
    LoadingSpinnerModule,
    EmptyModule,
    WidgetEnrollModule,
    WidgetMatchModule,
    UserNotVerifiedModule,
    AuthSchedulesModule,
    TransferAuthsModule,
  ],
  exports:[
    PrisonsVisitsPageComponent,
    TransfersPageComponent,
  ]
})
export class PrisonsPageModule { }
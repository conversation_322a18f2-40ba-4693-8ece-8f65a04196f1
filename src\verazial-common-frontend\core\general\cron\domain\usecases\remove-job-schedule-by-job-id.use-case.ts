import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CronServiceRepository } from "../repository/cron-service.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";

export class RemoveJobScheduleByJobIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private cronServiceRepository: CronServiceRepository) { }
    execute(params: { id: string }): Promise<SuccessResponse> {
        return this.cronServiceRepository.removeJobScheduleByJobId(params);
    }
}
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AppRegistryEntity } from "../entities/app-registry.entity";
import { AppRegistryRepository } from "../repositories/app-registry.repository";

export class GetAppRegistriesUseCase implements UseCaseGrpc<void, AppRegistryEntity[]> {
    constructor(
        private appRegistryRepository: AppRegistryRepository
    ) { }
    execute(): Promise<AppRegistryEntity[]> {
        return this.appRegistryRepository.getAppRegistries();
    }
}
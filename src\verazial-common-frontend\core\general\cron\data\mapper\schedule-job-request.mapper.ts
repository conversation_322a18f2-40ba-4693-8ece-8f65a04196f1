import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ScheduleJobRequestModel } from "../../common/models/schedule-job-request.model";
import { ScheduleJobRequestGrpcModel } from "src/verazial-common-frontend/core/generated/cron/cron-service_pb";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";

export class ScheduleJobRequestMapper extends Mapper<ScheduleJobRequestGrpcModel, ScheduleJobRequestModel> {
    override mapFrom(param: ScheduleJobRequestGrpcModel): ScheduleJobRequestModel {
        throw new Error("Method not implemented.");
    }
    override mapTo(param: ScheduleJobRequestModel): ScheduleJobRequestGrpcModel {
        let grpcModel = new ScheduleJobRequestGrpcModel();
        grpcModel.setId(param.id!);
        grpcModel.setDatetimestart(param.dateTimeStart ? dateToTimestamp(param.dateTimeStart) : undefined);
        return grpcModel;
    }
}
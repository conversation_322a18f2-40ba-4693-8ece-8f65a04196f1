
import { TaskFlowRepository } from "../../repository/task-flow.repository";
import { TaskFlowEntity } from "../../entity/task-flow.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetAllTaskFlowsUseCase implements UseCaseGrpc<void, TaskFlowEntity[]> {
    constructor(private taskFlowRepository: TaskFlowRepository) { }
    execute(params: void): Promise<TaskFlowEntity[]> {
        return this.taskFlowRepository.getAllTaskFlows()
    }
}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { VerificationPageRoutingModule } from './verification-page-routing.module';
import { VerificationPageComponent } from './verification-page/verification-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { PaginatorModule } from 'primeng/paginator';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { DiLicenseModule } from 'src/verazial-common-frontend/core/general/license/data/di-license.module';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
  declarations: [
        VerificationPageComponent
  ],
  imports: [
    CommonModule,
    VerificationPageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    ProgressSpinnerModule,
    DropdownModule,
    PaginatorModule,
    ProgressSpinnerModule,
    ProgressBarModule,
    SkeletonModule,
    DiLicenseModule,
    InputTextModule,
  ],
  exports: [
    VerificationPageComponent
  ]
})
export class VerificationPageModule { }

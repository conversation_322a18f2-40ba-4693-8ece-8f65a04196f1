<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>
<p-toast></p-toast>
<app-list-groups [readAndWritePermissions]="canReadAndWrite" [groups]="listGroupsCategories" (updateGroups)="updateGroupData($event)"></app-list-groups>
<!-- <div [formGroup]="form">
    <div *ngIf="listGroupsCategoriesIsEmpty; else showListGroups">
        <app-empty
        (clicked)="createNewGroupCategory($event)"
        [readAndWritePermissions]="canReadAndWrite"
        buttonLabel="category.new_category"
        titleLabel="category.no_categories_available"></app-empty>
    </div>
    <ng-template #showListGroups>
        
    </ng-template>

    <p-dialog [(visible)]="showNewGroupDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
        <ng-template pTemplate="header">
            <div></div>
            <div class="dialog-title">
                {{ 'category.new_category' | translate }}
            </div>
        </ng-template>
        <ng-template pTemplate="content">
            <app-new-group [readAndWritePermissions]="canReadAndWrite" (operationStatus)="proccessGroupOperation($event)"></app-new-group>
        </ng-template>
    </p-dialog>
</div> -->
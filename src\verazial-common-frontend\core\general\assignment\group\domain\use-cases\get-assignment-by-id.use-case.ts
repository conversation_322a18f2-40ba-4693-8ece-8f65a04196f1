import { AssignmentRespository } from "../repository/assignment.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AssignmentEntity } from "../entity/assignment.entity";

export class GetAssignmentByIdUseCase implements UseCaseGrpc<{ id: string }, AssignmentEntity> {
    constructor(private repository: AssignmentRespository) { }
    execute(params: { id: string; }): Promise<AssignmentEntity> {
        return this.repository.getAssignmentById(params);
    }
}
import { DataSourceEntity } from "../entities/data-source.entity";
import { DataSourceRepository } from "../repositories/data-source.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateAppDataSourceByIdUseCase implements UseCaseGrpc<DataSourceEntity, SuccessResponse> {
    constructor(private dataSourceRepository: DataSourceRepository) { }
    execute(params: DataSourceEntity): Promise<SuccessResponse> {
        return this.dataSourceRepository.updateAppDataSourceById(params);
    }
}
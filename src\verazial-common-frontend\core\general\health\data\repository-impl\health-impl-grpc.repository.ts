import { Injectable } from "@angular/core";
import { HealthEntity } from "../../domain/entity/health.entity";
import { HealthRepository } from "../../domain/repository/health.repository";
import { HealthGrpcMapper } from "../mapper/health-grpc.mapper";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { environment } from "src/environments/environment";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { CoreHealthClient } from "src/verazial-common-frontend/core/generated/monitoring/health/HealthServiceClientPb";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";

@Injectable({
    providedIn: 'root',
})
export class HealthRepositoryGrpcImpl extends HealthRepository {

    healthMapper = new HealthGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }

    override getStatus(): Promise<HealthEntity> {

        let coreHealthClient = new CoreHealthClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreHealthClient.getStatus(new Empty, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.healthMapper.mapFrom(response));
                }
            });
        });
    }

}
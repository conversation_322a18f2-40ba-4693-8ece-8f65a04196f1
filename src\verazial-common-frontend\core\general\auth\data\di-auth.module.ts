import { CommonModule } from "@angular/common";
import { AuthRepository } from "../domain/repository/auth.repository";
import { AuthRepositoryImpl } from "./repository-impl/auth-impl.repository";
import { NgModule } from "@angular/core";
import { AuthenticateByUserUseCase } from "../domain/use-cases/authenticate-by-user.use-case";
import { CheckTokenUseCase } from "../domain/use-cases/check-token.use-case";
import { RefreshByUserUseCase } from "../domain/use-cases/refresh-by-user.use-case";
import { AuthenticateByLicenseUseCase } from "../domain/use-cases/authenticate-by-license.use-case";
import { ChangeTenantUseCase } from "../domain/use-cases/change-tenant.use-case";

const authenticateByUserUseCaseFactory =
    (authRepository: AuthRepository) => new AuthenticateByUserUseCase(authRepository);

const checkTokenUseCaseFactory =
    (authRepository: AuthRepository) => new CheckTokenUseCase(authRepository);

const refreshByUserUseCaseFactory =
    (authRepository: AuthRepository) => new RefreshByUserUseCase(authRepository);

const authenticateByLicenseUseCaseFactory =
    (authRepository: AuthRepository) => new AuthenticateByLicenseUseCase(authRepository);

const changeTenantUseCaseFactory =
    (authRepository: AuthRepository) => new ChangeTenantUseCase(authRepository);

export const authenticateByUserUseCaseProvider = {
    provide: AuthenticateByUserUseCase,
    useFactory: authenticateByUserUseCaseFactory,
    deps: [AuthRepository]
}

export const checkTokenUseCaseProvider = {
    provide: CheckTokenUseCase,
    useFactory: checkTokenUseCaseFactory,
    deps: [AuthRepository]
}

export const refreshByUserUseCaseProvider = {
    provide: RefreshByUserUseCase,
    useFactory: refreshByUserUseCaseFactory,
    deps: [AuthRepository]
}

export const authenticateByLicenseUseCaseProvider = {
    provide: AuthenticateByLicenseUseCase,
    useFactory: authenticateByLicenseUseCaseFactory,
    deps: [AuthRepository]
}

export const changeTenantUseCaseProvider = {
    provide: ChangeTenantUseCase,
    useFactory: changeTenantUseCaseFactory,
    deps: [AuthRepository]
}

@NgModule({
    providers: [
        authenticateByUserUseCaseProvider,
        checkTokenUseCaseProvider,
        refreshByUserUseCaseProvider,
        authenticateByLicenseUseCaseProvider,
        changeTenantUseCaseProvider,
        { provide: AuthRepository, useClass: AuthRepositoryImpl }
    ],
    imports: [
        CommonModule,
    ]
})
export class DiAuthModule { }
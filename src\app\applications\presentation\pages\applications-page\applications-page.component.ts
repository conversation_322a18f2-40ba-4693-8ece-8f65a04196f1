import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { Confirmation, ConfirmationService, MessageService } from "primeng/api";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { AppRegistryEntity } from "src/verazial-common-frontend/core/general/app-registry/domain/entities/app-registry.entity";
import { AddAppRegistryUseCase } from "src/verazial-common-frontend/core/general/app-registry/domain/use-cases/add-app-registry.use-case";
import { DeleteAppRegistryByIdUseCase } from "src/verazial-common-frontend/core/general/app-registry/domain/use-cases/delete-app-registry-by-id.use-case";
import { GetAppRegistriesUseCase } from "src/verazial-common-frontend/core/general/app-registry/domain/use-cases/get-app-registries.use-case";
import { GetAppRegistryByIdUseCase } from "src/verazial-common-frontend/core/general/app-registry/domain/use-cases/get-app-registry-by-id.use-case";
import { UpdateAppRegistryUseCase } from "src/verazial-common-frontend/core/general/app-registry/domain/use-cases/update-app-registry.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
  selector: 'app-applications-page',
  templateUrl: './applications-page.component.html',
  styleUrls: ['./applications-page.component.css'],
  providers: [MessageService, ConfirmationService]
})
export class ApplicationsPageComponent implements OnInit, OnDestroy {

  isLoading: boolean = false;
  listAppRegistries: AppRegistryEntity[] = [];

  /** Access */
  canReadAndWrite: boolean = false;
  access_identifier: string = AccessIdentifier.PASS_APPS;

  private rejectTimeout: any;

  constructor(
    private translateService: TranslateService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private checkPermissionsService: CheckPermissionsService,
    private loggerService: ConsoleLoggerService,
    private localStorageService: LocalStorageService,
    private auditTrailService: AuditTrailService,
    private getAppRegistriesUseCase: GetAppRegistriesUseCase,
    private addAppRegistryUseCase: AddAppRegistryUseCase,
    private getAppRegistryByIdUseCase: GetAppRegistryByIdUseCase,
    private updateAppRegistryUseCase: UpdateAppRegistryUseCase,
    private deleteAppRegistryByIdUseCase: DeleteAppRegistryByIdUseCase,
  ) { }

  ngOnInit(): void {
    this.isLoading = true;

    this.canReadAndWrite = this.checkPermissionsService.hasReadAndWritePermissions(this.access_identifier);

    this.getAppRegistriesUseCase.execute().then(
      (data) => {
        this.loggerService.debug(data);
        this.listAppRegistries = data;
      },
      (e) => {
        this.loggerService.error(e);
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant('content.errorTitle'),
          detail: `${this.translateService.instant('messages.error_retrieving_data')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_APP_REGISTRIES, 0, 'ERROR', '', at_attributes);
      }
    )
    .finally(() => {
      this.isLoading = false;
    });
  }

  ngOnDestroy(): void {
  }

  onAdd(appRegistry: AppRegistryEntity) {
    // console.log('onAdd', appRegistry);
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(appRegistry) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.ADD_APP_REG, ReasonActionTypeEnum.CREATE, () => {
      this.addAppRegistryUseCase.execute({ applications: [appRegistry] }).then(
        (data) => {
          this.loggerService.debug(data);
          this.listAppRegistries = [...this.listAppRegistries, appRegistry];
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.success_save_record'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        },
        (e) => {
          this.loggerService.error(e);
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('messages.error_save_record')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(appRegistry) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_REG, 0, 'ERROR', '', at_attributes);
        }
      );
    }, at_attributes, false);
  }

  onEdit(appRegistry: AppRegistryEntity) {
    // console.log('onEdit', appRegistry);
    this.getAppRegistryByIdUseCase.execute({ id: appRegistry.id! }).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(appRegistry) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.MOD_APP_REG, ReasonActionTypeEnum.UPDATE, () => {
          this.updateAppRegistryUseCase.execute({ application: appRegistry }).then(
            (data) => {
              this.loggerService.debug(data);
              this.listAppRegistries = [...this.listAppRegistries.filter(item => item.id !== appRegistry.id), data];
              this.messageService.add({
                severity: 'success',
                summary: this.translateService.instant('content.successTitle'),
                detail: this.translateService.instant('messages.success_update_record'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
            },
            (e) => {
              this.loggerService.error(e);
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_update_record')}: ${e.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
              const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(appRegistry) },
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_REG, 0, 'ERROR', '', at_attributes);
            }
          );
        }, at_attributes, false);
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(appRegistry) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_REG, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  onDelete(appRegistry: AppRegistryEntity) {
    // console.log('onDelete', appRegistry);
    let id = appRegistry.id;
    if (id != undefined) {
      this.confirmationService.confirm({
        message: `${this.translateService.instant('messages.delete_single_record')} <b>${appRegistry.name}</b>?`,
        header: this.translateService.instant('messages.delete_confirmation_header'),
        icon: 'pi pi-exclamation-triangle',
        rejectButtonStyleClass:"p-button-text",
        acceptButtonStyleClass:"ng-confirm-button",
        acceptIcon: "none",
        rejectIcon: "none",
        acceptLabel: this.translateService.instant("delete"),
        rejectLabel: this.translateService.instant("no"),
        accept: () => {
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(appRegistry) },
          ];
          this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.DEL_APP_REG, ReasonActionTypeEnum.DELETE, () => {
            this.deleteAppRegistryByIdUseCase.execute({ id: id! }).then(
              (data) => {
                this.loggerService.debug(data);
                this.listAppRegistries = this.listAppRegistries.filter(item => item.id !== appRegistry.id);
                this.messageService.add({
                  severity: 'success',
                  summary: this.translateService.instant('content.successTitle'),
                  detail: this.translateService.instant('messages.success_delete_record'),
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              },
              (e) => {
                this.loggerService.error(e);
                this.messageService.add({
                  severity: 'error',
                  summary: this.translateService.instant('content.errorTitle'),
                  detail: `${this.translateService.instant('messages.error_delete_record')}: ${e.message}`,
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                  { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(appRegistry) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_APP_REG, 0, 'ERROR', '', at_attributes);
              }
            );
          }, at_attributes, false);
        },
        reject: (type: Confirmation) => {
          this.clearRejectTimeout();
        }
      });

      // Set a timeout to automatically trigger the reject action after 10 seconds
      this.rejectTimeout = setTimeout(() => {
        this.confirmationService.close(); // Close the dialog
      }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000);
    }
  }

  private clearRejectTimeout() {
    if (this.rejectTimeout) {
      clearTimeout(this.rejectTimeout);
    }
    this.rejectTimeout = null;
  }
}
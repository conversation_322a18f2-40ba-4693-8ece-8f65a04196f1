import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AppRegistryEntity } from "../entities/app-registry.entity";
import { AppRegistryRepository } from "../repositories/app-registry.repository";

export class AddAppRegistryUseCase implements UseCaseGrpc<{ applications: AppRegistryEntity[]}, AppRegistryEntity[]> {
    constructor(
        private appRegistryRepository: AppRegistryRepository
    ) { }
    execute(params: { applications: AppRegistryEntity[] }): Promise<AppRegistryEntity[]> {
        return this.appRegistryRepository.addAppRegistry(params);
    }
}
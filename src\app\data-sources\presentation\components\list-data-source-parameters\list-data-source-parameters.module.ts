import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListDataSourceParametersComponent } from './list-data-source-parameters/list-data-source-parameters.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';



@NgModule({
  declarations: [
    ListDataSourceParametersComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    InputTextModule,
    ButtonModule,
    DropdownModule,
    TableModule,
    TooltipModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    ListDataSourceParametersComponent
  ]
})
export class ListDataSourceParametersModule { }

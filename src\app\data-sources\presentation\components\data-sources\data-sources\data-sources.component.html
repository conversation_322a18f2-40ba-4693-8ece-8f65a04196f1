<p-toast></p-toast>

<div [formGroup]="form">
    <p-stepper [(activeStep)]="active">
        <p-stepperPanel header="Header I">
            <ng-template pTemplate="header" let-onClick="onClick" let-index="index">
                <div class="flex flex-column flex-wrap gap-2 step-header">
                    <button class="bg-transparent border-none inline-flex flex-column gap-2" (onClick)="onClick.emit()">
                        <span
                            class="border-circle w-2rem h-2rem inline-flex align-items-center justify-content-center"
                            [ngClass]="{
                                'active-step': index == active,
                                'checked-step': index != active
                            }"
                        >
                        @if(index == active){
                            {{index + 1}}
                        }@else{
                            <i class="pi pi-check"></i>
                        }
                        </span>
                    </button>
                    <label>
                        @if(index == active){
                            <label><b>{{ 'pass_datasource.datasource' | translate }}</b></label>
                        }@else{
                            <label>{{ 'pass_datasource.datasource' | translate }}</label>
                        }
                    </label>
                </div>
                
            </ng-template>
            <ng-template pTemplate="content" let-nextCallback="nextCallback">
                
                <div class="flex flex-column gap-1 mx-auto" style="min-height: 16rem; max-width: 20rem">
                    <div class="field p-fluid">
                        <label class="text-sm font-semibold" for="dataSourceName">{{ 'content.name' | translate }}</label>
                        <input id="dataSourceName" formControlName="dataSourceName" 
                        pInputText id="input" type="text"
                        [ngClass]="!isValid('dataSourceName') && form.controls['dataSourceName'].touched? 'ng-invalid ng-dirty':'' " />
                        <small *ngIf="!isValid('dataSourceName') && form.controls['dataSourceName'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                    <div class="field p-fluid">
                        <label class="text-sm font-semibold" for="dataSourceName">{{ 'content.type' | translate }}</label>
                        <p-dropdown 
                            appendTo="body" 
                            [options]="listSourceTypes" 
                            placeholder="{{'content.select' | translate}}" 
                            optionLabel="value"
                            [(ngModel)]="selectedSourceType"
                            formControlName="sourceType"
                            id="sourceType"
                            dataKey="key"
                            [ngClass]="!isValid('sourceType') && form.controls['sourceType'].touched? 'ng-invalid ng-dirty':'' "
                            >
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedSourceType">
                                    <div>{{selectedSourceType.value}}</div>
                                </div>
                            </ng-template>
                            <ng-template let-sourceType pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ sourceType.value }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <small *ngIf="!isValid('sourceType') && form.controls['sourceType'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>

                    <div class="field p-fluid">
                        <label class="text-sm font-semibold" for="method">{{ 'pass_datasource.method' | translate }}</label>
                        <input id="method" formControlName="method" pInputText id="input" type="text" 
                        [ngClass]="!isValid('method') && form.controls['method'].touched? 'ng-invalid ng-dirty':'' "
                        />
                        <small *ngIf="!isValid('method') && form.controls['method'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>
                <!-- div class="dashed-line-vertical"></div-->
                <div class="flex justify-content-center">
                    <div class="flex pt-4 gap-3 justify-content-center">
                        <p-button (onClick)="cancelEvent()" 
                        [style]="{'color': '#000000', 'border': 'none', 'background': '#FFFFFF' }" 
                        label="{{ 'cancel' | translate }}" />
                        <p-button (onClick)="nextCallback.emit()" 
                        [disabled]="!isValid('dataSourceName') || !isValid('sourceType') || !isValid('method')? true : false"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#204887' }" 
                        label="{{ 'next' | translate }}" />
                    </div>
                </div>
            </ng-template>
        </p-stepperPanel>
        <p-stepperPanel header="Header I">
            <ng-template pTemplate="header" let-onClick="onClick" let-index="index">
                <div class="flex flex-column flex-wrap gap-2 step-header">
                    <button class="bg-transparent border-none inline-flex flex-column gap-2" (onClick)="onClick.emit()">
                        <span
                            class="border-circle w-2rem h-2rem inline-flex align-items-center justify-content-center"
                            [ngClass]="{
                                'active-step': index <= active,
                            'inactive-step': index > active
                            }"
                        >
                        {{index + 1}}
                        
                        </span>
                    </button>
                    <label>
                        @if(index == active){
                            <label><b>{{ 'pass_datasource.parameters' | translate }}</b></label>
                        }@else{
                            <label>{{ 'pass_datasource.parameters' | translate }}</label>
                        }
                    </label>
                </div>
                
            </ng-template>
            <ng-template pTemplate="content" let-prevCallback="prevCallback">
                <div class="flex flex-column">
                    <div class="flex gap-3">
                        <app-data-source-parameters (outputData)="addParameter($event)"></app-data-source-parameters>
                        <div class="dashed-line-horizontal"></div>
                        <app-list-data-source-parameters 
                            [inputData]="listDataSourceParameters" 
                            (edited)="updateParameter($event)" 
                            (removed)="deleteParameter($event)">
                        </app-list-data-source-parameters>
                        <!--div class="mt-3 mb-3">
                            <label (click)="showParameters()" class="flex text-sm font-semibold justify-content-end">{{ 'buttons.show_parameters' | translate }}</label>
                        </div>
                        
                        <div class="line"></div-->
                    </div>
                    <div class="dashed-line-vertical"></div>
                    
                    <div class="flex pt-4 gap-3 justify-content-center">
                        <p-button (onClick)="prevCallback.emit()" 
                        [style]="{'color': '#000000', 'border': 'none', 'background': '#FFFFFF' }" 
                        label="{{ 'back' | translate }}" />
                        <p-button (onClick)="saveDatasource()" 
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#204887' }" 
                        [disabled]="listDataSourceParameters.length == 0 ? true : false"
                        label="{{ 'save' | translate }}" />
                    </div>
                </div>
                
            </ng-template>
        </p-stepperPanel>
    </p-stepper>
</div>

<p-dialog [(visible)]="showParametersDialog" styleClass="p-fluid" [modal]="true" [closable]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="text-center mt-3 mb-3 text-l font-semibold">
            {{ 'content.parameter' | translate }}    
        </div>
    </ng-template>
    
    <ng-template pTemplate="content">
        <app-list-data-source-parameters [inputData]="listDataSourceParameters" (edited)="updateParameter($event)" (removed)="deleteParameter($event)"></app-list-data-source-parameters>
    </ng-template>
</p-dialog>
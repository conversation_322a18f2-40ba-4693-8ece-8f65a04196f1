import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { DynamicFormComponent } from "./dynamic-form/dynamic-form.component";
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ButtonModule } from 'primeng/button';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CardModule } from 'primeng/card';
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { AccordionModule } from 'primeng/accordion';
import { TooltipModule } from "primeng/tooltip";

@NgModule({
    declarations: [
        DynamicFormComponent
    ],
    imports: [
      CommonModule,
      /** Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG */
      InputTextModule,
      DropdownModule,
      InputSwitchModule,
      ButtonModule,
      InputTextareaModule,
      CardModule,
      ConfirmDialogModule,
      AccordionModule,
      TooltipModule,
    ],
    exports: [
        DynamicFormComponent
    ]
  })
  export class DynamicFormModule { }
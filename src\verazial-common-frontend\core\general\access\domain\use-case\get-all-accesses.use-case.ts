import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AccessEntity } from "../entity/access.entity";
import { AccessRepository } from "../repository/access.repository";

export class GetAllAccessesUseCase implements UseCaseGrpc<void, AccessEntity[]> {
    constructor(private accessRepository: AccessRepository) { }
    execute(params: void): Promise<AccessEntity[]> {
        return this.accessRepository.getAllAccesses();
    }
}
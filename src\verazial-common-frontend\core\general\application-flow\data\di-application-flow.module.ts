import { NgModule } from "@angular/core";
import { ApplicationFlowRepository } from "../domain/repository/application-flow.repository";
import { AddApplicationFlowUseCase } from "../domain/use-cases/add-application-flow.use-case";
import { DeleteApplicationFlowByAppIdUseCase } from "../domain/use-cases/delete-application-flow-by-app-id.use-case";
import { GetApplicationFlowByAppIdUseCase } from "../domain/use-cases/get-application-flow-by-app-id.use-case";
import { UpdateApplicationFlowByAppIdUseCase } from "../domain/use-cases/update-application-flow-by-app-id.use-case";
import { ApplicationFlowRepositoryImpl } from "./repository-impl/application-flow-impl.repository";
import { CommonModule } from "@angular/common";

const addApplicationFlowUseCaseFactory =
    (applicationFlowRepository: ApplicationFlowRepository) => new AddApplicationFlowUseCase(applicationFlowRepository);

const updateApplicationFlowByAppIdUseCaseFactory =
    (applicationFlowRepository: ApplicationFlowRepository) => new UpdateApplicationFlowByAppIdUseCase(applicationFlowRepository);

const getApplicationFlowByAppIdUseCaseFactory =
    (applicationFlowRepository: ApplicationFlowRepository) => new GetApplicationFlowByAppIdUseCase(applicationFlowRepository);

const deleteApplicationFlowByAppIdUseCaseFactory =
    (applicationFlowRepository: ApplicationFlowRepository) => new DeleteApplicationFlowByAppIdUseCase(applicationFlowRepository);

export const addApplicationFlowUseCaseProvider = {
    provide: AddApplicationFlowUseCase,
    useFactory: addApplicationFlowUseCaseFactory,
    deps: [ApplicationFlowRepository]
}

export const updateApplicationFlowByAppIdUseCaseProvider = {
    provide: UpdateApplicationFlowByAppIdUseCase,
    useFactory: updateApplicationFlowByAppIdUseCaseFactory,
    deps: [ApplicationFlowRepository]
}

export const getApplicationFlowByAppIdUseCaseProvider = {
    provide: GetApplicationFlowByAppIdUseCase,
    useFactory: getApplicationFlowByAppIdUseCaseFactory,
    deps: [ApplicationFlowRepository]
}

export const deleteApplicationFlowByAppIdUseCaseProvider = {
    provide: DeleteApplicationFlowByAppIdUseCase,
    useFactory: deleteApplicationFlowByAppIdUseCaseFactory,
    deps: [ApplicationFlowRepository]
}

@NgModule({
    providers: [
        addApplicationFlowUseCaseProvider,
        updateApplicationFlowByAppIdUseCaseProvider,
        getApplicationFlowByAppIdUseCaseProvider,
        deleteApplicationFlowByAppIdUseCaseProvider,
        { provide: ApplicationFlowRepository, useClass: ApplicationFlowRepositoryImpl }
    ],
    imports: [
        CommonModule,
    ]
})
export class DiApplicationFlowModule { }
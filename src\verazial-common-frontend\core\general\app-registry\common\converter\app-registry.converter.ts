import { AppRegistryListGrpcModel } from "src/verazial-common-frontend/core/generated/app-registry/app_registry_pb";
import { AppRegistryEntity } from "../../domain/entities/app-registry.entity";
import { AppRegistryMapper } from "../../data/mapper/app-registry.mapper";

export function convertListOfAppRegistryToAppRegistryListGrpcModel(appRegistryList: AppRegistryEntity[]): AppRegistryListGrpcModel {
    const appRegistryMapper = new AppRegistryMapper();
    let appRegistryListGrpcModel = new AppRegistryListGrpcModel();
    appRegistryList.forEach((appRegistry) => {
        appRegistryListGrpcModel.addApplications(appRegistryMapper.mapTo(appRegistry));
    });
    return appRegistryListGrpcModel;
}

export function convertAppRegistryListGrpcModelToListOfAppRegistry(appRegistryListGrpcModel?: AppRegistryListGrpcModel): AppRegistryEntity[] {
    const appRegistryMapper = new AppRegistryMapper();
    let listOfAppRegistry: AppRegistryEntity[] = [];
    appRegistryListGrpcModel?.getApplicationsList().forEach((appRegistry) => {
        listOfAppRegistry.push(appRegistryMapper.mapFrom(appRegistry))
    });
    return listOfAppRegistry;
}
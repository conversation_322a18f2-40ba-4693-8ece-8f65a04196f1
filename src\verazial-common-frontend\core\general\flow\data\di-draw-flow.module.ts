import { NgModule } from "@angular/core";
import { DrawFlowRepository } from "../domain/repository/draw-flow.repository";
import { CreateDrawFlowUseCase } from "../domain/use-cases/draw-flow/create-draw-flow.use-case";
import { DeleteDrawFlowByTaskFlowIdUseCase } from "../domain/use-cases/draw-flow/delete-draw-flow-by-task-flow-id.use-case";
import { GetDrawFlowByTaskFlowIdUseCase } from "../domain/use-cases/draw-flow/get-draw-flow-by-task-flow-id.use-case";
import { UpdateDrawFlowUseCase } from "../domain/use-cases/draw-flow/update-draw-flow.use-case";
import { DrawFlowRepositoryImpl } from "./repository-impl/draw-flow-impl.repository";
import { CommonModule } from "@angular/common";
import { HttpClientModule } from "@angular/common/http";

const createDrawFlowUseCaseFactory =
    (drawFlowRepository: DrawFlowRepository) => new CreateDrawFlowUseCase(drawFlowRepository);

const getDrawFlowByTaskFlowIdUseCaseFactory =
    (drawFlowRepository: DrawFlowRepository) => new GetDrawFlowByTaskFlowIdUseCase(drawFlowRepository);

const deleteDrawFlowByTaskFlowIdUseCaseFactory =
    (drawFlowRepository: DrawFlowRepository) => new DeleteDrawFlowByTaskFlowIdUseCase(drawFlowRepository);

const updateDrawFlowUseCaseFactory =
    (drawFlowRepository: DrawFlowRepository) => new UpdateDrawFlowUseCase(drawFlowRepository);

export const createDrawFlowUseCaseProvider = {
    provide: CreateDrawFlowUseCase,
    useFactory: createDrawFlowUseCaseFactory,
    deps: [DrawFlowRepository]
}

export const getDrawFlowByTaskFlowIdUseCaseProvider = {
    provide: GetDrawFlowByTaskFlowIdUseCase,
    useFactory: getDrawFlowByTaskFlowIdUseCaseFactory,
    deps: [DrawFlowRepository]
}

export const deleteDrawFlowByTaskFlowIdUseCaseProvider = {
    provide: DeleteDrawFlowByTaskFlowIdUseCase,
    useFactory: deleteDrawFlowByTaskFlowIdUseCaseFactory,
    deps: [DrawFlowRepository]
}

export const updateDrawFlowUseCaseProvider = {
    provide: UpdateDrawFlowUseCase,
    useFactory: updateDrawFlowUseCaseFactory,
    deps: [DrawFlowRepository]
}

@NgModule({
    providers: [
        createDrawFlowUseCaseProvider,
        getDrawFlowByTaskFlowIdUseCaseProvider,
        deleteDrawFlowByTaskFlowIdUseCaseProvider,
        updateDrawFlowUseCaseProvider,
        { provide: DrawFlowRepository, useClass: DrawFlowRepositoryImpl },
    ],
    imports: [
        CommonModule,
        HttpClientModule,
    ],
})
export class DiDrawFlowModule { }
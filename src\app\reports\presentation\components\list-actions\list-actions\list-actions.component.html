<p-toast />
<div class="subcontainer" style="margin: 0px">
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ tittleText | translate}}</label>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfActions"
            (onFilter)="onFilter($event, dt)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [sortField]="'numId'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column" pSortableColumn="data.applicationId"> {{ 'signaturesTable.applicationId' | translate }} <p-sortIcon field="data.applicationId"></p-sortIcon></th>
                    <th *ngIf="audit" class="fixed-column" pSortableColumn="data.commonAttributes.locationId"> {{ 'reports.reportColumn2' | translate }} <p-sortIcon field="data.commonAttributes.locationId"></p-sortIcon></th>
                    <th *ngIf="audit" class="fixed-column" pSortableColumn="data.commonAttributes.segmentId"> {{ 'reports.reportColumn3' | translate }} <p-sortIcon field="data.commonAttributes.segmentId"></p-sortIcon></th>
                    <th *ngIf="audit" class="fixed-column" pSortableColumn="data.commonAttributes.deviceId"> {{ 'reports.reportColumn4' | translate }} <p-sortIcon field="data.commonAttributes.deviceId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="data.actionName">{{ 'flow.action' | translate }} <p-sortIcon field="data.actionName"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="data.commonAttributes.executorId">{{ 'signaturesTable.executor' | translate }} <p-sortIcon field="data.commonAttributes.executorId"></p-sortIcon></th>
                    <th *ngIf="audit && false" class="fixed-column" pSortableColumn="data.commonAttributes.executorProfile">{{ 'reports.reportColumn7' | translate }} <p-sortIcon field="data.commonAttributes.executorId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="data.commonAttributes.receiverId">{{ 'signaturesTable.receptor' | translate }} <p-sortIcon field="data.commonAttributes.receiverId"></p-sortIcon></th>
                    <th *ngIf="audit && false" class="fixed-column" pSortableColumn="data.commonAttributes.receiverProfile">{{ 'reports.reportColumn7' | translate }} <p-sortIcon field="data.commonAttributes.receiverId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="createdAt">{{ 'signaturesTable.date' | translate }} <p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="data.timmingAttributes.totalTime">{{ 'signaturesTable.totalTime' | translate }} <p-sortIcon field="data.timmingAttributes.totalTime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="data.commonAttributes.actionResult">{{ 'signaturesTable.actionResult' | translate }} <p-sortIcon field="data.commonAttributes.actionResult"></p-sortIcon></th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="data.applicationId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="audit">
                        <p-columnFilter type="text" field="data.commonAttributes.locationId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="audit">
                        <p-columnFilter type="text" field="data.commonAttributes.segmentId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="audit">
                        <p-columnFilter type="text" field="data.commonAttributes.deviceId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="data.actionName" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="data.commonAttributes.executorId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="audit && false">
                        <p-columnFilter type="text" field="data.commonAttributes.executorProfile" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="data.commonAttributes.receiverId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="audit && false">
                        <p-columnFilter type="text" field="data.commonAttributes.receiverProfile" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="createdAt"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                    [showTime]="true"
                                    [hourFormat]="'24'"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="numeric" field="data.timmingAttributes.totalTime" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="data.commonAttributes.actionResult" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-action>
                <tr>
                    <td  showDelay="1000" pTooltip="{{action.data?.applicationId ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.applicationId ?? '' }}</td>

                    <td  *ngIf="audit" showDelay="1000" pTooltip="{{action.data?.commonAttributes?.locationId ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes?.locationId ?? '' }}</td>
                    <td  *ngIf="audit" showDelay="1000" pTooltip="{{action.data?.commonAttributes?.segmentId ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes?.segmentId ?? '' }}</td>
                    <td  *ngIf="audit" showDelay="1000" pTooltip="{{action.data?.commonAttributes?.deviceId ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes?.deviceId ?? '' }}</td>

                    <td  showDelay="1000" pTooltip="{{action.data?.actionName ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.actionName ?? '' }}</td>
                    <td  showDelay="1000" pTooltip="{{action.data?.commonAttributes.executorId ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes.executorId ?? '' }}</td>
                    <td  *ngIf="audit && false" showDelay="1000" pTooltip="{{action.data?.commonAttributes.executorProfile ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes.executorProfile ?? '' }}</td>
                    <td  showDelay="1000" pTooltip="{{action.data?.commonAttributes.receiverId ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes.receiverId ?? '' }}</td>
                    <td  *ngIf="audit && false" showDelay="1000" pTooltip="{{action.data?.commonAttributes.receiverProfile ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{ action.data?.commonAttributes.receiverProfile ?? '' }}</td>
                    <td  showDelay="1000" pTooltip="{{formatDate(action.createdAt)}}" tooltipPosition="top" class="ellipsis-cell">{{ formatDate(action.createdAt) }}</td>
                    <td  showDelay="1000" pTooltip="{{action.data.timmingAttributes.totalTime ?? 0}} ms" tooltipPosition="top" class="ellipsis-cell">{{action.data.timmingAttributes.totalTime ?? 0}} ms</td>
                    <td  showDelay="1000" pTooltip="{{action.data?.commonAttributes?.actionResult ?? ''}}" tooltipPosition="top" class="ellipsis-cell">{{action.data?.commonAttributes?.actionResult ?? ''}}</td>
                    <td alignFrozen="right" (click)="emitDetails(action)" pFrozenColumn [frozen]="true" class="custom-border" style="cursor: pointer; width: 100px">
                        <div class="flex flex-row" >
                            <td  showDelay="1000" tooltipPosition="top" class="ellipsis-cell" style="font-weight: bolder;">{{'signaturesTable.details' | translate}} ></td>
                        </div>
                    </td>


                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr *ngIf="!isAddingData">
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
                <ng-container *ngIf="isAddingData">
                    <tr *ngFor="let row of [].constructor(10); let i = index">
                        <td><p-skeleton></p-skeleton></td>
                        <td *ngIf="audit"><p-skeleton></p-skeleton></td>
                        <td *ngIf="audit"><p-skeleton></p-skeleton></td>
                        <td *ngIf="audit"><p-skeleton></p-skeleton></td>
                        <td><p-skeleton></p-skeleton></td>
                        <td><p-skeleton></p-skeleton></td>
                        <td *ngIf="audit && false"><p-skeleton></p-skeleton></td>
                        <td><p-skeleton></p-skeleton></td>
                        <td *ngIf="audit && false"><p-skeleton></p-skeleton></td>
                        <td><p-skeleton></p-skeleton></td>
                        <td><p-skeleton></p-skeleton></td>
                        <td><p-skeleton></p-skeleton></td>
                    </tr>
                </ng-container>
            </ng-template>
        </p-table>

    </div>
</div>
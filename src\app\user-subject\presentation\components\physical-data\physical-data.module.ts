import { NgModule } from '@angular/core';
import { PhysicalDataComponent } from './physical-data/physical-data.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AccordionModule } from 'primeng/accordion';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FloatLabelModule } from 'primeng/floatlabel';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { CameraDialogModule } from 'src/verazial-common-frontend/modules/shared/components/camera-dialog/camera-dialog.module';
import { ToastModule } from 'primeng/toast';
import { CarouselModule } from 'primeng/carousel';
import { CameraModule } from 'src/verazial-common-frontend/modules/shared/components/camera/camera.module';
// import { CarouselModule } from 'ngx-carousel-ease';

@NgModule({
  declarations: [
    PhysicalDataComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Forms */
    ReactiveFormsModule,
    FormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG Modules */
    AccordionModule,
    DialogModule,
    ButtonModule,
    DropdownModule,
    ProgressSpinnerModule,
    FloatLabelModule,
    ConfirmDialogModule,
    ToastModule,
    /* Carousel */
    CarouselModule,
    /* Custom Modules */
    CameraDialogModule,
    CameraModule,
  ],
  exports: [
    PhysicalDataComponent
  ]
})
export class PhysicalDataModule { }

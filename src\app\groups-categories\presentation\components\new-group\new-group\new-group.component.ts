import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { FilterService, MenuItem } from 'primeng/api';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { AttributeData } from 'src/verazial-common-frontend/core/models/attribute-data.model';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { environment } from 'src/environments/environment';
import { SubjectEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { GetAllSubjectsUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case';
import { GetSettingsByApplicationUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import { formatDate } from 'src/verazial-common-frontend/core/util/date-to-timestamp';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { GetAllRolesUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { Table } from 'primeng/table';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { GetGroupCategoryByIdUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-group-category-by-id.use-case';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { OperationType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum';
import { DeviceLocation } from 'src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/device-location.inteface';
import { CategoryLocationEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/category-location.entity';
import { CategorySubjectEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/category-subject.entity';
import { DayTimeScheduleEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/day-time-schedule.entity';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';
import { ScheduleTypeEnum } from 'src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-type.enum';
import { CreateGroupCategoryUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/create-group-category.use-case';
import { UpdateGroupCategoryByIdUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/update-group-category-by-id.use-case';
import { DeleteCategorySubjectUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/delete-category-subject.use-case';
import { DeleteCategoryLocationUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/delete-category-location.use-case';
import { CategoryScheduleEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/category-schedule.entity';
import { ScheduleCustom } from 'src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-custom.interface';
import { DateModel } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/date.model';
import { UserSubjectEnum } from 'src/verazial-common-frontend/core/models/user-subject.enum';
import { GetSubjectsRequestEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/get-subjects-request.entity';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { KonektorPropertiesEntity } from 'src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';

@Component({
  selector: 'app-new-group',
  templateUrl: './new-group.component.html',
  styleUrl: './new-group.component.css',
})
export class NewGroupComponent implements OnInit, OnDestroy {
  @Input() groupData: GroupCategoryEntity | undefined;
  @Input() readAndWritePermissions: boolean = false;
  @Input() operationType!: OperationType;
  @Output() operationStatus = new EventEmitter<OperationStatus>();

  locationsApp: string = 'GENERAL_SETTINGS';

  managerSettings?: GeneralSettings;
  konektorProperties?: KonektorPropertiesEntity;

  items: MenuItem[] | undefined;
  activeIndex: number = 0;
  isLoading: boolean = false;
  loadingUsers: boolean = true;
  loadingLocations: boolean = true;
  minDate: Date | undefined;

  selectAll: boolean = false;
  selectedUsers: SubjectEntity[] = [];
  selectedDevicesLocation: DeviceLocation[] = [];

  selectedGroupType: AttributeData | undefined;
  selectedLocation: AttributeData | undefined;
  selectedSegment: AttributeData | undefined;
  selectedDevice: AttributeData | undefined;
  selectedSchedule: AttributeData | undefined;

  listOfOriginalCategoryLocations: CategoryLocationEntity[] = [];
  listOfOriginalCategorySubjects: CategorySubjectEntity[] = [];
  listOfOriginalDayTime: DayTimeScheduleEntity[] = [];

  selectedGroup: GroupCategoryEntity | undefined;

  listLocations: AttributeData[] = [];
  listSegments: AttributeData[] = [];
  listDevices: AttributeData[] = [];
  listUsers: SubjectEntity[] = [];
  listDeviceLocation: DeviceLocation[] = [];

  subjectType = UserSubjectEnum.SUBJECT;
  useLazyLoad: boolean = false;
  getSubjectsRequest = new GetSubjectsRequestEntity();

  createUpdateButtonTitle: string = this.translateService.instant('save');

  opType = OperationType ;

  groupCategoryType = GroupCategoryType;

  allRoles: RoleEntity[] = [];

  stepOptions: AttributeData[] = [
    { key: this.translateService.instant('category.category_type'), value: "0" },
    { key: this.translateService.instant('category.configuration'), value: "1" },
  ];

  groupTypes: AttributeData[] = [
    { key: GroupCategoryType.USERS, value: this.translateService.instant('content.subject') },
    { key: GroupCategoryType.LOCATIONS, value: this.translateService.instant('content.location') },
    { key: GroupCategoryType.SCHEDULES, value: this.translateService.instant('content.schedule') }
  ]

  listScheduleTypes: AttributeData[] = [
    { key: ScheduleTypeEnum.ALL, value: this.translateService.instant('content.everyday') },
    { key: ScheduleTypeEnum.WORKING_DAYS, value: this.translateService.instant('content.monday_to_friday') },
    { key: ScheduleTypeEnum.CUSTOM, value: this.translateService.instant('content.customised') }
  ]

  days: AttributeData[] = [
    { key: 'MONDAY', value: this.translateService.instant('content.monday') },
    { key: 'TUESDAY', value: this.translateService.instant('content.tuesday') },
    { key: 'WEDNESDAY', value: this.translateService.instant('content.wednesday') },
    { key: 'THURSDAY', value: this.translateService.instant('content.thursday') },
    { key: 'FRIDAY', value: this.translateService.instant('content.friday') },
    { key: 'SATURDAY', value: this.translateService.instant('content.saturday') },
    { key: 'SUNDAY', value: this.translateService.instant('content.sunday') },
  ]

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  selectedType!: GroupCategoryType;
  selectedScheduleType: string = "";

  showDateRange: boolean = false;
  showDateRangeCustom: boolean = false;

  public form: FormGroup = this.fb.group({
    groupName: ['', [Validators.required]],
    groupType: ['', [Validators.required]],
    groupDescription: [],
    /* Location */
    location: ['', [Validators.required]],
    segment: ['', [Validators.required]],
    device: ['', [Validators.required]],
    /* Schedule */
    dateInit: [],
    dateEnd: [],
    dateInitCustom: [],
    dateEndCustom: [],
    showDateInterval: [],
    showDateIntervalCustom: [],
    scheduleInit: ['', [Validators.required]],
    scheduleEnd: ['', [Validators.required]],
    scheduletype: ['', [Validators.required]],
    MONDAYInit: [],
    TUESDAYInit: [],
    WEDNESDAYInit: [],
    THURSDAYInit: [],
    FRIDAYInit: [],
    SATURDAYInit: [],
    SUNDAYInit: [],
    MONDAYEnd: [],
    TUESDAYEnd: [],
    WEDNESDAYEnd: [],
    THURSDAYEnd: [],
    FRIDAYEnd: [],
    SATURDAYEnd: [],
    SUNDAYEnd: [],
    stepOptions: ['0'],
    search: []
  });

  constructor(
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private validatorService: ValidatorService,
    private translateService: TranslateService,
    private fb: FormBuilder,
    private createGroupCategoryUseCase: CreateGroupCategoryUseCase,
    private updateGroupCategoryByIdUseCase: UpdateGroupCategoryByIdUseCase,
    private getAllSubjectsUseCase: GetAllSubjectsUseCase,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private deleteCategorySubjectUseCase: DeleteCategorySubjectUseCase,
    private deleteCategoryLocationUseCase: DeleteCategoryLocationUseCase,
    private getAllRolesUseCase: GetAllRolesUseCase,
    private filterService: FilterService,
    private auditTrailService: AuditTrailService,
    private getGroupCategoryByIdUseCase: GetGroupCategoryByIdUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
  ) {
    this.filterService.register('customStringArray', (value: any, filter: any): boolean => {
      if (!filter) return true; // If no filter provided, show all
      else if (typeof value === 'object' && typeof filter === 'string') {
        return value.map((role: RoleEntity) => role.name).join(', ').toLowerCase().includes(filter.toLowerCase());
      }
      return false;
    });
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnDestroy(): void {

  }

  ngOnInit(): void {
    this.selectedGroup = this.groupData;
    this.cleanFields();
    this.items = [
      {
        label: this.translateService.instant('category.category_type'),
      },
      {
        label: this.translateService.instant('category.configuration'),
      }
    ];

    this.getSubjectsRequest.offset = 0;
    this.getSubjectsRequest.limit = 10;

    this.minDate = new Date();

    this.getAllRoles();
    this.onScheduleTypeChange();
    this.getAdminConfig();
    this.getAllSubjects();

    if (this.groupData) {
      this.isLoading = true;
      this.getGroupCategoryByIdUseCase.execute({ id: this.groupData.id! }).then(
        (data) => {
          this.selectedGroup = data;
          this.groupData = data;
          if (data.categoryLocations && data.categoryLocations.length > 0) {
            this.listOfOriginalCategoryLocations = data.categoryLocations.map(item => ({ ...item }));
          }
          if (data.categorySubjects && data.categorySubjects.length > 0) {
            this.listOfOriginalCategorySubjects = data.categorySubjects.map(item => ({ ...item }));
          }
          if (data.categorySchedule) {
            if (data.categorySchedule.dayTime && data.categorySchedule.dayTime.length > 0) {
              this.listOfOriginalDayTime = data.categorySchedule.dayTime.map(item => ({ ...item }));
            }
          }
          setTimeout(() => {
            this.fillFields();
          }, 300);
        },
        (e) => {
          this.loggerService.error(e);
          let responseStatus: OperationStatus = {
            status: Status.ERROR,
            message: e.message
          }
          this.operationStatus.emit(responseStatus);
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.RECORD_ID, value: this.groupData?.id!.toString() },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_GROUP_CATEGORY_BY_ID, 0, 'ERROR', '', at_attributes);
        }
      )
      .finally(() => {
        this.isLoading = false;
      });
    }

  }

  getAllRoles() {
    this.getAllRolesUseCase.execute().then(
      (data) => {
        data.forEach(role => {
          if (role.type == RoleType.SUBJECT) {
            this.allRoles.push({
              id: role.id,
              name: role.name,
              level: role.level,
              type: role.type,
              description: role.description,
              showInMenu: role.showInMenu,
              createdAt: undefined,
              updatedAt: undefined,
            });
          }
        });
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      },
    );
  }

  fillFields() {
    this.createUpdateButtonTitle = this.translateService.instant('update');
    this.form.get('groupName')?.setValue(this.groupData?.name);
    this.form.get('groupDescription')?.setValue(this.groupData?.description);
    this.selectedGroup = this.groupData;

    this.selectedGroupType = this.groupTypes.find((v, _) => v.key == this.selectedGroup?.type)
    this.selectedType = this.groupData?.type!;

    if (this.groupData?.type === GroupCategoryType.LOCATIONS) {
      this.loadingLocations = true;
      if (this.groupData.categoryLocations) {
        let listSavedDevicesLocation: CategoryLocationEntity[] = this.groupData.categoryLocations;
        listSavedDevicesLocation.forEach((device) => {
          let tmpDevLoc = this.listDeviceLocation.find((v, _) => v.id == device.deviceId);
          if (tmpDevLoc) {
            this.selectedDevicesLocation = [...this.selectedDevicesLocation, tmpDevLoc];
          }
        });
      }
      this.loadingLocations = false;
    } else if (this.groupData?.type == GroupCategoryType.SCHEDULES) {
      let sch = this.selectedGroup?.categorySchedule;
      this.selectedSchedule = this.listScheduleTypes.find((_v, _k) => _v.key == this.groupData?.attributeType);

      let schType = this.groupData.categorySchedule;

      if (this.selectedSchedule?.key == ScheduleTypeEnum.ALL || this.selectedSchedule?.key == ScheduleTypeEnum.WORKING_DAYS) {

        this.form.get('showDateInterval')?.setValue(schType?.hasDateRange);
        if (schType?.hasDateRange) {
          this.showDateRange = true;
        }
        const dateInit = new Date(schType?.dateInit?.toDate()!);
        //formatDate(`${schType?.dateInit?.month}/${schType?.dateInit?.day}/${schType?.dateInit?.year}`);
        const dateEnd = new Date(schType?.dateEnd?.toDate()!);
        //formatDate(`${schType?.dateEnd?.month}/${schType?.dateEnd?.day}/${schType?.dateEnd?.year}`);

        this.form.get('dateInit')?.setValue(dateInit);
        this.form.get('dateEnd')?.setValue(dateEnd);
        this.form.get('scheduleInit')?.setValue(schType?.timeInit);
        this.form.get('scheduleEnd')?.setValue(schType?.timeEnd);
      } else if (this.selectedSchedule?.key == ScheduleTypeEnum.CUSTOM) {
        this.form.get('showDateIntervalCustom')?.setValue(schType?.hasDateRange);
        if (schType?.hasDateRange) {
          this.showDateRange = true;
        }
        const dateInit = new Date(schType?.dateInit?.toDate()!);
        //formatDate(`${schType?.dateInit?.month}/${schType?.dateInit?.day}/${schType?.dateInit?.year}`);
        const dateEnd = new Date(schType?.dateEnd?.toDate()!);
        //formatDate(`${schType?.dateEnd?.month}/${schType?.dateEnd?.day}/${schType?.dateEnd?.year}`);

        this.form.get('dateInitCustom')?.setValue(dateInit);
        this.form.get('dateEndCustom')?.setValue(dateEnd);
        if (schType) {
          let listOfDayTime = schType.dayTime

          for (const schedule of listOfDayTime!) {
            this.form.get(`${schedule.day}Init`)?.setValue(schedule.timeInit);
            this.form.get(`${schedule.day}End`)?.setValue(schedule.timeEnd);
          }
        }
      }
    }
  }

  getAdminConfig() {
    this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
      (data) => {
        this.managerSettings = data.settings;
      },
      (e) => {
        this.loggerService.error(e);
        let status: OperationStatus = {
          status: Status.ERROR,
          message: `Getting Settings - ${e.message}`
        }
        this.operationStatus.emit(status);
        this.loggerService.error('Error Retrieving Manager Settings:');
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.APPLICATION_ID, value: environment.application },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
      }
    ).finally(() => {
      this.getKonektorPropertiesUseCase.execute().subscribe({
        next: (data) => {
          this.konektorProperties = data;
        },
        error: (e) => {
          this.loggerService.error(e);
          let status: OperationStatus = {
            status: Status.ERROR,
            message: `Getting Konektor ProPerties - ${e.message}`
          }
          this.operationStatus.emit(status);
          this.loggerService.error('Error Retrieving Manager Settings:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.APPLICATION_ID, value: environment.application },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
        },
      });
    });
    this.getSettingsByApplicationUseCase.execute({ applicationName: this.locationsApp }).then(
      (data) => {
        let config = data.settings;
        if (config?.devicesIdN) {
          let keysDevices = config.devicesIdN.keys();
          for (const keyDev of keysDevices) {
            if (config?.devicesIdN.get(keyDev)) {
              let newDevLoc: DeviceLocation;
              // Segment search
              for (const segment of config.devicesIdN?.get(keyDev) as string[]) {
                // Location search
                const locations = config.segmentsIdN?.get(segment) as string[];
                if (locations) {
                  for (const location of locations as string[]) {
                    let newDevLoc: DeviceLocation = {
                      id: location.replace(/ /g, "") + segment.replace(/ /g, "") + keyDev.replace(/ /g, ""),
                      location: location,
                      segment: segment,
                      device: keyDev
                    }
                    this.listDeviceLocation = [...this.listDeviceLocation, newDevLoc];
                  }
                }
              }
            }
          }
        }

        if (this.groupData) {
          this.loadingLocations = true;
          if (this.groupData.categoryLocations) {
            let listSavedDevicesLocation: CategoryLocationEntity[] = this.groupData.categoryLocations;
            listSavedDevicesLocation.forEach((device) => {
              let tmpDevLoc = this.listDeviceLocation.find((v, _) => v.id == device.deviceId);
              if (tmpDevLoc) {
                this.selectedDevicesLocation = [...this.selectedDevicesLocation, tmpDevLoc];
              }
            });
          }
        }
        this.loadingLocations = false;
      },
      (e) => {
        this.loggerService.error(e);
        let status: OperationStatus = {
          status: Status.ERROR,
          message: `Getting Settings - ${e.message}`
        }
        this.operationStatus.emit(status);
        this.loggerService.error('Error Retrieving Manager Settings:');
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.APPLICATION_ID, value: this.locationsApp },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  getAllSubjects() {
    this.getAllSubjectsUseCase.execute({ offset: 0, limit: 10000 }).then(
      (response) => {
        this.listUsers = response;
        if (this.groupData) {
          if (this.groupData!.categorySubjects) {
            let listSavedUsers: CategorySubjectEntity[] = this.groupData!.categorySubjects;
            listSavedUsers.forEach((user) => {
              let tmpUser = this.listUsers.find((v, _) => v.numId == user.subjectId);
              if (tmpUser) {
                this.selectedUsers = [...this.selectedUsers, tmpUser];
              }
            });
          }
        }
        this.loadingUsers = false;
      },
      (e) => {
        let status: OperationStatus = {
          status: Status.ERROR,
          message: `Getting subjects - ${e.message}`
        }
        this.operationStatus.emit(status);
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_SUBJECTS, 0, 'ERROR', '', at_attributes);
      })
  }

  setSelection(event: any): void {
    this.selectedUsers = [... event];
  }

  onActiveTabIndexChange(event: any){
    this.activeIndex = event.value;
    this.form.get('stepOptions')?.setValue(this.activeIndex);
  }

  onActiveIndexChange(event: number) {
    this.activeIndex = event;
  }

  onSelectType(event: any) {
    this.selectedType = event.value.key;
  }

  selectScheduleType(event: any) {
    this.selectScheduleType = event.value.key;
  }

  onNext() {
    this.form.get('groupName')?.markAsTouched();
    if (this.isValid('groupName')) {
      this.activeIndex += 1;
      this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }
  }

  onBack() {
    this.activeIndex -= 1;
    this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  onScheduleTypeChange() {
    this.form.get('scheduletype')?.valueChanges
      .subscribe((type) => {
        if (type) {
          this.selectedScheduleType = type.key
        }
      });
  }

  saveGroup() {
    let groupTypeSelected = this.form.get('groupType')?.value.key;
    // let groupData: GroupCategoryDto;
    if (groupTypeSelected == GroupCategoryType.USERS) {

      //!(DQ) Don't forget to verify if at least one user has been inclueded in a group, meanwhile it will commented
      /*if(!this.selectedUsers){
        let status: OperationStatus = {
          status: Status.ERROR,
          message: this.translateService.instant('')
        }
        this.operationStatus.emit(status);
        return;
      }*/

      let listOfategorySubjects: CategorySubjectEntity[] = [];

      const subjectsToBeRemoved = this.listOfOriginalCategorySubjects.filter(user =>
        !this.selectedUsers.some(selectedUser => selectedUser.numId == user.subjectId)
      );

      this.selectedUsers.forEach(user => {
        let selectedUser = new CategorySubjectEntity();
        let searchSubjectId = this.listOfOriginalCategorySubjects.find(obj => obj.subjectId === user.numId);
        if (searchSubjectId) {
          selectedUser.id = searchSubjectId.id;
          selectedUser.groupCategoryId = searchSubjectId.groupCategoryId;
        }
        selectedUser.subjectId = user.numId;
        listOfategorySubjects.push(selectedUser);
      })

      let groupCategory = new GroupCategoryEntity();

      // Check if the category ID exists to update the group's information
      if (this.groupData) {
        groupCategory.id = this.groupData.id;
      }

      groupCategory.name = this.form.get('groupName')?.value;
      groupCategory.type = GroupCategoryType.USERS;
      groupCategory.description = this.form.get('groupDescription')?.value == undefined ? '' : this.form.get('groupDescription')?.value,
        groupCategory.categorySubjects = listOfategorySubjects;

      // Remove subjects from the group of subjects
      if (subjectsToBeRemoved.length > 0) {
        this.deleteCategorySubject(subjectsToBeRemoved);
      }

      // Submit the new changes
      this.submitGroupData(groupCategory);


    } else if (groupTypeSelected == GroupCategoryType.LOCATIONS) {

      let listOfategoryLocations: CategoryLocationEntity[] = [];

      const locationsToBeRemoved = this.listOfOriginalCategoryLocations.filter(location =>
        !this.selectedDevicesLocation.some(deviceLocation => deviceLocation.id == location.deviceId)
      );

      this.selectedDevicesLocation.forEach(device => {
        let selectedLocation = new CategoryLocationEntity();
        let searchLocationId = this.listOfOriginalCategorySubjects.find(obj => obj.groupCategoryId === device.id);
        if (searchLocationId) {
          selectedLocation.id = searchLocationId.id;
          selectedLocation.groupCategoryId = searchLocationId.groupCategoryId;
        }
        selectedLocation.deviceId = device.id;
        listOfategoryLocations.push(selectedLocation);
      })

      let groupCategory = new GroupCategoryEntity();

      // Check if the category ID exists to update the group's information
      if (this.groupData) {
        groupCategory.id = this.groupData.id;
      }

      groupCategory.name = this.form.get('groupName')?.value;
      groupCategory.type = GroupCategoryType.LOCATIONS;
      groupCategory.description = this.form.get('groupDescription')?.value == undefined ? '' : this.form.get('groupDescription')?.value,
        groupCategory.categoryLocations = listOfategoryLocations;

      // Remove subjects from the group of subjects
      if (locationsToBeRemoved.length > 0) {
        this.deleteCategoryLocations(locationsToBeRemoved);
      }

      this.submitGroupData(groupCategory);

    } else if (groupTypeSelected == GroupCategoryType.SCHEDULES) {

      this.form.get('scheduletype')?.markAsTouched();

      if (!this.isValid('scheduletype')) {
        let status: OperationStatus = {
          status: Status.ERROR,
          message: this.translateService.instant('messages.invalid_shedule_type')
        }
        this.operationStatus.emit(status);
        return;
      }

      if (this.selectedScheduleType == ScheduleTypeEnum.ALL || this.selectedScheduleType == ScheduleTypeEnum.WORKING_DAYS) {

        let scheduleEntity = new CategoryScheduleEntity();
        let groupCategory = new GroupCategoryEntity();

        if (this.groupData) {
          groupCategory.id = this.groupData.id;
          scheduleEntity.id = this.groupData.categorySchedule?.id;
        }

        this.form.get('scheduleInit')?.markAsTouched();
        this.form.get('scheduleEnd')?.markAsTouched();
        if (!this.isValid('scheduleInit') || !this.isValid('scheduleEnd')) {
          return;
        }
        let timeInit = this.form.get('scheduleInit')?.value;
        let timeEnd = this.form.get('scheduleEnd')?.value;

        scheduleEntity.timeInit = timeInit;
        scheduleEntity.timeEnd = timeEnd;
        scheduleEntity.hasDateRange = this.form.get('showDateInterval')?.value;

        if (this.form.get('showDateInterval')?.value) {
          let dateInit: DateModel = new DateModel();
          dateInit.day = this.form.get('dateInit')?.value.getDate();
          dateInit.month = this.form.get('dateInit')?.value.getMonth() + 1;
          dateInit.year = this.form.get('dateInit')?.value.getFullYear();
          let dateEnd: DateModel = new DateModel();
          dateEnd.day = this.form.get('dateEnd')?.value.getDate();
          dateEnd.month = this.form.get('dateEnd')?.value.getMonth() + 1;
          dateEnd.year = this.form.get('dateEnd')?.value.getFullYear();
          scheduleEntity.dateInit = dateInit;
          scheduleEntity.dateEnd = dateEnd;
        }

        groupCategory.name = this.form.get('groupName')?.value;
        groupCategory.type = GroupCategoryType.SCHEDULES;
        groupCategory.description = this.form.get('groupDescription')?.value == undefined ? '' : this.form.get('groupDescription')?.value,
          groupCategory.attributeType = this.selectedScheduleType;
        groupCategory.categorySchedule = scheduleEntity;

        this.submitGroupData(groupCategory);

      } else if (this.selectedScheduleType == ScheduleTypeEnum.CUSTOM) {
        let groupCategoryEntity = new GroupCategoryEntity();
        let dateTimeSchedules: DayTimeScheduleEntity[] = [];

        let customSchedule: ScheduleCustom;

        let categorySchedule = new CategoryScheduleEntity();

        if (this.groupData) {
          groupCategoryEntity.id = this.groupData.id;
          categorySchedule.id = this.groupData.categorySchedule?.id;
        }

        this.days.forEach((day) => {
          if (this.form.get(day.key + 'Init')?.value && this.form.get(day.key + 'End')?.value) {

            if (new Date('1/1/1999 ' + this.form.get(day.key + 'Init')?.value) > new Date('1/1/1999 ' + this.form.get(day.key + 'End')?.value)) {
              let status: OperationStatus = {
                status: Status.ERROR,
                message: this.translateService.instant('messages.error_range_dates')
              }
              this.operationStatus.emit(status);
              throw "Error dates";
            }

            let dateTimeSchedule = new DayTimeScheduleEntity();

            let searchDayTimeId = this.listOfOriginalDayTime.find(obj => obj.day === day.key);

            if (searchDayTimeId) {
              dateTimeSchedule.categoryScheduleId = this.groupData?.categorySchedule?.id;
              dateTimeSchedule.id = searchDayTimeId.id;
            }

            dateTimeSchedule.day = day.key as string;
            dateTimeSchedule.timeInit = this.form.get(day.key + 'Init')?.value,
              dateTimeSchedule.timeEnd = this.form.get(day.key + 'End')?.value,

              dateTimeSchedules.push(dateTimeSchedule);
          }
        });

        categorySchedule.groupCategoryId = groupCategoryEntity.id;
        categorySchedule.hasDateRange = this.form.get('showDateIntervalCustom')?.value;
        categorySchedule.dayTime = dateTimeSchedules;

        if (this.form.get('showDateIntervalCustom')?.value) {
          let dateInitCustom: DateModel = new DateModel();
          dateInitCustom.day = this.form.get('dateInitCustom')?.value.getDate();
          dateInitCustom.month = this.form.get('dateInitCustom')?.value.getMonth();
          dateInitCustom.year = this.form.get('dateInitCustom')?.value.getFullYear();
          let dateEndCustom: DateModel = new DateModel();
          dateEndCustom.day = this.form.get('dateEndCustom')?.value.getDate();
          dateEndCustom.month = this.form.get('dateEndCustom')?.value.getMonth();
          dateEndCustom.year = this.form.get('dateEndCustom')?.value.getFullYear();
          categorySchedule.dateInit = dateInitCustom;
          categorySchedule.dateEnd = dateEndCustom;
        }

        if (dateTimeSchedules.length == 0) {
          let status: OperationStatus = {
            status: Status.ERROR,
            message: this.translateService.instant('messages.no_days_selected')
          }
          this.operationStatus.emit(status);
          return;
        }

        groupCategoryEntity.name = this.form.get('groupName')?.value;
        groupCategoryEntity.type = GroupCategoryType.SCHEDULES;
        groupCategoryEntity.description = this.form.get('groupDescription')?.value == undefined ? '' : this.form.get('groupDescription')?.value;
        groupCategoryEntity.attributeType = ScheduleTypeEnum.CUSTOM;
        groupCategoryEntity.categorySchedule = categorySchedule

        this.submitGroupData(groupCategoryEntity);
      }

    } else {
      return;
    }
  }

  cleanFields() {
    this.form.get('groupName')?.reset();
    this.form.get('groupType')?.reset();
    this.form.get('groupDescription')?.reset();
  }

  submitGroupData(groupCategory: GroupCategoryEntity) {
    let responseStatus: OperationStatus;
    if (this.groupData) {
      // Update the group
      this.getGroupCategoryByIdUseCase.execute({ id: groupCategory.id ?? this.groupData.id! }).then(
        (data) => {
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(groupCategory) },
          ];
          this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_CAT, ReasonActionTypeEnum.UPDATE, () => {
            this.updateGroupCategoryByIdUseCase.execute({ group: groupCategory }).then(
              (_) => {
                this.cleanFields();
                responseStatus = {
                  status: Status.SUCCESS,
                  message: ''
                }
                this.operationStatus.emit(responseStatus);
              },
              (e) => {
                this.loggerService.error(e);
                let responseStatus: OperationStatus = {
                  status: Status.ERROR,
                  message: e.message
                }
                this.operationStatus.emit(responseStatus);
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                  { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(groupCategory) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_CAT, 0, 'ERROR', '', at_attributes);
              }
            );
          }, at_attributes);
        },
        (e) => {
          this.loggerService.error(e);
          let responseStatus: OperationStatus = {
            status: Status.ERROR,
            message: e.message
          }
          this.operationStatus.emit(responseStatus);
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.RECORD_ID, value: groupCategory.id!.toString() },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_GROUP_CATEGORY_BY_ID, 0, 'ERROR', '', at_attributes);
        }
      );
      return
    }

    // Create a new group
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(groupCategory) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_CAT, ReasonActionTypeEnum.CREATE, () => {
      this.createGroupCategoryUseCase.execute({ group: groupCategory }).then(
        (_) => {
          this.cleanFields();
          responseStatus = {
            status: Status.SUCCESS,
            message: ''
          }
          this.operationStatus.emit(responseStatus);
        },
        (e) => {
          this.loggerService.error(e);
          let responseStatus: OperationStatus = {
            status: Status.ERROR,
            message: e.message
          }
          this.operationStatus.emit(responseStatus);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(groupCategory) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_CAT, 0, 'ERROR', '', at_attributes);
        }
      );
    }, at_attributes);
  }

  // Delete a list of Category subjects
  deleteCategorySubject(listOfSubjects: CategorySubjectEntity[]) {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(listOfSubjects) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_CAT, ReasonActionTypeEnum.DELETE, () => {
      this.deleteCategorySubjectUseCase.execute({ listOfSubjects: listOfSubjects }).then(
        (_) => { },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(listOfSubjects) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_CAT, 0, 'ERROR', '', at_attributes);
        }
      )
    }, at_attributes);
  }

  // Delete a list of Category locations
  deleteCategoryLocations(listOfLocations: CategoryLocationEntity[]) {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(listOfLocations) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_CAT, ReasonActionTypeEnum.DELETE, () => {
      this.deleteCategoryLocationUseCase.execute({ listOfLocations: listOfLocations }).then(
        (_) => { },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(listOfLocations) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_CAT, 0, 'ERROR', '', at_attributes);
        }
      )
    }, at_attributes);
  }

  onSelectionChange(event: any) {
    // this.loggerService.debug(event);
  }

  onSelectAllChange(event: any) {
    // this.loggerService.debug(event);
  }

  showDateInterval() {
    this.showDateRange = !this.showDateRange;
  }

  showDateIntervalCustom() {
    this.showDateRangeCustom = !this.showDateRangeCustom;
  }

  // Convert the list of roles to string
  listOfRolesToString(roles?: RoleEntity[]): string {
    let stringOfRoles: string = "";

    if (roles && roles?.length > 0) {
      stringOfRoles = roles?.map(role => role.name == 'SYSTEM_USER' ? this.translateService.instant('role_names.SYSTEM_USER') : role.name).join(', ');
    }

    return stringOfRoles
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['birthdate'].value){
      this.rangeDates = null;
      this.formGroup.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    this.rangeDates = this.formGroup.get('date')?.value;
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
  }
}
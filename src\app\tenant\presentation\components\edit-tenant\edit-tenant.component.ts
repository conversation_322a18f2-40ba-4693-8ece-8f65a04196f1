import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem, MessageService, PrimeNGConfig } from 'primeng/api';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { Country } from 'src/verazial-common-frontend/core/models/country.interface';
import { GenericKeyValue } from 'src/verazial-common-frontend/core/models/key-value.interface';
import { CountryCityService } from 'src/verazial-common-frontend/core/services/country.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { TenantEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/tenant.entity';
import { TenantStatus } from 'src/verazial-common-frontend/core/general/tenant/common/models/tenant-status.enum';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AttributeData } from 'src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model';
import { OperationType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum';
import { AddTenantRequestEntity, DbConfigEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/add-tenant-request.entity';

@Component({
  selector: 'app-edit-tenant',
  templateUrl: './edit-tenant.component.html',
  styleUrl: './edit-tenant.component.css'
})
export class EditTenantComponent implements OnInit, OnChanges {

  @Input() tenantData: TenantEntity | undefined;
  @Input() listOfTenants: TenantEntity[] = [];
  @Output() tenatDataOutput = new EventEmitter<TenantEntity>();
  @Output() onCancelTenant = new EventEmitter<void>();
  @Output() onSaveTenant = new EventEmitter<AddTenantRequestEntity>();

  showTenantId: boolean = false;

  nextSaveEnabled: boolean = false;

  items?: MenuItem[];
  stepOptions?: AttributeData[]
  activeIndex: number = 0;
  opType = OperationType;
  operationType: OperationType = OperationType.INSERT;

  isLoading: boolean = false;

  countries: Country[] = [];
  selectedCountry: Country | undefined;
  cities: GenericKeyValue[] = [];
  selectedCity: GenericKeyValue | undefined;
  selectCityDisabled: boolean = false;
  selectedStatus: GenericKeyValue | undefined;
  isTenantActive: boolean = false;

  tenantStatus: GenericKeyValue[] = [
    { key: TenantStatus.ACTIVE, value: this.translate.instant('content.active') },
    { key: TenantStatus.INACTIVE, value: this.translate.instant('content.inactive') },
    { key: TenantStatus.UNDER_VERIFICATION, value: this.translate.instant('content.under_verification') }
  ]

  // Data
  addTenantEntity?: AddTenantRequestEntity;
  tenant?: TenantEntity;
  dbConfig?: DbConfigEntity;

  public form: FormGroup = this.fb.group({
    // Tenant Entity
    name: ['', [Validators.required]],
    nif: ['', [Validators.required]],
    email: ['', [Validators.required, Validators.email]],
    country: [''],
    city: [''],
    status: ['', [Validators.required]],
    isActive: [false, [Validators.required]],
    dbEngine: ['', [Validators.required]],
    dbHost: ['', [Validators.required]],
    dbPort: ['', [Validators.required]],
    dbDatabase: ['', [Validators.required]],
    dbUsername: ['', [Validators.required]],
    dbPassword: ['', [Validators.required]],
    stepOptions: ['0'],
  });

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private primengConfig: PrimeNGConfig,
    private localStorage: LocalStorageService,
    private validatorService: ValidatorService,
    private messageService: MessageService,
    private countryCityService: CountryCityService,
    private loggerService: ConsoleLoggerService,
    private checkPermissionsService: CheckPermissionsService,
  ) { }

  ngOnInit(): void {
    this.showTenantId = this.checkPermissionsService.hasReadPermissions(AccessIdentifier.SHOW_TENANT_IDS);
    this.operationType = this.tenantData?.id ? OperationType.UPDATE : OperationType.INSERT;

    this.items = [
      {
        label: this.translate.instant('content.general'),
      },
      {
        label: this.translate.instant('tenant.database_config'),
      },
    ];
    this.stepOptions = [
      { key: this.translate.instant('content.general'), value: "0" },
      { key: this.translate.instant('tenant.database_config'), value: "1" },
    ];
    // this.isLoading = true;
    // this.getCountries(() => {
    //   if (this.tenantData) {
    //     this.fillContent();
    //     this.getCityByCountry({ value: this.selectedCountry }, () => {
    //       this.fillContent();
    //       this.isLoading = false;
    //     });
    //     this.isLoading = false;
    //   }
    //   else {
    //     this.isLoading = false;
    //   }
    // });
    this.fillContent();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.ngOnInit();
  }

  // getCountries(_callback: Function) {
  //   this.isLoading = true;
  //   this.countryCityService.getCountries().subscribe({
  //     next: (response) => {
  //       this.countries = response.data as Country[];
  //     },
  //     error: (error) => {
  //       this.loggerService.error(error);
  //     },
  //     complete: () => {
  //       _callback();
  //       this.isLoading = false;
  //     },
  //   })
  // }

  // getCityByCountry(country: any, _callback?: Function) {
  //   this.isLoading = true;
  //   this.trackDataChange();
  //   this.selectCityDisabled = true;
  //   if (country.value) {
  //     this.countryCityService.getCityByCountry(country.value.iso2).subscribe({
  //       next: (response: any) => {
  //         this.cities = response.data.map((city: string) => ({ key: city, value: city }));
  //       },
  //       error: (error) => {
  //         this.loggerService.error(error);
  //       },
  //       complete: () => {
  //         this.selectCityDisabled = false;
  //         if (_callback) {
  //           _callback();
  //         }
  //         this.isLoading = false;
  //       }
  //     })
  //   }
  // }

  fillContent() {
    if (this.tenantData) {
      this.form.get('name')?.setValue(this.tenantData.name);
      this.form.get('nif')?.setValue(this.tenantData.nif);
      this.form.get('email')?.setValue(this.tenantData.email);
      // this.selectedCountry = this.countries.find((country) => country.iso2 == this.tenantData?.country);
      // this.form.get('country')?.setValue(this.selectedCountry);
      // this.selectedCity = this.cities.find((city) => city.key == this.tenantData?.city);
      // this.form.get('city')?.setValue(this.selectedCity);
      this.selectedStatus = this.tenantStatus.find((status) => status.key == this.tenantData?.status);
      this.form.get('status')?.setValue(this.selectedStatus);
      this.isTenantActive = this.tenantData.isActive;
      this.form.get('isActive')?.setValue(this.isTenantActive);
    }
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  checkSpecificError(field: string, error: string): boolean {
    return this.validatorService.checkSpecificError(this.form, field, error);
  }

  isRequiredField(field: string): boolean {
    return this.validatorService.isRequiredField(this.form, field);
  }

  trackDataChange() {
    // this.loggerService.debug(this.form.get('country')?.value)
    if (this.isValid('name') && this.isValid('nif') && this.isValid('email') && this.isValid('country')
      && this.isValid('city') && this.isValid('status')) {
      this.tenant = new TenantEntity();
      this.tenant.id = this.tenantData?.id;
      this.tenant.name = this.form.get('name')?.value;
      this.tenant.nif = this.form.get('nif')?.value;
      this.tenant.email = this.form.get('email')?.value;
      this.tenant.country = this.tenant.country ?? ''; //this.form.get('country')?.value.iso2;
      this.tenant.city = this.tenant.city ?? ''; //this.form.get('city')?.value.key;
      this.tenant.status = this.form.get('status')?.value.key;
      this.tenant.isActive = this.form.get('isActive')?.value;
      this.dbConfig = new DbConfigEntity();
      this.dbConfig.engine = this.form.get('dbEngine')?.value;
      this.dbConfig.host = this.form.get('dbHost')?.value;
      this.dbConfig.port = this.form.get('dbPort')?.value;
      this.dbConfig.database = this.form.get('dbDatabase')?.value;
      this.dbConfig.user = this.form.get('dbUsername')?.value;
      this.dbConfig.password = this.form.get('dbPassword')?.value;

      if (this.operationType == OperationType.INSERT) {
        this.nextSaveEnabled = this.activeIndex != 0 ? this.form.valid : true;
      }
      else {
        this.nextSaveEnabled = true;
      }
    }
    else {
      this.nextSaveEnabled = false;
    }
  }

  onActiveTabIndexChange(event: any) {
    this.activeIndex = Number(event.value);
    this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
  }

  onCancel() {
    this.onCancelTenant.emit();
  }

  onBack() {
    this.activeIndex -= 1;
    this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
  }

  onNext() {
    let nif = this.form.get('nif')?.value;
    let duplicates = this.listOfTenants.filter((tenant) => tenant.nif?.toLowerCase() == nif.toLowerCase());
    console.log(duplicates);
    if (duplicates.length > 0) {
      this.messageService.add({ severity: 'error', summary: this.translate.instant('titles.error'), detail: this.translate.instant('messages.duplicate_nif') });
      return;
    }
    this.activeIndex += 1;
    this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
  }

  onSave() {
    this.addTenantEntity = new AddTenantRequestEntity();
    this.addTenantEntity.tenantModel = this.tenant;
    this.addTenantEntity.dbConfig = this.dbConfig;
    // this.loggerService.debug(this.addTenantEntity);
    this.onSaveTenant.emit(this.addTenantEntity);
  }

  validateInput(event: KeyboardEvent): boolean {
    const pattern = /^[a-zA-Z0-9]$/; // Only letters and digits
    const inputChar = event.key;

    if (!pattern.test(inputChar)) {
      event.preventDefault(); // Prevent invalid character input
      return false;
    }
    return true;
  }
}
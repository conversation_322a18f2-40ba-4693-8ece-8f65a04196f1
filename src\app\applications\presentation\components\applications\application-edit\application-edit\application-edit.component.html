<div class="grid" [formGroup]="form">
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
        <label class="label-form" for="name">{{ 'content.name' | translate }} <span *ngIf="isRequiredField('name')" class="requiredStar">*</span></label>
        <input type="text" pInputText formControlName="name" (ngModelChange)="trackDataChanges()" />
        <small *ngIf="!isValid('name') && form.controls['name'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
        <label class="label-form" for="baseIdentifier">{{ 'content.baseIdentifier' | translate }} <span *ngIf="isRequiredField('baseIdentifier')" class="requiredStar">*</span></label>
        <input type="text" pInputText formControlName="baseIdentifier" (ngModelChange)="trackDataChanges()" />
        <small *ngIf="!isValid('baseIdentifier') && form.controls['baseIdentifier'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    <div class="col-12 flex flex-column field">
        <label class="label-form" for="description">{{ 'content.description' | translate }} <span *ngIf="isRequiredField('description')" class="requiredStar">*</span></label>
        <textarea pInputTextarea formControlName="description" rows="5" cols="30" (ngModelChange)="trackDataChanges()"></textarea>
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
        <label class="label-form" for="type">{{ 'content.type' | translate }} <span *ngIf="isRequiredField('type')" class="requiredStar">*</span></label>
        <p-dropdown
            appendTo="body"
            [options]="typeOptions"
            placeholder="{{ 'content.select' | translate}}"
            optionLabel="value"
            formControlName="type"
            (onChange)="trackDataChanges()"
        ></p-dropdown>
        <small *ngIf="!isValid('type') && form.controls['type'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
</div>
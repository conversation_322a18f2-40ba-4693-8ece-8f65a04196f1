import { Observable, map, take } from "rxjs";
import { CredentialEntity } from "../../domain/entity/credential.entity";
import { CredentialRepository } from "../../domain/repositories/credential.repository";
import { CredentialMapper } from "../mapper/credential.mapper";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { CredentialModel } from "../model/credential.model";

const httpOptions = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa(environment.credential_user + ':' + environment.credential_password)
    })
};

@Injectable({
    providedIn: 'root',
})
export class CredentialRepositoryImpl extends CredentialRepository {

    credentialMapper = new CredentialMapper();

    constructor(private http: HttpClient) {
        super()
    }

    override addCredentials(credentials: CredentialEntity): Observable<any> {
        return this.http.post<CredentialModel>(environment.credentialsAPI + `credentials`, credentials, httpOptions)
            .pipe(map(this.credentialMapper.mapFrom));
    }

    override getCredentialsByNumId(param: { numId: string; }): Observable<any> {
        return this.http.get<any>(environment.credentialsAPI + `credentials/num-id/${param.numId}`, httpOptions)
            .pipe(map(this.credentialMapper.mapFrom));
    }

    override getCredentialsByNumIdAndSubjectAppId(param: { numId: string; subjectAppId: string; }): Observable<any> {
        return this.http.get<any>(environment.credentialsAPI + `credentials/${param.numId}/${param.subjectAppId}`, httpOptions)
            .pipe(map(this.credentialMapper.mapFrom));
    }

    override updateCredentialsByNumId(param: { numId: string; password: string; }): Observable<any> {
        return this.http.put<any>(environment.credentialsAPI + `credentials/update/num-id/${param.numId}/${param.password}`, {}, httpOptions)
            .pipe(take(1));
    }

    override updateCredentialPasswordByNumIdAndSubjectAppId(param: { numId: string; subjectAppId: string; password: string; }): Observable<any> {
        return this.http.put<any>(environment.credentialsAPI + `credentials/update/${param.numId}/${param.subjectAppId}/${param.password}`, {}, httpOptions)
            .pipe(take(1));
    }

    override deleteCredentialsByNumId(param: { numId: string; }): Observable<any> {
        return this.http.delete<any>(environment.credentialsAPI + `credentials/delete/${param.numId}`, httpOptions)
            .pipe(take(1));
    }

    override deleteCredentialsById(param: { id: string; }): Observable<any> {
        return this.http.delete<any>(environment.credentialsAPI + `credentials/delete/num-id/${param.id}`, httpOptions)
            .pipe(take(1));
    }

    override deleteCredentialByNumIdAndSubjectAppId(param: { numId: string; subjectAppId: string; }): Observable<any> {
        return this.http.delete<any>(environment.credentialsAPI + `credentials/delete/${param.numId}/${param.subjectAppId}`, httpOptions)
            .pipe(take(1));
    }
}

import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { UnenrollIdentityResponseEntity } from "../../domain/entity/unenroll-identity.entity";
import { UnenrollIdentityResponse } from "src/verazial-common-frontend/core/generated/api-binder/apibinder_pb";

export class ApiBinderMapper extends Mapper<UnenrollIdentityResponse, UnenrollIdentityResponseEntity> {
    override mapFrom(param: UnenrollIdentityResponse): UnenrollIdentityResponseEntity {
        return {
            biomResponse: {
                subjectId: param.getBiomresponse()?.getSubjectid(),
                status: param.getBiomresponse()?.getStatus(),
            },
        }
    }
    override mapTo(param: UnenrollIdentityResponseEntity): UnenrollIdentityResponse {
        throw new Error("Method not implemented.");
    }
}
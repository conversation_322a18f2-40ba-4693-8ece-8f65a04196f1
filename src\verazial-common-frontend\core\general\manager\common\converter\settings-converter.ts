import { AppCredentialsGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/app_credentials_pb";
import { ArrayOfAuditTrail, AuditTrailGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/audit_trail_pb";
import { AuthVerificationGrpcModel, ExpirationsGrpcModel, ExpirationTimeGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/auth_verification_pb";
import { ConcurrentSessionsGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/concurrent_sessions_pb";
import { CredentialsGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/credentials_pb";
import { EndPointsGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/end_points_pb";
import { MapOfStrings, GeneralSettingsCont1ModelGrpc } from "src/verazial-common-frontend/core/generated/manager/settings/common/general_settings_pb";
import { GitTokenGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/git_token_pb";
import { ArrayOfLicenses, LicenseGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/license_pb";
import { ArrayOfSegmentFilters, SegmentedFilterGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/segmented_filter_pb";
import { SubjectTabsGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/subject_tabs_pb";
import { TechnologiesAvailableGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/technologies_available_pb";
import { WidgetGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/widget_pb";
import { ArrayOfStrings } from "src/verazial-common-frontend/core/generated/util_pb";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";
import { AuditTrailConfig } from "../models/audit-trail-config.module";
import { ConcurrentSessions } from "../models/concurrent-sessions.model";
import { Credentials } from "../models/credentials.model";
import { EndPoint } from "../models/endpoint.model";
import { GitToken } from "../models/git-token.model";
import { LicenseData } from "../models/license-data.model";
import { SegmentedSearchFilter } from "../models/segmented-search-filter.model";
import { ServerConnections } from "../models/server-connections.model";
import { SubjectTabsConfig } from "../models/subject-tabs-config.module";
import { Technologies } from "../models/technologies.model";
import { UserVerification } from "../models/user-verification.model";
import { WidgetConfig } from "../models/widget-config.module";
import { CustomFieldModel } from "../models/custom-field.model";
import { ArrayOfCustomField, ArrayOfCustomFieldAttribute, CustomFieldGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/custom_field_pb";
import { CustomFieldTypesGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/custom_field_types_pb";
import { CustomFieldTypes } from "../models/custom-field-type.enum";
import { CustomFieldAttributeGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/custom_field_attribute_pb";
import { CustomFieldAttribute } from "../models/custom-field-attribute.model";
import { ArrayOfLdapConnections, ArrayOfLdapMethods, LdapActionGrpcEnum, LdapConnectionGrpcModel, LdapMethodGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/ldap_connection_pb";
import { LdapConnectionModel } from "../models/ldap-connection.model";
import { ExtraDataModel } from "../models/extra-data.model";
import { ArrayOfExtraData, ExtraDataGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/extra_data_pb";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";
import { AppliedToEnumGrpc } from "src/verazial-common-frontend/core/generated/manager/settings/common/applied_to_grpc_pb";
import { AppliedTo } from "../models/applied-to.enum";
import { TenantsLicenses } from "../models/tenants-licenses.model";
import { ArrayOfTenantLicenses, ArrayOfTenantsLicenses, TenantsLicensesGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/tenants_licenses_pb";
import { GenericDataGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/generic_data_pb";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { ApiMethodModel } from "../models/api-method.model";
import { ApiActionGrpcEnum, ApiExternalConnectionGrpcModel, ApiMethodGrpcModel, ArrayOfApiExternalConnections, HttpMethodGrpcEnum, ListOfApiMethodGrpcModel, StringMap } from "src/verazial-common-frontend/core/generated/manager/settings/common/api_external_connection_pb";
import { ApiActionEnum } from "../models/api-action.enum";
import { HttpMethodEnum } from "../models/http-method.enum";
import { ApiExternalConnectionModel } from "../models/api-external-connection.model";
import { LdapMethodModel } from "../models/ldap-method.model";
import { LdapActionEnum } from "../models/ldap-action.enum";
import { CustomFieldGroupModel } from "../models/custom-field-group.model";
import { ArrayOfCustomFieldGroup, CustomFieldGroupGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/custom_field_group_pb";
import { Catalog } from "../models/catalog.model";
import { ArrayOfCatalogs, CatalogGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/catalog_pb";
import { Expirations, ExpirationTime } from "../models/auth-verification.model";
import { PasswordComplexityGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/password_complexity_pb";
import { PasswordComplexity } from "../models/password-complexity.model";
import { GeneralSettingsCont1 } from "../models/general-settings.model";
import { NewLocationModel } from "../models/new-location.model";
import { ArrayOfNewLocation, NewLocationGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/new_location_pb";
import { ArrayOfEntryExitControlRoleRelationRestrictionGrpcModel, BiometricSignaturesAuthorizedRolesGrpcModel, EntryExitControlRoleRelationRestrictionGrpcModel, PrisonsSettingsGrpcModel, TransferAuthConfigGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/prisons_settings_pb";
import { BiometricSignaturesAuthorizedRolesModel, EntryExitControlRoleRelationRestrictionModel, PrisonsSettingsModel, TransferAuthConfigModel } from "../models/prisons-settings.model";
import { CustomPartnerLogoGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/custom_partner_logo_pb";
import { CustomPartnerLogoModel } from "../models/custom-partner-logo.model";
import { CronServiceJob } from "../models/cron-service-jobs";
import { ArrayOfCronServiceJobs, CronServiceJobGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/cron_service_job_pb";
import { ArrayOfLanguageRecordGrpcModel, ArrayOfTranslationGroupGrpcModel, ArrayOfTranslationGrpcModel, LanguageRecordGrpcModel, TranslationGroupGrpcModel, TranslationGrpcModel } from "src/verazial-common-frontend/core/generated/manager/settings/common/translation_pb";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "../models/translation.model";

export function convertGrpcGeneralSettingsCont1ModelToGeneralSettingsCont1(grpcGeneralSettingsCont1?: GeneralSettingsCont1ModelGrpc): GeneralSettingsCont1 {
    let generalSettings: GeneralSettingsCont1 = {
        expirations: convertGrpcExpirationsToExpirations(grpcGeneralSettingsCont1?.getExpirations()!),
        passwordComplexity: convertGrpcPasswordComplexityToPasswordComplexity(grpcGeneralSettingsCont1?.getPasswordcomplexity()!),
        exportReportReasons: convertArrayOfStringsToListOfStrings(grpcGeneralSettingsCont1?.getExportreportreasons()!),
        newLocations: convertArrayOfNewLocationsToListOfNewLocation(grpcGeneralSettingsCont1?.getNewlocations()!),
        prisonsSettings: convertGrpcPrisonsSettingsToPrisonsSettings(grpcGeneralSettingsCont1?.getPrisonssettings()!),
        autoPalmCapture: grpcGeneralSettingsCont1?.getAutopalmcapture()!,
        autoPalmCaptureTime: grpcGeneralSettingsCont1?.getAutopalmcapturetime()!,
        customPartnerLogo: convertCustomPartnerLogoGrpcModelToCustomPartnerLogoModel(grpcGeneralSettingsCont1?.getCustompartnerlogo()!),
        showSuccess: grpcGeneralSettingsCont1?.getShowsuccess() || false,
        cronServiceConfig: convertArrayOfCronServiceJobToListOfCronServiceJob(grpcGeneralSettingsCont1?.getCronserviceconfig()!),
        roleManagementReasons: convertArrayOfStringsToListOfStrings(grpcGeneralSettingsCont1?.getRolemanagementreasons()!),
        readingTimePass: grpcGeneralSettingsCont1?.getReadingtimepass() || 1,
        inputTextAreaThreshold: grpcGeneralSettingsCont1?.getInputtextareathreshold() || 25,
        userSubjectLazyLoadThreshold: grpcGeneralSettingsCont1?.getUsersubjectlazyloadthreshold() || 10000,
        prometheusCredentials: convertGrpcCredentialsToCredential(grpcGeneralSettingsCont1?.getPrometheuscredentials()),
        passAppManagementReasons: convertArrayOfStringsToListOfStrings(grpcGeneralSettingsCont1?.getPassappmanagementreasons()!),
        applicationFlowTypes: convertArrayOfStringsToListOfStrings(grpcGeneralSettingsCont1?.getApplicationflowtypes()!),
        translations: convertArrayOfTranslationGroupToListOfTranslationGroup(grpcGeneralSettingsCont1?.getTranslations()!),
        passwordManagementReasons: convertArrayOfStringsToListOfStrings(grpcGeneralSettingsCont1?.getPasswordmanagementreasons()),
    }
    return generalSettings;
}

export function convertGeneralSettingsCont1ToGrpcGeneralSettingsCont1Model(generalSettings?: GeneralSettingsCont1): GeneralSettingsCont1ModelGrpc {
    let generalSettingsGrpc = new GeneralSettingsCont1ModelGrpc();
    generalSettingsGrpc.setExpirations(convertExpirationsToGrpcExpirations(generalSettings?.expirations));
    generalSettingsGrpc.setPasswordcomplexity(convertPasswordComplexityToGrpcPasswordComplexity(generalSettings?.passwordComplexity));
    generalSettingsGrpc.setExportreportreasons(convertListOfStringsToArrayOfStrings(generalSettings?.exportReportReasons));
    generalSettingsGrpc.setNewlocations(convertListOfNewLocationToArrayOfNewLocations(generalSettings?.newLocations));
    generalSettingsGrpc.setPrisonssettings(convertPrisonsSettingsToGrpcPrisonsSettings(generalSettings?.prisonsSettings));
    generalSettingsGrpc.setAutopalmcapture(generalSettings?.autoPalmCapture!);
    generalSettingsGrpc.setAutopalmcapturetime(generalSettings?.autoPalmCaptureTime!);
    generalSettingsGrpc.setCustompartnerlogo(convertCustomPartnerLogoModelToCustomPartnerLogoGrpcModel(generalSettings?.customPartnerLogo!));
    generalSettingsGrpc.setShowsuccess(generalSettings?.showSuccess || false);
    generalSettingsGrpc.setCronserviceconfig(convertListOfCronServiceJobToArrayOfCronServiceJobs(generalSettings?.cronServiceConfig!));
    generalSettingsGrpc.setRolemanagementreasons(convertListOfStringsToArrayOfStrings(generalSettings?.roleManagementReasons!));
    generalSettingsGrpc.setReadingtimepass(generalSettings?.readingTimePass || 1);
    generalSettingsGrpc.setInputtextareathreshold(generalSettings?.inputTextAreaThreshold || 25);
    generalSettingsGrpc.setUsersubjectlazyloadthreshold(generalSettings?.userSubjectLazyLoadThreshold || 10000);
    generalSettingsGrpc.setPrometheuscredentials(convertCredentialToGrpcCredentials(generalSettings?.prometheusCredentials));
    generalSettingsGrpc.setPassappmanagementreasons(convertListOfStringsToArrayOfStrings(generalSettings?.passAppManagementReasons!));
    generalSettingsGrpc.setApplicationflowtypes(convertListOfStringsToArrayOfStrings(generalSettings?.applicationFlowTypes!));
    generalSettingsGrpc.setTranslations(converListOfTranslationGroupToArrayOfTranslationGroup(generalSettings?.translations!));
    generalSettingsGrpc.setPasswordmanagementreasons(convertListOfStringsToArrayOfStrings(generalSettings?.passwordManagementReasons));
    return generalSettingsGrpc;
}

export function convertCustomPartnerLogoGrpcModelToCustomPartnerLogoModel(grpcModel?: CustomPartnerLogoGrpcModel): CustomPartnerLogoModel {
    let model = new CustomPartnerLogoModel();
    model = {
        customLoginLogoEnabled: grpcModel?.getCustomloginlogoenabled(),
        customLoginLogoRawStaticResourceId: grpcModel?.getCustomloginlogorawstaticresourceid(),
        customLoginLogoType: grpcModel?.getCustomloginlogotype(),
        customTopBarLogoEnabled: grpcModel?.getCustomtopbarlogoenabled(),
        customTopBarLogoRawStaticResourceId: grpcModel?.getCustomtopbarlogorawstaticresourceid(),
        customTopBarLogoType: grpcModel?.getCustomtopbarlogotype()
    }
    return model;
}

export function convertCustomPartnerLogoModelToCustomPartnerLogoGrpcModel(model?: CustomPartnerLogoModel): CustomPartnerLogoGrpcModel {
    let grpcModel = new CustomPartnerLogoGrpcModel();
    grpcModel.setCustomloginlogoenabled(model?.customLoginLogoEnabled!);
    grpcModel.setCustomloginlogorawstaticresourceid(model?.customLoginLogoRawStaticResourceId!);
    grpcModel.setCustomloginlogotype(model?.customLoginLogoType!);
    grpcModel.setCustomtopbarlogoenabled(model?.customTopBarLogoEnabled!);
    grpcModel.setCustomtopbarlogorawstaticresourceid(model?.customTopBarLogoRawStaticResourceId!);
    grpcModel.setCustomtopbarlogotype(model?.customTopBarLogoType!);
    return grpcModel;
}

export function convertTechnologyGrpcToTechnology(grpcTechModel: TechnologiesAvailableGrpcModel) {
    let technology = new Technologies();
    technology = {
        iris: grpcTechModel.getIris(),
        facial: grpcTechModel.getFacial(),
        dactilar: grpcTechModel.getDactilar(),
        card: grpcTechModel.getCard(),
        pin: grpcTechModel.getPin(),
        dactilarRolled: grpcTechModel.getDactilarrolled(),
        palm: grpcTechModel.getPalm()
    };
    return technology;
}

export function convertTechnologyToTechnologyGrpc(technology: Technologies): TechnologiesAvailableGrpcModel {
    let grpcTechModel = new TechnologiesAvailableGrpcModel();
    grpcTechModel.setIris(technology.iris);
    grpcTechModel.setFacial(technology.facial);
    grpcTechModel.setDactilar(technology.dactilar);
    grpcTechModel.setCard(technology.card);
    grpcTechModel.setPin(technology.pin);
    grpcTechModel.setDactilarrolled(technology.dactilarRolled);
    grpcTechModel.setPalm(technology.palm);
    return grpcTechModel;
}

export function convertGrpcEndPointToEndpoint(grpcEndpoint?: EndPointsGrpcModel): ServerConnections {
    let endpoint: ServerConnections = {
        biographicsConnection: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getBiographicsconnection()),
        biometricsConnection: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getBiometricsconnection()),
        storageConnection: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getStorageconnection()),
        managerConnection: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getManagerconnection()),
        biographicsConnectionClient: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getBiographicsconnectionclient()),
        clockConnection: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getClockconnection()),
        actionsConnection: convertGrpcAppCredentialsToAppCredentials(grpcEndpoint?.getActionsconnection()),
    }
    return endpoint;
}

export function convertEndpointToGrpcEndPoint(endpoint?: ServerConnections) {
    let endPointsGrpcModel = new EndPointsGrpcModel();
    endPointsGrpcModel.setBiographicsconnection(convertAppCredentialsToGrpcAppCredentials(endpoint?.biographicsConnection));
    endPointsGrpcModel.setBiometricsconnection(convertAppCredentialsToGrpcAppCredentials(endpoint?.biometricsConnection));
    endPointsGrpcModel.setStorageconnection(convertAppCredentialsToGrpcAppCredentials(endpoint?.storageConnection));
    endPointsGrpcModel.setManagerconnection(convertAppCredentialsToGrpcAppCredentials(endpoint?.managerConnection));
    endPointsGrpcModel.setBiographicsconnectionclient(convertAppCredentialsToGrpcAppCredentials(endpoint?.biographicsConnectionClient));
    endPointsGrpcModel.setClockconnection(convertAppCredentialsToGrpcAppCredentials(endpoint?.clockConnection));
    endPointsGrpcModel.setActionsconnection(convertAppCredentialsToGrpcAppCredentials(endpoint?.actionsConnection));
    return endPointsGrpcModel;
}

export function convertArrayOfCustomFieldToCustomFields(arrayOfCustomFields?: ArrayOfCustomField): CustomFieldModel[] {
    let customFields: CustomFieldModel[] = [];
    arrayOfCustomFields?.getCustomfieldsList().forEach((customField) => {
        customFields.push(convertGrpcCustomFieldToCustomField(customField))
    });
    return customFields;
}

export function convertCustomFieldsToArrayOfCustomField(customFields?: CustomFieldModel[]): ArrayOfCustomField {
    let arrayOfCustomField = new ArrayOfCustomField();
    customFields?.forEach((customField) => {
        arrayOfCustomField.addCustomfields(convertCustomFieldToGrpcCustomField(customField))
    });
    return arrayOfCustomField;
}

export function convertGrpcCustomFieldToCustomField(grpcCustomField?: CustomFieldGrpcModel): CustomFieldModel {
    let customField: CustomFieldModel = {
        id: grpcCustomField?.getId(),
        parameter: grpcCustomField?.getParameter(),
        name: grpcCustomField?.getName(),
        description: grpcCustomField?.getDescription(),
        type: getGrpcCustomFieldTypesToCustomFieldTypes(grpcCustomField?.getType()!),
        fieldData: convertArrayOfCustomFieldAttributeToCustomFieldAttributes(grpcCustomField?.getFielddata()),
        group: convertGrpcCustomFieldGroupToCustomFieldGroup(grpcCustomField?.getGroup())
    }
    return customField;
}

export function convertCustomFieldToGrpcCustomField(customField?: CustomFieldModel): CustomFieldGrpcModel {
    let grpcCustomField = new CustomFieldGrpcModel();
    grpcCustomField.setId(customField?.id!);
    grpcCustomField.setParameter(customField?.parameter!);
    grpcCustomField.setName(customField?.name!);
    grpcCustomField.setDescription(customField?.description!);
    grpcCustomField.setType(getCustomFieldTypesToGrpcCustomFieldTypes(customField?.type!));
    grpcCustomField.setFielddata(convertCustomFieldAttributesToArrayOfCustomFieldAttribute(customField?.fieldData!));
    grpcCustomField.setGroup(convertCustomFieldGroupToGrpcCustomFieldGroup(customField?.group!));
    return grpcCustomField;
}

export function convertArrayOfCustomFieldGroupToCustomFieldGroups(arrayOfCustomFieldGroups?: ArrayOfCustomFieldGroup): CustomFieldGroupModel[] {
    let customFieldGroups: CustomFieldGroupModel[] = [];
    arrayOfCustomFieldGroups?.getCustomfieldgroupsList().forEach((customFieldGroup) => {
        customFieldGroups.push(convertGrpcCustomFieldGroupToCustomFieldGroup(customFieldGroup))
    });
    return customFieldGroups;
}

export function convertCustomFieldGroupsToArrayOfCustomFieldGroup(customFieldGroups?: CustomFieldGroupModel[]): ArrayOfCustomFieldGroup {
    let arrayOfCustomFieldGroup = new ArrayOfCustomFieldGroup();
    customFieldGroups?.forEach((customFieldGroup) => {
        arrayOfCustomFieldGroup.addCustomfieldgroups(convertCustomFieldGroupToGrpcCustomFieldGroup(customFieldGroup))
    });
    return arrayOfCustomFieldGroup;
}

export function convertGrpcCustomFieldGroupToCustomFieldGroup(grpcCustomFieldGroup?: CustomFieldGroupGrpcModel): CustomFieldGroupModel {
    let customFieldGroup: CustomFieldGroupModel = {
        id: grpcCustomFieldGroup?.getId(),
        name: grpcCustomFieldGroup?.getName(),
        description: grpcCustomFieldGroup?.getDescription(),
        roleId: grpcCustomFieldGroup?.getRoleid()
    }
    return customFieldGroup;
}

export function convertCustomFieldGroupToGrpcCustomFieldGroup(customFieldGroup?: CustomFieldGroupModel): CustomFieldGroupGrpcModel {
    let grpcCustomFieldGroup = new CustomFieldGroupGrpcModel();
    grpcCustomFieldGroup.setId(customFieldGroup?.id!);
    grpcCustomFieldGroup.setName(customFieldGroup?.name!);
    grpcCustomFieldGroup.setDescription(customFieldGroup?.description!);
    grpcCustomFieldGroup.setRoleid(customFieldGroup?.roleId!);
    return grpcCustomFieldGroup;
}

function getGrpcCustomFieldTypesToCustomFieldTypes(customFieldType: CustomFieldTypesGrpcModel): CustomFieldTypes {
    switch (customFieldType) {
        case CustomFieldTypesGrpcModel.INPUT:
            return CustomFieldTypes.INPUT;
        case CustomFieldTypesGrpcModel.DROPDOWN:
            return CustomFieldTypes.DROPDOWN;
        case CustomFieldTypesGrpcModel.TOGGLE:
            return CustomFieldTypes.TOGGLE;
        default:
            throw new Error('CustomFieldType not found');
    }
}

function getCustomFieldTypesToGrpcCustomFieldTypes(customFieldType: CustomFieldTypes): CustomFieldTypesGrpcModel {
    switch (customFieldType) {
        case CustomFieldTypes.INPUT:
            return CustomFieldTypesGrpcModel.INPUT;
        case CustomFieldTypes.DROPDOWN:
            return CustomFieldTypesGrpcModel.DROPDOWN;
        case CustomFieldTypes.TOGGLE:
            return CustomFieldTypesGrpcModel.TOGGLE;
        default:
            throw new Error('CustomFieldType not found');
    }
}

export function convertArrayOfCustomFieldAttributeToCustomFieldAttributes(arrayOfCustomFieldAttribute?: ArrayOfCustomFieldAttribute): CustomFieldAttribute[] {
    let customFieldAttributes: CustomFieldAttribute[] = [];
    arrayOfCustomFieldAttribute?.getFieldattributesList().forEach((grpcCustomFieldAttribute) => {
        customFieldAttributes.push({
            key: grpcCustomFieldAttribute.getKey(),
            value: grpcCustomFieldAttribute.getValue()
        });
    });
    return customFieldAttributes;
}

export function convertCustomFieldAttributesToArrayOfCustomFieldAttribute(customFieldAttributes?: CustomFieldAttribute[]): ArrayOfCustomFieldAttribute {
    let arrayOfCustomFieldAttribute = new ArrayOfCustomFieldAttribute();
    customFieldAttributes?.forEach((customFieldAttribute) => {
        let grpcCustomFieldAttribute = new CustomFieldAttributeGrpcModel();
        grpcCustomFieldAttribute.setKey(customFieldAttribute.key!);
        grpcCustomFieldAttribute.setValue(customFieldAttribute.value!);
        arrayOfCustomFieldAttribute.addFieldattributes(grpcCustomFieldAttribute);
    });
    return arrayOfCustomFieldAttribute;
}

export function convertGrpcAppCredentialsToAppCredentials(grpcAppCredencials?: AppCredentialsGrpcModel): EndPoint {
    let appCredentials: EndPoint = {
        url: grpcAppCredencials?.getUrl(),
        credentials: convertGrpcCredentialsToCredential(grpcAppCredencials?.getCredentials())
    }
    return appCredentials;
}

export function convertAppCredentialsToGrpcAppCredentials(endPoint?: EndPoint): AppCredentialsGrpcModel {
    let appCredentialsGrpcModel = new AppCredentialsGrpcModel();
    appCredentialsGrpcModel.setUrl(endPoint?.url!);
    appCredentialsGrpcModel.setCredentials(convertCredentialToGrpcCredentials(endPoint?.credentials));
    return appCredentialsGrpcModel;
}

export function convertGrpcCredentialsToCredential(grpcCredentials?: CredentialsGrpcModel): Credentials {
    let credentials: Credentials = {
        user: grpcCredentials?.getUser(),
        password: grpcCredentials?.getPassword()
    }
    return credentials
}

export function convertCredentialToGrpcCredentials(credentials?: Credentials): CredentialsGrpcModel {
    let credentialsGrpcModel = new CredentialsGrpcModel();
    credentialsGrpcModel.setUser(credentials?.user!);
    credentialsGrpcModel.setPassword(credentials?.password!);
    return credentialsGrpcModel;
}

export function convertGrpcAuditTrailToAuditTrail(grpcAuditTrail?: AuditTrailGrpcModel): AuditTrailConfig {
    let auditTrail: AuditTrailConfig = {
        actionName: grpcAuditTrail?.getActionname(),
        code: grpcAuditTrail?.getCode(),
        isEnable: grpcAuditTrail?.getIsenable()
    }
    return auditTrail;
}

export function convertAuditTrailToGrpcAuditTrail(auditTrail?: AuditTrailConfig): AuditTrailGrpcModel {
    let grpcAuditTrail = new AuditTrailGrpcModel();
    grpcAuditTrail.setActionname(auditTrail?.actionName!);
    grpcAuditTrail.setCode(auditTrail?.code!)
    grpcAuditTrail.setIsenable(auditTrail?.isEnable!)
    return grpcAuditTrail;
}

export function convertArrayGrpcAuditTrailToListAuditTrail(arrayOfAuditTrail?: ArrayOfAuditTrail): AuditTrailConfig[] {
    let listAuditTrails: AuditTrailConfig[] = [];
    arrayOfAuditTrail?.getAudittrailList().forEach((auditTrail) => {
        listAuditTrails.push(convertGrpcAuditTrailToAuditTrail(auditTrail))
    });
    return listAuditTrails;
}

export function convertListAuditTrailToArrayGrpcAuditTrail(listAuditTrails?: AuditTrailConfig[]): ArrayOfAuditTrail {
    let arrayOfAuditTrail = new ArrayOfAuditTrail();
    listAuditTrails?.forEach(value => {
        arrayOfAuditTrail.addAudittrail(convertAuditTrailToGrpcAuditTrail(value))
    })
    return arrayOfAuditTrail;
}

export function convertGrpcWidgetToWidget(grpcWidget?: WidgetGrpcModel): WidgetConfig {
    let widget: WidgetConfig = {
        url: grpcWidget?.getUrl(),
        matchKey: grpcWidget?.getMatchkey(),
        enrollKey: grpcWidget?.getEnrollkey(),
        user: grpcWidget?.getUser(),
        password: grpcWidget?.getPassword(),
    };
    return widget;
}

export function convertWidgetToGrpcWidget(widget?: WidgetConfig): WidgetGrpcModel {
    let grpcWidget = new WidgetGrpcModel();
    grpcWidget.setUrl(widget?.url!);
    grpcWidget.setMatchkey(widget?.matchKey!);
    grpcWidget.setEnrollkey(widget?.enrollKey!);
    grpcWidget.setUser(widget?.user!);
    grpcWidget.setPassword(widget?.password!);
    return grpcWidget;
}

export function convertArrayOfStringsToListOfStrings(arrayOfStrings?: ArrayOfStrings): string[] {
    let listOfStrings: string[] = [];
    arrayOfStrings?.getValList()?.forEach(val => {
        listOfStrings.push(val as string)
    });
    return listOfStrings;
}

export function convertArrayOfNewLocationsToListOfNewLocation(arrayOfLocations?: ArrayOfNewLocation): NewLocationModel[] {
    let listOfLocations: NewLocationModel[] = [];
    arrayOfLocations?.getLocationsList()?.forEach(location => {
        let newLocation: NewLocationModel = {
            name: location.getName(),
            id: location.getId(),
            relatedTo: location.getRelatedto(),
            occupiedBy: convertArrayOfStringsToListOfStrings(location.getOccupiedby()),
            children: convertArrayOfNewLocationsToListOfNewLocation(location.getChildren())
        }
        listOfLocations.push(newLocation);
    });
    return listOfLocations;
}

export function convertListOfStringsToArrayOfStrings(listOfString?: string[]): ArrayOfStrings {
    let arrayOfStrings = new ArrayOfStrings();
    listOfString?.forEach(value => {
        arrayOfStrings.addVal(value);
    })
    return arrayOfStrings;
}

export function convertListOfNewLocationToArrayOfNewLocations(listOfNewLocation?: NewLocationModel[]): ArrayOfNewLocation {
    let arrayOfNewLocation = new ArrayOfNewLocation();
    listOfNewLocation?.forEach(newLocation => {
        let location = new NewLocationGrpcModel();
        location.setName(newLocation.name!);
        location.setId(newLocation.id!);
        location.setRelatedto(newLocation.relatedTo!);
        location.setOccupiedby(convertListOfStringsToArrayOfStrings(newLocation.occupiedBy));
        location.setChildren(convertListOfNewLocationToArrayOfNewLocations(newLocation.children));
        arrayOfNewLocation.addLocations(location);
    });
    return arrayOfNewLocation;
}


export function convertGrpcConcSessionsToConcSessions(grpc?: ConcurrentSessionsGrpcModel): ConcurrentSessions {
    let concurrentSession: ConcurrentSessions = {
        numberSessions: grpc?.getSessionsallowed(),
        lockTimeIn: grpc?.getLocktimein(),
        lockTime: grpc?.getLocktime(),
        isEnable: grpc?.getIsenable(),
    }
    return concurrentSession;
}

export function convertConcSessionsToGrpcConcSessions(concurrentSession?: ConcurrentSessions): ConcurrentSessionsGrpcModel {
    let grpc = new ConcurrentSessionsGrpcModel();
    grpc.setSessionsallowed(concurrentSession?.numberSessions!);
    grpc.setLocktimein(concurrentSession?.lockTimeIn!);
    grpc.setLocktime(concurrentSession?.lockTime!);
    grpc.setIsenable(concurrentSession?.isEnable!);
    return grpc;
}

export function convertGrpcSubjectTabsToSubjectTabs(grpc: SubjectTabsGrpcModel): SubjectTabsConfig {
    let subjectTabs: SubjectTabsConfig = {
        // Additional Tabs
        showAdditionalTabs: grpc.getShowadditionaltabs(),
        // Extended Bio Fields
        showExtendedBioFieldsTab: grpc.getShowextendedbiofieldstab(),
        restrictExtendedBioFieldsTabToSpecificRoles: grpc.getRestrictextendedbiofieldstabtospecificroles(),
        extendedBioFieldsRoles: grpc.getExtendedbiofieldsroles(),
        // Physical Data
        showPhysicalDataTab: grpc.getShowphysicaldatatab(),
        restrictPhysicalDataTabToSpecificRoles: grpc.getRestrictphysicaldatatabtospecificroles(),
        physicalDataRoles: grpc.getPhysicaldataroles(),
        // Profile Picture
        showProfilePictureTab: grpc.getShowprofilepicturetab(),
        restrictProfilePictureTabToSpecificRoles: grpc.getRestrictprofilepicturetabtospecificroles(),
        profilePictureRoles: grpc.getProfilepictureroles(),
        // Relations
        showRelationsTab: grpc?.getShowrelationstab(),
        restrictRelationsTabToSpecificRoles: grpc?.getRestrictrelationstabtospecificroles(),
        relationsRoles: grpc?.getRelationsroles(),
        // Locations
        showLocationsTab: grpc?.getShowlocationstab(),
        restrictLocationsTabToSpecificRoles: grpc?.getRestrictlocationstabtospecificroles(),
        locationsRoles: grpc?.getLocationsroles(),
        // Entries and Exits
        showEntriesExitsTab: grpc?.getShowentriesexitstab(),
        restrictEntriesExitsTabToSpecificRoles: grpc?.getRestrictentriesexitstabtospecificroles(),
        entriesExitsRoles: grpc?.getEntriesexitsroles(),
        // Entry Exit Authorizations
        showEntryExitAuthorizationsTab: grpc?.getShowentryexitauthorizationstab(),
        restrictEntryExitAuthorizationsTabToSpecificRoles: grpc?.getRestrictentryexitauthorizationstabtospecificroles(),
        entryExitAuthorizationsRoles: grpc?.getEntryexitauthorizationsroles(),
        showEntryExitAuthDetails: grpc?.getShowentryexitauthdetails(),
        entryExitAuthDetailFields: convertArrayOfCustomFieldToCustomFields(grpc?.getEntryexitauthdetailfields()),
        entryExitAuthDetailFieldGroups: convertArrayOfCustomFieldGroupToCustomFieldGroups(grpc?.getEntryexitauthdetailfieldgroups()),
        // Files
        showFilesTab: grpc?.getShowfilestab(),
        restrictFilesTabToSpecificRoles: grpc?.getRestrictfilestabtospecificroles(),
        filesRoles: grpc?.getFilesroles(),
        subjectFileTypes: convertArrayOfStringsToListOfStrings(grpc?.getSubjectfiletypes()),
        acceptedFiles: grpc?.getAcceptedfiles(),
        maxFileSize: grpc?.getMaxfilesize(),
    }
    return subjectTabs
}

export function convertSubjectTabsToGrpcSubjectTabs(subjectTabs?: SubjectTabsConfig): SubjectTabsGrpcModel {
    let grpc = new SubjectTabsGrpcModel();
    // Additional Tabs
    grpc.setShowadditionaltabs(subjectTabs?.showAdditionalTabs!);
    // Extended Bio Fields
    grpc.setShowextendedbiofieldstab(subjectTabs?.showExtendedBioFieldsTab!);
    grpc.setRestrictextendedbiofieldstabtospecificroles(subjectTabs?.restrictExtendedBioFieldsTabToSpecificRoles!);
    grpc.setExtendedbiofieldsroles(subjectTabs?.extendedBioFieldsRoles!);
    // Physical Data
    grpc.setShowphysicaldatatab(subjectTabs?.showPhysicalDataTab!);
    grpc.setRestrictphysicaldatatabtospecificroles(subjectTabs?.restrictPhysicalDataTabToSpecificRoles!);
    grpc.setPhysicaldataroles(subjectTabs?.physicalDataRoles!);
    // Profile Picture
    grpc.setShowprofilepicturetab(subjectTabs?.showProfilePictureTab!);
    grpc.setRestrictprofilepicturetabtospecificroles(subjectTabs?.restrictProfilePictureTabToSpecificRoles!);
    grpc.setProfilepictureroles(subjectTabs?.profilePictureRoles!);
    // Relations
    grpc.setShowrelationstab(subjectTabs?.showRelationsTab!);
    grpc.setRestrictrelationstabtospecificroles(subjectTabs?.restrictRelationsTabToSpecificRoles!);
    grpc.setRelationsroles(subjectTabs?.relationsRoles!);
    // Locations
    grpc.setShowlocationstab(subjectTabs?.showLocationsTab!);
    grpc.setRestrictlocationstabtospecificroles(subjectTabs?.restrictLocationsTabToSpecificRoles!);
    grpc.setLocationsroles(subjectTabs?.locationsRoles!);
    // Entries and Exits
    grpc.setShowentriesexitstab(subjectTabs?.showEntriesExitsTab!);
    grpc.setRestrictentriesexitstabtospecificroles(subjectTabs?.restrictEntriesExitsTabToSpecificRoles!);
    grpc.setEntriesexitsroles(subjectTabs?.entriesExitsRoles!);
    // Entry Exit Authorizations
    grpc.setShowentryexitauthorizationstab(subjectTabs?.showEntryExitAuthorizationsTab!);
    grpc.setRestrictentryexitauthorizationstabtospecificroles(subjectTabs?.restrictEntryExitAuthorizationsTabToSpecificRoles!);
    grpc.setEntryexitauthorizationsroles(subjectTabs?.entryExitAuthorizationsRoles!);
    grpc.setShowentryexitauthdetails(subjectTabs?.showEntryExitAuthDetails!);
    grpc.setEntryexitauthdetailfields(convertCustomFieldsToArrayOfCustomField(subjectTabs?.entryExitAuthDetailFields));
    grpc.setEntryexitauthdetailfieldgroups(convertCustomFieldGroupsToArrayOfCustomFieldGroup(subjectTabs?.entryExitAuthDetailFieldGroups));
    // Files
    grpc.setShowfilestab(subjectTabs?.showFilesTab!);
    grpc.setRestrictfilestabtospecificroles(subjectTabs?.restrictFilesTabToSpecificRoles!);
    grpc.setFilesroles(subjectTabs?.filesRoles!);
    grpc.setSubjectfiletypes(convertListOfStringsToArrayOfStrings(subjectTabs?.subjectFileTypes));
    grpc.setAcceptedfiles(subjectTabs?.acceptedFiles!);
    grpc.setMaxfilesize(subjectTabs?.maxFileSize!);
    return grpc;
}

export function convertGrpcPrisonsSettingsToPrisonsSettings(grpc?: PrisonsSettingsGrpcModel): PrisonsSettingsModel {
    let prisonsSettings: PrisonsSettingsModel = {
        isPrisonsEnabled: grpc?.getIsprisonsenabled(),
        prisonerProfileId: grpc?.getPrisonerprofileid(),
        showBelongingsTab: grpc?.getShowbelongingstab(),
        showJudicialFileTab: grpc?.getShowjudicialfiletab(),
        judicialFileFields: convertArrayOfCustomFieldToCustomFields(grpc?.getJudicialfilefields()),
        judicialFileFieldGroups: convertArrayOfCustomFieldGroupToCustomFieldGroups(grpc?.getJudicialfilefieldgroups()),
        entryExitControlRoleRelationRestrictions: convertArrayOfEntryExitControlRoleRelationRestrictionGrpcModelToListOfEntryExitControlRoleRelationRestrictionModel(grpc?.getEntryexitcontrolrolerelationrestrictions()),
        transferAuthConfig: convertGrpcTransferAuthModelToTransferAuthModel(grpc?.getTransferauthconfig()),
        bioSignAuthRoles: convertGrpcBiometricSignaturesAuthorizedRolesModelToBiometricSignaturesAuthorizedRolesModel(grpc?.getBiosignauthroles()),
    }
    return prisonsSettings;
}

export function convertPrisonsSettingsToGrpcPrisonsSettings(prisonsSettings?: PrisonsSettingsModel): PrisonsSettingsGrpcModel {
    let grpc = new PrisonsSettingsGrpcModel();
    grpc.setIsprisonsenabled(prisonsSettings?.isPrisonsEnabled!);
    grpc.setPrisonerprofileid(prisonsSettings?.prisonerProfileId!);
    grpc.setShowbelongingstab(prisonsSettings?.showBelongingsTab!);
    grpc.setShowjudicialfiletab(prisonsSettings?.showJudicialFileTab!);
    grpc.setJudicialfilefields(convertCustomFieldsToArrayOfCustomField(prisonsSettings?.judicialFileFields));
    grpc.setJudicialfilefieldgroups(convertCustomFieldGroupsToArrayOfCustomFieldGroup(prisonsSettings?.judicialFileFieldGroups));
    grpc.setEntryexitcontrolrolerelationrestrictions(convertListOfEntryExitControlRoleRelationRestrictionModelToArrayOfEntryExitControlRoleRelationRestrictionGrpcModel(prisonsSettings?.entryExitControlRoleRelationRestrictions));
    grpc.setTransferauthconfig(convertTransferAuthModelToGrpcTransferAuthModel(prisonsSettings?.transferAuthConfig));
    grpc.setBiosignauthroles(convertBiometricSignaturesAuthorizedRolesModelToGrpcBiometricSignaturesAuthorizedRolesModel(prisonsSettings?.bioSignAuthRoles));
    return grpc;
}

export function convertGrpcBiometricSignaturesAuthorizedRolesModelToBiometricSignaturesAuthorizedRolesModel(grpcModel?: BiometricSignaturesAuthorizedRolesGrpcModel): BiometricSignaturesAuthorizedRolesModel {
    return {
        belongingsReceptionMainRole: grpcModel?.getBelongingsreceptionmainrole(),
        belongingsReceptionRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getBelongingsreceptionroles()),
        belongingsReturnMainRole: grpcModel?.getBelongingsreturnmainrole(),
        belongingsReturnRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getBelongingsreturnroles()),
        authorizeTransferAuthsMainRole: grpcModel?.getAuthorizetransferauthsmainrole(),
        authorizeTransferAuthsRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getAuthorizetransferauthsroles()),
        cancelTransferAuthsMainRole: grpcModel?.getCanceltransferauthsmainrole(),
        cancelTransferAuthsRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getCanceltransferauthsroles()),
        authorizePrisonerEntryExitMainRole: grpcModel?.getAuthorizeprisonerentryexitmainrole(),
        authorizePrisonerEntryExitRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getAuthorizeprisonerentryexitroles()),
        signPrisonerEntryExitMainRole: grpcModel?.getSignprisonerentryexitmainrole(),
        signPrisonerEntryExitRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getSignprisonerentryexitroles()),
    }
}

export function convertBiometricSignaturesAuthorizedRolesModelToGrpcBiometricSignaturesAuthorizedRolesModel(model?: BiometricSignaturesAuthorizedRolesModel): BiometricSignaturesAuthorizedRolesGrpcModel {
    let grpcModel = new BiometricSignaturesAuthorizedRolesGrpcModel();
    grpcModel.setBelongingsreceptionmainrole(model?.belongingsReceptionMainRole!);
    grpcModel.setBelongingsreceptionroles(convertListOfStringsToArrayOfStrings(model?.belongingsReceptionRoles));
    grpcModel.setBelongingsreturnmainrole(model?.belongingsReturnMainRole!);
    grpcModel.setBelongingsreturnroles(convertListOfStringsToArrayOfStrings(model?.belongingsReturnRoles));
    grpcModel.setAuthorizetransferauthsmainrole(model?.authorizeTransferAuthsMainRole!);
    grpcModel.setAuthorizetransferauthsroles(convertListOfStringsToArrayOfStrings(model?.authorizeTransferAuthsRoles));
    grpcModel.setCanceltransferauthsmainrole(model?.cancelTransferAuthsMainRole!);
    grpcModel.setCanceltransferauthsroles(convertListOfStringsToArrayOfStrings(model?.cancelTransferAuthsRoles));
    grpcModel.setAuthorizeprisonerentryexitmainrole(model?.authorizePrisonerEntryExitMainRole!);
    grpcModel.setAuthorizeprisonerentryexitroles(convertListOfStringsToArrayOfStrings(model?.authorizePrisonerEntryExitRoles));
    grpcModel.setSignprisonerentryexitmainrole(model?.signPrisonerEntryExitMainRole!);
    grpcModel.setSignprisonerentryexitroles(convertListOfStringsToArrayOfStrings(model?.signPrisonerEntryExitRoles));
    return grpcModel;
}

export function convertGrpcTransferAuthModelToTransferAuthModel(grpcModel?: TransferAuthConfigGrpcModel): TransferAuthConfigModel {
    return {
        responsibleSubjectRoles: convertArrayOfStringsToListOfStrings(grpcModel?.getResponsiblesubjectroles()),
        showTransferAuthDetails: grpcModel?.getShowtransferauthdetails(),
        transferAuthDetailFields: convertArrayOfCustomFieldToCustomFields(grpcModel?.getTransferauthdetailfields()),
        transferAuthDetailFieldGroups: convertArrayOfCustomFieldGroupToCustomFieldGroups(grpcModel?.getTransferauthdetailfieldgroups())
    }
}

export function convertTransferAuthModelToGrpcTransferAuthModel(model?: TransferAuthConfigModel): TransferAuthConfigGrpcModel {
    let grpcModel = new TransferAuthConfigGrpcModel();
    grpcModel.setResponsiblesubjectroles(convertListOfStringsToArrayOfStrings(model?.responsibleSubjectRoles));
    grpcModel.setShowtransferauthdetails(model?.showTransferAuthDetails!);
    grpcModel.setTransferauthdetailfields(convertCustomFieldsToArrayOfCustomField(model?.transferAuthDetailFields));
    grpcModel.setTransferauthdetailfieldgroups(convertCustomFieldGroupsToArrayOfCustomFieldGroup(model?.transferAuthDetailFieldGroups));
    return grpcModel;
}

export function convertGrpcEntryExitControlRoleRelationRestrictionModelToEntryExitControlRoleRelationRestrictionModel(grpcModel?: EntryExitControlRoleRelationRestrictionGrpcModel): EntryExitControlRoleRelationRestrictionModel {
    return {
        entryRole: grpcModel?.getEntryrole(),
        relatedRole: grpcModel?.getRelatedrole(),
    }
}

export function convertListOfEntryExitControlRoleRelationRestrictionModelToArrayOfEntryExitControlRoleRelationRestrictionGrpcModel(list?: EntryExitControlRoleRelationRestrictionModel[]): ArrayOfEntryExitControlRoleRelationRestrictionGrpcModel {
    let arrayOfEntryExitControlRoleRelationRestrictionGrpcModel = new ArrayOfEntryExitControlRoleRelationRestrictionGrpcModel();
    list?.forEach((model) => {
        arrayOfEntryExitControlRoleRelationRestrictionGrpcModel.addEntryexitcontrolrolerelationrestriction(convertEntryExitControlRoleRelationRestrictionModelToGrpcEntryExitControlRoleRelationRestrictionModel(model));
    });
    return arrayOfEntryExitControlRoleRelationRestrictionGrpcModel;
}

export function convertEntryExitControlRoleRelationRestrictionModelToGrpcEntryExitControlRoleRelationRestrictionModel(model?: EntryExitControlRoleRelationRestrictionModel): EntryExitControlRoleRelationRestrictionGrpcModel {
    let grpcModel = new EntryExitControlRoleRelationRestrictionGrpcModel();
    grpcModel.setEntryrole(model?.entryRole!);
    grpcModel.setRelatedrole(model?.relatedRole!);
    return grpcModel;
}

export function convertArrayOfEntryExitControlRoleRelationRestrictionGrpcModelToListOfEntryExitControlRoleRelationRestrictionModel(array?: ArrayOfEntryExitControlRoleRelationRestrictionGrpcModel): EntryExitControlRoleRelationRestrictionModel[] {
    let list: EntryExitControlRoleRelationRestrictionModel[] = [];
    array?.getEntryexitcontrolrolerelationrestrictionList().forEach((grpcModel) => {
        list.push(convertGrpcEntryExitControlRoleRelationRestrictionModelToEntryExitControlRoleRelationRestrictionModel(grpcModel));
    });
    return list;
}

export function convertGrpcAuthVerifToAuthVerif(grpc?: AuthVerificationGrpcModel): UserVerification {
    let userVerification: UserVerification = {
        loginAttempts: grpc?.getLoginattempts(),
        lockTimeIn: grpc?.getLocktimein(),
        lockTime: grpc?.getLocktime(),
        isEnable: grpc?.getIsenable()!,
    }
    return userVerification;
}

export function convertAuthVerifToGrpcAuthVerif(userVerification?: UserVerification): AuthVerificationGrpcModel {
    let grpc = new AuthVerificationGrpcModel();
    grpc.setLoginattempts(userVerification?.loginAttempts!);
    grpc.setLocktimein(userVerification?.lockTimeIn!);
    grpc.setLocktime(userVerification?.lockTime!);
    grpc.setIsenable(userVerification?.isEnable!);
    return grpc;
}

export function convertGrpcExpirationTimeToExpirationTime(expirationTimeGrpc?: ExpirationTimeGrpcModel): ExpirationTime {
    let expirationTime: ExpirationTime = {
        expirationTime: expirationTimeGrpc?.getExpirationtime(),
        expirationTimeIn: expirationTimeGrpc?.getExpirationtimein()
    }
    return expirationTime;
}

export function convertExpirationTimeToGrpcExpirationTime(expirationTime?: ExpirationTime): ExpirationTimeGrpcModel {
    let expirationTimeGrpc = new ExpirationTimeGrpcModel();
    expirationTimeGrpc.setExpirationtime(expirationTime?.expirationTime!);
    expirationTimeGrpc.setExpirationtimein(expirationTime?.expirationTimeIn!);
    return expirationTimeGrpc;
}

export function convertGrpcExpirationsToExpirations(expirationGrpc?: ExpirationsGrpcModel): Expirations {
    let expiration: Expirations = {
        passwordRecoveryExpiration: convertGrpcExpirationTimeToExpirationTime(expirationGrpc?.getPasswordrecoveryexpiration()),
        passwordExpiration: convertGrpcExpirationTimeToExpirationTime(expirationGrpc?.getPasswordexpiration()),
        prisonsTransferAuthExpiration: convertGrpcExpirationTimeToExpirationTime(expirationGrpc?.getPrisonstransferauthexpiration())
    }
    return expiration;
}

export function convertExpirationsToGrpcExpirations(expiration?: Expirations): ExpirationsGrpcModel {
    let expirationGrpc = new ExpirationsGrpcModel();
    expirationGrpc.setPasswordrecoveryexpiration(convertExpirationTimeToGrpcExpirationTime(expiration?.passwordRecoveryExpiration));
    expirationGrpc.setPasswordexpiration(convertExpirationTimeToGrpcExpirationTime(expiration?.passwordExpiration));
    expirationGrpc.setPrisonstransferauthexpiration(convertExpirationTimeToGrpcExpirationTime(expiration?.prisonsTransferAuthExpiration));
    return expirationGrpc;
}

export function convertGrpcPasswordComplexityToPasswordComplexity(passwordComplexityGrpc?: PasswordComplexityGrpcModel): PasswordComplexity {
    let passwordComplexity: PasswordComplexity = {
        min: passwordComplexityGrpc?.getMin(),
        max: passwordComplexityGrpc?.getMax(),
        regex: passwordComplexityGrpc?.getRegex(),
        isEnabled: passwordComplexityGrpc?.getIsenabled(),
        errorMessage: convertGrpcTranslationModelToTranslationModel(passwordComplexityGrpc?.getErrormessage()),
    }
    return passwordComplexity;
}

export function convertPasswordComplexityToGrpcPasswordComplexity(passwordComplexity?: PasswordComplexity): PasswordComplexityGrpcModel {
    let passwordComplexityGrpc = new PasswordComplexityGrpcModel();
    passwordComplexityGrpc.setMin(passwordComplexity?.min!);
    passwordComplexityGrpc.setMax(passwordComplexity?.max!);
    passwordComplexityGrpc.setRegex(passwordComplexity?.regex!);
    passwordComplexityGrpc.setIsenabled(passwordComplexity?.isEnabled!);
    passwordComplexityGrpc.setErrormessage(convertTranslationModelToGrpcTranslationModel(passwordComplexity?.errorMessage));
    return passwordComplexityGrpc;
}

export function convertGrpcTranslationModelToTranslationModel(grpcModel?: TranslationGrpcModel): TranslationModel {
    let translation: TranslationModel = {
        key: grpcModel?.getKey(),
        translations: convertArrayOfLanguageRecordGrpcModelToListOfLanguageRecordModel(grpcModel?.getTranslations())
    }
    return translation;
}

export function convertTranslationModelToGrpcTranslationModel(translation?: TranslationModel): TranslationGrpcModel {
    let grpcModel = new TranslationGrpcModel();
    grpcModel.setKey(translation?.key!);
    grpcModel.setTranslations(convertListOfLanguageRecordModelToArrayOfLanguageRecordGrpcModel(translation?.translations));
    return grpcModel;
}

export function convertArrayOfTranslationGrpcModelToListOfTranslationModel(arrayOfTranslation?: ArrayOfTranslationGrpcModel): TranslationModel[] {
    let listOfTranslation: TranslationModel[] = [];
    arrayOfTranslation?.getTranslationsList().forEach((grpcModel) => {
        listOfTranslation.push(convertGrpcTranslationModelToTranslationModel(grpcModel));
    });
    return listOfTranslation;
}

export function convertListOfTranslationModelToArrayOfTranslationGrpcModel(listOfTranslation?: TranslationModel[]): ArrayOfTranslationGrpcModel {
    let arrayOfTranslation = new ArrayOfTranslationGrpcModel();
    listOfTranslation?.forEach((translation) => {
        arrayOfTranslation.addTranslations(convertTranslationModelToGrpcTranslationModel(translation));
    });
    return arrayOfTranslation;
}

export function convertGrpcLanguageRecordModelToLanguageRecordModel(grpcModel?: LanguageRecordGrpcModel): LanguageRecordModel {
    let languageRecord: LanguageRecordModel = {
        languageCode: grpcModel?.getLanguagecode(),
        value: grpcModel?.getValue(),
    }
    return languageRecord;
}

export function convertLanguageRecordModelToGrpcLanguageRecordModel(languageRecord?: LanguageRecordModel): LanguageRecordGrpcModel {
    let grpcModel = new LanguageRecordGrpcModel();
    grpcModel.setLanguagecode(languageRecord?.languageCode!);
    grpcModel.setValue(languageRecord?.value!);
    return grpcModel;
}

export function convertArrayOfLanguageRecordGrpcModelToListOfLanguageRecordModel(arrayOfLanguage?: ArrayOfLanguageRecordGrpcModel): LanguageRecordModel[] {
    let listOfLanguageRecord: LanguageRecordModel[] = [];
    arrayOfLanguage?.getLanguagerecordsList().forEach((grpcModel) => {
        listOfLanguageRecord.push(convertGrpcLanguageRecordModelToLanguageRecordModel(grpcModel));
    });
    return listOfLanguageRecord;
}

export function convertListOfLanguageRecordModelToArrayOfLanguageRecordGrpcModel(listOfLanguageRecord?: LanguageRecordModel[]): ArrayOfLanguageRecordGrpcModel {
    let arrayOfLanguageRecord = new ArrayOfLanguageRecordGrpcModel();
    listOfLanguageRecord?.forEach((languageRecord) => {
        arrayOfLanguageRecord.addLanguagerecords(convertLanguageRecordModelToGrpcLanguageRecordModel(languageRecord));
    });
    return arrayOfLanguageRecord;
}

export function convertGrpcTranslationGroupToTranslationGroup(grpcModel?: TranslationGroupGrpcModel): TranslationGroup {
    let translationGroup: TranslationGroup = {
        id: grpcModel?.getId(),
        translations: convertArrayOfTranslationGrpcModelToListOfTranslationModel(grpcModel?.getTranslations()),
    }
    return translationGroup;
}

export function convertTranslationGroupToGrpcTranslationGroup(translationGroup?: TranslationGroup): TranslationGroupGrpcModel {
    let grpcModel = new TranslationGroupGrpcModel();
    grpcModel.setId(translationGroup?.id!);
    grpcModel.setTranslations(convertListOfTranslationModelToArrayOfTranslationGrpcModel(translationGroup?.translations));
    return grpcModel;
}

export function convertArrayOfTranslationGroupToListOfTranslationGroup(arrayOfTranslationGroup?: ArrayOfTranslationGroupGrpcModel): TranslationGroup[] {
    let translationGroup: TranslationGroup[] = [];
    arrayOfTranslationGroup?.getTranslationgroupsList().forEach((grpcModel) => {
        translationGroup.push(convertGrpcTranslationGroupToTranslationGroup(grpcModel));
    });
    return translationGroup;
}

export function converListOfTranslationGroupToArrayOfTranslationGroup(translationGroup?: TranslationGroup[]): ArrayOfTranslationGroupGrpcModel {
    let arrayOfTranslationGroup = new ArrayOfTranslationGroupGrpcModel();
    translationGroup?.forEach((translationGroup) => {
        arrayOfTranslationGroup.addTranslationgroups(convertTranslationGroupToGrpcTranslationGroup(translationGroup));
    });
    return arrayOfTranslationGroup;
}

export function convertArrayOfLicensesToListLicenseData(listLicenseGrpcData: ArrayOfLicenses): LicenseData[] {
    let listLicenseData: LicenseData[] = [];
    listLicenseGrpcData.getLicensesList().forEach(license => {
        let licenseData: LicenseData = {
            guid: license.getGuid(),
            serialNumber: license.getSerialnumber(),
            macString: license.getMacstring(),
            ipString: license.getIpstring(),
            createdAt: new Date(license.getCreatedat()?.getSeconds()!! * 1000 + Math.round(license.getCreatedat()?.getNanos()!! / 1e6)),
        }
        listLicenseData.push(licenseData);
    });
    return listLicenseData;
}

export function convertListLicenseDataToArrayOfLicenses(listLicenseData?: LicenseData[]): ArrayOfLicenses {
    let arrayOfLicenses = new ArrayOfLicenses();
    listLicenseData?.forEach(licenseData => {
        let license = new LicenseGrpcModel();
        license.setGuid(licenseData.guid!);
        license.setSerialnumber(licenseData.serialNumber!);
        license.setMacstring(licenseData.macString!);
        license.setIpstring(licenseData.ipString!);
        license.setCreatedat(dateToTimestamp(licenseData.createdAt!));
    })
    return arrayOfLicenses;
}

export function convertListOfSegmentedFiltersToArrayOfSegmentFilters(listOfSegmentedFilters?: SegmentedSearchFilter[])
    : ArrayOfSegmentFilters {
    let arrayOfSegmentFilters = new ArrayOfSegmentFilters();
    listOfSegmentedFilters?.forEach(filter => {
        let newFilter = new SegmentedFilterGrpcModel();
        newFilter.setName(filter.name);
        newFilter.setRelatedto(filter.relatedTo);
        newFilter.setSecondsearch(filter.secondSearch);
        newFilter.setValues(convertListOfStringsToArrayOfStrings(filter.values));

        arrayOfSegmentFilters.addSegmentfilters(newFilter);
    })
    return arrayOfSegmentFilters;
}

export function convertArrayOfSegmentFiltersToListOfSegmentedFilters(arrayOfSegmentFilters?: ArrayOfSegmentFilters)
    : SegmentedSearchFilter[] {
    let listOfSegmentedFilters: SegmentedSearchFilter[] = [];
    arrayOfSegmentFilters?.getSegmentfiltersList().forEach(filter => {
        let newFilter: SegmentedSearchFilter = {
            name: filter.getName(),
            relatedTo: filter.getRelatedto(),
            secondSearch: filter.getSecondsearch(),
            values: convertArrayOfStringsToListOfStrings(filter.getValues()!)
        }
        listOfSegmentedFilters.push(newFilter)
    });
    return listOfSegmentedFilters;
}

export function convertGitTokenGrpcToGitToken(gitTokenGrpc?: GitTokenGrpcModel): GitToken {
    let gitToken: GitToken = {
        token: gitTokenGrpc?.getToken(),
        expireDate: new Date(gitTokenGrpc?.getExpiredate()?.getSeconds()!! * 1000 + Math.round(gitTokenGrpc?.getExpiredate()?.getNanos()!! / 1e6))
    }
    return gitToken;
}

export function convertGitTokenToGitTokenGrpc(gitToken?: GitToken): GitTokenGrpcModel {
    let gitTokenGrpc = new GitTokenGrpcModel();
    gitTokenGrpc.setToken(gitToken?.token!);
    gitTokenGrpc.setExpiredate(dateToTimestamp(gitToken?.expireDate!))
    return gitTokenGrpc;
}

export function convertGrpcMapOfStringsToMapOfStrings(mapOfStringsGrpc?: MapOfStrings): Map<string, string[]> {
    let mapOfStrings: Map<string, string[]> = new Map();
    mapOfStringsGrpc?.getValuesMap().forEach((v, k) => {
        mapOfStrings.set(k, convertArrayOfStringsToListOfStrings(v));
    });
    return mapOfStrings;
}

export function convertMapOfStringsToGrpcMapOfStrings(mapOfStrings?: Map<string, string[]>): MapOfStrings {
    let mapOfStringsGrpc = new MapOfStrings();
    mapOfStrings?.forEach((v, k) => {
        mapOfStringsGrpc.getValuesMap().set(k, convertListOfStringsToArrayOfStrings(v));
    })
    return mapOfStringsGrpc;
}

/**
 * Converts a LdapMethodModel object to a LdapMethodGrpcModel object.
 * @param ldapMethod The LdapMethodModel object to convert.
 * @returns A LdapMethodGrpcModel object.
 */
export function convertLdapMethodToLdapMethodGrpc(ldapMethod?: LdapMethodModel): LdapMethodGrpcModel {
    const ldapMethodGrpc = new LdapMethodGrpcModel();
    ldapMethodGrpc.setAction(toEnum(LdapActionGrpcEnum, ldapMethod?.action!)!);
    ldapMethodGrpc.setRequeststructureandmapping(convertMapToStringMap(ldapMethod?.requestStructureAndMapping!));
    ldapMethodGrpc.setResponsestructureandmapping(convertMapToStringMap(ldapMethod?.responseStructureAndMapping!));
    ldapMethodGrpc.setLocalmethodname(ldapMethod?.localMethodName!);
    ldapMethodGrpc.setId(ldapMethod?.id!);
    return ldapMethodGrpc;
}

export function convertLdapMethodGrpcToLdapMethod(ldapMethodGrpc?: LdapMethodGrpcModel): LdapMethodModel {
    const ldapMethod = new LdapMethodModel();
    ldapMethod.action = toEnum(LdapActionEnum, ldapMethodGrpc?.getAction()!)!;
    ldapMethod.requestStructureAndMapping = convertStringMapToMap(ldapMethodGrpc?.getRequeststructureandmapping()!);
    ldapMethod.responseStructureAndMapping = convertStringMapToMap(ldapMethodGrpc?.getResponsestructureandmapping()!);
    ldapMethod.localMethodName = ldapMethodGrpc?.getLocalmethodname();
    ldapMethod.id = ldapMethodGrpc?.getId();
    return ldapMethod;
}

export function convertArrayOfLdapMethodsToListOfLdapMethods(arrayOfLdapMethods?: ArrayOfLdapMethods): LdapMethodModel[] {
    let listOfLdapMethods: LdapMethodModel[] = [];
    arrayOfLdapMethods?.getLdapmethodList().forEach(ldapMethod => {
        listOfLdapMethods.push(convertLdapMethodGrpcToLdapMethod(ldapMethod));
    });
    return listOfLdapMethods;
}

export function convertListOfLdapMethodsToArrayOfLdapMethods(listOfLdapMethods?: LdapMethodModel[]): ArrayOfLdapMethods {
    let arrayOfLdapMethods = new ArrayOfLdapMethods();
    listOfLdapMethods?.forEach(ldapMethod => {
        arrayOfLdapMethods.addLdapmethod(convertLdapMethodToLdapMethodGrpc(ldapMethod));
    });
    return arrayOfLdapMethods;
}

/**
 * Converts a LdapConnectionModel object to a LdapConnectionGrpcModel object.
 * @param ldapConnection The LdapConnectionModel object to convert.
 * @returns A LdapConnectionGrpcModel object.
 */
export function convertLdapConnectionToLdapConnectionGrpc(ldapConnection?: LdapConnectionModel): LdapConnectionGrpcModel {
    const ldapConnectionGrpc = new LdapConnectionGrpcModel();
    ldapConnectionGrpc.setLdaphost(ldapConnection?.ldapHost!);
    ldapConnectionGrpc.setLdapport(ldapConnection?.ldapPort!);
    ldapConnectionGrpc.setBinddn(ldapConnection?.bindDN!);
    ldapConnectionGrpc.setPassword(ldapConnection?.password!);
    ldapConnectionGrpc.setSearchbase(ldapConnection?.searchBase!);
    ldapConnectionGrpc.setAdminuserobjectguid(ldapConnection?.adminUserObjectGUID!);
    ldapConnectionGrpc.setAdminuserlocalroleid(ldapConnection?.adminUserLocalRoleId!);
    ldapConnectionGrpc.setAppliedto(toEnum(AppliedToEnumGrpc, ldapConnection?.appliedTo!)!);
    ldapConnectionGrpc.setResponsefields(convertListOfStringsToArrayOfStrings(ldapConnection?.responseFields));
    ldapConnectionGrpc.setConnectionid(ldapConnection?.connectionId!);
    ldapConnectionGrpc.setAllowupdating(ldapConnection?.allowUpdating!);
    ldapConnectionGrpc.setAllowdeleting(ldapConnection?.allowDeleting!);
    ldapConnectionGrpc.setAllowcreating(ldapConnection?.allowCreating!);
    ldapConnectionGrpc.setAllowreading(ldapConnection?.allowReading!);
    ldapConnectionGrpc.setIsactive(ldapConnection?.isActive!);
    ldapConnectionGrpc.setUsersubjectfieldid(ldapConnection?.userSubjectFieldId!);
    ldapConnectionGrpc.setFieldscanbeupdated(convertListOfStringsToArrayOfStrings(ldapConnection?.fieldsCanBeUpdated));
    ldapConnectionGrpc.setConnectionname(ldapConnection?.connectionName!);
    ldapConnectionGrpc.setUsername(ldapConnection?.username!);
    ldapConnectionGrpc.setLdapmethods(convertListOfLdapMethodsToArrayOfLdapMethods(ldapConnection?.ldapMethods));
    ldapConnectionGrpc.setSslconnection(ldapConnection?.sslConnection!);
    return ldapConnectionGrpc;
}

/**
 * Converts a LdapConnectionGrpcModel object to a LdapConnectionModel object.
 * @param ldapConnectionGrpc The LdapConnectionGrpcModel object to convert.
 * @returns A LdapConnectionModel object.
 */
export function convertLdapConnectionGrpcToLdapConnection(ldapConnectionGrpc?: LdapConnectionGrpcModel): LdapConnectionModel {
    const ldapConnection = new LdapConnectionModel();
    ldapConnection.ldapHost = ldapConnectionGrpc?.getLdaphost();
    ldapConnection.ldapPort = ldapConnectionGrpc?.getLdapport();
    ldapConnection.bindDN = ldapConnectionGrpc?.getBinddn();
    ldapConnection.password = ldapConnectionGrpc?.getPassword();
    ldapConnection.searchBase = ldapConnectionGrpc?.getSearchbase();
    ldapConnection.adminUserObjectGUID = ldapConnectionGrpc?.getAdminuserobjectguid();
    ldapConnection.adminUserLocalRoleId = ldapConnectionGrpc?.getAdminuserlocalroleid();
    ldapConnection.appliedTo = toEnum(AppliedTo, ldapConnectionGrpc?.getAppliedto()!);
    ldapConnection.responseFields = convertArrayOfStringsToListOfStrings(ldapConnectionGrpc?.getResponsefields());
    ldapConnection.connectionId = ldapConnectionGrpc?.getConnectionid();
    ldapConnection.allowUpdating = ldapConnectionGrpc?.getAllowupdating();
    ldapConnection.allowDeleting = ldapConnectionGrpc?.getAllowdeleting();
    ldapConnection.allowCreating = ldapConnectionGrpc?.getAllowcreating();
    ldapConnection.allowReading = ldapConnectionGrpc?.getAllowreading();
    ldapConnection.isActive = ldapConnectionGrpc?.getIsactive();
    ldapConnection.userSubjectFieldId = ldapConnectionGrpc?.getUsersubjectfieldid();
    ldapConnection.fieldsCanBeUpdated = convertArrayOfStringsToListOfStrings(ldapConnectionGrpc?.getFieldscanbeupdated());
    ldapConnection.connectionName = ldapConnectionGrpc?.getConnectionname();
    ldapConnection.ldapMethods = convertArrayOfLdapMethodsToListOfLdapMethods(ldapConnectionGrpc?.getLdapmethods());
    ldapConnection.sslConnection = ldapConnectionGrpc?.getSslconnection();
    return ldapConnection;
}

export function convertArrayOfLdapConnectionsToListOfLdapConnections(arrayOfLdapConnections?: ArrayOfLdapConnections): LdapConnectionModel[] {
    let listOfLdapConnections: LdapConnectionModel[] = [];
    arrayOfLdapConnections?.getLdapconnectionList().forEach((ldapConnection: LdapConnectionGrpcModel | undefined) => {
        listOfLdapConnections.push(convertLdapConnectionGrpcToLdapConnection(ldapConnection));
    });
    return listOfLdapConnections;
}

export function convertListOfLdapConnectionsToArrayOfLdapConnections(listOfLdapConnections?: LdapConnectionModel[]): ArrayOfLdapConnections {
    let arrayOfLdapConnections = new ArrayOfLdapConnections();
    listOfLdapConnections?.forEach(ldapConnection => {
        arrayOfLdapConnections.addLdapconnection(convertLdapConnectionToLdapConnectionGrpc(ldapConnection));
    });
    return arrayOfLdapConnections;
}

export function convertApiMethodToApiMethodGrpcModel(apiMethod?: ApiMethodModel): ApiMethodGrpcModel {
    const apiMethodGrpc = new ApiMethodGrpcModel();
    apiMethodGrpc.setUrl(apiMethod?.url!);
    apiMethodGrpc.setAction(toEnum(ApiActionGrpcEnum, apiMethod?.action!)!);
    apiMethodGrpc.setMethod(toEnum(HttpMethodGrpcEnum, apiMethod?.method!)!);
    apiMethodGrpc.setRequeststructureandmapping(convertMapToStringMap(apiMethod?.requestStructureAndMapping!));
    apiMethodGrpc.setResponsestructureandmapping(convertMapToStringMap(apiMethod?.responseStructureAndMapping!));
    apiMethodGrpc.setUrlparametersmapping(convertMapToStringMap(apiMethod?.urlParametersMapping!));
    apiMethodGrpc.setLocalmethodname(apiMethod?.localMethodName!);
    apiMethodGrpc.setId(apiMethod?.id!);
    return apiMethodGrpc;
}

export function convertApiMethodGrpcModelToApiMethod(apiMethodGrpc?: ApiMethodGrpcModel): ApiMethodModel {
    const apiMethod = new ApiMethodModel();
    apiMethod.url = apiMethodGrpc?.getUrl();
    apiMethod.action = toEnum(ApiActionEnum, apiMethodGrpc?.getAction()!);
    apiMethod.method = toEnum(HttpMethodEnum, apiMethodGrpc?.getMethod()!);
    apiMethod.requestStructureAndMapping = convertStringMapToMap(apiMethodGrpc?.getRequeststructureandmapping()!);
    apiMethod.responseStructureAndMapping = convertStringMapToMap(apiMethodGrpc?.getResponsestructureandmapping()!);
    apiMethod.urlParametersMapping = convertStringMapToMap(apiMethodGrpc?.getUrlparametersmapping()!);
    apiMethod.localMethodName = apiMethodGrpc?.getLocalmethodname();
    apiMethod.id = apiMethodGrpc?.getId();
    return apiMethod;
}

export function convertStringMapToMap(stringMap: StringMap): Map<string, string> {
    const map = new Map<string, string>();
    stringMap.getMapofstringsMap().forEach((value, key) => map.set(key, value));
    return map;
}

export function convertMapToStringMap(map: Map<string, string>): StringMap {
    const stringMap = new StringMap();
    map.forEach((value, key) => stringMap.getMapofstringsMap().set(key, value));
    return stringMap;
}

/**
 * Converts an array of ApiMethodGrpcModel objects to a list of ApiMethodModel objects.
 * @param arrayOfApiMethods The array of ApiMethodGrpcModel objects to convert.
 * @returns A list of ApiMethodModel objects.
 */
export function convertArrayOfApiMethodsToListOfApiMethods(arrayOfApiMethods?: ListOfApiMethodGrpcModel): ApiMethodModel[] {
    let listOfApiMethods: ApiMethodModel[] = [];
    arrayOfApiMethods?.getApimethodList().forEach(apiMethod => {
        listOfApiMethods.push(convertApiMethodGrpcModelToApiMethod(apiMethod));
    });
    return listOfApiMethods;
}

/**
 * Converts a list of ApiMethodModel objects to an array of ApiMethodGrpcModel objects.
 * @param listOfApiMethods The list of ApiMethodModel objects to convert.
 * @returns An array of ApiMethodGrpcModel objects.
 */
export function convertListOfApiMethodsToArrayOfApiMethods(listOfApiMethods?: ApiMethodModel[]): ListOfApiMethodGrpcModel {
    let arrayOfApiMethods = new ListOfApiMethodGrpcModel();
    listOfApiMethods?.forEach(apiMethod => {
        arrayOfApiMethods.addApimethod(convertApiMethodToApiMethodGrpcModel(apiMethod));
    });
    return arrayOfApiMethods;
}

/**
 * Converts an ApiExternalConnectionGrpcModel object to an ApiExternalConnectionModel object.
 * @param apiExternalConnectionGrpc The ApiExternalConnectionGrpcModel object to convert.
 * @returns An ApiExternalConnectionModel object.
 */
export function convertApiExternalConnectionGrpcToApiExternalConnection(apiExternalConnectionGrpc?: ApiExternalConnectionGrpcModel): ApiExternalConnectionModel {
    const apiExternalConnection = new ApiExternalConnectionModel();
    apiExternalConnection.connectionId = apiExternalConnectionGrpc?.getConnectionid();
    apiExternalConnection.credentials = convertGrpcCredentialsToCredential(apiExternalConnectionGrpc?.getCredentials());
    apiExternalConnection.apiToken = apiExternalConnectionGrpc?.getApitoken();
    apiExternalConnection.adminUserObjectGUID = apiExternalConnectionGrpc?.getAdminuserobjectguid();
    apiExternalConnection.adminUserLocalRoleId = apiExternalConnectionGrpc?.getAdminuserlocalroleid();
    apiExternalConnection.appliedTo = toEnum(AppliedTo, apiExternalConnectionGrpc?.getAppliedto()!);
    apiExternalConnection.apiMethods = convertArrayOfApiMethodsToListOfApiMethods(apiExternalConnectionGrpc?.getApimethods());
    apiExternalConnection.allowUpdating = apiExternalConnectionGrpc?.getAllowupdating();
    apiExternalConnection.allowDeleting = apiExternalConnectionGrpc?.getAllowdeleting();
    apiExternalConnection.allowCreating = apiExternalConnectionGrpc?.getAllowcreating();
    apiExternalConnection.allowReading = apiExternalConnectionGrpc?.getAllowreading();
    apiExternalConnection.isActive = apiExternalConnectionGrpc?.getIsactive();
    apiExternalConnection.userSubjectFieldId = apiExternalConnectionGrpc?.getUsersubjectfieldid();
    apiExternalConnection.connectionName = apiExternalConnectionGrpc?.getConnectionname();
    return apiExternalConnection;
}

/**
 * Converts an ApiExternalConnectionModel object to an ApiExternalConnectionGrpcModel object.
 * @param apiExternalConnection The ApiExternalConnectionModel object to convert.
 * @returns An ApiExternalConnectionGrpcModel object.
 */
export function convertApiExternalConnectionToApiExternalConnectionGrpc(apiExternalConnection?: ApiExternalConnectionModel): ApiExternalConnectionGrpcModel {
    const apiExternalConnectionGrpc = new ApiExternalConnectionGrpcModel();
    apiExternalConnectionGrpc.setConnectionid(apiExternalConnection?.connectionId!);
    apiExternalConnectionGrpc.setCredentials(convertCredentialToGrpcCredentials(apiExternalConnection?.credentials!));
    apiExternalConnectionGrpc.setApitoken(apiExternalConnection?.apiToken!);
    apiExternalConnectionGrpc.setAdminuserobjectguid(apiExternalConnection?.adminUserObjectGUID!);
    apiExternalConnectionGrpc.setAdminuserlocalroleid(apiExternalConnection?.adminUserLocalRoleId!);
    apiExternalConnectionGrpc.setAppliedto(toEnum(AppliedToEnumGrpc, apiExternalConnection?.appliedTo!)!);
    apiExternalConnectionGrpc.setApimethods(convertListOfApiMethodsToArrayOfApiMethods(apiExternalConnection?.apiMethods!));
    apiExternalConnectionGrpc.setAllowupdating(apiExternalConnection?.allowUpdating!);
    apiExternalConnectionGrpc.setAllowdeleting(apiExternalConnection?.allowDeleting!);
    apiExternalConnectionGrpc.setAllowcreating(apiExternalConnection?.allowCreating!);
    apiExternalConnectionGrpc.setAllowreading(apiExternalConnection?.allowReading!);
    apiExternalConnectionGrpc.setIsactive(apiExternalConnection?.isActive!);
    apiExternalConnectionGrpc.setConnectionname(apiExternalConnection?.connectionName!);
    apiExternalConnectionGrpc.setUsersubjectfieldid(apiExternalConnection?.userSubjectFieldId!);
    return apiExternalConnectionGrpc;
}

/**
 * Converts an array of ApiExternalConnectionGrpcModel objects to a list of ApiExternalConnectionModel objects.
 * @param arrayOfApiExternalConnections The array of ApiExternalConnectionGrpcModel objects to convert.
 * @returns A list of ApiExternalConnectionModel objects.
 */
export function convertArrayOfApiExternalConnectionsToListOfApiExternalConnections(arrayOfApiExternalConnections?: ArrayOfApiExternalConnections): ApiExternalConnectionModel[] {
    let listOfApiExternalConnections: ApiExternalConnectionModel[] = [];
    arrayOfApiExternalConnections?.getApiexternalconnectionList().forEach(apiExternalConnection => {
        listOfApiExternalConnections.push(convertApiExternalConnectionGrpcToApiExternalConnection(apiExternalConnection));
    });
    return listOfApiExternalConnections;
}

export function convertListOfApiExternalConnectionsToArrayOfApiExternalConnections(listOfApiExternalConnections?: ApiExternalConnectionModel[]): ArrayOfApiExternalConnections {
    let arrayOfApiExternalConnections = new ArrayOfApiExternalConnections();
    listOfApiExternalConnections?.forEach(apiExternalConnection => {
        arrayOfApiExternalConnections.addApiexternalconnection(convertApiExternalConnectionToApiExternalConnectionGrpc(apiExternalConnection));
    });
    return arrayOfApiExternalConnections;
}

export function convertArrayOfExtraDataToExtraData(arrayOfExtraData?: ArrayOfExtraData): ExtraDataModel[] {
    let extraData: ExtraDataModel[] = [];
    arrayOfExtraData?.getExtradataList().forEach((grpcExtraData) => {
        extraData.push({
            name: grpcExtraData.getName(),
            parameter: grpcExtraData.getParameter(),
            description: grpcExtraData.getDescription()
        });
    });
    return extraData;
}

export function convertExtraDataToArrayOfExtraData(extraData?: ExtraDataModel[]): ArrayOfExtraData {
    let arrayOfExtraData = new ArrayOfExtraData();
    extraData?.forEach((extraDataItem) => {
        let grpcExtraData = new ExtraDataGrpcModel();
        grpcExtraData.setName(extraDataItem.name!);
        grpcExtraData.setParameter(extraDataItem.parameter!);
        grpcExtraData.setDescription(extraDataItem.description!);

        arrayOfExtraData.addExtradata(grpcExtraData);
    });
    return arrayOfExtraData;
}

export function convertGenericToGenericDataGrpcModel(data: GenericKeyValue): GenericDataGrpcModel {
    let genericDataGrpcModel = new GenericDataGrpcModel();

    genericDataGrpcModel.setKey(data.key as string);
    genericDataGrpcModel.setValue(Number(data.value));

    return genericDataGrpcModel;
}

export function convertGenericDataGrpcModelToGeneric(genericDataGrpc: GenericDataGrpcModel, tenantId: string): GenericKeyValue {
    let generic: GenericKeyValue = {
        id: tenantId,
        key: genericDataGrpc.getKey(),
        value: genericDataGrpc.getValue().toString()
    }
    return generic;
}

export function convertArrayOfTenantLicensesToListOfTenantLicenses(arrayOfTenantLicenses?: ArrayOfTenantLicenses, tenantId?: string):
    GenericKeyValue[] {
    let listOfTenantLicenses: GenericKeyValue[] = [];
    arrayOfTenantLicenses?.getLicenseList().forEach(license => {

        listOfTenantLicenses.push(
            convertGenericDataGrpcModelToGeneric(license, tenantId as string)
        )
    });
    return listOfTenantLicenses;
}

export function convertListOfTenantLicensesToArrayOfTenantLicenses(listOfTenantLicenses?: GenericKeyValue[]):
    ArrayOfTenantLicenses {
    let arrayOfTenantLicenses = new ArrayOfTenantLicenses();

    listOfTenantLicenses?.forEach(license => {
        arrayOfTenantLicenses.addLicense(
            convertGenericToGenericDataGrpcModel(license)
        )
    });

    return arrayOfTenantLicenses;
}

export function convertTenantsLicensesToTenantLicensesGrpcModel(tenantsLicenses: TenantsLicenses): TenantsLicensesGrpcModel {
    let tenantsLicensesGrpcModel = new TenantsLicensesGrpcModel();

    tenantsLicensesGrpcModel.setTenantid(tenantsLicenses.tenantId!);
    tenantsLicensesGrpcModel.setLicensesinfo(
        convertListOfTenantLicensesToArrayOfTenantLicenses(tenantsLicenses.licensesInfo!)
    )

    return tenantsLicensesGrpcModel;
}

export function convertTenantLicensesGrpcModelToTenantsLicenses(tenantsLicensesGrpcModel?: TenantsLicensesGrpcModel):
    TenantsLicenses {
    let tenantsLicenses = new TenantsLicenses();

    tenantsLicenses.tenantId = tenantsLicensesGrpcModel?.getTenantid();
    tenantsLicenses.licensesInfo =
        convertArrayOfTenantLicensesToListOfTenantLicenses(tenantsLicensesGrpcModel?.getLicensesinfo()!, tenantsLicensesGrpcModel?.getTenantid()!)

    return tenantsLicenses;
}

export function convertArrayOfTenantsLicensesToListOfTenantsLicenses(arrayOfTenantLicenses?: ArrayOfTenantsLicenses):
    TenantsLicenses[] {
    let tenantsLicenses: TenantsLicenses[] = [];

    arrayOfTenantLicenses?.getLicensesList().forEach(data => {
        tenantsLicenses.push(
            convertTenantLicensesGrpcModelToTenantsLicenses(data)
        )
    });
    return tenantsLicenses;
}

export function convertListOfTenantsLicensesToArrayOfTenantsLicenses(tenantsLicenses?: TenantsLicenses[]):
    ArrayOfTenantsLicenses {
    let arrayOfTenantsLicenses = new ArrayOfTenantsLicenses();

    tenantsLicenses?.forEach(license => {
        arrayOfTenantsLicenses.addLicenses(
            convertTenantsLicensesToTenantLicensesGrpcModel(license)
        )
    })

    return arrayOfTenantsLicenses;
}

export function convertCatalogToCatalogGrpcModel(catalog: Catalog) {
    let catalogGrpcModel = new CatalogGrpcModel();
    catalogGrpcModel.setId(catalog.id!);
    catalogGrpcModel.setParameter(catalog.parameter!);
    catalogGrpcModel.setName(catalog.name!);
    catalogGrpcModel.setDescription(catalog.description!);
    catalogGrpcModel.setOptions(catalog.options!);
    return catalogGrpcModel;
}

export function convertCatalogGrpcModelToCatalog(catalogGrpcModel: CatalogGrpcModel): Catalog {
    let catalog: Catalog = {
        id: catalogGrpcModel.getId(),
        parameter: catalogGrpcModel.getParameter(),
        name: catalogGrpcModel.getName(),
        description: catalogGrpcModel.getDescription(),
        options: catalogGrpcModel.getOptions()
    }
    return catalog;
}

export function convertCronServiceJobToCronServiceJobGrpcModel(model: CronServiceJob) {
    let grpcModel = new CronServiceJobGrpcModel();
    grpcModel.setId(model.id!);
    grpcModel.setParameter(model.parameter!);
    grpcModel.setName(model.name!);
    grpcModel.setDescription(model.description!);
    grpcModel.setDailywithintimerange(model.dailyWithinTimeRange!);
    grpcModel.setTimeinit(dateToTimestamp(model.timeInit!));
    grpcModel.setTimeend(dateToTimestamp(model.timeEnd!));
    grpcModel.setTime(model.time!);
    grpcModel.setTimein(model.timeIn!);
    grpcModel.setActive(model.active!);
    grpcModel.setLanguage(model.language!);
    return grpcModel;
}

export function convertCronServiceJobGrpcModelToCronServiceJob(grpcModel: CronServiceJobGrpcModel): CronServiceJob {
    let model: CronServiceJob = {
        id: grpcModel.getId(),
        parameter: grpcModel.getParameter(),
        name: grpcModel.getName(),
        description: grpcModel.getDescription(),
        dailyWithinTimeRange: grpcModel.getDailywithintimerange(),
        timeInit: new Date(grpcModel.getTimeinit()?.getSeconds()!! * 1000 + Math.round(grpcModel.getTimeinit()?.getNanos()!! / 1e6)),
        timeEnd: new Date(grpcModel.getTimeend()?.getSeconds()!! * 1000 + Math.round(grpcModel.getTimeend()?.getNanos()!! / 1e6)),
        time: grpcModel.getTime(),
        timeIn: grpcModel.getTimein(),
        active: grpcModel.getActive(),
        language: grpcModel.getLanguage()
    }
    return model;
}

export function convertCatalogsToArrayOfCatalogs(catalogs?: Catalog[]): ArrayOfCatalogs {
    let arrayOfCatalogs = new ArrayOfCatalogs();
    catalogs?.forEach(catalog => {
        arrayOfCatalogs.addCatalogs(convertCatalogToCatalogGrpcModel(catalog));
    });
    return arrayOfCatalogs;
}

export function convertArrayOfCatalogsToCatalogs(arrayOfCatalogs?: ArrayOfCatalogs): Catalog[] {
    let catalogs: Catalog[] = [];
    arrayOfCatalogs?.getCatalogsList().forEach(catalog => {
        catalogs.push(convertCatalogGrpcModelToCatalog(catalog));
    });
    return catalogs;
}

export function convertListOfCronServiceJobToArrayOfCronServiceJobs(list?: CronServiceJob[]): ArrayOfCronServiceJobs {
    let arrayOfCronServiceJobs = new ArrayOfCronServiceJobs();
    list?.forEach(job => {
        arrayOfCronServiceJobs.addCronservicejobs(convertCronServiceJobToCronServiceJobGrpcModel(job));
    });
    return arrayOfCronServiceJobs;
}

export function convertArrayOfCronServiceJobToListOfCronServiceJob(array?: ArrayOfCronServiceJobs): CronServiceJob[] {
    let list: CronServiceJob[] = [];
    array?.getCronservicejobsList().forEach(job => {
        list.push(convertCronServiceJobGrpcModelToCronServiceJob(job));
    });
    return list;
}
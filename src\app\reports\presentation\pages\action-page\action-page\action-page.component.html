<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>
<p-toast></p-toast>
<div class="main-container">

    <!--<div class="main-progress">
        <p-progressBar [value]="progressValue" [color]="progressColor" [showValue]="progressEnabled">
            <ng-template pTemplate="content" let-value style="background-color: #0ab4ba; border: solid 1px white; height: 10px "></ng-template>
        </p-progressBar>
    </div>-->
    <div class="topBarItems" id="topBar">
        <div class="leftSideItems">
            <div class="recrodsTitle">
                {{'menu.reports_actions' | translate}}
            </div>
        </div>
        <div class="rightSideItems">
            <div [formGroup]="datesForm" class="centerDiv searchButtonGroupSmall">


                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.applicationId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}" id="application" formControlName="application"
                            [(ngModel)]="selectedApp" [options]="appOptions" optionLabel="name" dataKey="name"
                            optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedApp">
                                    <div>{{ selectedApp | translate}}</div>
                                </div>
                            </ng-template>
                            <ng-template let-appOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ appOption.name | translate}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.actionId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}" id="action" formControlName="action"
                            [(ngModel)]="selectedAction" [options]="actionOptions" optionLabel="name" dataKey="name"
                            optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedAction">
                                    <div>{{ selectedAction | translate }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-actionOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ actionOption.name | translate }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.receiverId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <input pInputText type="text"
                        formControlName="subject"
                    />
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-calendar appendTo="body" formControlName="rangeDates" [iconDisplay]="'input'" [showIcon]="true"
                            [(ngModel)]="dates" selectionMode="range" [class.ng-invalid]="dateError"
                            [class.ng-dirty]="dateError" aria-describedby="date-help1" [readonlyInput]="true"
                            inputId="multiple" dateFormat="{{ 'dateFormat' | translate }}" [showButtonBar]="true"[showTime]="true"
                            [hourFormat]="'24'"></p-calendar>
                        <div *ngIf="dateError">
                            <small class="error" id="date-help1">{{ dateErrorMessage | translate }}</small>
                        </div>
                    </div>
                </div>
                <!-- <ngx-verazial-ui-field label="{{ 'signaturesTable.endDate' | translate }}" id="endDate" formControlName="endDate" type="date" item></ngx-verazial-ui-field> -->
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}"
                    class="searchButton searchButtonNormal" label="{{ 'update' | translate }}" [rounded]="true"
                    (click)="getAllActions()"></p-button>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}"
                    class="searchButton searchButtonSmall" icon="pi pi-refresh" [rounded]="true"
                    (click)="getAllActions()"></p-button>
                <!-- <ngx-verazial-ui-button class="searchButton" text="{{ 'update' | translate }}" backgroundColor="#162746" fontColor="white" (click)="getAllActions()" button></ngx-verazial-ui-button> -->
            </div>
        </div>
    </div>
    <div class="card">
        <div class="container-plot">
            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '260px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions2' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions12' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData1"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            verificationComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData1"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '285px', height: '120px' }" styleClass="card-m">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions3' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions13' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData2"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            identificationComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData2"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '295px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions4' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions14' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData3"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            newSubjectComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData3"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '295px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions5' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions15' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData4"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            deletionComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData4"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <!--<div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '295px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tActions6' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tActions16' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData5" style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{ modBioComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData5" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>

                </p-card>
            </div>-->

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '255px', height: '120px' }">


                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions7' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions17' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData6"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            newSampleComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData6"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>


                </p-card>
            </div>

            <!--<div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '285px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tActions9' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tActions19' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData7" style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{ newPictureComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData7" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>

                </p-card>
            </div>-->

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '275px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions8' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions18' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData8"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            deletionSamplesComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData8"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <!--<div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '275px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tActions10' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tActions20' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData9" style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{ passwordLoginComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData9" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>

                </p-card>
            </div>-->


            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '335px', height: '120px' }">


                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{
                            'reports.tActions11' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{
                            'reports.tActions21' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData10"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            noDevComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData10"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>
        </div>

        <app-list-actions [readOnly]="true" [readAndWritePermissions]="false" [listOfActions]="virtualActionsData"
            (details)="showDetails($event)" [isAddingData]="addingData"></app-list-actions>

        <!-- Create new role -->
        <p-dialog [(visible)]="showDetailsDialog" styleClass="p-fluid custom-dialog" [modal]="true" [closable]="true">
            <ng-template pTemplate="content">
                <div class="flex flex-column m-4" style="margin-top: 0px !important">
                    <p class="detail-header">{{'signaturesTable.details' | translate}}</p>
                    <pre style="white-space: pre-wrap; word-wrap: break-word; word-break: break-word; max-width: 100%">{{ detailsDialogText }}</pre>
                </div>
            </ng-template>
        </p-dialog>

        <div class="container-plot">
            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '300px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{'reports.tActions22'
                            | translate}}</label>
                        <label *ngIf="!showSpinners"
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">
                            (s)</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData12"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            totalTimeNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData12"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '300px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{'reports.tActions23'
                            | translate}}</label>
                        <label *ngIf="!showSpinners"
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">
                            (s)</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData13"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            userTimeNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData13"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '300px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{'reports.tActions24'
                            | translate}}</label>
                        <label *ngIf="!showSpinners"
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">
                            (s)</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData14"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            netTimeNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData14"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>


                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '300px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners"
                            style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{'reports.tActions25'
                            | translate}}</label>
                        <label *ngIf="!showSpinners"
                            style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">
                            (s)</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top: 1rem">
                        <label *ngIf="!showNoData15"
                            style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{
                            serverTimeNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData15"
                            style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData'
                            | translate }}</label>
                    </div>

                </p-card>
            </div>

        </div>

    </div>
</div>
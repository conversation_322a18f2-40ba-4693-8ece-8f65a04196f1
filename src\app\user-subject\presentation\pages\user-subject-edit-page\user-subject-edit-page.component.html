<p-toast />
<p-confirmDialog />
<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<app-user-not-verified [userIsVerified]="userIsVerified" [subjectType]="isUser ? '_user' : ''" [verifyReady]="widgetReady && !verifyReady" (modified)="userVerifying($event)"></app-user-not-verified>
<p-card *ngIf="userSubject">
    <app-user-subject-card
        [canReadAndWrite]="canReadAndWrite"
        [userSubject]="userSubject"
        [isVerified]="verified"
        [userIsVerified]="userIsVerified"
        [showButtons]="true"
        [disableButtons]="modified"
        [verifyBtnAvailable]="isTenantValid() && verifyBtnAvailable && userIsVerified && !modified"
        [disabledVerifyBtnToolTip]="disabledVerifyBtnToolTip"
        [editMode]="editMode"
        [editEnabled]="allowEditAfterIdSearch || verified"
        [deleteEnabled]="allowDeleteAfterIdSearch || verified"
        (result)="UserSubjectCardResult($event)"
    ></app-user-subject-card>
</p-card>
<div *ngIf="userSubject" class="mt-1">
    <p-tabView
        [(activeIndex)]="activeIndex"
        (onChange)="onTabChange($event)"
        *ngIf="subjectTabsConfig.showAdditionalTabs else widgetOnlyContainer"
        [scrollable]="true"
    >
        <p-tabPanel [disabled]="this.modified" header="{{ 'titles.biometric_data' | translate }}">
            <div class="flex justify-content-center mt-2">
                <ng-container *ngTemplateOutlet="widget" />
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="subjectTabsConfig.showExtendedBioFieldsTab && isTabEnabled(subjectTabsConfig.restrictExtendedBioFieldsTabToSpecificRoles, subjectTabsConfig.extendedBioFieldsRoles)" header="{{ 'titles.extended_biographic_data' | translate }}">
            <div class="flex justify-content-center mt-2">
                <ng-container *ngTemplateOutlet="dynamicFormContent" />
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="subjectTabsConfig.showPhysicalDataTab && isTabEnabled(subjectTabsConfig.restrictPhysicalDataTabToSpecificRoles, subjectTabsConfig.physicalDataRoles)" header="{{ 'titles.physical_data' | translate }}">
            <div class="mt-2 mx-6">
                <app-physical-data
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [canReadAndWrite]="canReadAndWrite && verified && userIsVerified"
                    [isUser]="isUser"
                    [userIsVerified]="userIsVerified"
                    [subjectIsVerified]="verified"
                ></app-physical-data>
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="subjectTabsConfig.showFilesTab && isTabEnabled(subjectTabsConfig.restrictFilesTabToSpecificRoles, subjectTabsConfig.filesRoles)" header="{{ 'titles.subject_files' | translate }}">
            <div class="mt-2 mx-6">
                <app-subject-files
                    [readAndWritePermissions]="canReadAndWrite && userIsVerified"
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [userIsVerified]="userIsVerified"
                    [subjectIsVerified]="verified"
                    [managerSettings]="managerSettings"
                ></app-subject-files>
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="subjectTabsConfig.showProfilePictureTab && isTabEnabled(subjectTabsConfig.restrictProfilePictureTabToSpecificRoles, subjectTabsConfig.profilePictureRoles)" header="{{ 'titles.profile_pic_history' | translate }}">
            <div class="mt-2 mx-6">
                <app-pic-history
                    [parentLoading]="imageUpdating"
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [canReadAndWrite]="canReadAndWrite && verified && userIsVerified"
                    [isUser]="isUser"
                ></app-pic-history>
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="subjectTabsConfig.showRelationsTab && isTabEnabled(subjectTabsConfig.restrictRelationsTabToSpecificRoles, subjectTabsConfig.relationsRoles)" header="{{ 'titles.related_subjects' | translate }}">
            <div class="mt-2 mx-6">
                <app-related-subjects
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [userIsVerified]="userIsVerified"
                    [managerSettings]="managerSettings"
                ></app-related-subjects>
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="subjectTabsConfig.showLocationsTab && isTabEnabled(subjectTabsConfig.restrictLocationsTabToSpecificRoles, subjectTabsConfig.locationsRoles)" header="{{ 'headers.locations' | translate }}">
            <div class="mt-2 mx-6">
                <app-user-locations
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [isPrisoner]="isPrisoner()"
                    [userIsVerified]="userIsVerified"
                    (updateSubjectLocation)="updateSegmentedAttributes()"
                ></app-user-locations>
            </div>
        </p-tabPanel>
        <p-tabPanel [selectOnFocus]="true" [disabled]="this.modified" *ngIf="subjectTabsConfig.showEntriesExitsTab && isTabEnabled(subjectTabsConfig.restrictEntriesExitsTabToSpecificRoles, subjectTabsConfig.entriesExitsRoles)" header="{{ 'titles.entries_and_exits' | translate }}">
            <div class="mt-2 mx-6">
                <app-entries-exits
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [userIsVerified]="userIsVerified"
                    [isVerified]="verified && biometricSamples"
                    [userSubjectDetails]="userSubjectDetails"
                    [isPrisoner]="isPrisoner()"
                    [prisonsConfig]="prisonsConfig"
                    [managerSettings]="managerSettings"
                    [konektorProperties]="konektorProperties"
                    (updateSubjectStatus)="updateSegmentedAttributes()"
                ></app-entries-exits>
            </div>
        </p-tabPanel>
        <p-tabPanel [selectOnFocus]="true" [disabled]="this.modified" *ngIf="subjectTabsConfig.showEntryExitAuthorizationsTab && isTabEnabled(subjectTabsConfig.restrictEntryExitAuthorizationsTabToSpecificRoles, subjectTabsConfig.entryExitAuthorizationsRoles)" header="{{ 'titles.entry_exit_authorizations' | translate }}">
            <div class="mt-2 mx-6">
                <app-entry-exit-auths-list
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [userIsVerified]="userIsVerified"
                    [isPrisoner]="isPrisoner()"
                ></app-entry-exit-auths-list>
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="prisonsConfig.isPrisonsEnabled && prisonsConfig.showBelongingsTab && isPrisoner()" header="{{ 'headers.belongings' | translate }}">
            <div class="mt-2 mx-6">
                <app-belongings-list
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [userIsVerified]="userIsVerified"
                ></app-belongings-list>
            </div>
        </p-tabPanel>
        <p-tabPanel [disabled]="this.modified" *ngIf="prisonsConfig.isPrisonsEnabled && prisonsConfig.showJudicialFileTab && isPrisoner()" header="{{ 'headers.judicial_files' | translate }}">
            <div class="mt-2 mx-6">
                <app-judicial-files-list
                    [userSubject]="isUser ? userSubjectData : userSubject"
                    [userIsVerified]="userIsVerified"
                    (contentModified)="updateModified($event)"
                ></app-judicial-files-list>
            </div>
        </p-tabPanel>
    </p-tabView>

    <ng-template #widgetOnlyContainer>
        <div class="flex justify-content-center">
            <ng-container *ngTemplateOutlet="widget" />
        </div>
    </ng-template>
</div>

<!-- Dynamic Form -->
<ng-template #dynamicFormContent></ng-template>

<!-- Widget Enroll -->
<ng-template #widget>
    <app-widget-enroll
        [numId]="numId"
        [widgetUrl]="widgetUrl"
        [verified]="verified && userIsVerified && canReadAndWrite"
        [managerSettings]="managerSettings"
        [konektorProperties]="konektorProperties"
        [ready]="widgetReady"
        [isUser]="subjectUserData != undefined"
        [updateAttributes]="updateAttributes"
        [subject]="userSubject"
        (result)="onWidgetEnrollResult($event)"
    ></app-widget-enroll>
</ng-template>

<!-- Widget Verify -->
<app-widget-match
    [numId]="numId"
    [subject]="userSubject"
    [widgetUrl]="widgetUrl"
    [verified]="verified"
    [managerSettings]="managerSettings"
    [konektorProperties]="konektorProperties"
    [ready]="verifyReady"
    (result)="onWidgetMatchResult($event)"
></app-widget-match>

<!-- Edit User/Subject Dialog -->
<p-dialog
    [focusTrap]="false"
    [(visible)]="showEditSubjectDialog"
    [modal]="true"
    styleClass="p-fluid"
    [draggable]="false"
    [resizable]="false"
    [closable]="false">
    <ng-template pTemplate="header">
        <div class="flex align-items-center justify-content-center">
            <label class="dialogHeaderLabel">{{'titles.edit_' + this.userSubjectType | translate}}</label>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-edit-user-subject
            [readAndWritePermissions]="canReadAndWrite && userIsVerified"
            [userSubject]="userSubjectDataEdit"
            [isUser]="isUser"
            [isVerified]="verified"
            [userIsVerified]="userIsVerified"
            [actionType]="((editMode || allowEditAfterIdSearch) && canReadAndWrite) ? updateUserSubjectActionType : viewOnlyUserSubjectActionType"
            (outputData)="onEditUserSubject($event)"
            (picture)="onEditUserSubjectPicture($event)"
            (allowSave)="allowSave($event)"
            (allowDelete)="allowDelete($event)"
        ></app-edit-user-subject>
    </ng-template>
    <ng-template pTemplate="footer">
        <!-- Edit Subject Dialog Footer Buttons -->
        <div class="flex justify-content-between">
            <div class="flex align-items-center justify-content-start width100">
                <p-button *ngIf="!isUser && subjectUserData == undefined && canReadAndWriteOther && newUserSaveAllowed" pRipple [style]="{'background': '#009BA9', 'border-color': '#009BA9'}" class="m-1"
                    [disabled]="!userIsVerified"
                    label="{{'create_new_' + (isUser ? 'subject' : 'user') | translate}}" (click)="openNewUserDialog()"></p-button>
                <p-button *ngIf="isUser" pRipple [style]="{'background': '#15294A', 'border-color': '#15294A'}" class="m-1"
                    label="{{'buttons.sendPasswordRecovery' | translate}}" (click)="sendPasswordRecovery()"></p-button>
            </div>
            <div class="flex align-items-center justify-content-end width100">
                <p-button *ngIf="(editMode || allowDeleteAfterIdSearch) && canReadAndWrite" pRipple
                    severity="danger" class="m-1"
                    [disabled]="!userIsVerified || (!isUser && subjectUserData != undefined) || !deleteAllowed"
                    [style]="{'pointer-events': 'auto'}"
                    pTooltip="{{ (!isUser && subjectUserData != undefined) ? ('messages.cannot_delete_subject_user' | translate) : ((!deleteAllowed) ? ('messages.cannot_delete_subject_user_ds' | translate) : '') }}" tooltipPosition="top"
                    label="{{'delete_entity' | translate}}" (click)="confirmDelete(userSubject)"></p-button>
                <p-button *ngIf="(editMode || allowEditAfterIdSearch) && canReadAndWrite" pRipple
                    [disabled]="isDisableSaveButton || !modified || !userIsVerified || !saveAllowed"
                    [style]="{'background': '#009BA9', 'border-color': '#009BA9', 'pointer-events': 'auto'}" class="m-1"
                    pTooltip="{{ (!saveAllowed) ? ('messages.cannot_update_subject_user_ds' | translate) : '' }}" tooltipPosition="top"
                    label="{{'save' | translate}}" (click)="onSubmitEditSubject()"></p-button>
                <p-button pRipple severity="secondary" class="m-1"
                    label="{{'cancel' | translate}}" (click)="closeEditSubjectDialog()"></p-button>
            </div>
        </div>
    </ng-template>
</p-dialog>

<p-dialog
    [focusTrap]="false"
    [(visible)]="showNewUserDialog"
    [modal]="true"
    styleClass="p-fluid"
    [draggable]="false"
    [resizable]="false"
    [closable]="false"
>
    <ng-template pTemplate="header">
        <div class="flex align-items-center justify-content-center">
            <label class="dialogHeaderLabel">{{'create_new_user' | translate}}</label>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <div class="flex flex-row gap-6 p-3" [formGroup]="newUserForm">
            <div class="flex flex-column">
                <div class="flex flex-column">
                    <div class="flex flex-row gap-3">
                        <div class="field" [style]="{'width':'237px'}">
                            <label class="label-form" for="emai">{{ 'content.email' | translate }}</label>
                            <input type="text" pInputText
                                formControlName="email"
                                (ngModelChange)="trackDataChange()"
                                [class.ng-dirty]="!isValid('email') && newUserForm.controls['email'].touched" />
                            <small *ngIf="!isValid('email') && newUserForm.controls['email'].touched" [style]="{'color': 'red'}">
                                {{ 'messages.error_isRequiredField' | translate }}
                            </small>
                        </div>
                        <div class="field" [style]="{'width':'237px'}">
                            <label class="label-form" for="roles">{{ 'table.profile' | translate }}</label>
                            <p-multiSelect appendTo="body" maxSelectedLabels="1" [options]="roles" formControlName="roles"
                                (ngModelChange)="trackDataChange()" optionLabel="name"
                                placeholder="{{ 'content.select' | translate }}" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <!-- Create User Dialog Footer Buttons -->
        <div class="flex align-items-center justify-content-end width100">
            <p-button pRipple [style]="{'background': '#009BA9', 'border-color': '#009BA9'}" class="m-1"
                [disabled]="!newUserReadyToSave"
                label="{{'save' | translate}}" (click)="onSubmitNewUser()"></p-button>
            <p-button pRipple severity="secondary" class="m-1"
                label="{{'cancel' | translate}}" (click)="closeNewUserDialog()"></p-button>
        </div>
    </ng-template>
</p-dialog>
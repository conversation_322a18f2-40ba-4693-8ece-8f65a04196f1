import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output, ViewChild, ViewContainerRef } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { MenuItem, MessageService } from "primeng/api";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { DetailsDataEntity } from "src/verazial-common-frontend/core/general/common/entity/details-data.entity";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { AttributeData } from "src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { EntryExitAuthType } from "src/verazial-common-frontend/core/general/prisons/common/enums/entry-exit-auth-type.enum";
import { AuthStatus } from "src/verazial-common-frontend/core/general/prisons/common/enums/transfer-auth-status.enum";
import { EntryExitAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/entry-exit-auth/entry-exit-auth.entity";
import { CreateEntryExitAuthUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/entry-exit-auth/create-entry-exit-auth.use-case";
import { GetEntryExitAuthByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/entry-exit-auth/get-entry-exit-auth-by-id.use-case";
import { UpdateEntryExitAuthUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/entry-exit-auth/update-entry-exit-auth.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { BioSignatureResult } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures/bio-signatures.component";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { DynamicFormAttributes, DynamicFormComponent } from "../../dynamic-form/dynamic-form/dynamic-form.component";
import { CustomFieldModel } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field.model";
import { CustomFieldTypes } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field-type.enum";
import { environment } from "src/environments/environment";
import { DropdownChangeEvent } from "primeng/dropdown";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";

@Component({
    selector: 'app-entry-exit-auth-edit',
    templateUrl: './entry-exit-auth-edit.component.html',
    styleUrl: './entry-exit-auth-edit.component.css'
})
export class EntryExitAuthEditComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() canReadAndWrite: boolean = false;
    @Input() userIsVerified: boolean = false;
    @Input() userSubject?: SubjectEntity;
    @Input() isPrisoner: boolean = false;
    @Input() operationType!: OperationType;
    @Input() entryExitAuth?: EntryExitAuthEntity;
    @Input() listOfAllSubjects: SubjectEntity[] = [];
    @Input() listOfAllRoles: RoleEntity[] = [];
    @Input() managerSettings?: GeneralSettings;
    @Input() createUpdateButtonTitle: string = this.translateService.instant('save');
    // Outputs
    @Output() operationStatus = new EventEmitter<OperationStatus>();

    isLoading: boolean = false;
    editEnabled: boolean = true;
    isInProgress: boolean = false;
    modified: boolean = false;
    private rejectTimeout: any;

    items?: MenuItem[];
    stepOptions?: AttributeData[]
    activeIndex: number = 0;
    opType = OperationType;
    minDate: Date | undefined;

    // Settings
    konektorProperties?: KonektorPropertiesEntity;

    // Entry Exit Auth Form
    selectedEntryExitAuth?: EntryExitAuthEntity;
    selectAuthType?: GenericKeyValue;
    authTypeOptions: GenericKeyValue[] = [];
    showEndDate: boolean = true;
    selectAuthReason?: GenericKeyValue;
    authReasonOptions: GenericKeyValue[] = [];
    authReasonParameter = 'entry-exit-auth-reasons';
    showDetails: boolean = false;
    public form: FormGroup = this.fb.group({
        // id
        // subjectId
        // authReason
        authReason: ['', [Validators.required]],
        // authRegistrationDate
        // authStartDateTime
        authStartDateTime: ['', [Validators.required]],
        // authEndDateTime
        authEndDateTime: [],
        // type
        type: ['', [Validators.required]],
        // observations
        observations: [],
        // status
        // authUserId
        // authUserNumId
        // authUserSignatureDate
        // authUserSignatureTech
        // isCompleted
        // actualStartDateTime
        // actualEndDateTime
        // elapsedTime
        // startActionId
        // endActionId
        // createdBy
        // updatedBy
        // createdAt
        // updatedAt
        stepOptions: ['0'],
    });
    /* Custom Field Details Form */
    detailsData: DetailsDataEntity[] = [];
    @ViewChild('dynamicFormContent', { read: ViewContainerRef, static: true }) dynamicFormContent: ViewContainerRef;
    formAttributes: DynamicFormAttributes[] = [];
    showDynamicForm: boolean = false;
    componentRef: any;
    formModified: boolean = false;
    invalidDates: boolean = false;

    // Signatures
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
    authSignatureWidget: boolean = false;
    authUser?: SubjectEntity;
    authUserImage: string = '';
    authSignatureData?: {
        id?: string,
        numId?: string,
        tech?: string,
        date?: Date,
    };
    hasAuthSignature: boolean = false;
    restrictAuthRoles: string[] = [];
    segmentedSearchAuthRole: string = '';

    constructor(
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private translateService: TranslateService,
        private messageService: MessageService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private auditTrailService: AuditTrailService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getEntryExitAuthByIdUseCase: GetEntryExitAuthByIdUseCase,
        private updateEntryExitAuthUseCase: UpdateEntryExitAuthUseCase,
        private createEntryExitAuthUseCase: CreateEntryExitAuthUseCase,
    ) {
        this.dynamicFormContent = {} as ViewContainerRef;
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }

    ngOnInit() {
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                if (data) {
                    this.konektorProperties = data;
                }
            },
            error: (e) => {
                this.loggerService.error(e);
            },
        });
        this.form.reset();
        this.items = [
            { label: this.translateService.instant('content.general'), },
            // { label: this.translateService.instant('content.details'), },
            { label: this.translateService.instant('content.signatures'), },
        ];
        this.stepOptions = [
            { key: this.translateService.instant('content.general'), value: "0" },
            // { key: this.translateService.instant('content.details'), value: "1" },
            { key: this.translateService.instant('content.signatures'), value: "1" },
        ];
        if (this.managerSettings) {
            let options = this.managerSettings.catalogs?.find((catalog) => catalog.parameter === this.authReasonParameter)?.options;
            this.authReasonOptions = options ? (JSON.parse(options) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.authReasonParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }
        this.setTypeOptions();
        setTimeout(() => {
            this.fillFields();
        }, 300);
        this.ngOnChanges();
    }

    handleBeforeUnload(event: Event) {
      this.ngOnDestroy();
    }

    ngOnDestroy(): void {
      this.updateModified(false);
      // Clean up the timeout if the component is destroyed
      this.clearRejectTimeout();
    }

    ngOnChanges() {
        this.showDetails = this.managerSettings?.subjectTabsConfig?.showEntryExitAuthDetails ?? false;
        if (this.showDetails) {
            this.items = [
                { label: this.translateService.instant('content.general'), },
                { label: this.translateService.instant('content.details'), },
                { label: this.translateService.instant('content.signatures'), },
            ];
            this.stepOptions = [
                { key: this.translateService.instant('content.general'), value: "0" },
                { key: this.translateService.instant('content.details'), value: "1" },
                { key: this.translateService.instant('content.signatures'), value: "2" },
            ];
        }
        else {
            this.items = [
                { label: this.translateService.instant('content.general'), },
                { label: this.translateService.instant('content.signatures'), },
            ];
            this.stepOptions = [
                { key: this.translateService.instant('content.general'), value: "0" },
                { key: this.translateService.instant('content.signatures'), value: "1" },
            ];
        }
        if (this.canReadAndWrite && this.userIsVerified && this.editEnabled) {
            this.form.enable();
        }
        else {
            this.form.disable();
            this.form.controls['stepOptions'].enable();
        }
        this.setTypeOptions();
        this.restrictAuthRoles = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.authorizePrisonerEntryExitRoles ?? [];
        this.segmentedSearchAuthRole = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.authorizePrisonerEntryExitMainRole ?? '';
    }

    setTypeOptions() {
        if (this.isPrisoner) {
            this.authTypeOptions = [
                // { key: this.translateService.instant('content.entry'), value: EntryExitAuthType.ENTRY },
                { key: this.translateService.instant('content.exit'), value: EntryExitAuthType.EXIT },
                { key: this.translateService.instant('prisons_tab.definitive_exit'), value: EntryExitAuthType.DFN_EXIT },
            ]
        }
        else {
            this.authTypeOptions = [
                { key: this.translateService.instant('content.entry'), value: EntryExitAuthType.ENTRY },
                { key: this.translateService.instant('content.exit'), value: EntryExitAuthType.EXIT },
                // { key: this.translateService.instant('prisons_tab.definitive_exit'), value: EntryExitAuthType.DFN_EXIT },
            ]
        }
    }

    typeChanged(event: DropdownChangeEvent){
        this.showEndDate = event.value != EntryExitAuthType.DFN_EXIT;
        if (event.value == EntryExitAuthType.DFN_EXIT) {
            this.form.get('authEndDateTime')?.clearValidators();
            this.form.get('authEndDateTime')?.updateValueAndValidity();
        }
        else {
            this.form.get('authEndDateTime')?.setValidators([Validators.required]);
            this.form.get('authEndDateTime')?.updateValueAndValidity();
        }
    }

    fillFields() {
        if (!this.entryExitAuth) {
            this.onCancel();
            this.componentRef?.destroy();
            this.detailsData = [];
            this.buildDynamicForm();
            return;
        }
        this.selectAuthReason = this.authReasonOptions.find((option) => option.key == this.entryExitAuth?.authReason);
        this.form.get('authReason')?.setValue(this.selectAuthReason);
        this.form.get('authStartDateTime')?.setValue(this.entryExitAuth?.authStartDateTime);
        this.form.get('authEndDateTime')?.setValue(this.entryExitAuth?.authEndDateTime);
        this.form.get('type')?.setValue(this.entryExitAuth?.type);
        this.showEndDate = this.entryExitAuth?.type != EntryExitAuthType.DFN_EXIT;
        this.form.get('observations')?.setValue(this.entryExitAuth?.observations);
        this.isInProgress = this.entryExitAuth?.status == AuthStatus.IN_PROGRESS;
        this.hasAuthSignature = this.entryExitAuth?.authUserNumId != '' && this.entryExitAuth?.authUserNumId != null && this.entryExitAuth?.authUserNumId != undefined;
        this.authSignatureData = {
            id: this.hasAuthSignature ? this.entryExitAuth?.authUserId : undefined,
            numId: this.hasAuthSignature ? this.entryExitAuth?.authUserNumId : undefined,
            tech: this.hasAuthSignature ? this.entryExitAuth?.authUserSignatureTech : undefined,
            date: this.hasAuthSignature ? this.entryExitAuth?.authUserSignatureDate : undefined,
        }
        if (!this.hasAuthSignature) {
            this.authSignatureData = undefined;
        }
        if (this.hasAuthSignature) {
            this.form.disable();
            this.form.controls['stepOptions'].enable();
            this.editEnabled = false;
        }
        else {
            this.form.enable();
            this.editEnabled = true;
        }
        this.activeIndex = 0;
        this.form.controls['stepOptions'].setValue(this.activeIndex.toString());
        this.componentRef?.destroy();
        this.detailsData = this.entryExitAuth?.detailsData ?? [];
        this.buildDynamicForm();
        this.ngOnChanges();
        this.isLoading = false;
    }

    /* Dynamic Form Functions */

    buildDynamicForm() {
        const customFields: CustomFieldModel[] = this.managerSettings?.subjectTabsConfig?.entryExitAuthDetailFields || [];
        if (customFields.length != 0) {
            this.loggerService.info('There are Fields to submit');
            this.formAttributes = [];
            // Classify the attributes of the action
            customFields.forEach((customField: CustomFieldModel) => {
                if (customField.type == CustomFieldTypes.INPUT || customField.type == CustomFieldTypes.DROPDOWN || customField.type == CustomFieldTypes.TOGGLE) {
                    let type: CustomFieldTypes = customField.type;
                    let required = false;
                    let maxCharacters = 0;
                    let options: string[] = [];
                    customField.fieldData?.forEach((data: any) => {
                        if (data.key.includes('required-checkbox')) {
                            required = data.value == 'true';
                        }
                        if (data.key.includes('options-listbox')) {
                            options = JSON.parse(data.value);
                        }
                        if (data.key.includes('max-characters-textbox')) {
                            maxCharacters = data.value;
                        }
                    });
                    if (type == CustomFieldTypes.INPUT && maxCharacters > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 25)) {
                        type = CustomFieldTypes.TEXTAREA;
                    }
                    const formAttribute: DynamicFormAttributes = {
                        type: type,
                        label: customField.name,
                        key: customField.parameter,
                        value: this.detailsData?.find((detail: DetailsDataEntity) => detail.parameter == customField.parameter)?.value || '',
                        detailId: this.detailsData?.find((detail: DetailsDataEntity) => detail.parameter == customField.parameter)?.id || '',
                        required: required,
                        options: options,
                        disabled: !this.canReadAndWrite,
                        group: customField.group?.name ?? '',
                        groupRole: customField.group?.roleId ?? '',
                        tooltip: customField.description ?? '',
                        translations: this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'entryExitAuthDetailFields')?.translations?.find((t: TranslationModel) => t.key === customField.parameter) || new TranslationModel(),
                    };
                    this.formAttributes.push(formAttribute);
                }
            });
            this.loggerService.debug(this.formAttributes);
            // If the action requires to show a form, render the dynamic form
            if (this.formAttributes.length > 0) {
                this.loggerService.info('There is a Form to Render');
                const formFields = this.convertFormControlArrayToObject(this.formAttributes);
                this.loggerService.debug(formFields);
                this.componentRef = this.dynamicFormContent.createComponent(DynamicFormComponent);
                this.componentRef.instance.controlsConfig = formFields;
                this.componentRef.instance.showForm = this.showDynamicForm;
                this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && this.userIsVerified && this.editEnabled;
                this.componentRef.instance.userSubjectRoles = this.userSubject?.roles;
                this.componentRef.instance.groupTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'entryExitAuthDetailFieldGroups')?.translations || [];

                // Subscribe to the form submission event
                this.componentRef.instance.formSubmitted.subscribe((formData: any) => {
                    this.loggerService.debug('Form Data:');
                    this.loggerService.debug(formData);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(formData) }
                    ];
                    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_TRANSFER_AUTHORIZATION, ReasonActionTypeEnum.UPDATE, () => { this.updateDetails(formData); }, at_attributes, false);
                });
                // Subscribe to the form modified event
                this.componentRef.instance.formModified.subscribe((modified: boolean) => {
                    this.updateModified(modified);
                });
            }
        }
    }

    toggleDynamicForm() {
        if (this.componentRef) {
            this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && this.userIsVerified;
            this.componentRef.instance.toggleFormState(this.canReadAndWrite && this.userIsVerified);
        }
    }

    /* Details */
    updateDetails(formData: any) {
        this.loggerService.debug("Updating Details");
        Object.keys(formData).forEach((key: string) => {
            const dynamicFormField = this.formAttributes.find((attr: DynamicFormAttributes) => attr.key == key);
            const foundDetail = this.detailsData?.find((detail: DetailsDataEntity) => detail.id == dynamicFormField?.detailId);

            let detail: DetailsDataEntity = new DetailsDataEntity();
            //id
            detail.id = foundDetail ? foundDetail.id : (this.detailsData?.length! + 1).toString();
            //name
            detail.name = dynamicFormField?.label;
            //parameter
            detail.parameter = key;
            //value
            detail.value = formData[key].toString();
            if (foundDetail) {
                this.detailsData?.splice(this.detailsData?.indexOf(foundDetail), 1, detail);
            } else {
                this.detailsData?.push(detail);
            }
        });
        this.updateModified(false);
        // this.messageService.add({
        //     severity: 'success',
        //     summary: this.translateService.instant('content.successTitle'),
        //     detail: this.translateService.instant('messages.success_general'),
        //     life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        // });
    }

    saveEntryExitAuth() {
        this.isLoading = true;
        if (this.form.invalid) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('messages.error_entry_exit_authorization'),
                detail: this.translateService.instant('messages.error_message_required_fields')
            });
            this.isLoading = false;
            return;
        }

        let entryExitAuth: EntryExitAuthEntity = this.entryExitAuth ?? {};
        entryExitAuth.id = this.entryExitAuth?.id ?? undefined;
        entryExitAuth.subjectId = this.userSubject?.id;
        entryExitAuth.authReason = this.form.get('authReason')?.value.value;
        entryExitAuth.authRegistrationDate = this.operationType == OperationType.INSERT ? new Date() : this.isValidDate(this.entryExitAuth?.authRegistrationDate?.toString()!) ? this.entryExitAuth?.authRegistrationDate : undefined;
        entryExitAuth.authStartDateTime = this.form.get('authStartDateTime')?.value;
        entryExitAuth.authEndDateTime = this.form.get('authEndDateTime')?.value;
        entryExitAuth.type = this.form.get('type')?.value;
        entryExitAuth.observations = this.form.get('observations')?.value ?? undefined;
        entryExitAuth.status = this.hasAuthSignature ? AuthStatus.AUTHORIZED : AuthStatus.CREATED;
        entryExitAuth.detailsData = this.detailsData ?? '[]';
        entryExitAuth.authUserId = this.authSignatureData?.id ?? undefined;
        entryExitAuth.authUserNumId = this.authSignatureData?.numId ?? undefined;
        entryExitAuth.authUserSignatureDate = this.isValidDate(this.authSignatureData?.date?.toString()!) ? this.authSignatureData?.date : undefined;
        entryExitAuth.authUserSignatureTech = this.authSignatureData?.tech ?? undefined;
        entryExitAuth.isCompleted = false;
        entryExitAuth.actualStartDateTime = this.isValidDate(this.entryExitAuth?.actualStartDateTime?.toString()!) ? this.entryExitAuth?.actualStartDateTime : undefined;
        entryExitAuth.actualEndDateTime = this.isValidDate(this.entryExitAuth?.actualEndDateTime?.toString()!) ? this.entryExitAuth?.actualEndDateTime : undefined;
        entryExitAuth.elapsedTime = this.entryExitAuth?.elapsedTime ?? undefined;
        entryExitAuth.startActionId = this.entryExitAuth?.startActionId && this.entryExitAuth.startActionId != '' ? this.entryExitAuth.startActionId : undefined;
        entryExitAuth.endActionId = this.entryExitAuth?.endActionId && this.entryExitAuth.endActionId != '' ? this.entryExitAuth.endActionId : undefined;
        entryExitAuth.createdBy = this.entryExitAuth?.createdBy ?? this.localStorageService.getUser()?.id;
        entryExitAuth.updatedBy = this.localStorageService.getUser()?.id;
        entryExitAuth.createdAt = this.isValidDate(this.entryExitAuth?.createdAt?.toString()!) ? this.entryExitAuth?.createdAt : undefined;
        entryExitAuth.updatedAt = undefined;

        this.loggerService.debug(entryExitAuth);
        if (entryExitAuth.id && this.operationType == OperationType.UPDATE) {
            this.getEntryExitAuthByIdUseCase.execute({ id: entryExitAuth.id }).then(
                (data) => {
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(entryExitAuth) },
                    ];
                    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_ENTRY_EXIT_AUTHORIZATION, ReasonActionTypeEnum.UPDATE, () => {
                        this.updateEntryExitAuthUseCase.execute({ entryExitAuth: entryExitAuth }).then(
                            (data) => {
                                this.entryExitAuth = data;
                                let status: OperationStatus = {
                                    status: Status.SUCCESS,
                                    message: this.translateService.instant('messages.success_entry_exit_authorization_updated')
                                }
                                this.operationStatus.emit(status);
                            },
                            (e) => {
                                this.loggerService.error(e);
                            }
                        )
                        .finally(() => {
                            this.isLoading = false;
                        });
                    }, at_attributes);
                },
                (e) => {
                    this.loggerService.error(e);
                    let responseStatus: OperationStatus = {
                        status: Status.ERROR,
                        message: e.message
                    }
                    this.operationStatus.emit(responseStatus);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.RECORD_ID, value: entryExitAuth.id!.toString() },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_TRANSFER_AUTHORIZATION_BY_ID, 0, 'ERROR', '', at_attributes);
                }
            );
        }
        else {
            const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(entryExitAuth) },
            ];
            this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_ENTRY_EXIT_AUTHORIZATION, ReasonActionTypeEnum.CREATE, () => {
                this.createEntryExitAuthUseCase.execute({ entryExitAuth: entryExitAuth }).then(
                    (data) => {
                        this.entryExitAuth = data;
                        let status: OperationStatus = {
                            status: Status.SUCCESS,
                            message: this.translateService.instant('messages.success_entry_exit_authorization_created')
                        }
                        this.operationStatus.emit(status);
                    },
                    (e) => {
                        this.loggerService.error(e);
                    }
                )
                .finally(() => {
                    this.isLoading = false;
                });
            }, at_attributes);
        }
    }

    onActiveTabIndexChange(event: any){
      this.activeIndex = Number(event.value);
      this.showDynamicForm = this.showDetails ? this.activeIndex == 1 : this.activeIndex == -1;
      this.componentRef.instance.showForm = this.showDynamicForm;
      this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onNext() {
        this.form.markAllAsTouched();
        if (this.activeIndex == 0 && this.showEndDate) {
            let authStartDateTime: Date = this.form.get('authStartDateTime')?.value;
            let authEndDateTime: Date = this.form.get('authEndDateTime')?.value;
            if (authStartDateTime >= authEndDateTime) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('messages.error_dates'),
                    detail: this.translateService.instant('messages.error_start_end_dates')
                });
                return;
            }
        }
        if (this.form.valid) {
            this.activeIndex += 1;
            this.showDynamicForm = this.showDetails ? this.activeIndex == 1 : this.activeIndex == -1;
            this.componentRef.instance.showForm = this.showDynamicForm;
            this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
        }
    }

    onBack() {
        this.activeIndex -= 1;
        this.showDynamicForm = this.showDetails ? this.activeIndex == 1 : this.activeIndex == -1;
        this.componentRef.instance.showForm = this.showDynamicForm;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onCancel() {
        this.entryExitAuth = undefined;
        this.form.reset();
        this.selectAuthReason = undefined;
        this.authUser = undefined;
        this.authSignatureData = undefined;
        this.hasAuthSignature = false;
        this.isLoading = false;
    }

    onClose() {
        this.onCancel();
        let result: OperationStatus = {
            status: Status.SUCCESS,
            message: 'CLOSE'
        }
        this.operationStatus.emit(result);
    }

    onSelectEndDateTime(event: Date) {
        if (this.activeIndex == 0) {
            let authStartDateTime: Date = this.form.get('authStartDateTime')?.value;
            let authEndDateTime: Date = this.form.get('authEndDateTime')?.value;
            if (authStartDateTime && authEndDateTime) {
                this.invalidDates = authStartDateTime >= authEndDateTime;
            }
            else {
                this.invalidDates = false;
            }
        }
    }

    userAuthSignatureResult(event: BioSignatureResult) {
        this.authSignatureData = {
            id: event.id,
            numId: event.numId,
            tech: event.tech,
            date: event.date,
        }
        this.hasAuthSignature = true;
    }

    isRequiredField(field: string, form: FormGroup = this.form): boolean {
        return this.validatorService.isRequiredField(form, field);
    }

    isValid(field: string, form: FormGroup = this.form): boolean {
        return this.validatorService.isValidField(form, field);
    }

    isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    /**
     * Convert an array of form controls to an object
     * @param arr Array of form controls
     * @returns Object with the form controls
     */
    convertFormControlArrayToObject(arr: any[]) {
      const result: any = {};
      arr.forEach(item => {
        result[item.key] = item;
      });
      return result;
    }

    updateModified(modified: boolean) {
      this.modified = modified;
    }

    private clearRejectTimeout() {
      if (this.rejectTimeout) {
        clearTimeout(this.rejectTimeout);
      }
      this.rejectTimeout = null;
    }

    getLabel(key: string, catalogParameter: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter)?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}
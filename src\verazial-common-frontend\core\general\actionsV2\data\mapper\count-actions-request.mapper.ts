import { CountActionsRequest, SearchActionsRequest } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { FilterMapper } from "./filter.mapper";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";
import { CountActionsRequestEntity, CountModesEntity } from "../../domain/entity/count-actions-request.entity";

export class CountActionsRequestMapper extends Mapper<CountActionsRequest, CountActionsRequestEntity>{

    filterMapper = new FilterMapper();

    override mapFrom(param: CountActionsRequest): CountActionsRequestEntity {
        throw new Error("Method not implemented.");
    }

    override mapTo(param: CountActionsRequestEntity): CountActionsRequest {
        let countActionsRequest = new CountActionsRequest();
        countActionsRequest.setStartTime(param.startTime ? dateToTimestamp(param.startTime) : undefined);
        countActionsRequest.setEndTime(param.endTime ? dateToTimestamp(param.endTime) : undefined);
        countActionsRequest.setFiltersList(param.filters.map(filter => this.filterMapper.mapTo(filter)));
        
        if(param.groupByAttributePath)
            countActionsRequest.setGroupByAttributePathsList(param.groupByAttributePath);

        if (param.extraCountModes && param.extraCountModes.length > 0) {
            countActionsRequest.setExtraCountModesList(
                param.extraCountModes.map((mode) => this.mapCountModes(mode))
            );
        }

        return countActionsRequest;
    }

    private mapCountModes(mode: CountModesEntity): CountActionsRequest.CountModes {
        let countModes = new CountActionsRequest.CountModes();
    
        if (mode.mode) {
            let averageValue = new CountActionsRequest.CountModes.AverageValue();
            averageValue.setAttributePath(mode.mode.attributePath);
            countModes.setAverageValue(averageValue);
        }
    
        return countModes;
    }

}
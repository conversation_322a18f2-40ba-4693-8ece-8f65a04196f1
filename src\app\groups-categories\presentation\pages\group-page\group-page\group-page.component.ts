import { AfterContentChecked, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, MenuItem } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { GetAllGroupsCategoriesUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-all-groups-categories.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-group-page',
  templateUrl: './group-page.component.html',
  styleUrl: './group-page.component.css',
  providers: [MessageService, ConfirmationService]
})

export class GroupPageComponent implements OnInit, AfterContentChecked {

  @Output() updateGroups = new EventEmitter<boolean>();

  showNewGroupDialog: boolean = false;
  listGroupsCategoriesIsEmpty: boolean = true;
  listGroupsCategories: GroupCategoryEntity[] = [];
  isLoading: boolean = true;
  showDialog: boolean = false;

  items: MenuItem[] | undefined;
  activeIndex: number = 0;

  // Access code identifier
  access_identifier: string = AccessIdentifier.CATEGORIES;
  canReadAndWrite: boolean = false;

  constructor(
    private checkPermissions: CheckPermissionsService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private fb: FormBuilder,
    private getAllGroupsCategoriesUseCase: GetAllGroupsCategoriesUseCase,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
  ) { }

  ngAfterContentChecked(): void {
  }

  ngOnInit(): void {
    this.getAllGroupsCategories();
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
  }

  getAllGroupsCategories() {
    this.getAllGroupsCategoriesUseCase.execute().then(
      (data) => {
        this.listGroupsCategories = data;
        if (data.length > 0) {
          this.listGroupsCategoriesIsEmpty = false;
        }
        this.isLoading = false;
      },
      (e) => {
        this.isLoading = false;
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_GROUP_CATEGORIES, 0, 'ERROR', '', at_attributes);
      });
  }

  public form: FormGroup = this.fb.group({

  });

  createNewGroupCategory() {
    this.showNewGroupDialog = true;
  }

  updateGroupData(event: boolean) {
    this.showNewGroupDialog = false;
    this.getAllGroupsCategories();
  }

  proccessGroupOperation(event: OperationStatus) {
    if (event.status == Status.SUCCESS) {
      this.messageService.add({ severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000 });
      this.showNewGroupDialog = false;
      this.getAllGroupsCategories();
    } else {
      this.messageService.add({ severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${event.message}`,
      life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000 });
    }
  }
}

<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-content-center w-full h-full mt-4">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" [ngClass]="{'spinner': isLoading}"></p-progressSpinner>
    </div>
</ng-template>
<div *ngIf="!isLoading else loadingSpinner" class="subcontainer">
    <div *ngIf="!showJudicialFileDialog else judicialFileDialog" class="subcontainer-list gap-3">
        <div *ngIf="listOfJudicialFiles.length > 0 else empty">
            <div class="flex flex-row justify-content-between flex-wrap gap-2">
                <div class="flex flex-row align-items-center">
                    <div class="pr-3">
                        <label class="subcontainer-title">{{ 'titles.judicial_files' | translate}}</label>
                    </div>
                    <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                        @if(selectedJudicialFiles.length > 0){
                            <div>
                                {{ selectedJudicialFiles.length + ' ' + ('content.selected' | translate) }}
                            </div>
                            <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedJudicialFiles.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="editJudicialFile()"></button>
                            <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteJudicialFile()"></button>
                        }
                    </div>
                </div>
                <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                    <p-iconField iconPosition="right">
                        <input pInputText type="text"
                            (input)="dt.filterGlobal($event.target.value, 'contains')"
                            placeholder="{{ 'content.search' | translate }}"
                        />
                        <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                    </p-iconField>
                    <div class="add-action-main-full">
                        <p-button
                            ngClass="add-action-main-full"
                            [disabled]="!(canReadAndWrite && userIsVerified)"
                            [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            label="{{ 'content.new_record' | translate }}"
                            icon="pi pi-plus" iconPos="right"
                            [rounded]="true"
                            (onClick)="onNewJudicialFile()"
                        ></p-button>
                    </div>
                    <div class="add-action-main-small">
                        <p-button
                            [disabled]="!(canReadAndWrite && userIsVerified)"
                            [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                            icon="pi pi-plus"
                            [rounded]="true"
                            (onClick)="onNewJudicialFile()"
                        ></p-button>
                    </div>
                </div>
            </div>
            <div></div>
            <p-table
                #dt
                [value]="listOfJudicialFiles"
                (onFilter)="onFilter($event, dt)"
                [(selection)]="selectedJudicialFiles"
                (selectionChange)="onJudicialFilesSelectionChange($event)"
                dataKey="id"
                [rowHover]="true"
                [paginator]="true"
                [rows]="10"
                [rowsPerPageOptions]="[5, 10, 20]"
                [scrollable]="true"
                scrollDirection="horizontal"
                [tableStyle]="{ 'min-width': '75rem' }"
                styleClass="fixed-table"
                [showCurrentPageReport]="true"
                currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                [globalFilterFields]="[
                    'id',
                    'judicialFile',
                    'registrationDate',
                ]"
                [sortField]="'id'" [sortOrder]="1">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 4rem"></th>
                        <th class="fixed-column" pSortableColumn="id"> {{ 'content.id' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                        <th class="fixed-column" pSortableColumn="judicialFile">{{ 'content.judicialFile' | translate }} <p-sortIcon field="onJudicialFilesSelectionChange"></p-sortIcon></th>
                        <th class="fixed-column" pSortableColumn="registrationDate">{{ 'content.registrationDate' | translate }} <p-sortIcon field="registrationDate"></p-sortIcon></th>
                        <th class="fixed-column" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                    </tr>
                    <tr>
                        <th style="width: 4rem">
                            <p-tableHeaderCheckbox/>
                        </th>
                        <th>
                            <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th>
                            <p-columnFilter type="text" field="judicialFile" [showMenu]="false" matchMode="contains">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th>
                            <p-columnFilter type="date" field="registrationDate" [showMenu]="false" matchMode="customDateRange">
                                <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                    <p-calendar
                                        formControlName="date"
                                        selectionMode="range"
                                        (onSelect)="applyDateRangeFilter(dt, 'registrationDate')"
                                        (onInput)="applyDateRangeFilter(dt, 'registrationDate')"
                                        (onClickOutside)="applyDateRangeFilter(dt, 'registrationDate')"
                                        placeholder="{{ 'content.select' | translate }}"
                                        dateFormat="{{ 'dateFormat' | translate }}"
                                        appendTo="body"
                                    ></p-calendar>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-judicialFile let-rowIndex="rowIndex">
                    <tr class="p-selectable-row" [pSelectableRow]="judicialFile" [pSelectableRowIndex]="rowIndex">
                        <td>
                            <p-tableCheckbox [value]="judicialFile"></p-tableCheckbox>
                        </td>
                        <td (click)="editJudicialFile(judicialFile)" showDelay="1000" pTooltip="{{judicialFile.id}}" tooltipPosition="top" class="ellipsis-cell">{{ judicialFile.id }}</td>
                        <td (click)="editJudicialFile(judicialFile)" showDelay="1000" pTooltip="{{judicialFile.judicialFile | translate }}" tooltipPosition="top" class="ellipsis-cell">{{ judicialFile.judicialFile | translate }}</td>
                        @if(isValidDate(judicialFile.registrationDate)){
                            <td (click)="editJudicialFile(judicialFile)" showDelay="1000" pTooltip="{{judicialFile.registrationDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ judicialFile.registrationDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                        }@else{
                            <td (click)="editJudicialFile(judicialFile)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                        }
                        <!-- <td (click)="editJudicialFile(judicialFile)" showDelay="1000" pTooltip="{{judicialFile.registrationDate}}" tooltipPosition="top" class="ellipsis-cell">{{ judicialFile.registrationDate }}</td> -->
                        <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                            <div class="flex flex-row">
                                <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editJudicialFile(judicialFile)"></button>
                                <button pButton pRipple [disabled]="!canReadAndWrite || managerSettings?.allowRemoveIdentity == false || !userIsVerified" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteJudicialFile(judicialFile)"></button>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.judicial_files' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <app-empty
        [readAndWritePermissions]="canReadAndWrite && userIsVerified"
        buttonLabel="content.new_record"
        titleLabel="titles.no_judicial_files_records_available"
        contentHeight="300px"
        (clicked)="onNewJudicialFile()">
    >
    </app-empty>
</ng-template>

<ng-template #judicialFileDialog>
    <div >
        <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
            {{ isNew ? ('content.new_judicial_file' | translate) : ('headers.judicial_file' | translate) + ' ' + judicialFile?.id }}
        </label>
    </div>
    <div>
        <div class="flex justify-content-end pb-3 requiredFieldsLabel">
            {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
        </div>
        <form [formGroup]="form">
            <div class="form-fields">
                <div class="grid">
                    <div class="col-6" style="padding-bottom: 0rem;">
                        <label class="label-form">{{ 'content.subject' | translate }}</label>
                        <input type="text" pInputText class="w-full" value="{{ userSubject.names + ' ' + userSubject.lastNames }}" [disabled]="true"/>
                    </div>
                    <!-- Judicial File -->
                    <div class="col-6" style="padding-bottom: 0rem;">
                        <div class="pb-1">
                            <label class="label-form" for="judicialFile">{{ 'content.judicialFile' | translate }} <span
                                    *ngIf="isRequiredField('judicialFile', form)" class="requiredStar">*</span></label>
                            <input pInputTextarea class="w-full"
                                formControlName="judicialFile"
                                (ngModelChange)="trackDataChange()"
                            />
                            <small *ngIf="!isValid('judicialFile', form) && form.controls['judicialFile'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>
                    <!-- Comments -->
                    <div class="col-12" style="padding-bottom: 0rem;">
                        <div class="pb-1">
                            <label class="label-form" for="comments">{{ 'content.comments' | translate }} <span
                                    *ngIf="isRequiredField('comments', form)" class="requiredStar">*</span></label>
                            <textarea pInputTextarea class="w-full"
                                rows="3" formControlName="comments" [autoResize]="true"
                                (ngModelChange)="trackDataChange()"
                            ></textarea>
                            <small *ngIf="!isValid('comments', form) && form.controls['comments'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</ng-template>
<ng-template #dynamicFormContent></ng-template>
<div *ngIf="showJudicialFileDialog" class="flex flex-row gap-1 mr-3 my-3 justify-content-end">
    <p-button
        *ngIf="canReadAndWrite && userIsVerified"
        [disabled]="!(canReadAndWrite && userIsVerified) || isDisabledSaveButton || formModified"
        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px'}"
        label="{{ 'save'| translate }}"
        (onClick)="saveJudicialFile()"
    ></p-button>
    <p-button
        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}"
        label="{{ (canReadAndWrite && userIsVerified ? 'cancel' : 'close')| translate }}"
        (onClick)="onCancelDialog()"
    ></p-button>
</div>
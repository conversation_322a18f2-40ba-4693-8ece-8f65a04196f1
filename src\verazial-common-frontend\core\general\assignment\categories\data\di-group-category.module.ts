import { NgModule } from "@angular/core";
import { GroupCategoryRepository } from "../domain/repository/group-category.repository";
import { CreateGroupCategoryUseCase } from "../domain/use-cases/create-group-category.use-case";
import { GetAllGroupsCategoriesUseCase } from "../domain/use-cases/get-all-groups-categories.use-case";
import { GetGroupCategoryByIdUseCase } from "../domain/use-cases/get-group-category-by-id.use-case";
import { UpdateGroupCategoryByIdUseCase } from "../domain/use-cases/update-group-category-by-id.use-case";
import { GroupCategoryRepositoryImpl } from "./repository-impl/group-category-impl.repository";
import { CommonModule } from "@angular/common";
import { HttpClientModule } from "@angular/common/http";
import { DeleteGroupCategoryByIdUseCase } from "../domain/use-cases/delete-group-category-by-id.use-case";
import { AddCategorySubjectUseCase } from "../domain/use-cases/add-category-subject.use-case";
import { DeleteCategorySubjectUseCase } from "../domain/use-cases/delete-category-subject.use-case";
import { AddCategoryLocationUseCase } from "../domain/use-cases/add-category-location.use-case";
import { DeleteCategoryLocationUseCase } from "../domain/use-cases/delete-category-location.use-case";
import { UpdateCategoryScheduleUseCase } from "../domain/use-cases/update-category-schedule.use-case";
import { AddDayTimeScheduleUseCase } from "../domain/use-cases/add-day-time-schedule.use-case";
import { UpdateDayTimeScheduleUseCase } from "../domain/use-cases/update-day-time-schedule.use-case";
import { DeleteDayTimeScheduleUseCase } from "../domain/use-cases/delete-day-time-schedule.use-case";

const createGroupCategoryUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new CreateGroupCategoryUseCase(groupCategoryRepository);

const getAllGroupsCategoriesUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new GetAllGroupsCategoriesUseCase(groupCategoryRepository);

const getGroupCategoryByIdUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new GetGroupCategoryByIdUseCase(groupCategoryRepository);

const deleteGroupCategoryByIdUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new DeleteGroupCategoryByIdUseCase(groupCategoryRepository);

const updateGroupCategoryByIdUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new UpdateGroupCategoryByIdUseCase(groupCategoryRepository);

const addCategorySubjectUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new AddCategorySubjectUseCase(groupCategoryRepository);

const deleteCategorySubjectUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new DeleteCategorySubjectUseCase(groupCategoryRepository);

const addCategoryLocationUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new AddCategoryLocationUseCase(groupCategoryRepository);

const deleteCategoryLocationUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new DeleteCategoryLocationUseCase(groupCategoryRepository);

const updateCategoryScheduleUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new UpdateCategoryScheduleUseCase(groupCategoryRepository);

const addDayTimeScheduleUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new AddDayTimeScheduleUseCase(groupCategoryRepository);

const updateDayTimeScheduleUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new UpdateDayTimeScheduleUseCase(groupCategoryRepository);

const deleteDayTimeScheduleUseCaseFactory =
    (groupCategoryRepository: GroupCategoryRepository) => new DeleteDayTimeScheduleUseCase(groupCategoryRepository);

export const createGroupCategoryUseCaseProvider = {
    provide: CreateGroupCategoryUseCase,
    useFactory: createGroupCategoryUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const getAllGroupsCategoriesUseCaseProvider = {
    provide: GetAllGroupsCategoriesUseCase,
    useFactory: getAllGroupsCategoriesUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const getGroupCategoryByIdUseCaseProvider = {
    provide: GetGroupCategoryByIdUseCase,
    useFactory: getGroupCategoryByIdUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const deleteGroupCategoryByIdUseCaseProvider = {
    provide: DeleteGroupCategoryByIdUseCase,
    useFactory: deleteGroupCategoryByIdUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const updateGroupCategoryByIdUseCaseProvider = {
    provide: UpdateGroupCategoryByIdUseCase,
    useFactory: updateGroupCategoryByIdUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const addCategorySubjectUseCaseProvider = {
    provide: AddCategorySubjectUseCase,
    useFactory: addCategorySubjectUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const deleteCategorySubjectUseCaseProvider = {
    provide: DeleteCategorySubjectUseCase,
    useFactory: deleteCategorySubjectUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const addCategoryLocationUseCaseProvider = {
    provide: AddCategoryLocationUseCase,
    useFactory: addCategoryLocationUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const deleteCategoryLocationUseCaseProvider = {
    provide: DeleteCategoryLocationUseCase,
    useFactory: deleteCategoryLocationUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const updateCategoryScheduleUseCaseProvider = {
    provide: UpdateCategoryScheduleUseCase,
    useFactory: updateCategoryScheduleUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const addDayTimeScheduleUseCaseProvider = {
    provide: AddDayTimeScheduleUseCase,
    useFactory: addDayTimeScheduleUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const updateDayTimeScheduleUseCaseProvider = {
    provide: UpdateDayTimeScheduleUseCase,
    useFactory: updateDayTimeScheduleUseCaseFactory,
    deps: [GroupCategoryRepository]
}

export const deleteDayTimeScheduleUseCaseProvider = {
    provide: DeleteDayTimeScheduleUseCase,
    useFactory: deleteDayTimeScheduleUseCaseFactory,
    deps: [GroupCategoryRepository]
}

@NgModule({
    providers: [
        createGroupCategoryUseCaseProvider,
        getAllGroupsCategoriesUseCaseProvider,
        getGroupCategoryByIdUseCaseProvider,
        deleteGroupCategoryByIdUseCaseProvider,
        updateGroupCategoryByIdUseCaseProvider,
        addCategorySubjectUseCaseProvider,
        deleteCategorySubjectUseCaseProvider,
        addCategoryLocationUseCaseProvider,
        deleteCategoryLocationUseCaseProvider,
        updateCategoryScheduleUseCaseProvider,
        addDayTimeScheduleUseCaseProvider,
        updateDayTimeScheduleUseCaseProvider,
        deleteDayTimeScheduleUseCaseProvider,
        { provide: GroupCategoryRepository, useClass: GroupCategoryRepositoryImpl },
    ],
    imports: [
        CommonModule,
        HttpClientModule,
    ],
})
export class DiGroupCategoryModule { }

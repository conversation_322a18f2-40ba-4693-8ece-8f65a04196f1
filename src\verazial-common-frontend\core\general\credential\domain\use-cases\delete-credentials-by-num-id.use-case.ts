import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialRepository } from "../repositories/credential.repository";
import { Observable } from "rxjs";

export class DeleteCredentialsByNumIdUseCase implements UseCase<{ numId: string }, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: { numId: string; }): Observable<any> {
        return this.credentialsRepository.deleteCredentialsByNumId(params);
    }
}
import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialRepository } from "../repositories/credential.repository";
import { Observable } from "rxjs";

export class UpdateCredentialPasswordByNumIdAndSubjectAppIdUseCase implements UseCase<{ numId: string, subjectAppId: string, password: string }, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: { numId: string; subjectAppId: string; password: string; }): Observable<any> {
        return this.credentialsRepository.updateCredentialPasswordByNumIdAndSubjectAppId(params);
    }

}
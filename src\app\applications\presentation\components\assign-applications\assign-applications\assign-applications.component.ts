import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective, FormBuilder } from '@angular/forms';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';

@Component({
  selector: 'app-assign-applications',
  templateUrl: './assign-applications.component.html',
  styleUrls: ['./assign-applications.component.css']
})
export class AssignApplicationsComponent implements OnInit{
  

  public form: FormGroup = this.fb.group({
    datilar: [],
    facial: [],
    iris: [],
  })

  /**
   * 
   * @param rootFormGroup 
   * @param fb 
   * @param validator 
   */

  dropdownList: any[] = [];
  dropdownSettings:IDropdownSettings={};
  showDropDownOptions: boolean = false;

  constructor(
    private fb: FormBuilder, 
    private validator: ValidatorService) { }

  ngOnInit(): void {
    
    this.dropdownList = [
      { item_id: 1, item_text: 'APP1' },
      { item_id: 2, item_text: 'APP2' },
      { item_id: 3, item_text: 'APP3' },
      { item_id: 4, item_text: 'APP4' }
    ];

    this.dropdownSettings = {
      idField: 'item_id',
      textField: 'item_text',
      singleSelection: false,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 3
    };
  }

  /**
   * 
   * @param field 
   * @returns 
   */
  isValid(field: string) {
    return this.validator.isValidField(this.form, field);
  }

  onItemSelect(event: any){}

  showElement(){
    this.showDropDownOptions = !this.showDropDownOptions;
  }
  

  submitFormData() {
    
  }

}

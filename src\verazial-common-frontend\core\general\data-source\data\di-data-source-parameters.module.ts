import { NgModule } from "@angular/core";
import { DataSourceParametersRepository } from "../domain/repositories/data-source-parameters.repository";
import { AddAppDataSourceParamsUseCase } from "../domain/use-cases/add-app-data-source-params.use-case";
import { DeleteDataSourceParamsByIdUseCase } from "../domain/use-cases/delete-data-source-params-by-id.use-case";
import { DeleteParamByDataSourceIdUseCase } from "../domain/use-cases/delete-param-by-data-source-id.use-case";
import { GetAllParamsByDataSourceIdUseCase } from "../domain/use-cases/get-all-params-by-data-source-id.use-case";
import { GetAppDataSourceParamByIdUseCase } from "../domain/use-cases/get-app-data-source-param-by-id.use-case";
import { UpdateAppDataSourceParamByIdUseCase } from "../domain/use-cases/update-app-data-source-param-by-id.use-case";
import { CommonModule } from "@angular/common";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { DataSourceParamRepositoryGrpcImpl } from "./repository-impl/data-source-param-impl-grpc.repository";

const addAppDataSourceParamsUseCaseFactory =
    (dataSourceParametersRepository: DataSourceParametersRepository) => new AddAppDataSourceParamsUseCase(dataSourceParametersRepository);

const updateAppDataSourceParamByIdUseCaseFactory =
    (dataSourceParametersRepository: DataSourceParametersRepository) => new UpdateAppDataSourceParamByIdUseCase(dataSourceParametersRepository);

const getAppDataSourceParamByIdUseCaseFactory =
    (dataSourceParametersRepository: DataSourceParametersRepository) => new GetAppDataSourceParamByIdUseCase(dataSourceParametersRepository);

const getAllParamsByDataSourceIdUseCaseFactory =
    (dataSourceParametersRepository: DataSourceParametersRepository) => new GetAllParamsByDataSourceIdUseCase(dataSourceParametersRepository);

const deleteDataSourceParamsByIdUseCaseFactory =
    (dataSourceParametersRepository: DataSourceParametersRepository) => new DeleteDataSourceParamsByIdUseCase(dataSourceParametersRepository);

const deleteParamByDataSourceIdUseCaseFactory =
    (dataSourceParametersRepository: DataSourceParametersRepository) => new DeleteParamByDataSourceIdUseCase(dataSourceParametersRepository);

export const addAppDataSourceParamsUseCaseProvider = {
    provide: AddAppDataSourceParamsUseCase,
    useFactory: addAppDataSourceParamsUseCaseFactory,
    deps: [DataSourceParametersRepository]
}

export const updateAppDataSourceParamByIdUseCaseProvider = {
    provide: UpdateAppDataSourceParamByIdUseCase,
    useFactory: updateAppDataSourceParamByIdUseCaseFactory,
    deps: [DataSourceParametersRepository]
}

export const getAppDataSourceParamByIdUseCaseProvider = {
    provide: GetAppDataSourceParamByIdUseCase,
    useFactory: getAppDataSourceParamByIdUseCaseFactory,
    deps: [DataSourceParametersRepository]
}

export const getAllParamsByDataSourceIdUseCaseProvider = {
    provide: GetAllParamsByDataSourceIdUseCase,
    useFactory: getAllParamsByDataSourceIdUseCaseFactory,
    deps: [DataSourceParametersRepository]
}

export const deleteDataSourceParamsByIdUseCaseProvider = {
    provide: DeleteDataSourceParamsByIdUseCase,
    useFactory: deleteDataSourceParamsByIdUseCaseFactory,
    deps: [DataSourceParametersRepository]
}

export const deleteParamByDataSourceIdUseCaseProvider = {
    provide: DeleteParamByDataSourceIdUseCase,
    useFactory: deleteParamByDataSourceIdUseCaseFactory,
    deps: [DataSourceParametersRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        addAppDataSourceParamsUseCaseProvider,
        updateAppDataSourceParamByIdUseCaseProvider,
        getAppDataSourceParamByIdUseCaseProvider,
        getAllParamsByDataSourceIdUseCaseProvider,
        deleteDataSourceParamsByIdUseCaseProvider,
        deleteParamByDataSourceIdUseCaseProvider,
        { provide: DataSourceParametersRepository, useClass: DataSourceParamRepositoryGrpcImpl },
        provideHttpClient(withInterceptorsFromDi())
    ]
})
export class DiDataSourceParametersModule { }
import { CategoryLocationGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { CategoryLocationEntity } from "../../domain/entity/category-location.entity";

export class CategoryLocationMapper extends Mapper<CategoryLocationGrpc, CategoryLocationEntity> {
    override mapFrom(param: CategoryLocationGrpc): CategoryLocationEntity {
        let categoryLocation = new CategoryLocationEntity();

        categoryLocation.id = param.getId();
        categoryLocation.groupCategoryId = param.getGroupcategoryid();
        categoryLocation.deviceId = param.getDeviceid();

        return categoryLocation
    }
    override mapTo(param: CategoryLocationEntity): CategoryLocationGrpc {
        let categoryLocation = new CategoryLocationGrpc();

        categoryLocation.setId(param.id!);
        categoryLocation.setGroupcategoryid(param.groupCategoryId!);
        categoryLocation.setDeviceid(param.deviceId!)

        return categoryLocation
    }
}
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AssignmentRespository } from "../repository/assignment.repository";
import { AssignmentElementEntity } from "../entity/assignment-elements.entity";

export class DeleteAssignmentElementByIdUseCase implements UseCaseGrpc<{ elements: AssignmentElementEntity[] }, SuccessResponse> {
    constructor(private repository: AssignmentRespository) { }
    execute(params: { elements: AssignmentElementEntity[] }): Promise<SuccessResponse> {
        return this.repository.deleteAssignmentElementById(params);
    }
}
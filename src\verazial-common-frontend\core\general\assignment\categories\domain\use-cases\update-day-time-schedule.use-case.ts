import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { GroupCategoryRepository } from "../repository/group-category.repository";
import { DayTimeScheduleEntity } from "../entity/day-time-schedule.entity";

export class UpdateDayTimeScheduleUseCase implements UseCaseGrpc<{ dayTimeSchedule: DayTimeScheduleEntity }, DayTimeScheduleEntity> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { dayTimeSchedule: DayTimeScheduleEntity; }): Promise<DayTimeScheduleEntity> {
        return this.groupCategoryRepository.updateDayTimeSchedule(params);
    }
}
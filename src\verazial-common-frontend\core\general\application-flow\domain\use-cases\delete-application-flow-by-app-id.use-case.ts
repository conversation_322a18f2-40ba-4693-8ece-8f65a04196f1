import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationFlowRepository } from "../repository/application-flow.repository";

export class DeleteApplicationFlowByAppIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private applicationFlowRespository: ApplicationFlowRepository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.applicationFlowRespository.deleteApplicationFlowByAppId(params);
    }
}
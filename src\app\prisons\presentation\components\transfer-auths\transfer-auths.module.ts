import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { AccordionModule } from "primeng/accordion";
import { ButtonModule } from "primeng/button";
import { CalendarModule } from "primeng/calendar";
import { CarouselModule } from "primeng/carousel";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DialogModule } from "primeng/dialog";
import { DropdownModule } from "primeng/dropdown";
import { FloatLabelModule } from "primeng/floatlabel";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputSwitchModule } from "primeng/inputswitch";
import { InputTextModule } from "primeng/inputtext";
import { InputTextareaModule } from "primeng/inputtextarea";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { SelectButtonModule } from "primeng/selectbutton";
import { StepsModule } from "primeng/steps";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { LoadingSpinnerModule } from "src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module";
import { TransferAuthsListComponent } from "./transfer-auths-list/transfer-auths-list.component";
import { TransferAuthEditComponent } from "./transfer-auth-edit/transfer-auth-edit.component";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { TreeSelectModule } from "primeng/treeselect";
import { WidgetSearchModule } from "src/verazial-common-frontend/modules/shared/components/widget-search/widget-search.module";
import { BioTechButtonsModule } from "src/verazial-common-frontend/modules/shared/components/bio-tech-buttons/bio-tech-buttons.module";
import { BioSignaturesModule } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures.module";
import { TransferAuthExecuteComponent } from "./transfer-auth-execute/transfer-auth-execute.component";
import { ListUserSubjectModule } from "src/app/user-subject/presentation/components/list-user-subject/list-user-subject.module";

@NgModule({
    declarations: [
      TransferAuthsListComponent,
      TransferAuthEditComponent,
      TransferAuthExecuteComponent,
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      InputTextareaModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      CalendarModule,
      AccordionModule,
      ScrollPanelModule,
      CarouselModule,
      FloatLabelModule,
      StepsModule,
      ToastModule,
      SelectButtonModule,
      TreeSelectModule,
      /* Custom Modules */
      LoadingSpinnerModule,
      EmptyModule,
      BioTechButtonsModule,
      WidgetSearchModule,
      BioSignaturesModule,
      ListUserSubjectModule,
    ],
    exports: [
      TransferAuthsListComponent,
      TransferAuthEditComponent,
      TransferAuthExecuteComponent,
    ]
  })
  export class TransferAuthsModule { }
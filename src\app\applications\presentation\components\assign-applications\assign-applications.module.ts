import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AssignApplicationsComponent } from './assign-applications/assign-applications.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { UiButtonModule, UiSwitchModule, UiAccordionModule, UiAlertMessageModule, UiTextModule } from 'ngx-verazial-ui-lib';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';



@NgModule({
  declarations: [
    AssignApplicationsComponent
  ],
  imports: [
    CommonModule,
    /* Verazial Components */
    UiButtonModule,
    UiSwitchModule,
    UiAccordionModule,
    UiAlertMessageModule,
    UiTextModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    NgMultiSelectDropDownModule
  ],
  exports:[
    AssignApplicationsComponent
  ]
})
export class AssignApplicationsModule { }

import { AssignmentRespository } from "../repository/assignment.repository";
import { AssignmentEntity } from "../entity/assignment.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetAllAssignmentsUseCase implements UseCaseGrpc<void, AssignmentEntity[]> {
    constructor(private repository: AssignmentRespository) { }
    execute(params: void): Promise<AssignmentEntity[]> {
        return this.repository.getAllAssignments();
    }
}
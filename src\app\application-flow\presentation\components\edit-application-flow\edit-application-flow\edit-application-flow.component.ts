import { DOCUMENT } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, Inject, Input, LOCALE_ID, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import Drawflow from 'drawflow';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { NodeInOut } from 'src/verazial-common-frontend/core/general//application-flow/common/interfaces/node-in-out.interface';
import { NodeInterface } from 'src/verazial-common-frontend/core/general//application-flow/common/interfaces/node.interface';
import { WindowTarget } from 'src/verazial-common-frontend/core/general//application-flow/common/interfaces/windows-target.enum';
import { ApplicationFlowEntity } from 'src/verazial-common-frontend/core/general//application-flow/domain/entity/application-flow.entity';
import { AddApplicationFlowUseCase } from 'src/verazial-common-frontend/core/general//application-flow/domain/use-cases/add-application-flow.use-case';
import { GetApplicationFlowByAppIdUseCase } from 'src/verazial-common-frontend/core/general//application-flow/domain/use-cases/get-application-flow-by-app-id.use-case';
import { UpdateApplicationFlowByAppIdUseCase } from 'src/verazial-common-frontend/core/general//application-flow/domain/use-cases/update-application-flow-by-app-id.use-case';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { ApplicationWindowEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/application-window.entity';
import { ApplicationEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/application.entity';
import { WindowParametersEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/window-parameters.entity';
import { AddAppWindowUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/add-app-window.use-case';
import { AddApplicationsUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/add-applications.use-case';
import { AddWindowParamUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/add-window-param.use-case';
import { DeleteAppWindowByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/delete-app-window-by-id.use-case';
import { DeleteWindowParamByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/delete-window-param-by-id.use-case';
import { GetAppWindowByAppIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-app-window-by-app-id.use-case';
import { GetAppWindowByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-app-window-by-id.use-case';
import { GetApplicationByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-application-by-id.use-case';
import { GetWindowParamByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-window-param-by-id.use-case';
import { GetWindowParamsByWindowIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-window-params-by-window-id.use-case';
import { UpdateAppWindowByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/update-app-window-by-id.use-case';
import { UpdateApplicationByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/update-application-by-id.use-case';
import { UpdateWindowParamByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/update-window-param-by-id.use-case';
import { DataSourceEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source.entity';
import { GetAllDataSourcesUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/get-all-data-sources.use-case';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { GenericKeyValue } from 'src/verazial-common-frontend/core/models/key-value.interface';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { v4 as uuidv4 } from 'uuid';
import { AppRegistryEntity } from 'src/verazial-common-frontend/core/general/app-registry/domain/entities/app-registry.entity';
import { GetAppRegistriesUseCase } from 'src/verazial-common-frontend/core/general/app-registry/domain/use-cases/get-app-registries.use-case';
import { PassListEvents } from 'src/verazial-common-frontend/core/models/pass-list-events.enum';
import { DeleteWindowParamsByWindowIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/delete-window-params-by-window-id.use-case';

@Component({
  selector: 'app-edit-application-flow',
  templateUrl: './edit-application-flow.component.html',
  styleUrl: './edit-application-flow.component.css'
})
export class EditApplicationFlowComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  // Inputs
  @Input() applicationData!: ApplicationEntity | undefined;
  // Outputs
  @Output() return = new EventEmitter<boolean>();

  editor!: Drawflow;

  showLastUpdate: boolean = false;

  isEditing: boolean = false;

  lastUpdate: string = "";

  headerWindowDialog: string = "";

  publishText: string = "flow.publish";

  publishIcon: string = "import";

  isPublishDisabled: boolean = true;

  windows: ApplicationWindowEntity[] = [];

  uniqueWindows: ApplicationWindowEntity[] = [];

  sortedWindows: ApplicationWindowEntity[] = [];

  listComponents: WindowParametersEntity[] = [];

  options: MenuItem[] | undefined;

  listNodesDrawFlow: NodeInterface[] = [];

  hideStyle: string = 'display: none;';

  showAttributes: string = "display: none;";

  attributeStyle: string = "display: block;";

  dataComponentLoading: boolean = false;

  showWindowDialog: boolean = false;

  optionNotValid: boolean = false;

  showSaveApplicationDialog: boolean = false;

  selectedNodeId: number = 0;

  tempSelectedWindow!: ApplicationWindowEntity | undefined;

  selectedWindow!: ApplicationWindowEntity;

  applicationTitle: string = "";

  tmpApplicationFlow!: ApplicationFlowEntity;

  applicationSaved: boolean = false;

  applicationDialogHeader: string = "";

  selectedDataSource: DataSourceEntity | undefined;

  selectedApplicationType: GenericKeyValue | undefined;

  selectedTechnology: GenericKeyValue | undefined;

  selectedFlowType: GenericKeyValue | undefined;

  savedWindows: ApplicationWindowEntity[] = [];

  originalApplicationFlow: ApplicationFlowEntity | undefined;

  savedListNodesDrawFlow: NodeInterface[] = [];

  isFlowPublished: boolean = true;

  listAppRegistry: AppRegistryEntity[] = [];

  selectedAppRegistry: AppRegistryEntity | undefined;

  private rejectTimeout: any;

  listOfApplicationFlowTypes: GenericKeyValue[] = [];

  // prevSelectedWindow!: ApplicationWindowEntity;

  listTargets: GenericKeyValue[] = [
    { key: WindowTarget.AUTHENTICATION, value: this.translate.instant('pass_application.authentication') },
    { key: WindowTarget.PROCESS, value: this.translate.instant('pass_application.process') },
    { key: WindowTarget.UPDATE_CREDENTIALS, value: this.translate.instant('pass_application.update_credentials') }
  ];

  mobile_item_selec: string = "";

  nodeCounter: number = 0;

  listComponentTypes: GenericKeyValue[] = [
    { key: 'TEXTBOX', value: 'Textbox' },
    { key: 'PASSWORD', value: 'Password' },
    { key: 'COMBOBOX', value: 'Combobox' },
    { key: 'CHECKBOX', value: 'Checkbox' },
    { key: 'BUTTON', value: 'Button' },
    { key: 'IMAGE-BUTTON', value: 'Image Button' },
    { key: 'LIST-CHECKBOX', value: 'List Checkbox' },
    { key: 'DATAGRID', value: 'Datagrid' }
  ];

  listEvents: GenericKeyValue[] = Object.values(PassListEvents).map(event => ({
    key: event,
    value: this.formatEventName(event)
  }));

  private formatEventName(event: string): string {
    return event.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
  }

  listDataSources: DataSourceEntity[] = [];

  isPublishChecked: boolean = false;

  listTechnologies: GenericKeyValue[] = [
    { key: '.NET', value: '.NET' },
    // {key: 'PYTHON', value: 'Python'},
    { key: 'JAVA', value: 'Java' },
    // {key: 'C++', value: 'C++'},
    { key: 'HTML', value: 'HTML' }
  ];

  listApplicationTypes: GenericKeyValue[] = [
    { key: 'DESKTOP', value: 'Desktop' },
    { key: 'WEB', value: 'Web' }
  ];

  constructor(
    @Inject(DOCUMENT) private document: any,
    @Inject(LOCALE_ID) private locale: string,
    private translate: TranslateService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private fb: FormBuilder,
    private validator: ValidatorService,
    private getAllDataSourcesUseCase: GetAllDataSourcesUseCase,
    private getAppRegistriesUseCase: GetAppRegistriesUseCase,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
    // Application
    private addApplicationUseCase: AddApplicationsUseCase,
    private updateApplicationByIdUseCase: UpdateApplicationByIdUseCase,
    private getApplicationByIdUseCase: GetApplicationByIdUseCase,
    // Application Flow
    private getApplicationFlowUseCase: GetApplicationFlowByAppIdUseCase,
    private addApplicationFlowUseCase: AddApplicationFlowUseCase,
    private updateApplicationFlowByIdUseCase: UpdateApplicationFlowByAppIdUseCase,
    private getApplicationFlowByAppIdUseCase: GetApplicationFlowByAppIdUseCase,
    // Windows
    private addAppWindowUseCase: AddAppWindowUseCase,
    private updateAppWindowByAppIdUseCase: UpdateAppWindowByIdUseCase,
    private updateAppWindowByIdUseCase: UpdateAppWindowByIdUseCase,
    private deleteAppWindowByIdUseCase: DeleteAppWindowByIdUseCase,
    private deleteAppWindowByAppIdUseCase: DeleteAppWindowByIdUseCase,
    private getAppWindowByAppIdUseCase: GetAppWindowByAppIdUseCase,
    private getAppWindowByIdUseCase: GetAppWindowByIdUseCase,
    // Window's parameters
    private addWindowParamUseCase: AddWindowParamUseCase,
    private updateWindowParamByIdUseCase: UpdateWindowParamByIdUseCase,
    private deleteWindowParamByIdUseCase: DeleteWindowParamByIdUseCase,
    private deleteWindowParamsByWindowIdUseCase: DeleteWindowParamsByWindowIdUseCase,
    private getWindowParamsByWindowIdUseCase: GetWindowParamsByWindowIdUseCase,
    private getWindowParamByIdUseCase: GetWindowParamByIdUseCase,
  ) { }

  public form: FormGroup = this.fb.group({
    windowNamePanel: [{ disabled: true }],
    applicationTitle: [],
    applicationName: ['', Validators.required],
    appRegistryId: ['', Validators.required],
    applicationPath: ['', Validators.required],
    technology: ['', Validators.required],
    applicationType: ['', Validators.required],
    dataSource: [],
    windowName: ['', Validators.required],
    windowTarget: ['', Validators.required],
    windowTargetPanel: [],
    event: [],
    componentType: [],
    isPublished: [],
    flowType: [],
  });

  ngOnInit(): void {

    this.form.controls['applicationTitle'].setValue(this.translate.instant('pass_application.application'));

    // Window button options
    this.options = [
      {
        label: this.translate.instant("edit"),
        icon: 'pi pi-fw pi-pencil',
        command: () => {
          this.isEditing = true;
          this.editWindow();
        }
      },
      {
        label: this.translate.instant("remove"),
        icon: 'pi pi-fw pi-trash',
        command: () => {
          this.deleteWindowDialogBox();
          // this.removeAction();
        }
      }
    ]

    // DrawFlow initialisation
    var id = document.getElementById("drawflow");
    this.editor = new Drawflow(id!!);

    this.editor.reroute = true;
    this.editor.reroute_fix_curvature = true;

    this.editor.zoom_max = 1.6;
    this.editor.zoom_min = 0.5;

    this.editor.start();

    // Get all datasources
    this.getAllDataSources();
    // Get all App Registries
    this.getAppRegistries();

    if (this.applicationData) {
      this.applicationSaved = true;
      this.form.controls['applicationTitle'].setValue(this.applicationData.applicationName!);
      this.getApplicationFlow(this.applicationData.id!);
      this.isPublishDisabled = false;
      if (this.applicationData.status) {
        this.publishText = 'flow.unpublished';
        this.publishIcon = "export";
        this.isFlowPublished = false;
      } else {
        this.publishText = 'flow.publish';
        this.publishIcon = "import";
        this.isFlowPublished = true;
      }
    }

    // Disable fields
    this.form.get('applicationTitle')?.disable();

    // Load flow types
    const flowTypes = this.localStorageService.getSessionSettings()?.continued1?.applicationFlowTypes?.map(str => ({key: str, value: str})) ?? [];
    this.listOfApplicationFlowTypes = flowTypes;

  }

  ngAfterViewInit(): void {
    this.initDrawFlow();
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.clearRejectTimeout();
  }

  async getAllDataSources() {
    this.listDataSources = await this.getAllDataSourcesUseCase.execute();
  }

  getAppRegistries() {
    this.getAppRegistriesUseCase.execute().then(
      (data) => {
        this.listAppRegistry = data;
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_APP_REGISTRIES, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  async getWindowsByAppId(appId: string) {
    let result = await this.getAppWindowByAppIdUseCase.execute({ appId });
    if (result.length > 0) {
      result.forEach((window) => {
        let tmp = this.windows.find(tmpW => tmpW.id == window.id)
        if (!tmp) {
          this.uniqueWindows.push(window);
          // this.windows.push(window);
          this.savedWindows.push(window);
        }
      });
    }
  }

  // Get the Application Flow
  getApplicationFlow(appId: string) {
    this.getApplicationFlowUseCase.execute({ id: appId }).then(
      (appFlow) => {
        this.originalApplicationFlow = appFlow;
        let tmpDrawFlow = JSON.parse(appFlow.drawFlow!);
        let tmpDataKeys = Object.keys(tmpDrawFlow.drawflow.Home.data);
        this.editor.import(JSON.parse(appFlow.drawFlow!));

        tmpDataKeys.forEach((key) => {
          this.selectedNodeId = tmpDrawFlow.drawflow.Home.data[key].id
          let node: NodeInterface = {
            id: this.selectedNodeId,
            window: tmpDrawFlow.drawflow.Home.data[key].data,
          }

          // Load windows data
          let tmpUniqueWindows: ApplicationWindowEntity[] = [];

          this.windows.push(tmpDrawFlow.drawflow.Home.data[key].data);

          tmpUniqueWindows.push(tmpDrawFlow.drawflow.Home.data[key].data)
          this.uniqueWindows = tmpUniqueWindows.filter(
            (obj, index, self) =>
              index === self.findIndex((o) => o.id === obj.id)
          );

          this.savedWindows.push(JSON.parse(JSON.stringify(tmpDrawFlow.drawflow.Home.data[key].data)));

          // Update UI Components
          if (node.window.windowParameters) {
            if (node.window.windowParameters.length > 0) {
              let parameters = node.window.windowParameters;
              this.updateComponentDiv(parameters);
              parameters.forEach((param: WindowParametersEntity) => {
                this.addFormControl(param.id!, this.selectedNodeId);
              })
            }
          }

          // Add window with status = true to the list of Nodes
          if (node.window.status) {
            this.savedListNodesDrawFlow.push(JSON.parse(JSON.stringify(node)));
            this.listNodesDrawFlow.push(node);
          }

        });
        this.getWindowsByAppId(appId);
        this.nodeCounter = isFinite(Math.max(...tmpDataKeys.map(Number))) ? Math.max(...tmpDataKeys.map(Number)) : 0; // Math.max(...tmpDataKeys.map(Number));
      },
      (error) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: error.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }


  // Initialise the DrwaFlow component
  initDrawFlow() {
    this.editor.on('nodeSelected', (id) => {
      // Show panel
      this.hideStyle = "display: inline-block;";
      // Get the data of the drawflow
      let data = this.editor.export().drawflow.Home.data[id];

      // Node ID
      this.selectedNodeId = id;

      // Save the window's attributes
      if (this.selectedWindow != undefined) {
        this.saveAttributeData(this.selectedWindow);
      }

      this.listComponents = [];
      // Get the selected window
      if(this.windows.length > 1){
        this.selectedWindow = [...this.windows.filter((_v, _) => (_v.id == data.name) && (_v.windowOrder == this.selectedNodeId))][0];
      }else{
        this.selectedWindow = this.windows[0];
      }

      this.windows.forEach((window) => {
        if ((window.id == this.selectedWindow.id)  && (window.windowOrder == this.selectedNodeId)) {
          if (window.windowParameters) {
            this.listComponents = window.windowParameters;
          }
        }
      });

      if (this.listComponents.length > 0) {
        this.dataComponentLoading = true
        this.attributeStyle = "visibility: hidden;";

        setTimeout(() => {
          this.dataComponentLoading = false;
          this.attributeStyle = "display: block;";
          this.loadFieldsData(this.listComponents);
        }, 500);
      }

      // Fill the textbox of the side panel
      this.form.controls['windowNamePanel'].setValue(this.selectedWindow.windowName);
      this.form.controls['windowTargetPanel'].setValue(this.selectedWindow.target);
      // Disable fields
      this.form.get('windowNamePanel')?.disable();
      this.form.get('windowTargetPanel')?.disable();

    });

    this.editor.on('moduleChanged', (name) => {

    });

    this.editor.on('nodeUnselected', (id) => {
      this.hideStyle = "display: none;"
    });

    this.editor.on('nodeRemoved', (id) => {
      this.listNodesDrawFlow = [...this.listNodesDrawFlow.filter((_v, k) => (_v.id != id)
        && (_v.window.windowOrder != this.selectedWindow.windowOrder) )];

      this.windows = [...this.windows.filter(w => w.windowOrder != id)];

    });
  }

  ngOnChanges(changes: SimpleChanges): void {

  }

  onDragStart(event: any, window: ApplicationWindowEntity) {
    if (event.type === "touchstart") {
      this.mobile_item_selec = event.target.closest(".drag-drawflow").getAttribute('data-node');
    } else {
      event.dataTransfer.setData("node", JSON.stringify(window));
    }
  }

  // Add new components to the window
  addNewComponent() {
    let newComponent: WindowParametersEntity = {
      id: uuidv4(),
      applicationWindowId: this.selectedWindow.id,
      displayName: "",
      name: "",
      windowOrder: Number(this.selectedNodeId),
      attributeName: "",
      uiComponentType: "",
      componentPosition: 0,
      triggerOrder: 0,
      event: PassListEvents.CLICK,
      showComponentToUser: false
    }

    this.addFormControl(newComponent.id!, this.selectedNodeId);

    this.windows.forEach((window) => {
      if ((window.id == this.selectedWindow.id) && (window.windowOrder == this.selectedNodeId)) {
        newComponent.applicationWindowId = window.id;
        this.listComponents = [...this.listComponents, newComponent];
        window.windowParameters = this.listComponents;
      }
    });

    this.updateComponentDiv(this.listComponents);
  }


  addFormControl(id: string, windowOrder: number) {
    this.form.addControl(id + '-' + `${windowOrder}` + '-window-display-name', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-window-name', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-attribute-name', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-component-type', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-trigger-order', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-position', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-event', new FormControl(''));
    this.form.addControl(id + '-' + `${windowOrder}` + '-display-component-user', new FormControl(''));
  }

  saveAttributeData(node: ApplicationWindowEntity) {
    this.windows.forEach((window) => {
      // Look for the window to add the attributes
      if ((window.id == node.id) && (window.windowOrder == this.selectedWindow.windowOrder)) {
        window.windowParameters?.forEach((component) => {
          component.displayName = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-window-display-name'].value;
          component.name = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-window-name'].value;
          component.attributeName = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-attribute-name'].value;
          component.uiComponentType = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-component-type'].value.key ?? component.uiComponentType;
          component.triggerOrder = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-trigger-order'].value;
          component.componentPosition = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-position'].value;
          component.event = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-event'].value.key ?? component.event;
          component.showComponentToUser = this.form.controls[component.id + '-' + `${this.selectedWindow.windowOrder}` + '-display-component-user'].value == "" ? false : true;
        });
      }
    });

    this.listNodesDrawFlow.forEach((window) => {
      let wn = [...this.windows.filter((_v, k) => (_v.id == window.window.id)
        && (_v.windowOrder == this.selectedWindow.windowOrder))];

      if ((window.window.id == node.id) && (window.window.windowOrder == this.selectedWindow.windowOrder)) {
        window.window.windowParameters = wn[0].windowParameters;
      }
    });
  }

  addWindow() {
    this.headerWindowDialog = this.translate.instant("pass_application.add_window");
    this.cleanFields();
    this.showWindowDialog = true;
  }

  editWindow() {
    if (this.tempSelectedWindow) {
      this.headerWindowDialog = this.translate.instant("pass_application.edit_window");
      this.isEditing = true;
      this.form.controls['windowName'].setValue(this.tempSelectedWindow.windowName);
      this.form.controls['windowTarget'].patchValue(this.tempSelectedWindow.target);
      this.showWindowDialog = true;
    }
  }

  onRightClick(window: ApplicationWindowEntity) {
    this.tempSelectedWindow = window;
  }

  //Fills the data
  loadFieldsData(components: WindowParametersEntity[]) {
    components.forEach((component) => {
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-window-display-name'].setValue(component.displayName);
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-window-name'].setValue(component.name);
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-attribute-name'].setValue(component.attributeName);

      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-trigger-order'].setValue(component.triggerOrder);
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-position'].setValue(component.componentPosition);

      const tmpUiComponentType = this.listComponentTypes.find(componentType => componentType.key == component.uiComponentType);
      const element = document.getElementById(component.id + '-' + `${component.windowOrder}` + '-component-type');
      if (element) {
        const spanWithAriaLabel = element.querySelector('span.p-dropdown-label');
        if (spanWithAriaLabel) {
          spanWithAriaLabel.textContent = tmpUiComponentType?.value! as string;
        }
      }

      const tmpUiComponentEvent = this.listEvents.find(event => event.key == component.event);
      const elementEvent = document.getElementById(component.id + '-' + `${component.windowOrder}` + '-event');
      if (elementEvent) {
        const spanWithAriaLabelEvent = elementEvent.querySelector('span.p-dropdown-label');
        if (spanWithAriaLabelEvent) {
          spanWithAriaLabelEvent.textContent = tmpUiComponentEvent?.value! as string;
        }
      }

      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-display-component-user'].setValue(component.showComponentToUser);
    });
  }

  // Delete an element Dialog box
  deleteElement(item: WindowParametersEntity) {
    this.confirmationService.confirm({
      message: this.translate.instant('content.message_remove') + '<b>' + item.attributeName + '</b>?',
      header: this.translate.instant("headers.remove_component"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translate.instant("yes"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        this.deleteComponent(item);
      },
      reject: () => {
        this.clearRejectTimeout();
      }
    });

    // Set a timeout to automatically trigger the reject action after 10 seconds
    this.rejectTimeout = setTimeout(() => {
      this.confirmationService.close(); // Close the dialog
    }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
  }

  deleteComponent(component: WindowParametersEntity) {
    this.listComponents = [...this.listComponents.filter((_v, k) => _v.id != component.id)];
    // Removing the component from the list components of the Window.
    this.windows.forEach((window) => {
      if (window.id == this.selectedWindow.id) {
        //Updating the component in the window
        window.windowParameters = this.listComponents;
        // Removing the components from the FormControl
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-window-display-name');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-window-name');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-attribute-name');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-component-type');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-trigger-order');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-position');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-event');
        this.form.removeControl(component.id!! + '-' + `${component.windowOrder}` + '-display-component-user');
      }
    });
    // Updating the node
    this.updateComponentDiv(this.listComponents);
  }

  deleteWindowDialogBox() {
    this.confirmationService.confirm({
      message: this.translate.instant('content.message_remove') + " <b>" + this.tempSelectedWindow?.windowName + "</b>?",
      header: this.translate.instant("headers.remove_window"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translate.instant("yes"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        this.deleteWindow();
      },
      reject: () => {
        this.clearRejectTimeout();
      }
    });

    // Set a timeout to automatically trigger the reject action after 10 seconds
    this.rejectTimeout = setTimeout(() => {
      this.confirmationService.close(); // Close the dialog
    }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
  }

  deleteWindow() {
    if (this.tempSelectedWindow) {

      this.listNodesDrawFlow.forEach((node) => {
        if (node.window.id == this.tempSelectedWindow?.id) {
          this.removeNodeConnections(node.id);
        }
      });

      this.listNodesDrawFlow.forEach((node) => {
        if (node.window.id == this.tempSelectedWindow?.id) {

          delete this.editor.drawflow.drawflow.Home.data[node.id];
          this.document.getElementById("node-" + node.id).remove();
          this.listNodesDrawFlow = [...this.listNodesDrawFlow.filter((_v, k) => _v.id != node.id)];

          // Remove node's connections
          let nodeIn = this.document.querySelectorAll(`svg.node_in_node-${node.id}`);
          let nodeOut = this.document.querySelectorAll(`svg.node_out_node-${node.id}`);
          nodeIn.forEach((connIn: any) => {
            connIn.remove();
          });

          nodeOut.forEach((connOut: any) => {
            connOut.remove();
          });
        }
      });

      this.uniqueWindows = [...this.uniqueWindows.filter((_v, k) => _v.id != this.tempSelectedWindow?.id)];

      if (this.uniqueWindows.length == 0) {
        this.hideStyle = "display: none;"
      }

      this.tempSelectedWindow = undefined;
    }
  }

  // Removing connections
  removeNodeConnections(nodeId: number) {
    this.listNodesDrawFlow.forEach((node) => {
      if (this.editor.drawflow.drawflow.Home.data[node.id].inputs[`input_1`]) {
        let connectionIn = this.editor.drawflow.drawflow.Home.data[node.id].inputs[`input_1`];

        connectionIn.connections.forEach((conn) => {
          if (conn.node == String(nodeId)) {
            connectionIn.connections = [...connectionIn.connections.filter((_v, k) => _v.node != String(nodeId))];
          }
        });
      }

      if (this.editor.drawflow.drawflow.Home.data[node.id].outputs[`output_1`]) {
        let connectionOut = this.editor.drawflow.drawflow.Home.data[node.id].outputs[`output_1`];
        connectionOut.connections.forEach((conn) => {
          if (conn.node == String(nodeId)) {
            connectionOut.connections = [...connectionOut.connections.filter((_v, k) => _v.node != String(nodeId))];
          }
        });
      }
    });
  }

  // Updates the component's name
  onChangeName(component: WindowParametersEntity) {
    this.listComponents.forEach((item) => {
      if (item.id == component.id) {
        item.displayName = this.form.controls[component.id + '-' + `${component.windowOrder}` + '-window-display-name'].value;
        // Update the component's name in the node
        let label = this.document.getElementById(`${item.id}-${component.windowOrder}-component-display-name`);
        label.innerHTML = this.form.controls[component.id + '-' + `${component.windowOrder}` + '-window-display-name'].value;
      }
    });
  }

  onDrop(event: any) {
    event.preventDefault();
    let data: ApplicationWindowEntity = JSON.parse(event.dataTransfer.getData("node"));

    data.windowParameters = [];
    data.status = true;

    // let winNode = [...this.listNodesDrawFlow.filter((_v, k) => _v.window.id == data.id)];
    /*if (winNode.length > 0) {
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('messages.window_used'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
      return;
    }*/

    this.nodeCounter += 1;

    let newNode: NodeInterface = {
      id: this.nodeCounter,
      window: data
    }

    this.addNodeToDrawFlow(newNode, event.clientX, event.clientY);
  }

  // Adding new nodes to the flow panel
  addNodeToDrawFlow(node: NodeInterface, pos_x: number, pos_y: number) {

    pos_x = pos_x * (this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * this.editor.zoom)) - (this.editor.precanvas.getBoundingClientRect().x * (this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * this.editor.zoom)));
    pos_y = pos_y * (this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * this.editor.zoom)) - (this.editor.precanvas.getBoundingClientRect().y * (this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * this.editor.zoom)));

    var htmlComponent = `
    <div>
      <div class="flow-container">
        <label class="component-label">${this.translate.instant('pass_application.window')}</label>
        <input class="text-action-component" type="text" id="${node.window.id}-${node.id}-node-window" placeholder="${node.window.windowName}" disabled>
        <div id="componentAttributeTitle-${node.id}"></div>
        <div class="action-container" id="${node.id}" [style]="${this.showAttributes}">
        </div>
      </div>
    </div>
    `;

    // Adding the action to the list of actions.
    this.listNodesDrawFlow = [...this.listNodesDrawFlow, node];

    node.window.windowOrder = undefined;

    this.windows.push(node.window);
    // this.loggerService.debug(this.listNodesDrawFlow);
    this.editor.addNode(node.window.id!!, 1, 1, pos_x, pos_y, String(node.id), {}, htmlComponent, false);

    // Update the window status
    this.windows.forEach((window) => {
      if ((window.id == node.window.id) && (window.windowOrder == null || window.windowOrder == undefined)) {
        window.status = true;
        window.windowOrder = node.id;
      }
    });
  }


  saveApplication() {
    if (this.applicationSaved) {
      this.applicationDialogHeader = this.translate.instant('pass_application.update_application_flow');
      this.form.controls['applicationName'].setValue(this.applicationData?.applicationName);
      this.form.controls['applicationPath'].setValue(this.applicationData?.fullPath);
      this.selectedAppRegistry = this.listAppRegistry.find(appReg => appReg.id == this.applicationData?.appRegistryId);
      this.selectedDataSource = this.listDataSources.find(ds => ds.id == this.applicationData?.dataSourceId);
      this.selectedTechnology = this.listTechnologies.find(tech => tech.key == this.applicationData?.technology);
      this.selectedFlowType = this.listOfApplicationFlowTypes.find(type => type.key == this.applicationData?.flowType);
      this.selectedApplicationType = this.listApplicationTypes.find(type => type.key == this.applicationData?.applicationType);
      this.form.controls['isPublished'].setValue(this.applicationData?.status);
    } else {
      this.applicationDialogHeader = this.translate.instant('pass_application.save_application_flow');
    }
    this.showSaveApplicationDialog = true;
  }

  onDragOver(event: any) {
    event.preventDefault();
  }

  isValid(field: string) {
    return this.validator.isValidField(this.form, field);
  }

  // Save window
  saveWindow() {

    this.form.controls['windowName'].touched;
    this.form.controls['windowTarget'].touched;

    if (!this.isValid('windowName') || !this.isValid('windowTarget')) {
      return
    }

    if (this.isEditing) {
      let windowName = this.form.controls['windowName'].value;
      let target = this.form.controls['windowTarget'].value.key;

      this.listNodesDrawFlow.forEach((node) => {
        if(this.selectedWindow){
          if (node.window.id == this.selectedWindow.id) {
            this.form.controls['windowNamePanel'].setValue(windowName);
            this.form.controls['windowTargetPanel'].setValue(target);
          }
        }

        if (node.window.id == this.tempSelectedWindow?.id) {
          node.window.windowName = this.form.controls['windowName'].value;
          node.window.target = this.form.controls['windowTarget'].value.key;
          node.window.status = false;
          this.document.getElementById(node.window.id + '-' + node.id + '-node-window').value = this.form.controls['windowName'].value;
        }

      });

      this.windows.forEach((window) => {
        if (window.id == this.tempSelectedWindow?.id) {
          window.windowName = this.form.controls['windowName'].value;
          window.target = this.form.controls['windowTarget'].value.key;
          window.status = false;
        }
      });
    } else {
      let searchWindow = [...this.windows.filter((_v, k) => _v.windowName == this.form.controls['windowName'].value)];

      if (searchWindow.length > 0) {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: this.translate.instant('content.window_exist'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        return
      }

      let newWindow: ApplicationWindowEntity = {
        id: uuidv4(),
        windowName: this.form.controls['windowName'].value,
        target: this.form.controls['windowTarget'].value.key,
        status: false
      }

      this.uniqueWindows = [...this.uniqueWindows, newWindow];
      /*this.uniqueWindows = tmpUniqueWindow.filter(
        (obj, index, self) =>
          index === self.findIndex((o) => o.id === obj.id)
      );*/

      // this.windows = [...this.windows, newWindow];
    }

    // Hide modal
    this.showWindowDialog = false;
    // Cleaning fields
    this.cleanFields();
  }

  closeActionDialog() {
    // Hide modal
    this.showWindowDialog = false;
    // Cleaning fields
    this.cleanFields();
  }

  // Cleaning and reset the fields
  cleanFields() {
    this.form.controls['windowName'].setValue('');
    this.form.controls['windowName'].reset();
    this.form.controls['windowName'].untouched;

    this.form.controls['windowTarget'].reset();
    this.form.controls['windowTarget'].untouched;

    this.form.controls['applicationName'].reset();
    this.form.controls['applicationName'].untouched;

    this.form.controls['flowType'].reset();
    this.form.controls['flowType'].untouched;

    this.form.controls['appRegistryId'].reset();
    this.form.controls['appRegistryId'].untouched;

    this.form.controls['applicationPath'].reset();
    this.form.controls['applicationPath'].untouched;

    this.form.controls['technology'].reset();
    this.form.controls['technology'].untouched;

    this.form.controls['applicationType'].reset();
    this.form.controls['applicationType'].untouched;

    this.form.controls['dataSource'].reset();
    this.form.controls['dataSource'].untouched;

    this.form.controls['isPublished'].reset();
    this.form.controls['isPublished'].untouched;

  }

  cleanDynamicFields(components: WindowParametersEntity[]) {
    components.forEach((component) => {
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-window-display-name'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-window-name'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-attribute-name'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-component-type'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-trigger-order'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-position'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-event'].reset();
      this.form.controls[component.id + '-' + `${component.windowOrder}` + '-display-component-user'].reset();
    });

  }

  closeSaveApplicationDialog() {
    this.showSaveApplicationDialog = false;
    this.cleanFields();
  }

  // Accept save application
  acceptSaveApplication() {
    // Save Data
    if (this.selectedWindow != undefined) {
      this.saveAttributeData(this.selectedWindow);
    }

    let application: ApplicationEntity | undefined = undefined;
    // Export DrawFlow data
    let drawFlowData = this.editor.export();
    let fullDrawFlow: any = undefined;

    if(this.windows.length > 0){
      // Sort nodes
      let sortedNodes = this.sortWindowsNodes(drawFlowData);
      // Update DrawFlow data with window information
      fullDrawFlow = this.addApplicationDataToDrawFlow(sortedNodes, drawFlowData);
    }else{
      fullDrawFlow = drawFlowData;
    }

    if (this.applicationSaved) {
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.MOD_APP_FLW, ReasonActionTypeEnum.UPDATE, () => {
        let dataSource: string = "";

        if (this.form.controls['dataSource'].value) {
          dataSource = this.form.controls['dataSource'].value.id;
        } else {
          dataSource = this.applicationData?.dataSourceId!;
        }

        application = {
          id: this.applicationData?.id,
          applicationName: this.form.controls['applicationName'].value,
          flowType: this.form.controls['flowType'].value != undefined ?
            this.form.controls['flowType'].value.key : this.applicationData?.flowType,
          appRegistryId: this.form.controls['appRegistryId'].value != undefined ?
            this.form.controls['appRegistryId'].value.id : this.applicationData?.appRegistryId,
          fullPath: this.form.controls['applicationPath'].value,
          technology: this.form.controls['technology'].value != undefined ?
            this.form.controls['technology'].value.key : this.applicationData?.technology,
          dataSourceId: dataSource,
          applicationType: this.form.controls['applicationType'].value != undefined ?
            this.form.controls['applicationType'].value.key : this.applicationData?.applicationType,
          status: this.form.controls['isPublished'].value
        }

        // Update application
        this.onUpdateApplication(application);

        this.tmpApplicationFlow = {
          id: this.originalApplicationFlow?.id,
          applicationId: this.originalApplicationFlow?.applicationId,
          drawFlow: JSON.stringify(fullDrawFlow)
        }

        this.onUpdateApplicationFlow(this.tmpApplicationFlow);

        // Getting windows to remove
        let windows_to_remove = this.savedWindows.filter(a => !this.windows.some(b => (b.id === a.id) && (b.windowOrder === a.windowOrder)));
        let windows_to_update = this.windows.filter(a => this.savedWindows.some(b => (b.id === a.id) && (b.windowOrder === a.windowOrder)));
        let windows_to_add = this.windows.filter(a => !this.savedWindows.some(b => (b.id === a.id) && (b.windowOrder === a.windowOrder)));

        // Remove windows
        if(windows_to_remove.length > 0){
          windows_to_remove.forEach( w => { this.onDeleteWindow(w) } )
        }

        // Add new windows
        if(windows_to_add.length > 0){
          windows_to_add.forEach(w => {
            w.applicationId = this.applicationData?.id;
            this.onSaveWindow(w);
          })
        }

        // Update windows
        if(windows_to_update.length > 0){
          windows_to_update.forEach(w => {
            const old_window = this.savedWindows.find(ow => (ow.id === w.id) && (ow.windowOrder == w.windowOrder));
            this.onUpdateWindow(w);
            // Only windows with status = true are the parameters updated
            if (w.status) {
              w.windowParameters?.forEach((param) => {
                let tmpParam = old_window?.windowParameters?.filter(p => p.id == param.id);
                if (tmpParam?.length == 0) {
                  this.onSaveWindowParameter([param]);
                } else {
                  this.onUpdateWindowParameter(param);
                }
              })
              old_window?.windowParameters?.forEach((param) => {
                let tmpParam = w.windowParameters?.find(p => (p.id == param.id));
                if (!tmpParam) {
                  this.onDeleteWindowParameter(param);
                }
              });
            }
          })
        }
        this.completeSaveApplication(application!);
      }, [], false);
    }
    else {
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.ADD_APP_FLW, ReasonActionTypeEnum.UPDATE, () => {
        this.form.controls['applicationName'].markAsTouched();
        this.form.controls['appRegistryId'].markAsTouched();
        this.form.controls['applicationPath'].markAsTouched();
        this.form.controls['applicationType'].markAsTouched();
        this.form.controls['technology'].markAllAsTouched();
        this.form.controls['flowType'].markAllAsTouched();

        if (!this.isValid('applicationName') || !this.isValid('applicationPath') ||
          !this.isValid('applicationType') || !this.isValid('technology')) {
          return
        }

        const applicationId = uuidv4();

        application = {
          id: applicationId,
          applicationName: this.form.controls['applicationName'].value,
          flowType: this.form.controls['flowType'].value.key,
          appRegistryId: this.form.controls['appRegistryId'].value != undefined ? this.form.controls['appRegistryId'].value.id : "",
          fullPath: this.form.controls['applicationPath'].value,
          technology: this.form.controls['technology'].value.key,
          dataSourceId: this.form.controls['dataSource'].value != undefined ? this.form.controls['dataSource'].value.id : "",
          applicationType: this.form.controls['applicationType'].value.key,
          status: this.form.controls['isPublished'].value
        }

        // Add the applicationId
        this.windows.forEach((window) => {
          window.applicationId = applicationId;
        })

        this.tmpApplicationFlow = {
          id: uuidv4(),
          applicationId: applicationId,
          drawFlow: JSON.stringify(fullDrawFlow)
        }
        // save application
        this.onSaveApplication(application);

        if (this.applicationData?.status != undefined) {
          this.isPublishDisabled = false;
          if (this.applicationData.status) {
            this.publishText = 'flow.unpublished';
            this.publishIcon = "export";
            this.isFlowPublished = false;
          } else {
            this.publishText = 'flow.publish';
            this.publishIcon = "import";
            this.isFlowPublished = true;
          }
        }
        this.completeSaveApplication(application!);
      }, [], false);
    }
  }

  completeSaveApplication(application: ApplicationEntity) {
    // Hide application dialog
    this.showSaveApplicationDialog = false;

    // Application Title
    this.form.controls['applicationTitle'].setValue(application.applicationName);

    this.applicationData = application;

    this.savedWindows = JSON.parse(JSON.stringify(this.windows));

    this.originalApplicationFlow = this.tmpApplicationFlow;

    // Clean fields
    this.cleanFields();
  }

  addApplicationDataToDrawFlow(nodes: NodeInOut[], drawFlowData: any): any {
    let winCount = 0;
    nodes.forEach((node) => {
      let drawFlowNode = [...this.listNodesDrawFlow.filter((_v, k) => _v.id == node.nodeId)];

      if (drawFlowNode.length > 0) {
        let window = [...this.windows.filter((_v, k) => _v.windowOrder == drawFlowNode[0].window.windowOrder)];
        if (window.length > 0) {
          // window[0].windowOrder = drawFlowData.drawflow.Home.data[drawFlowNode[0].id].id;//winCount;
          this.sortedWindows.push(window[0]);
          drawFlowData.drawflow.Home.data[drawFlowNode[0].id].data = window[0];
          winCount += 1;
        }
      }
    });
    return drawFlowData;
  }

  onUpdateApplication(application: ApplicationEntity) {
    this.getApplicationByIdUseCase.execute({ id: application.id! }).then(
      (data) => {
        this.updateApplicationByIdUseCase.execute(application).then(
          () => {
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(application) },
              { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP, 0, 'SUCCESS', '', at_attributes);
          },
          (e) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: e.message,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(application) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP, 0, 'ERROR', '', at_attributes);
          }
        )
      },
      (error) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: error.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  onUpdateApplicationFlow(flow: ApplicationFlowEntity) {
    this.getApplicationFlowByAppIdUseCase.execute({ id: flow.applicationId! }).then(
      (data) => {
        this.updateApplicationFlowByIdUseCase.execute(flow).then(
          () => {
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(flow) },
              { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_FLW, 0, 'SUCCESS', '', at_attributes);
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('content.successTitle'),
              detail: this.translate.instant('messages.updated_successfully'),
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          },
          (e) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: this.translate.instant('messages.error_update') + ` ${flow.applicationId}. ` + this.translate.instant('content.description') + `: ${e.message}.`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(flow) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_FLW, 0, 'ERROR', '', at_attributes);
          }
        )
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  onSaveDrawFlow(applicationFlow: ApplicationFlowEntity) {
    this.addApplicationFlowUseCase.execute(applicationFlow).then(
      () => {
        // this.loggerService.debug("DrawFlow saved");
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(applicationFlow) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_FLW, 0, 'SUCCESS', '', at_attributes);
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(applicationFlow) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_FLW, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  onSaveApplication(application: ApplicationEntity) {
    this.addApplicationUseCase.execute([application]).then(
      () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(application) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP, 0, 'SUCCESS', '', at_attributes);
        // save draw flow
        this.onSaveDrawFlow(this.tmpApplicationFlow);
        // Add windows
        this.windows.forEach((window) => {
          let tmpWin = this.sortedWindows.find(w => w.id == window.id);
          /*if (!tmpWin) {
            window.windowOrder = -1;
          }*/
          this.onSaveWindow(window);
        });

        this.applicationSaved = true;

        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.saved_successfully'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(application) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP, 0, 'ERROR', '', at_attributes);
      },
    );
  }

  onSaveWindow(window: ApplicationWindowEntity) {
    this.addAppWindowUseCase.execute(window).then(
      () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(window) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_WIN, 0, 'SUCCESS', '', at_attributes);
        if (window.windowParameters && window.windowParameters.length > 0) {
          this.onSaveWindowParameter(window.windowParameters);
        }
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(window) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_WIN, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  onDeleteWindow(window: ApplicationWindowEntity) {
    this.deleteAppWindowByIdUseCase.execute({ id: window.id!, order: window.windowOrder! }).then(
      () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(window) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_APP_WIN, 0, 'SUCCESS', '', at_attributes);
        // Remove Window's parameters
        this.deleteWindowParamsByWindowIdUseCase.execute({windowId: window.id!, order: window.windowOrder!}).then(
          () => {

          },
          (e) =>{
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: this.translate.instant('messages.error_remove') + ` ${window.windowName}. ` + this.translate.instant('content.description') + `: ${e}.`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        )
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: this.translate.instant('messages.error_remove') + ` ${window.windowName}. ` + this.translate.instant('content.description') + `: ${e}.`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(window) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_APP_WIN, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  onUpdateWindow(window: ApplicationWindowEntity) {
    this.getAppWindowByIdUseCase.execute({ id: window.id!, windowOrder: window.windowOrder! }).then(
      (data) => {
        this.updateAppWindowByIdUseCase.execute(window).then(
          () => {
            // this.loggerService.debug("Update window");
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(window) },
              { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_WIN, 0, 'SUCCESS', '', at_attributes);
          },
          (e) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: this.translate.instant('messages.error_update') + ` ${window.windowName}.` + this.translate.instant('content.description') + `: ${e}.`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(window) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_WIN, 0, 'ERROR', '', at_attributes);
          }
        )
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  onSaveWindowParameter(windowParameters: WindowParametersEntity[]) {
    this.addWindowParamUseCase.execute(windowParameters).then(
      () => {
        // this.loggerService.debug("Save window parameter");
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(windowParameters) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_WIN_PARAM, 0, 'SUCCESS', '', at_attributes);
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(windowParameters) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_WIN_PARAM, 0, 'ERROR', '', at_attributes);
      },
    );
  }

  onDeleteWindowParameter(parameter: WindowParametersEntity) {
    this.deleteWindowParamByIdUseCase.execute({ id: parameter.id!! }).then(
      () => {
        //this.loggerService.debug("Parameter removed" + `${parameter.name}` )
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(parameter) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_APP_WIN_PARAM, 0, 'SUCCESS', '', at_attributes);
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(parameter) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_APP_WIN_PARAM, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  onUpdateWindowParameter(parameter: WindowParametersEntity) {
    this.getWindowParamByIdUseCase.execute({ id: parameter.id! }).then(
      (data) => {
        this.updateWindowParamByIdUseCase.execute(parameter).then(
          () => {
            // this.loggerService.debug("Parameter updated" + `${parameter.name}` )
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameter) },
              { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_WIN_PARAM, 0, 'SUCCESS', '', at_attributes);
          },
          (e) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: e.message,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameter) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP_WIN_PARAM, 0, 'ERROR', '', at_attributes);
          }
        );
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  sortWindowsNodes(drawFlowData: any): NodeInOut[] {
    let nodesOrder: NodeInOut[] = [];

    if (this.listNodesDrawFlow.length > 1) {
      this.listNodesDrawFlow.forEach((node) => {
        drawFlowData.drawflow.Home.data[node.id].inputs['input_1'].connections.forEach((input: any) => {
          let tmp: NodeInOut = {
            nodeId: node.id,
            input: Number(input.node),
            output: null,
            order: 0
          }
          nodesOrder.push(tmp);
        });

        drawFlowData.drawflow.Home.data[node.id].outputs['output_1'].connections.forEach((output: any) => {
          let tmp: NodeInOut = {
            nodeId: node.id,
            input: null,
            output: Number(output.node),
            order: 0
          }
          nodesOrder.push(tmp);
        });
      });

      let combinedNodeInOut: NodeInOut[] = [];

      nodesOrder.forEach((node) => {
        if (combinedNodeInOut.length > 0) {
          let nodeData = [...combinedNodeInOut.filter((_v, k) => _v.nodeId == node.nodeId)];
          if (nodeData.length > 0) {
            combinedNodeInOut.forEach((pos) => {
              if (pos.nodeId == node.nodeId) {
                if (pos.input == null) {
                  pos.input = node.input;
                }

                if (pos.output == null) {
                  pos.output = node.output;
                }
              }
            })
          }
          else {
            combinedNodeInOut.push(node);
          }
        } else {
          combinedNodeInOut.push(node);
        }
      });

      // Filter nodes with null input and output
      const firstNodes = combinedNodeInOut.filter(node => node.input === null);
      const lastNodes = combinedNodeInOut.filter(node => node.output === null);

      // Remove the first and last nodes from the original list
      let sortedNodes = combinedNodeInOut.filter(node => node.input !== null && node.output !== null);

      // Sort nodes based on input and output
      sortedNodes.sort((a, b) => {
        if (a.input === b.output) {
          return -1;
        } else if (a.output === b.input) {
          return 1;
        } else {
          return 0;
        }
      });

      // Concatenate first nodes, sorted nodes, and last nodes
      sortedNodes = firstNodes.concat(sortedNodes).concat(lastNodes);

      return sortedNodes;
    }

    let tmp: NodeInOut = {
      nodeId: this.listNodesDrawFlow[0].id,
      input: null,
      output: null,
      order: 0
    }

    nodesOrder.push(tmp);

    return nodesOrder
  }

  onReturn() {
    this.return.emit(true);
  }

  // Updates the Node's components
  updateComponentDiv(attributes: WindowParametersEntity[]) {
    let element = this.document.getElementById(this.selectedNodeId);
    let attributeTitle = this.document.getElementById(`componentAttributeTitle-${this.selectedNodeId}`);

    let header = this.document.getElementById(`${this.selectedNodeId}-attribute-header`);

    element.innerHTML = "";
    attributeTitle.innerHTML

    if (!header) {
      attributeTitle.classList.add("attribute-header");
      attributeTitle.innerHTML = `<div class="divider" id="${this.selectedNodeId}-attribute-header">
                    <p><label class="label-div"> ${this.translate.instant("pass_application.window_components")} </label></p>
                </div>`
    }

    if (attributes.length == 0) {
      attributeTitle.classList.remove("attribute-header");
      attributeTitle.innerHTML = "";
    }

    attributes.forEach((item) => {
      var newDiv = this.document.createElement("div");
      newDiv.innerHTML = `<div class="attribute-box">
          <label id="${item.id}-${item.windowOrder}-component-display-name">${item.displayName != undefined ? item.displayName : ""}
          </label>
        </div>`;
      element.appendChild(newDiv);
    });
  }

  changeAppStatus() {
    if (this.applicationData?.status != undefined) {
      this.applicationData.status = !this.applicationData?.status!;
      if (this.applicationData.status) {
        this.publishText = 'flow.unpublished';
        this.publishIcon = "export";
        this.isFlowPublished = false;
      } else {
        this.publishText = 'flow.publish';
        this.publishIcon = "import";
        this.isFlowPublished = true;
      }
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.MOD_APP, ReasonActionTypeEnum.UPDATE, () => {
        this.onUpdateApplication(this.applicationData!);
      }, [], false);
    }
  }

  onComboboxChange(event: any) {
    const id = event.originalEvent['target'].id;
    const filteredId = id.split('_')[0];
    const element = document.getElementById(filteredId);
    if (element) {
      const spanWithAriaLabel = element.querySelector('span.p-dropdown-label');
      if (spanWithAriaLabel) {
        spanWithAriaLabel.textContent = event.value.value;
      }
    }
  }

  private clearRejectTimeout() {
    if (this.rejectTimeout) {
      clearTimeout(this.rejectTimeout);
    }
    this.rejectTimeout = null;
  }
}
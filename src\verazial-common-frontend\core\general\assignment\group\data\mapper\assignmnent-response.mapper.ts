import { AssignmentResponseGrpModel } from "src/verazial-common-frontend/core/generated/assignment/assignment_pb"
import { Mapper } from "src/verazial-common-frontend/core/mapper"
import { AssignmentResponseEntity } from "../../domain/entity/assignment-response.entity"
import { AttributeType } from "src/verazial-common-frontend/core/general/flow/common/enums/attribute-type.enum"
import { GroupCategoryType } from "../../../categories/common/models/group-category-type.enum"
import { TaskFlowEntity } from "src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity"
import { GroupCategoryEntity } from "../../../categories/domain/entity/group-category.entity"
import { DateModel } from "../../../categories/common/models/date.model"
import { Date } from "src/verazial-common-frontend/core/generated/util_pb"
import { TaskFlowMapper } from "src/verazial-common-frontend/core/general/flow/data/mapper/task-flow.mapper"
import { GroupCategoryMapper } from "../../../categories/data/mapper/group-category.mapper"

export class AssignmentResponseMapper extends Mapper<AssignmentResponseGrpModel, AssignmentResponseEntity> {

    taskFlowMapper = new TaskFlowMapper();
    groupCategoryMapper = new GroupCategoryMapper();

    override mapFrom(param: AssignmentResponseGrpModel): AssignmentResponseEntity {
        return {
            id: param.getId(),
            name: param.getName(),
            description: param.getDescription(),
            flows: param.getFlows()?.getTaskflowsList().map((flow) => {
                return this.taskFlowMapper.mapFrom(flow);
            }),
            locations: param.getLocations()?.getCategoriesList().map((category) => {
                return this.groupCategoryMapper.mapFrom(category);
            }),
            subjects: param.getSubjects()?.getCategoriesList().map((category) => {
                return this.groupCategoryMapper.mapFrom(category);
            }),
            schedules: param.getSchedules()?.getCategoriesList().map((category) => {
                return this.groupCategoryMapper.mapFrom(category);
            }),
            isRequiredWithinSchedule: param.getIsrequiredwithinschedule(),
            requiredSchedule: param.getRequiredschedule()?.getCategoriesList().map((category) => {
                return this.groupCategoryMapper.mapFrom(category);
            }),
            alertIfAllPerformedWithinSchedule: param.getAlertifallperformedwithinschedule(),
            alertIfNotPerformedWithinSchedule: param.getAlertifnotperformedwithinschedule(),
            alertIfPerformedOutsideSchedule: param.getAlertifperformedoutsideschedule(),
            usersToAlert: param.getUserstoalert()?.getCategoriesList().map((category) => {
                return this.groupCategoryMapper.mapFrom(category);
            }),
        }
    }

    getActionAttributesType(type: number): AttributeType {
        switch (type) {
            case 0:
                return AttributeType.INPUT;
            case 1:
                return AttributeType.DROPDOWN
            case 2:
                return AttributeType.TOGGLE
            case 3:
                return AttributeType.BUTTON
            case 4:
                return AttributeType.MESSAGE
            default:
                throw new Error("Invalid attribute type");
        }
    }

    getGroupCategoryType(type: number): GroupCategoryType {
        switch (type) {
            case 0:
                return GroupCategoryType.USERS;
            case 1:
                return GroupCategoryType.LOCATIONS;
            case 2:
                return GroupCategoryType.SCHEDULES;
            default:
                throw new Error("Invalid group category type");
        }
    }

    getGroupCategoryScheduleDateModel(date: Date): DateModel {
        return {
            year: date.getYear(),
            month: date.getMonth(),
            day: date.getDay(),
            toDate: () => { return null },
        }
    }

    override mapTo(param: AssignmentResponseEntity): AssignmentResponseGrpModel {
        throw new Error("Method not implemented.");
    }
}
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { AccessEntity } from "../../domain/entity/access.entity";
import { AccessRepository } from "../../domain/repository/access.repository";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { AccessMapper } from "../mapper/access.mapper";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { CoreAccessServiceClient } from "src/verazial-common-frontend/core/generated/access/AccessServiceClientPb";
import { AccessGrpcModel } from "src/verazial-common-frontend/core/generated/access/access_pb";
import { IntParam } from "src/verazial-common-frontend/core/generated/util_pb";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { HttpClient } from "@angular/common/http";

@Injectable({
    providedIn: 'root',
})
export class AccessRepositoryImpl extends AccessRepository {

    accessMapper = new AccessMapper();

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    /**
     * Add a new access
     * @param params access: AccessEntity;
     * @returns AccessEntity
     */
    override addAccess(params: { access: AccessEntity; }): Promise<AccessEntity> {
        let request = this.accessMapper.mapTo(params.access);

        let coreAccessServiceClient = new CoreAccessServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.addAccess(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.accessMapper.mapFrom(response.getAccess()!));
                    }
                }
            });
        });
    }

    /**
     * Get all accesses
     * @returns list of AccessEntity
     */
    override getAllAccesses(): Promise<AccessEntity[]> {
        let accesses: AccessEntity[] = [];

        let coreAccessServiceClient = new CoreAccessServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreAccessServiceClient.getAllAccesses(new Empty);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: AccessGrpcModel) => {
                accesses.push(this.accessMapper.mapFrom(response));

            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(accesses)
            });
        });
    }

    /**
     * Ge the access by ID
     * @param params id: number;
     * @returns AccessEntity
     */
    override getAccessById(params: { id: number; }): Promise<AccessEntity> {

        let request = new IntParam();
        request.setParameter(params.id);

        let coreAccessServiceClient = new CoreAccessServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.getAccessById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.accessMapper.mapFrom(response.getAccess()!));
                    }
                }
            });
        });
    }

    /**
     * Delete an access by Access ID
     * @param params { id: number; }
     * @returns SuccessResponse
     */
    override deleteAccessById(params: { id: number; }): Promise<SuccessResponse> {

        let request = new IntParam();
        request.setParameter(params.id)

        let success!: SuccessResponse;

        let coreAccessServiceClient = new CoreAccessServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.deleteAccessById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Update the access
     * @param params 
     * @returns AccessEntity
     */
    override updateAccessById(params: { access: AccessEntity; }): Promise<AccessEntity> {

        let request = this.accessMapper.mapTo(params.access);

        let coreAccessServiceClient = new CoreAccessServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreAccessServiceClient.updateAccessById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.accessMapper.mapFrom(response.getAccess()!));
                    }
                }
            });
        });
    }
}
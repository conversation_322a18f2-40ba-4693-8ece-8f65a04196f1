import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { GroupPageRoutingModule } from './group-page-routing.module';
import { GroupPageComponent } from './group-page/group-page.component';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { ToastModule } from 'primeng/toast';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { ListGroupsModule } from '../../components/list-groups/list-groups.module';
import { NewGroupModule } from '../../components/new-group/new-group.module';

@NgModule({
  declarations: [
    GroupPageComponent
  ],
  imports: [
    CommonModule,
    GroupPageRoutingModule,
    EmptyModule,
    TranslateModule,
    NewGroupModule,
    ListGroupsModule,
    ToastModule,
    LoadingSpinnerModule,
    /* PrimeNG modules */
    DialogModule,
    ButtonModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    GroupPageComponent
  ]
})
export class GroupPageModule { }

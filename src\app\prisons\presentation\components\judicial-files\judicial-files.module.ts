import { NgModule } from "@angular/core";
import { JudicialFilesListComponent } from "./judicial-files-list/judicial-files-list.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { InputTextareaModule } from "primeng/inputtextarea";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { CalendarModule } from "primeng/calendar";
import { AccordionModule } from "primeng/accordion";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";

@NgModule({
    declarations: [
      JudicialFilesListComponent,
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      InputTextareaModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      CalendarModule,
      AccordionModule,
      ScrollPanelModule,
      /* Custom Modules */
      EmptyModule,
    ],
    exports: [
      JudicialFilesListComponent,
    ]
  })
  export class JudicialFilesModule { }
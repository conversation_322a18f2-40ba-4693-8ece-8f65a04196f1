import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BiographicRepository } from "../domain/repository/biographic.repository";
import { GetActualTimeUseCase } from "../domain/use-cases/get-actual-time.use-case";
import { BiographicRepositoryImpl } from "./repository-impl/biographic-impl.repository";

const getActualtimeUseCaseFactory =
    (biographicRepository: BiographicRepository) => new GetActualTimeUseCase(biographicRepository);

export const getActualTimeUseCaseProvider = {
    provide: GetActualTimeUseCase,
    useFactory: getActualtimeUseCaseFactory,
    deps: [BiographicRepository]
}

@NgModule({
    providers: [
        getActualTimeUseCaseProvider,
        { provide: BiographicRepository, useClass: BiographicRepositoryImpl }
    ],
    imports: [
        CommonModule,
    ]
})
export class DiBiographicModule { }
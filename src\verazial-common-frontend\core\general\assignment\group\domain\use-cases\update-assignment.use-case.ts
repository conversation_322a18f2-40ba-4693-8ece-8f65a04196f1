import { AssignmentRespository } from "../repository/assignment.repository";
import { AssignmentEntity } from "../entity/assignment.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateAssignmentUseCase implements UseCaseGrpc<{ assignment: AssignmentEntity }, AssignmentEntity> {
    constructor(private repository: AssignmentRespository) { }
    execute(params: { assignment: AssignmentEntity; }): Promise<AssignmentEntity> {
        return this.repository.updateAssignment(params);
    }
}
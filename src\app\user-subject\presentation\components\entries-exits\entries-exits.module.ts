import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputTextModule } from "primeng/inputtext";
import { InputIconModule } from "primeng/inputicon";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { CalendarModule } from "primeng/calendar";
import { TreeSelectModule } from 'primeng/treeselect';
import { TooltipModule } from "primeng/tooltip";
import { EntriesExitsComponent } from "./entries-exits/entries-exits.component";
import { RoleSelectionModule } from "src/verazial-common-frontend/modules/shared/components/role-selection/role-selection.module";
import { StepsModule } from "primeng/steps";
import { SelectButtonModule } from "primeng/selectbutton";
import { AccordionModule } from "primeng/accordion";
import { BioTechButtonsModule } from "src/verazial-common-frontend/modules/shared/components/bio-tech-buttons/bio-tech-buttons.module";
import { WidgetMatchModule } from "src/verazial-common-frontend/modules/shared/components/widget-match/widget-match.module";
import { WidgetSearchModule } from "src/verazial-common-frontend/modules/shared/components/widget-search/widget-search.module";
import { TimeLeftModule } from "src/verazial-common-frontend/modules/shared/components/time-left/time-left.module";
import { BioSignaturesModule } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures.module";
import { ToastModule } from "primeng/toast";
import { InputTextareaModule } from "primeng/inputtextarea";
import { ScrollPanelModule } from "primeng/scrollpanel";

@NgModule({
    declarations: [
      EntriesExitsComponent
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      ProgressSpinnerModule,
      CalendarModule,
      TreeSelectModule,
      TooltipModule,
      RoleSelectionModule,
      StepsModule,
      SelectButtonModule,
      AccordionModule,
      ToastModule,
      ScrollPanelModule,

      /* Custom Modules */
      BioTechButtonsModule,
      WidgetMatchModule,
      WidgetSearchModule,
      TimeLeftModule,
      BioSignaturesModule
    ],
    exports: [
      EntriesExitsComponent
    ]
  })
  export class EntriesExitsModule { }
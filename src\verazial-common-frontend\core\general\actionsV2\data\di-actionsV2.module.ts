import { NgModule } from "@angular/core";
import { ActionsV2Repository } from "../domain/repository/actionsV2.repository";
import { CountActionsUseCase } from "../domain/use-cases/count-actions.use-case";
import { RegisterActionUseCase } from "../domain/use-cases/register-action.use-case";
import { SearchActionsUseCase } from "../domain/use-cases/search-actions.use-case";
import { CommonModule } from "@angular/common";
import { ActionsV2RepositoryImpl } from "./repository-impl/actionsv2-impl.repository";

const registerActionUseCaseFactory =
(ActionsV2Repository: ActionsV2Repository) => new RegisterActionUseCase(ActionsV2Repository);

const searchActionsUseCaseFactory =
(ActionsV2Repository: ActionsV2Repository) => new SearchActionsUseCase(ActionsV2Repository);

const countActionsUseCaseFactory =
(ActionsV2Repository: ActionsV2Repository) => new CountActionsUseCase(ActionsV2Repository);

export const registerActionUseCaseProvider = {
    provide: RegisterActionUseCase,
    useFactory: registerActionUseCaseFactory,
    deps: [ActionsV2Repository]
}

export const searchActionsUseCaseProvider = {
    provide: SearchActionsUseCase,
    useFactory: searchActionsUseCaseFactory,
    deps: [ActionsV2Repository]
}

export const countActionsUseCaseProvider = {
    provide: CountActionsUseCase,
    useFactory: countActionsUseCaseFactory,
    deps: [ActionsV2Repository]
}

@NgModule({
    providers:[
        registerActionUseCaseProvider,
        searchActionsUseCaseProvider,
        countActionsUseCaseProvider,
        { provide: ActionsV2Repository, useClass: ActionsV2RepositoryImpl }
    ],
    imports: [
        CommonModule
    ]
})
export class DiActionsV2Module {}
<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<div [formGroup]="form" class="dialog-container">
    <div class="flex justify-content-center">
        <p-selectButton
            [options]="stepOptions"
            formControlName="stepOptions"
            severity="secondary"
            multiple="false"
            allowEmpty="false"
            optionLabel="key"
            optionValue="value"
            dataKey="value"
            (onChange)="onActiveTabIndexChange($event)">
        </p-selectButton>
    </div>
    <div class="mt-5" *ngIf="transferAuth?.status === AuthStatus.AUTHORIZED">
        <p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh', overflowY: 'auto' }">
            @switch (activeIndex) {
                @case (0) {
                    <div class="signatures-container">
                        <div *ngFor="let prisoner of transferAuth?.listOfPrisoners" class="signature-item">
                            <app-bio-signatures
                                [showSignatureButton]="!hasAuthSignaturePrisioner(prisoner)"
                                [showExtraFormFields]="false"
                                [showSignatureInfo]="hasAuthSignaturePrisioner(prisoner)"
                                [signatureTitle]="'content.transferExitSignature'"
                                [signatureInputLabel]="'content.transferSubject'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="getSignatureData(prisoner)"
                                [userIsVerified]="userIsVerified"
                                [userNumId]="prisoner.userId"
                                [userName]="getSubjectNames(prisoner.userId)"
                                (outputResult)="userAuthSignatureResult($event, prisoner)"
                            ></app-bio-signatures>
                        </div>
                    </div>
                }
                @case (1) {
                    <div class="signatures-container">
                        <div *ngFor="let responsible of transferAuth?.listOfResponsibleUsers" class="signature-item">
                            <app-bio-signatures
                                [showSignatureButton]="!hasAuthSignaturePrisioner(responsible)"
                                [showExtraFormFields]="false"
                                [showSignatureInfo]="hasAuthSignaturePrisioner(responsible)"
                                [signatureTitle]="'content.transferExitSignature'"
                                [signatureInputLabel]="'content.transferResponsible'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="getSignatureData(responsible)"
                                [userIsVerified]="userIsVerified"
                                [userNumId]="responsible.userId"
                                [userName]="getSubjectNames(responsible.userId)"
                                (outputResult)="userAuthSignatureResult($event, responsible)"
                            ></app-bio-signatures>
                        </div>
                    </div>
                }
            }
        </p-scrollPanel>
    </div>
    <div class="mt-5" *ngIf="transferAuth?.status === AuthStatus.IN_PROGRESS">
        <p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh', overflowY: 'auto' }">
            @switch (activeIndex) {
                @case (0) {
                    <div class="signatures-container">
                        <div *ngFor="let prisoner of transferAuth?.listOfPrisoners" class="signature-item">
                            <app-bio-signatures
                                [showSignatureButton]="!hasAuthSignaturePrisioner(prisoner)"
                                [showExtraFormFields]="false"
                                [showSignatureInfo]="hasAuthSignaturePrisioner(prisoner)"
                                [signatureTitle]="'content.transferEntrySignature'"
                                [signatureInputLabel]="'content.transferSubject'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="getSignatureData(prisoner)"
                                [userIsVerified]="userIsVerified"
                                [userNumId]="prisoner.userId"
                                [userName]="getSubjectNames(prisoner.userId)"
                                (outputResult)="userAuthSignatureResult($event, prisoner)"
                            ></app-bio-signatures>
                        </div>
                    </div>
                }
                @case (1) {
                    <div class="signatures-container">
                        <div *ngFor="let responsible of transferAuth?.listOfResponsibleUsers" class="signature-item">
                            <app-bio-signatures
                                [showSignatureButton]="!hasAuthSignaturePrisioner(responsible)"
                                [showExtraFormFields]="false"
                                [showSignatureInfo]="hasAuthSignaturePrisioner(responsible)"
                                [signatureTitle]="'content.transferEntrySignature'"
                                [signatureInputLabel]="'content.transferResponsible'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="getSignatureData(responsible)"
                                [userIsVerified]="userIsVerified"
                                [userNumId]="responsible.userId"
                                [userName]="getSubjectNames(responsible.userId)"
                                (outputResult)="userAuthSignatureResult($event, responsible)"
                            ></app-bio-signatures>
                        </div>
                    </div>
                }
            }
        </p-scrollPanel>
    </div>
</div>


<div>
    <div class="footer-buttons-container">

        <p-button label="{{ 'cancel' | translate }}"
            (onClick)="onClose()"
            [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
        </p-button>

        <p-button
            [disabled]="!canReadAndWrite || !userIsVerified || !canUpdate"
            [label]="createUpdateButtonTitle"  iconPos="right"
            [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
            (onClick)="saveTransferAuth()">
        </p-button>

    </div>
</div>

<ng-template #mustVerifyUser>
    <div style="width: 200px;" class="signature-container flex flex-column justify-content-center align-items-center">
        {{ 'messages.verification_required_data' | translate }}
    </div>
</ng-template>
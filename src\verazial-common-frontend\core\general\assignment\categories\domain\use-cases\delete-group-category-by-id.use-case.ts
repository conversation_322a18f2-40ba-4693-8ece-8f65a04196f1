import { GroupCategoryRepository } from "../repository/group-category.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export class DeleteGroupCategoryByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.groupCategoryRepository.deleteGroupCategoryById(params);
    }
}

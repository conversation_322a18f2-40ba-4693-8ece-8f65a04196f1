import { DataSourceRepository } from "../repositories/data-source.repository";
import { DataSourceEntity } from "../entities/data-source.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetAppDataSourceByNameUseCase implements UseCaseGrpc<{ name: string }, DataSourceEntity> {
    constructor(private dataSourceRepository: DataSourceRepository) { }
    execute(params: { name: string; }): Promise<DataSourceEntity> {
        return this.dataSourceRepository.getAppDataSourceByName(params);
    }
}
import { PassListEvents } from "src/verazial-common-frontend/core/models/pass-list-events.enum";

export interface WindowParametersEntity {
    id?: string;
    applicationWindowId?: string;
    displayName?: string;
    name?: string;
    windowOrder?: number;
    attributeName?: string;
    uiComponentType?: string;
    componentPosition?: number;
    triggerOrder?: number;
    event?: PassListEvents;
    showComponentToUser?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
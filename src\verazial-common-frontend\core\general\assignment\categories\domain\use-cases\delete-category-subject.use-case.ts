import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CategorySubjectEntity } from "../entity/category-subject.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class DeleteCategorySubjectUseCase implements UseCaseGrpc<{ listOfSubjects: CategorySubjectEntity[] }, SuccessResponse> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { listOfSubjects: CategorySubjectEntity[]; }): Promise<SuccessResponse> {
        return this.groupCategoryRepository.deleteCategorySubject(params);
    }
}
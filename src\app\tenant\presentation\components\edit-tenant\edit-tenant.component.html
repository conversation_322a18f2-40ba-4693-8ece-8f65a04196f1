<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<div class="flex flex-column" [formGroup]="form">
    @if( operationType == opType.INSERT ){
        <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" (activeIndexChange)="onActiveIndexChange($event)"></p-steps>
    }

    <div class="mt-3">
        <p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh' }">
            @switch (activeIndex) {
                @case(0) {
                    <div class="flex justify-content-end requiredFieldsLabel">
                        {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                    </div>
                    <div class="grid">
                        <div *ngIf="showTenantId && tenantData?.id" class="col-12 flex flex-column">
                            <label class="label-form" for="id">{{ 'content.id' | translate }}</label>
                            <input
                                type="text"
                                pInputText
                                [(ngModel)]="tenantData!.id"
                                [ngModelOptions]="{standalone: true}"
                                [disabled]="true"
                            />
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="names">{{ 'tenant.name' | translate }} <span *ngIf="isRequiredField('name')" class="requiredStar">*</span></label>
                            <input
                                type="text"
                                pInputText
                                formControlName="name"
                                (ngModelChange)="trackDataChange()"
                                [class.ng-dirty]="!isValid('name') && form.controls['name'].touched"
                                styleClass="w-full"
                            />
                            <small *ngIf="!isValid('name') && form.controls['name'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="nif">{{ 'tenant.nif' | translate }} <span *ngIf="isRequiredField('nif')" class="requiredStar">*</span></label>
                            <input
                                type="text"
                                pInputText
                                formControlName="nif"
                                (ngModelChange)="trackDataChange()"
                                [class.ng-dirty]="!isValid('nif') && form.controls['nif'].touched"
                                styleClass="w-full"
                                maxlength="8"
                                (keypress)="validateInput($event)"
                            />
                            <small *ngIf="!isValid('nif') && form.controls['nif'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 flex flex-column">
                            <label class="label-form" for="email">{{ 'tenant.email' | translate }} <span *ngIf="isRequiredField('email')" class="requiredStar">*</span></label>
                            <input
                                type="text"
                                pInputText
                                formControlName="email"
                                (ngModelChange)="trackDataChange()"
                                [class.ng-dirty]="!isValid('email') && form.controls['email'].touched"
                                styleClass="w-full"
                            />
                            <small *ngIf="!isValid('email') && form.controls['email'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="country">{{ 'tenant.country' | translate }} <span *ngIf="isRequiredField('country')" class="requiredStar">*</span></label>
                            <p-dropdown
                                appendTo="body"
                                [options]="countries"
                                [filter]="true"
                                filterBy="name"
                                [showClear]="true"
                                formControlName="country"
                                [(ngModel)]="selectedCountry"
                                optionLabel="name"
                                (onChange)="getCityByCountry($event)"
                                placeholder="{{ 'content.select' | translate }}"
                                styleClass="w-full"
                            />
                            <small *ngIf="!isValid('country') && form.controls['country'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="city">{{ 'tenant.city' | translate }} <span *ngIf="isRequiredField('city')" class="requiredStar">*</span></label>
                            <p-dropdown
                                appendTo="body"
                                [options]="cities"
                                [filter]="true"
                                filterBy="key"
                                [showClear]="true"
                                formControlName="city"
                                [(ngModel)]="selectedCity"
                                optionLabel="value"
                                (onChange)="trackDataChange()"
                                placeholder="{{ 'content.select' | translate }}"
                                styleClass="w-full"
                            />
                            <small *ngIf="!isValid('city') && form.controls['city'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div> -->
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="status">{{ 'tenant.status' | translate }} <span *ngIf="isRequiredField('status')" class="requiredStar">*</span></label>
                            <p-dropdown
                                appendTo="body"
                                [options]="tenantStatus"
                                formControlName="status"
                                [(ngModel)]="selectedStatus"
                                optionLabel="value"
                                (onChange)="trackDataChange()"
                                placeholder="{{ 'content.select' | translate }}"
                                styleClass="w-full"
                            />
                            <small *ngIf="!isValid('city') && form.controls['city'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-row align-content-center align-items-center gap-3">
                            <label for="isActive">{{'tenant.is_active' | translate }}</label>
                            <p-inputSwitch (onChange)="trackDataChange()" formControlName="isActive" [(ngModel)]="isTenantActive"/>
                        </div>
                    </div>
                }
                @case(1) {
                    <div class="grid w-full">
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="dbEngine">{{ 'tenant.engine' | translate }} <span *ngIf="isRequiredField('dbEngine')" class="requiredStar">*</span></label>
                            <input type="text" pInputText formControlName="dbEngine" (ngModelChange)="trackDataChange()" />
                            <small *ngIf="!isValid('dbEngine') && form.controls['dbEngine'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="dbHost">{{ 'tenant.host' | translate }} <span *ngIf="isRequiredField('dbHost')" class="requiredStar">*</span></label>
                            <input type="text" pInputText formControlName="dbHost" (ngModelChange)="trackDataChange()"/>
                            <small *ngIf="!isValid('dbHost') && form.controls['dbHost'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="dbPort">{{ 'tenant.port' | translate }} <span *ngIf="isRequiredField('dbPort')" class="requiredStar">*</span></label>
                            <!-- <input type="text" pInputText formControlName="dbPort" /> -->
                            <p-inputNumber inputId="dbPort" formControlName="dbPort" [useGrouping]="false" appendTo="body" inputStyleClass="w-full lg:w-12rem md:w-12rem" styleClass="p-inputwrapper" (ngModelChange)="trackDataChange()"/>
                            <small *ngIf="!isValid('dbPort') && form.controls['dbPort'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="dbDatabase">{{ 'tenant.database' | translate }} <span *ngIf="isRequiredField('dbDatabase')" class="requiredStar">*</span></label>
                            <input type="text" pInputText formControlName="dbDatabase" (ngModelChange)="trackDataChange()"/>
                            <small *ngIf="!isValid('dbDatabase') && form.controls['dbDatabase'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="dbUsername">{{ 'tenant.username' | translate }} <span *ngIf="isRequiredField('dbUsername')" class="requiredStar">*</span></label>
                            <input type="text" pInputText formControlName="dbUsername" (ngModelChange)="trackDataChange()"/>
                            <small *ngIf="!isValid('dbUsername') && form.controls['dbUsername'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <div class="col-12 md:col-6 lg:col-6 flex flex-column">
                            <label class="label-form" for="dbPassword">{{ 'tenant.password' | translate }} <span *ngIf="isRequiredField('dbPassword')" class="requiredStar">*</span></label>
                            <p-password formControlName="dbPassword" [feedback]="false" [toggleMask]="true" appendTo="body" inputStyleClass="w-full lg:w-12rem md:w-12rem" styleClass="p-inputwrapper" (ngModelChange)="trackDataChange()"/>
                            <small *ngIf="!isValid('dbPassword') && form.controls['dbPassword'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>
                }
            }
        </p-scrollPanel>
    </div>
    <div>
        <div class="footer-buttons-container">
            @if (activeIndex==0) {
                <p-button label="{{ 'cancel' | translate }}"
                    (onClick)="onCancel()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
            }
            @else {
                <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left"
                    (onClick)="onBack()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                    </p-button>
            }
            @if (activeIndex!=(items?.length-1) && operationType == opType.INSERT) {
                <p-button label="{{ 'next' | translate }}" icon="pi pi-angle-right" iconPos="right"
                    (onClick)="onNext()"
                    [disabled]="!nextSaveEnabled"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
            }@else{
                <p-button
                    [disabled]="!nextSaveEnabled"
                    label="{{ 'save' | translate }}"  iconPos="right"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                    (onClick)="onSave()">
                </p-button>
            }
        </div>
    </div>
</div>

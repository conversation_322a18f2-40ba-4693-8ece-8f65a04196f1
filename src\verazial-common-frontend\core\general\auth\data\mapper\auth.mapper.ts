
import { TokenResponse } from "src/verazial-common-frontend/core/generated/auth/auth_pb";
import { AuthEntity } from "../../domain/entity/auth.entity";
import { Mapper } from "src/verazial-common-frontend/core/mapper";

export class AuthMapper extends Mapper<TokenResponse, AuthEntity> {
    override mapFrom(param: TokenResponse): AuthEntity {
        return {
            token: param.getToken()
        }
    }
    override mapTo(param: AuthEntity): TokenResponse {
        let auth = new TokenResponse()
        auth.setToken(param.token!!);
        return auth;
    }
}
import { ApplicationWindowEntity } from "../../domain/entities/application-window.entity";
import { ApplicationWindowRepository } from "../../domain/repositories/application-window.repository";
import { ApplicationWindowGrpcMapper } from "../mapper/application-window-grpc.mapper";
import { environment } from "src/environments/environment";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ApplicationWindowRequest, ApplicationWindowGrpcModel } from "src/verazial-common-frontend/core/generated/application/window_pb";
import { CoreApplicationWindowClient } from "src/verazial-common-frontend/core/generated/application/WindowServiceClientPb";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { IdOrder } from "src/verazial-common-frontend/core/generated/util_pb";

@Injectable({
    providedIn: 'root',
})
export class ApplicationWindowRepositoryGrpcImp extends ApplicationWindowRepository {

    applicationWindowGrpcMapper = new ApplicationWindowGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }

    /** Add a new application window */
    override addAppWindow(applicationWindow: ApplicationWindowEntity): Promise<ApplicationWindowEntity> {
        let request = this.applicationWindowGrpcMapper.mapTo(applicationWindow);

        let coreApplicationWindowClient = new CoreApplicationWindowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreApplicationWindowClient.addAppWindow(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationWindowGrpcMapper.mapFrom(
                            response.getApplicationwindowmodel()!
                        ));
                    }
                }
            });
        });
    }

    /** Update Application Window by ID */
    override updateAppWindowById(applicationWindow: ApplicationWindowEntity): Promise<SuccessResponse> {

        let request = this.applicationWindowGrpcMapper.mapTo(applicationWindow);

        let success!: SuccessResponse;

        let coreApplicationWindowClient = new CoreApplicationWindowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreApplicationWindowClient.updateAppWindowById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /** Get Applications by Window ID */
    override getAppWindowById(params: { id: string, windowOrder: number }): Promise<ApplicationWindowEntity> {
        
        console.log(params);

        let request = new IdOrder()

        request.setWindowid(params.id);
        request.setWindoworder(params.windowOrder);

        let coreApplicationWindowClient = new CoreApplicationWindowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreApplicationWindowClient.getAppWindowById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationWindowGrpcMapper.mapFrom(
                            response.getApplicationwindowmodel()!
                        ));
                    }
                }
            });
        });
    }

    /** Get application windows by Application ID */
    override getAppWindowByAppId(params: { appId: string; }): Promise<ApplicationWindowEntity[]> {

        let request = new ApplicationWindowRequest();

        request.setValue(params.appId);

        let coreApplicationWindowClient = new CoreApplicationWindowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        let grpc = coreApplicationWindowClient.getAppWindowByAppId(request, metadata);

        let applicationWindowResponse: ApplicationWindowEntity[] = [];


        return new Promise((resolve, reject) => {

            grpc.on('data', (response: ApplicationWindowGrpcModel) => {
                applicationWindowResponse.push(
                    this.applicationWindowGrpcMapper.mapFrom(response)
                );
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(applicationWindowResponse);
            });
        });
    }

    override deleteAppWindowById(params: { id: string, order: number }): Promise<SuccessResponse> {
        let request = new IdOrder()

        request.setWindowid(params.id);
        request.setWindoworder(params.order);

        let success!: SuccessResponse;

        let coreApplicationWindowClient = new CoreApplicationWindowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationWindowClient.deleteAppWindowById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

}
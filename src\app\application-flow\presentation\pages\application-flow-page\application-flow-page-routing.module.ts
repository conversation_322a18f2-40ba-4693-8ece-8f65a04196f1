import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ApplicationFlowPageComponent } from './application-flow-page/application-flow-page.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';

const routes: Routes = [
  {
    path: '', 
    component: ApplicationFlowPageComponent,
    canActivate: [AuthGuard, NavigationGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ApplicationFlowPageRoutingModule { }

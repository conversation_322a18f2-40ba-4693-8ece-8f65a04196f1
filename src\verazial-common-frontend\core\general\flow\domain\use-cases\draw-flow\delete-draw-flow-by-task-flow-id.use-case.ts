import { DrawFlowRepository } from "../../repository/draw-flow.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export class DeleteDrawFlowByTaskFlowIdUseCase implements UseCaseGrpc<{ taskFlowId: string }, SuccessResponse> {
    constructor(private drawFlowRepository: DrawFlowRepository) { }
    execute(params: { taskFlowId: string; }): Promise<SuccessResponse> {
        return this.drawFlowRepository.deleteDrawFlowByTaskFlowId(params);
    }
}
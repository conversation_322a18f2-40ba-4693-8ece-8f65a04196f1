import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AdminGroupRoutingModule } from './admin-group-routing.module';
import { AdminGroupComponent } from './admin-group/admin-group.component';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { GroupInfoModule } from '../../components/group-info/group-info.module';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { StepsModule } from 'primeng/steps';
import { FlowAssignmentModule } from '../../components/flow-assignment/flow-assignment.module';
import { CategoryAssignmentModule } from '../../components/category-assignment/category-assignment.module';
import { ListAssignmentsModule } from '../../components/list-assignments/list-assignments.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';


@NgModule({
  declarations: [
    AdminGroupComponent
  ],
  imports: [
    CommonModule,
    AdminGroupRoutingModule,
    EmptyModule,
    LoadingSpinnerModule,
    GroupInfoModule,
    FlowAssignmentModule,
    CategoryAssignmentModule,
    TranslateModule,
    ListAssignmentsModule,
    ConfirmDialogModule,
    /* PrimeNG modules */
    DialogModule,
    ButtonModule,
    StepsModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    AdminGroupComponent
  ]
})
export class AdminGroupModule { }

import { ActionAttributeGrpc, ActionDataGrpc, ActionGrpc, AttributeDataGrpc, AttributeTypeGrpc, ListOfActionAttributeGrpc, ListOfActionDataGrpc, ListOfActionGrpc, ListOfAttributeDataGrpc } from "src/verazial-common-frontend/core/generated/flow/task_flow_pb";
import { ArrayOfInts } from "src/verazial-common-frontend/core/generated/util_pb";
import { ActionModel } from "../models/action.model";
import { ActionAttribute } from "../models/action-attributes.model";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";
import { AttributeType } from "../enums/attribute-type.enum";
import { ActionData } from "../models/action-data.model";
import { AttributeData } from "../models/attribute-data.model";

export function arrayOfIntsToListOfNumbers(arrayOfInts?: ArrayOfInts): number[] {
    let listOfInts: number[] = [];

    arrayOfInts?.getValList()?.forEach(val => {
        listOfInts.push(Number(val))
    });

    return listOfInts;
}

export function listOfNumbersToArrayOfInts(listOfNumbers?: number[]): ArrayOfInts {
    let arrayOfInts = new ArrayOfInts();

    listOfNumbers?.forEach((val) => {
        arrayOfInts.addVal(val)
    });

    return arrayOfInts;
}

export function listOfActionGrpcToListOfAction(listOfActionGrpc?: ListOfActionGrpc): ActionModel[] {
    let listOfActions: ActionModel[] = [];
    listOfActionGrpc?.getFlowactionsList().forEach(action => {
        listOfActions.push(actionGrpcToAction(action))
    });
    return listOfActions
}

export function listOfActionToListOfActionGrpc(listOfActions?: ActionModel[]): ListOfActionGrpc {
    let listOfActionGrpc = new ListOfActionGrpc();

    listOfActions?.forEach(action => {
        listOfActionGrpc.addFlowactions(actionToActionGrpc(action));
    })

    return listOfActionGrpc;
}

export function actionToActionGrpc(action: ActionModel): ActionGrpc {
    let actionGrpc = new ActionGrpc();

    actionGrpc.setId(action?.id!);
    actionGrpc.setFromactions(listOfNumbersToArrayOfInts(action.fromActions));
    actionGrpc.setAction(listOfActionDataToListOfActionDataGrpc(action.action!!));

    return actionGrpc;
}

export function actionGrpcToAction(actionGrpc?: ActionGrpc): ActionModel {
    let actionModel = new ActionModel();

    actionModel.id = actionGrpc?.getId();
    actionModel.fromActions = arrayOfIntsToListOfNumbers(actionGrpc?.getFromactions());
    actionModel.action! = listOfActionDataGrpcToListOfActionData(actionGrpc?.getAction())[0]

    return actionModel;
}

export function listOfActionDataGrpcToListOfActionData(listOfActionDataGrpc?: ListOfActionDataGrpc): ActionData[] {
    let listOfActionsData: ActionData[] = [];
    listOfActionDataGrpc?.getActionList().forEach(action => {
        listOfActionsData.push(actionDataGrpcToActionData(action))
    })
    return listOfActionsData
}

export function listOfActionDataToListOfActionDataGrpc(actionData?: ActionData): ListOfActionDataGrpc {
    let listOfActionDataGrpc = new ListOfActionDataGrpc();

    listOfActionDataGrpc.addAction(actionDataToActionDataGrpc(actionData!));


    return listOfActionDataGrpc;
}

export function actionDataToActionDataGrpc(actionData: ActionData): ActionDataGrpc {
    let actionDataGrpc = new ActionDataGrpc();

    actionDataGrpc.setId(actionData.id!!);
    actionDataGrpc.setName(actionData.name!!);
    actionDataGrpc.setDescription(actionData.description ? actionData.description : "");
    actionDataGrpc.setAttributes(
        listOfActionAttributeToListOfActionAttributeGrpc(actionData.attributes))

    return actionDataGrpc;
}

export function actionDataGrpcToActionData(actionDataGrpc: ActionDataGrpc) {
    let actionData = new ActionData()

    actionData.id = actionDataGrpc.getId();
    actionData.name = actionDataGrpc.getName();
    actionData.description = actionDataGrpc.getDescription();
    actionData.attributes = listOfActionAttributeGrpcToListOfActionAttribute(
        actionDataGrpc.getAttributes());

    return actionData;
}

export function listOfActionAttributeToListOfActionAttributeGrpc(listOfActionAttribute?: ActionAttribute[]): ListOfActionAttributeGrpc {
    let listOfActionAttributeGrpc = new ListOfActionAttributeGrpc();

    listOfActionAttribute?.forEach(attribute => {
        listOfActionAttributeGrpc.addAttributes(actionAttributeToActionAttributeGrpc(attribute))
    })

    return listOfActionAttributeGrpc;
}

export function listOfActionAttributeGrpcToListOfActionAttribute(
    listOfActionAttributeGrpc?: ListOfActionAttributeGrpc): ActionAttribute[] {
    let listOfActionAttribute: ActionAttribute[] = [];

    listOfActionAttributeGrpc?.getAttributesList().forEach(attribute => {
        listOfActionAttribute.push(actionAttributeGrpcToActionAttribute(attribute));
    })

    return listOfActionAttribute;
}

export function actionAttributeToActionAttributeGrpc(actionAttribute: ActionAttribute): ActionAttributeGrpc {
    let actionAttributeGrpc = new ActionAttributeGrpc();

    actionAttributeGrpc.setId(actionAttribute.id!!);

    actionAttributeGrpc.setType(toAttributeTypeGrpc(actionAttribute.type!));
    actionAttributeGrpc.setName(actionAttribute?.name!)
    actionAttributeGrpc.setAttribute(listOfAttributeDataToListOfAttributeDataGrpc(actionAttribute.attribute));

    return actionAttributeGrpc
}

export function actionAttributeGrpcToActionAttribute(actionAttributeGrpc: ActionAttributeGrpc): ActionAttribute {
    let actionAttribute = new ActionAttribute();

    actionAttribute.id = actionAttributeGrpc.getId();
    actionAttribute.type = toAttributeTypeEnum(actionAttributeGrpc.getType());
    actionAttribute.name = actionAttributeGrpc.getName();
    actionAttribute.attribute = listOfAttributeDataGrpcTolistOfAttributeData(actionAttributeGrpc.getAttribute())

    return actionAttribute;
}

export function listOfAttributeDataToListOfAttributeDataGrpc(listOfAttributeData?: AttributeData[]): ListOfAttributeDataGrpc {
    let listOfAttributeDataGrpc = new ListOfAttributeDataGrpc();

    listOfAttributeData?.forEach(data => {
        listOfAttributeDataGrpc.addAttribute(attributeDataToAttributeDataGrpc(data))
    })
    return listOfAttributeDataGrpc;

}

export function listOfAttributeDataGrpcTolistOfAttributeData(listOfAttributeDataGrpc?: ListOfAttributeDataGrpc): AttributeData[] {
    let listOfAttributeData: AttributeData[] = [];
    listOfAttributeDataGrpc?.getAttributeList().forEach((data) => {
        listOfAttributeData.push(attributeDataGrpcToAttributeData(data))
    })
    return listOfAttributeData;
}

export function attributeDataToAttributeDataGrpc(attributeData: AttributeData): AttributeDataGrpc {
    let attributeDataGrpc = new AttributeDataGrpc();

    attributeDataGrpc.setKey(attributeData.key!);
    attributeDataGrpc.setValue(String(attributeData.value!));

    return attributeDataGrpc;
}

export function attributeDataGrpcToAttributeData(attributeDataGrpc: AttributeDataGrpc): AttributeData {
    let attributeData = new AttributeData();

    let value: string | boolean;

    if (attributeDataGrpc.getValue() == "true") {
        value = true;
    } else if (attributeDataGrpc.getValue() == "false") {
        value = false;
    } else {
        value = attributeDataGrpc.getValue();
    }

    attributeData.key = attributeDataGrpc.getKey();
    attributeData.value = value;

    return attributeData;
}

export function toAttributeTypeEnum(attributeType: AttributeTypeGrpc): AttributeType {
    switch (attributeType) {
        case AttributeTypeGrpc.INPUT:
            return AttributeType.INPUT;
        case AttributeTypeGrpc.DROPDOWN:
            return AttributeType.DROPDOWN;
        case AttributeTypeGrpc.TOGGLE:
            return AttributeType.TOGGLE;
        case AttributeTypeGrpc.BUTTON:
            return AttributeType.BUTTON;
        case AttributeTypeGrpc.MESSAGE:
            return AttributeType.MESSAGE;
        default:
            throw new Error("Invalid attribute type");
    }
}

export function toAttributeTypeGrpc(attributeType: AttributeType): AttributeTypeGrpc {
    switch (attributeType) {
        case AttributeType.INPUT:
            return AttributeTypeGrpc.INPUT;
        case AttributeType.DROPDOWN:
            return AttributeTypeGrpc.DROPDOWN;
        case AttributeType.TOGGLE:
            return AttributeTypeGrpc.TOGGLE;
        case AttributeType.BUTTON:
            return AttributeTypeGrpc.BUTTON;
        case AttributeType.MESSAGE:
            return AttributeTypeGrpc.MESSAGE;
        default:
            throw new Error("Invalid attribute type");
    }
}


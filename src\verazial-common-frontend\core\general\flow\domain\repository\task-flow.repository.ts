import { TaskFlow } from "src/verazial-common-frontend/core/models/task-flow.interface";
import { TaskFlowEntity } from "../entity/task-flow.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export abstract class TaskFlowRepository {
    abstract createTaskFlow(params: { taskFlow: TaskFlowEntity }): Promise<TaskFlowEntity>;
    abstract getAllTaskFlows(): Promise<TaskFlowEntity[]>;
    abstract getAllPublishedTaskFlows(): Promise<TaskFlowEntity[]>;
    abstract getTaskFlowById(params: { id: string }): Promise<TaskFlowEntity>;
    abstract deleteTaskFlowById(params: { id: string }): Promise<SuccessResponse>;
    abstract updateTaskFlow(params: { taskFlow: TaskFlowEntity }): Promise<TaskFlowEntity>;
}
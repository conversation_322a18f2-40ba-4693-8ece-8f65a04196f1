import { AuthenticateByUserRequest } from "src/verazial-common-frontend/core/generated/auth/auth_pb";

export function convertNumIdPassToNidPassCredentials(prams: { numId: string, password: string }): AuthenticateByUserRequest.NidPassCredentials {
    let nidPassCredentials = new AuthenticateByUserRequest.NidPassCredentials();
    nidPassCredentials.setNid(prams.numId);
    nidPassCredentials.setPassword(prams.password);
    return nidPassCredentials;
}

export function convertEmailPassToEmailPassCredentials(params: { email: string, password: string }): AuthenticateByUserRequest.EmailPassCredentials {
    let emailPassCredentials = new AuthenticateByUserRequest.EmailPassCredentials();
    emailPassCredentials.setEmail(params.email);
    emailPassCredentials.setPassword(params.password);
    return emailPassCredentials;
}
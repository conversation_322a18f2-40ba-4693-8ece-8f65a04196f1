import { Injectable } from "@angular/core";
import { AppRegistryRepository } from "../../domain/repositories/app-registry.repository";
import { AppRegistryMapper } from "../mapper/app-registry.mapper";
import { HttpClient } from "@angular/common/http";
import { AppRegistryEntity } from "../../domain/entities/app-registry.entity";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { CoreAppRegistryServiceClient } from "src/verazial-common-frontend/core/generated/app-registry/App_registryServiceClientPb";
import { environment } from "src/environments/environment";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { AppRegistryGrpcModel, AppRegistryListGrpcModel, AppRegistryRequest } from "src/verazial-common-frontend/core/generated/app-registry/app_registry_pb";
import { convertListOfAppRegistryToAppRegistryListGrpcModel } from "../../common/converter/app-registry.converter";
import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";

@Injectable({
    providedIn: 'root',
})
export class AppRegistryRepositoryImpl extends AppRegistryRepository {

    appRegistryMapper = new AppRegistryMapper();

    constructor(
        private httpClient: HttpClient
    ) {
        super();
    }

    override getAppRegistries(): Promise<AppRegistryEntity[]> {
        let appRegistries: AppRegistryEntity[] = [];

        let coreAppRegistryServiceClient = new CoreAppRegistryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        let grpc = coreAppRegistryServiceClient.getAppRegistries(new Empty());

        return new Promise((resolve, reject) => {
            grpc.on('data', (response) => {
                appRegistries.push(this.appRegistryMapper.mapFrom(response));
            });

            grpc.on('error', (err) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message
                }
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(appRegistries);
            });
        });
    }

    override getAppRegistryById(params: { id: string; }): Promise<AppRegistryEntity> {
        let request = new AppRegistryRequest();
        request.setValue(params.id);

        let coreAppRegistryServiceClient = new CoreAppRegistryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });


        return new Promise((resolve, reject) => {
            coreAppRegistryServiceClient.getAppRegistryById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    }
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                    else {
                        resolve(this.appRegistryMapper.mapFrom(response.getApplication()!));
                    }
                }
            });
        });
    }

    override getAppRegistryByName(params: { name: string; }): Promise<AppRegistryEntity> {
        let request = new AppRegistryRequest();
        request.setValue(params.name);

        let coreAppRegistryServiceClient = new CoreAppRegistryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });


        return new Promise((resolve, reject) => {
            coreAppRegistryServiceClient.getAppRegistryByName(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    }
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                    else {
                        resolve(this.appRegistryMapper.mapFrom(response.getApplication()!));
                    }
                }
            });
        });
    }

    override addAppRegistry(params: { applications: AppRegistryEntity[]; }): Promise<AppRegistryEntity[]> {
        let request = new AppRegistryListGrpcModel();
        request.setApplicationsList(params.applications.map(app => this.appRegistryMapper.mapTo(app)));

        let responseAppRegistry: AppRegistryEntity[] = [];

        let coreAppRegistryServiceClient = new CoreAppRegistryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        let grpc = coreAppRegistryServiceClient.addAppRegistry(request);

        return new Promise((resolve, reject) => {
            grpc.on('data', (response) => {
                responseAppRegistry.push(this.appRegistryMapper.mapFrom(response));
            });

            grpc.on('error', (err) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message
                }
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseAppRegistry);
            });
        });
    }

    override updateAppRegistry(params: { application: AppRegistryEntity; }): Promise<AppRegistryEntity> {
        let request = new AppRegistryGrpcModel();
        request = this.appRegistryMapper.mapTo(params.application);

        let coreAppRegistryServiceClient = new CoreAppRegistryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreAppRegistryServiceClient.updateAppRegistry(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    }
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                    else {
                        resolve(this.appRegistryMapper.mapFrom(response.getApplication()!));
                    }
                }
            });
        });
    }

    override deleteAppRegistryById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new AppRegistryRequest();
        request.setValue(params.id);

        let coreAppRegistryServiceClient = new CoreAppRegistryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreAppRegistryServiceClient.deleteAppRegistryById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    }
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                    else {
                        let success: SuccessResponse = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    }
                }
            });
        });
    }
}
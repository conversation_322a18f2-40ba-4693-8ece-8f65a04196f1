import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationFlowEntity } from "../entity/application-flow.entity";
import { ApplicationFlowRepository } from "../repository/application-flow.repository";

export class GetApplicationFlowByAppIdUseCase implements UseCaseGrpc<{ id: string }, ApplicationFlowEntity> {
    constructor(private applicationFlowRespository: ApplicationFlowRepository) { }
    execute(params: { id: string; }): Promise<ApplicationFlowEntity> {
        return this.applicationFlowRespository.getApplicationFlowByAppId(params);
    }
}
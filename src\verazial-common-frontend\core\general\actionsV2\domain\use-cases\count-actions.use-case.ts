import { ActionEntity } from "../entity/action.entity";
import { CountActionsRequestEntity } from "../entity/count-actions-request.entity";
import { CountActionsResponseEntity } from "../entity/count-actions-response.entity";
import { SearchActionsRequestEntity } from "../entity/search-actions-request.entity";
import { ActionsV2Repository } from "../repository/actionsV2.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";



export class CountActionsUseCase implements UseCaseGrpc<CountActionsRequestEntity,CountActionsResponseEntity>{
    constructor(private actionsV2Repository: ActionsV2Repository){}
    execute(params: CountActionsRequestEntity): Promise<CountActionsResponseEntity> {
        return this.actionsV2Repository.countActions(params);
    }
}
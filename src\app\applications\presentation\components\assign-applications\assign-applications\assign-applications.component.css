accordion-content-custom{
    width: 100px;
}
.content-column{
    display: flex;
    flex-direction: column;
    /*width: 100%;*/
}

.accordion-content-individual{
    display: flex;
    flex-direction: row;
}

.div-dropdown{
    position: absolute;
    z-index: 1;

}

.sub-container-main{
    width: 99%;
    min-width: 150px;
    padding: 10px 10px 10px 10px; 
}

.sub-container-column{
    display: flex;
    flex-direction: column;
    width: 99%;
    min-width: 150px;
    max-width: 240px;
    padding: 10px 10px 10px 10px; 
}

.sub-container-large{
    width: 99%;
    min-width: 250px;
    padding: 10px 10px 10px 10px;
}

.grid {
    padding-left: 10px;
    width: 500px;
    overflow-y: auto;
}

.line {
    width: 100%;
    height: 0;
    border: 2px solid #C4C4C4;
    margin: 3px;
    display: inline-block;
}

.centerpoint {
    position: absolute;
    top: 30%;
    left: 30%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

dialog {
    width: 550px;
    height: 300px;
    /*z-index: -1;*/
    border-radius: 20px;
    border-width: 1px;
}

dialog div {
    margin: 10px;
    /*z-index: 1;*/
}

.close-button-wrap {
    display: flex;
    position: absolute;
    left: 460px;
}

.close-button-img {
    height: 20px;
    width: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="style=fill"> <g id="close-circle"> <path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM8.46967 8.46967C8.76257 8.17678 9.23744 8.17678 9.53033 8.46967L12 10.9393L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967C15.8232 8.76257 15.8232 9.23744 15.5303 9.53033L13.0606 12L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7625 15.8232 14.4696 15.5303L12 13.0607L9.53033 15.5303C9.23743 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7625 8.46967 14.4696L10.9393 12L8.46967 9.53033C8.17678 9.23743 8.17678 8.76256 8.46967 8.46967Z" fill="black"></path> </g> </g> </g></svg>');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;

}

.close-label {
    padding-top: 8px;
}

import { TaskFlowRepository } from "../../repository/task-flow.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { TaskFlowEntity } from "../../entity/task-flow.entity";

export class GetAllPublishedTaskFlowsUseCase implements UseCaseGrpc<void, TaskFlowEntity[]> {
    constructor(private taskFlowRepository: TaskFlowRepository) { }
    execute(params: void): Promise<TaskFlowEntity[]> {
        return this.taskFlowRepository.getAllPublishedTaskFlows();
    }
}
import { WindowParametersRepository } from "../../domain/repositories/window-parameters.repository";
import { WindowParametersGrpcMapper } from "../mapper/window-parameters-grpc.mapper";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { WindowParametersEntity } from "../../domain/entities/window-parameters.entity";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ListWindowParametersGrpcModel, WindowParametersGrpcModel, WindowParametersRequest } from "src/verazial-common-frontend/core/generated/application/parameters_pb";
import { CoreWindowParametersClient } from "src/verazial-common-frontend/core/generated/application/ParametersServiceClientPb";
import { IdOrder } from "src/verazial-common-frontend/core/generated/util_pb";

@Injectable({
    providedIn: 'root',
})
export class WindowParametersRepositoryGrpcImpl extends WindowParametersRepository {

    windowParametersGrpcMapper = new WindowParametersGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }

    override addWindowParam(windowParams: WindowParametersEntity[]): Promise<WindowParametersEntity[]> {

        let request = new ListWindowParametersGrpcModel();

        request.setApplicationparametersmodelList(
            windowParams.map((params) => this.windowParametersGrpcMapper.mapTo(params))
        );

        let responseWindowParameters: WindowParametersEntity[] = [];

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        let grpc = coreWindowParametersClient.addWindowParam(request, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: WindowParametersGrpcModel) => {
                responseWindowParameters.push(this.windowParametersGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseWindowParameters);
            });
        });
    }

    override updateWindowParamById(windowParams: WindowParametersEntity): Promise<WindowParametersEntity> {

        let request = this.windowParametersGrpcMapper.mapTo(windowParams);

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreWindowParametersClient.updateWindowParamById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.windowParametersGrpcMapper.mapFrom(
                            response.getWindowparametersmodel()!
                        ));
                    }
                }
            });
        });
    }

    override getWindowParamById(params: { id: string; }): Promise<WindowParametersEntity> {
        let request = new WindowParametersRequest();

        request.setValue(params.id);

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreWindowParametersClient.getWindowParamById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.windowParametersGrpcMapper.mapFrom(
                            response.getWindowparametersmodel()!
                        ));
                    }
                }
            });
        });
    }

    override getWindowParamsByWindowId(params: { windowId: string; }): Promise<WindowParametersEntity[]> {
        let responseWindowParameters: WindowParametersEntity[] = [];

        let request = new WindowParametersRequest();

        request.setValue(params.windowId);

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        let grpc = coreWindowParametersClient.getWindowParamsByWindowId(request, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: WindowParametersGrpcModel) => {
                responseWindowParameters.push(this.windowParametersGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseWindowParameters);
            });
        });
    }

    override getWindowParamsByAppId(params: { appId: string; }): Promise<WindowParametersEntity[]> {
        let responseWindowParameters: WindowParametersEntity[] = [];

        let request = new WindowParametersRequest();

        request.setValue(params.appId);

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        let grpc = coreWindowParametersClient.getWindowParamsByAppId(request, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: WindowParametersGrpcModel) => {
                responseWindowParameters.push(this.windowParametersGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseWindowParameters);
            });
        });
    }

    override deleteWindowParamById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new WindowParametersRequest();

        request.setValue(params.id);
        let success!: SuccessResponse;

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreWindowParametersClient.deleteWindowParamById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override deleteWindowParamsByWindowId(params: { windowId: string, order: number }): Promise<SuccessResponse> {
        let request = new IdOrder();

        request.setWindowid(params.windowId);
        request.setWindoworder(params.order);
        let success!: SuccessResponse;

        let coreWindowParametersClient = new CoreWindowParametersClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreWindowParametersClient.deleteWindowParamsByWindowId(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

}
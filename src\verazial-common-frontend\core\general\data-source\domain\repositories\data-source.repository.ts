import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { DataSourceEntity } from "../entities/data-source.entity";

export abstract class DataSourceRepository {
    abstract addAppDataSource(dataSource: DataSourceEntity): Promise<DataSourceEntity>;
    abstract updateAppDataSourceById(dataSource: DataSourceEntity): Promise<SuccessResponse>;
    abstract getAppDataSourceById(params: { id: string }): Promise<DataSourceEntity>;
    abstract getAppDataSourceByName(params: { name: string }): Promise<DataSourceEntity>;
    abstract getAllDataSources(): Promise<DataSourceEntity[]>;
    abstract deleteDataSourceById(params: { id: string }): Promise<SuccessResponse>;
}
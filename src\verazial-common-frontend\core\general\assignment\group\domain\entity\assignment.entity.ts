import { AssignmentElementEntity } from "./assignment-elements.entity";

export class AssignmentEntity {
    id: string | undefined;
    name: string | undefined;
    description: string | undefined;
    elements: AssignmentElementEntity[] | undefined;
    isRequiredWithinSchedule: boolean | undefined;
    alertIfAllPerformedWithinSchedule: boolean | undefined;
    alertIfNotPerformedWithinSchedule: boolean | undefined;
    alertIfPerformedOutsideSchedule: boolean | undefined;
    createdAt: Date | undefined;
    updatedAt: Date | undefined;
}
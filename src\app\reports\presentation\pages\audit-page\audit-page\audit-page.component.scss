
.product-badge {
    border-radius: 2px;
    padding: .25em .5rem;
    font-weight: 700;
    font-size: 10px;
    letter-spacing: .2px;
    
    .status-in {
        background: #C8E6C9;
        color: #256029
    }

    .status-out {
        background: #FFCDD2;
        color: #c63737
    }

    .status-low {
        background: #FEEDAF;
        color: #8a5340
    }
}



.product-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    width: 100%;
    flex-wrap: nowrap;

    img {
        width: 100px;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
        margin-right: 1rem;
    }


    .image-container {
        width: 100px;
        height: 66px;
        margin-right: 1rem;
        padding: 5px;
    }

    .product-list-detail {
        flex: 1 1 0;
    }

    .product-list-detail {
        display: flex;
        flex-direction: column;
    }

    .product-list-action {
        display: flex;
        flex-direction: column;
    }

    .product-category-icon {
        vertical-align: middle;
        margin-right: 0.5rem;
    }

    .product-category {
        vertical-align: middle;
        line-height: 1;
    }

    &.loading-item {
        .image-container {
            width: 100px;
            height: 66px;
            animation: pulse 1s infinite ease-in-out;
            margin-right: 1rem;
            padding: 10px;
        }

        .product-list-detail {
            h5, .product-category {
                width: 150px;
                height: 14px;
                display: block;
                animation: pulse 1s infinite ease-in-out;
            }
        }

        .product-list-action {
            .product-badge {
                display: block;
                width: 100px;
                height: 14px;
                animation: pulse 1s infinite ease-in-out;
            }

            h6 {
                width: 25px;
                height: 14px;
                animation: pulse 1s infinite ease-in-out;
            }
        }
    }
}

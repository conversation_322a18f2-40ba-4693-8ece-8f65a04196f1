import { DOCUMENT, formatDate } from '@angular/common';
import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Chart, { ChartType } from 'chart.js/auto';
/*import { getActionsByUseCase } from 'src/app/config/domain/use-case/getActionsBy.use-case';
import { Action } from 'src/core/models/action.model';*/
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { GetAllUsersUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-all-users.use-case';
import { UserEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/user.entity';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';

@Component({
    selector: 'app-user-page',
    templateUrl: './user-page.component.html',
    styleUrl: './user-page.component.css'
})
export class UserPageComponent implements OnInit {


    selectedProfile: string = "";
    profileOptions: Array<any> = [];
    profiles: Array<any> = environment.profileNames;
    chart: any;
    pieChartUser: any;
    hChartUser: any;
    dChartUser: any;
    lChartUser: any;
    fChartUser: any;
    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";
    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        profile: [],
    });

    progressValue: string = "";
    progressEnabled: boolean = false;
    progressColor: string = "#0ab4ba";

    countMale: any;
    countFemale: any;
    countOther: any;
    countAdmin: any;
    countGeneral: any;
    countPrueba: any;
    countEnrolador: any;
    countMaster: any;

    countL1: any;
    countL2: any;
    countL3: any;
    countL4: any;
    countL5: any;
    countLO: any;
    countPicOk: any;
    countPicMissing: any;

    usersDataPreFiltered: UserEntity[] = [];
    usersData: any[] = [];
    showSpinners: boolean = true;

    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;
    showNoData5: boolean = false;


    constructor(
        @Inject(LOCALE_ID) private locale: string,
        @Inject(DOCUMENT) private document: Document,
        /*private getActionByUseCase: getActionsByUseCase,*/
        private getUsers: GetAllUsersUseCase,
        private reportsService: ReportsService,
        private translate: TranslateService,
        private fb: FormBuilder,
        private loggerService: ConsoleLoggerService,
    ) { }

    ngOnInit(): void {

        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);

        this.profiles.forEach(p => {

            this.profileOptions.push({ "name": p.name });

        });

        this.selectedProfile = environment.profileDefault;


        setTimeout(() => {
            this.getAllUsers();
        }, 1000);

    }

    getAllUsers() {

        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";

        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            return;
        }

        if (dateStartDate < dateEndDate) {

            const startDate = dateStartDate.toISOString().split('T')[0] + "T00:00:00.000Z";
            const endDate = dateEndDate.toISOString().split('T')[0] + "T59:59:59.000Z";

            this.progressEnabled = true;
            this.progressValue = "5";

            this.getUsers.execute({ offset: 0, limit: 10000 }).then(
                (data) => {

                    setTimeout(() => {

                        this.usersDataPreFiltered = data;

                        var index = 0;
                        this.countMale = 0;
                        this.countFemale = 0;
                        this.countOther = 0;

                        this.countAdmin = 0;
                        this.countGeneral = 0;
                        this.countPrueba = 0;
                        this.countEnrolador = 0;
                        this.countMaster = 0;

                        this.countL1 = 0;
                        this.countL2 = 0;
                        this.countL3 = 0;
                        this.countL4 = 0;
                        this.countL5 = 0;
                        this.countLO = 0;

                        this.countPicOk = 0;
                        this.countPicMissing = 0;

                        for (index; index < this.usersDataPreFiltered.length; index++) {

                            var usr = this.usersDataPreFiltered[index];

                            if (usr.createdAt && this.reportsService.isValidDate(usr.createdAt) && usr.createdAt.toISOString() >= startDate && usr.createdAt.toISOString() <= endDate) {

                                this.usersData.push(usr);

                                if (usr.gender == "male") {
                                    this.countMale++;
                                }
                                else if (usr.gender == "female") {
                                    this.countFemale++;
                                }
                                else {
                                    this.countOther++;
                                }

                                if (usr.roles?.find(r => r.name == "A"))
                                    this.countAdmin++;

                                if (usr.roles?.find(r => r.name == "M"))
                                    this.countMaster++;

                                if (usr.roles?.find(r => r.name == "P"))
                                    this.countPrueba++;

                                if (usr.roles?.find(r => r.name == "G"))
                                    this.countGeneral++;

                                if (usr.roles?.find(r => r.name == "EN"))
                                    this.countEnrolador++;

                                switch (usr.locationId) {

                                    case "Location 1":
                                        this.countL1++;
                                        break;

                                    case "Location 2":
                                        this.countL2++;
                                        break;

                                    case "Location 3":
                                        this.countL3++;
                                        break;

                                    case "Location 4":
                                        this.countL4++;
                                        break;

                                    case "Location 5":
                                        this.countL5++;
                                        break;

                                    default:
                                        this.countLO++;
                                        break;
                                }

                                switch (usr.pic) {

                                    case "assets/images/UserPic.svg":
                                        this.countPicMissing++;
                                        break;

                                    default:
                                        this.countPicOk++;
                                        break;
                                }
                            }

                            this.progressValue = (Math.ceil((100 * index) / this.usersDataPreFiltered.length)).toString();
                        }


                        // filter all users without role
                        /*let users = [...data.filter(v => v.email != "" || v.email != null)]
                        this.listOfUser = users;
                        this.isLoading = false;*/


                        this.hideSpinners();

                        this.progressValue = "100";

                        setTimeout(() => {

                            this.createPieChart();
                            this.createDoughnutChart();
                            this.createDoughnutChartLocation();
                            this.createDoughnutChartProfilePic();

                            this.progressEnabled = false;
                            this.progressValue = "0";
                        }, 2000);
                    }, 1000);
                },
                (e) => {
                    this.loggerService.error("Error retrieving users: " + e);
                    setTimeout(() => {
                        this.progressEnabled = false;
                        this.progressValue = "0";

                    }, 2000);
                }
            );

            /*this.getUsers.execute().subscribe({
                next: (usersReturn: any) => {


                    if (usersReturn.status == "SUCCESS") {

                        this.loggerService.debug("Users retrieved ok.")
                        //this.loggerService.debug(subjectsReturn);
                        //this.loggerService.debug(subjectsReturn.data);

                        setTimeout(() => {

                            this.usersDataPreFiltered = usersReturn.data;
                            this.loggerService.debug("Users retrieved ok.")
                            //this.loggerService.debug(usersReturn);
                            this.loggerService.debug(usersReturn.data);
                            //this.loggerService.debug(subjectsReturn.data.length);
                            //this.loggerService.debug("Subjects...")
                            var index = 0;
                            this.countMale = 0;
                            this.countFemale = 0;
                            this.countOther = 0;

                            this.countAdmin = 0;
                            this.countGeneral = 0;
                            this.countPrueba = 0;
                            this.countEnrolador = 0;
                            this.countMaster = 0;


                            this.countL1 = 0;
                            this.countL2 = 0;
                            this.countL3 = 0;
                            this.countL4 = 0;
                            this.countL5 = 0;
                            this.countLO = 0;

                            this.countPicOk = 0;
                            this.countPicMissing = 0;

                            for (index = 0; index < this.usersDataPreFiltered.length; index++) {

                                var usr = this.usersDataPreFiltered[index];

                                if (new Date(usr.createdAt).toISOString() >= startDate && new Date(usr.createdAt).toISOString() <= endDate) {

                                    this.usersData.push(usr);

                                    if (usr.gender == "male") {

                                        this.countMale++;
                                    }
                                    else {

                                        if (usr.gender == "female") {

                                            this.countFemale++;
                                        }
                                        else {
                                            this.countOther++;
                                        }

                                    }

                                    if (usr.profile == "A")
                                        this.countAdmin++;

                                    if (usr.profile == "M")
                                        this.countMaster++;

                                    if (usr.profile == "P")
                                        this.countPrueba++;

                                    if (usr.profile == "G")
                                        this.countGeneral++;

                                    if (usr.profile == "EN")
                                        this.countEnrolador++;

                                    switch (usr.locationId) {

                                        case "Location 1":
                                            this.countL1++;
                                            break;

                                        case "Location 2":
                                            this.countL2++;
                                            break;

                                        case "Location 3":
                                            this.countL3++;
                                            break;

                                        case "Location 4":
                                            this.countL4++;
                                            break;

                                        case "Location 5":
                                            this.countL5++;
                                            break;

                                        default:
                                            this.countLO++;
                                            break;


                                    }

                                    switch (usr.pic) {

                                        case "assets/images/UserPic.svg":
                                            this.countPicMissing++;
                                            break;

                                        default:
                                            this.countPicOk++;
                                            break;


                                    }

                                }

                                this.progressValue = (Math.ceil((100 * index) / this.usersDataPreFiltered.length)).toString();

                            }


                            this.hideSpinners();

                            this.progressValue = "100";

                            setTimeout(() => {

                                this.createPieChart();
                                this.createDoughnutChart();
                                this.createDoughnutChartLocation();
                                this.createDoughnutChartProfilePic();

                                this.progressEnabled = false;
                                this.progressValue = "0";
                            }, 2000);

                        },
                            1000)


                    }
                    else {
                        this.loggerService.deror("Error retrieving users.")
                        setTimeout(() => {
                            this.progressEnabled = false;
                            this.progressValue = "0";

                        }, 2000);
                    }

                },
                error: (e) => {
                    this.loggerService.error("Error retrieving user: " + e);
                    setTimeout(() => {
                        this.progressEnabled = false;
                        this.progressValue = "0";

                    }, 2000);
                }
            });*/



        }
        else {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            setTimeout(() => {
                this.progressEnabled = false;
                this.progressValue = "0";
            }, 2000);
        }
    }


    createPieChart() {

        var noData = this.translate.instant("messages.no_data");

        if (this.pieChartUser == null) {


            if (this.countMaster == 0 && this.countAdmin == 0 && this.countEnrolador == 0 && this.countGeneral == 0 && this.countPrueba == 0) {


                this.pieChartUser = new Chart("pieChartUser", {
                    type: 'pie' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        labels: [
                            noData,
                            noData,
                            noData,
                            noData,
                            noData
                        ],
                        datasets: [{
                            data: [20, 20, 20, 20, 20],
                            backgroundColor: [
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA
                            ],
                            hoverOffset: 4
                        }]
                    }

                });

            }
            else {

                this.pieChartUser = new Chart("pieChartUser", {
                    type: 'pie' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        labels: [
                            'Master',
                            'Admin',
                            'Enrolador',
                            'General',
                            'Otros'
                        ],
                        datasets: [{
                            data: [this.countMaster, this.countAdmin, this.countEnrolador, this.countGeneral, this.countPrueba],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,
                                environment.colorProfile3,
                                environment.colorProfile4,
                                environment.colorProfile5
                            ],
                            hoverOffset: 4
                        }]
                    }

                });
            }




        }
        else {


            if (this.countMaster == 0 && this.countAdmin == 0 && this.countEnrolador == 0 && this.countGeneral == 0 && this.countPrueba == 0) {


                // update graph
                this.pieChartUser.options.plugins.legend.display = true;
                this.pieChartUser.data.labels[0] = noData;
                this.pieChartUser.data.labels[1] = noData;
                this.pieChartUser.data.labels[2] = noData;
                this.pieChartUser.data.labels[3] = noData;
                this.pieChartUser.data.labels[4] = noData;

                this.pieChartUser.data.datasets[0].backgroundColor = environment.colorDisabledA,
                    this.pieChartUser.data.datasets[1].backgroundColor = environment.colorDisabledB,
                    this.pieChartUser.data.datasets[2].backgroundColor = environment.colorDisabledA,
                    this.pieChartUser.data.datasets[3].backgroundColor = environment.colorDisabledB,
                    this.pieChartUser.data.datasets[4].backgroundColor = environment.colorDisabledA,

                    this.pieChartUser.data.datasets[0].data = [20, 20, 20, 20, 20],
                    this.pieChartUser.update();

            }
            else {

                // update graph
                this.pieChartUser.options.plugins.legend.display = true;
                this.pieChartUser.data.labels[0] = "Master";
                this.pieChartUser.data.labels[1] = "Admin";
                this.pieChartUser.data.labels[2] = "Enrolador";
                this.pieChartUser.data.labels[3] = "General";
                this.pieChartUser.data.labels[4] = "Otros";

                this.pieChartUser.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.pieChartUser.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.pieChartUser.data.datasets[2].backgroundColor = environment.colorProfile3;
                this.pieChartUser.data.datasets[3].backgroundColor = environment.colorProfile4;
                this.pieChartUser.data.datasets[4].backgroundColor = environment.colorProfile5;

                this.pieChartUser.data.datasets[0].data = [this.countMaster, this.countAdmin, this.countEnrolador, this.countGeneral, this.countPrueba];
                this.pieChartUser.update();
            }


        }


    }


    createDoughnutChart() {

        var optionA = this.translate.instant("titles.male");
        var optionB = this.translate.instant("titles.female");
        var optionC = this.translate.instant("titles.otherGender");
        var noData = this.translate.instant("messages.no_data");

        if (this.hChartUser == null) {

            if (this.countMale == 0 && this.countFemale == 0 && this.countOther == 0) {

                this.hChartUser = new Chart("dChartUser", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [33, 33, 33],
                            backgroundColor: [
                                "#ececec",
                                "#acacac",
                                "#ececec"
                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            noData,
                            noData,
                            noData
                        ]
                    }
                });

            }
            else {

                this.hChartUser = new Chart("dChartUser", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [this.countMale, this.countFemale, this.countOther],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,
                                environment.colorOther
                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            optionA,
                            optionB,
                            optionC
                        ]
                    }
                });

            }




        } else {

            if (this.countMale == 0 && this.countFemale == 0 && this.countOther == 0) {


                // update graph
                this.hChartUser.options.plugins.legend.display = true;
                this.hChartUser.data.datasets[0].data = [33, 33, 33];
                this.hChartUser.data.labels[0] = noData;
                this.hChartUser.data.labels[1] = noData;
                this.hChartUser.data.labels[2] = noData;
                this.hChartUser.data.datasets[0].backgroundColor = environment.colorDisabledA;
                this.hChartUser.data.datasets[1].backgroundColor = environment.colorDisabledB;
                this.hChartUser.data.datasets[2].backgroundColor = environment.colorDisabledA;
                this.hChartUser.update();


            }
            else {

                // update graph
                this.hChartUser.options.plugins.legend.display = true;
                this.hChartUser.data.labels[0] = optionA;
                this.hChartUser.data.labels[1] = optionB;
                this.hChartUser.data.labels[2] = optionC;
                this.hChartUser.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.hChartUser.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.hChartUser.data.datasets[2].backgroundColor = environment.colorOther;
                this.hChartUser.data.datasets[0].data = [this.countMale, this.countFemale, this.countOther];
                this.hChartUser.update();
            }




        }


    }

    createDoughnutChartLocation() {

        var optionC = this.translate.instant("titles.otherGender");
        var noData = this.translate.instant("messages.no_data");

        if (this.lChartUser == null) {


            if (this.countL1 == 0 && this.countL2 == 0 && this.countL3 == 0 && this.countL4 == 0 && this.countL5 == 0 && this.countLO == 0) {


                this.lChartUser = new Chart("lChartUser", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [16, 16, 16, 16, 16, 16],
                            backgroundColor: [
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA,
                                environment.colorDisabledB,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            noData,
                            noData,
                            noData,
                            noData,
                            noData,
                            noData,
                        ]
                    }
                });
            }
            else {

                this.lChartUser = new Chart("lChartUser", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [this.countL1, this.countL2, this.countL3, this.countL4, this.countL5, this.countLO],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,
                                environment.colorProfile3,
                                environment.colorProfile4,
                                environment.colorProfile5,
                                environment.colorOther,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            'Location 1',
                            'Location 2',
                            'Location 3',
                            'Location 4',
                            'Location 5',
                            optionC,
                        ]
                    }
                });
            }




        }
        else {


            if (this.countL1 == 0 && this.countL2 == 0 && this.countL3 == 0 && this.countL4 == 0 && this.countL5 == 0 && this.countLO == 0) {

                // update graph
                this.lChartUser.options.plugins.legend.display = true;
                this.lChartUser.data.labels[0] = noData;
                this.lChartUser.data.labels[1] = noData;
                this.lChartUser.data.labels[2] = noData;
                this.lChartUser.data.labels[3] = noData;
                this.lChartUser.data.labels[4] = noData;
                this.lChartUser.data.labels[5] = noData;
                this.lChartUser.data.datasets[0].backgroundColor = environment.colorDisabledA;
                this.lChartUser.data.datasets[1].backgroundColor = environment.colorDisabledB;
                this.lChartUser.data.datasets[2].backgroundColor = environment.colorDisabledA;
                this.lChartUser.data.datasets[3].backgroundColor = environment.colorDisabledB;
                this.lChartUser.data.datasets[4].backgroundColor = environment.colorDisabledA;
                this.lChartUser.data.datasets[5].backgroundColor = environment.colorDisabledB;
                this.lChartUser.data.datasets[0].data = [this.countL1, this.countL2, this.countL3, this.countL4, this.countL5, this.countLO];
                this.lChartUser.update();

            }
            else {

                // update graph
                this.lChartUser.options.plugins.legend.display = true;
                this.lChartUser.data.labels[0] = 'Location 1';
                this.lChartUser.data.labels[1] = 'Location 2';
                this.lChartUser.data.labels[2] = 'Location 3';
                this.lChartUser.data.labels[3] = 'Location 4';
                this.lChartUser.data.labels[4] = 'Location 5';
                this.lChartUser.data.labels[5] = optionC;
                this.lChartUser.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.lChartUser.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.lChartUser.data.datasets[2].backgroundColor = environment.colorOther;
                this.lChartUser.data.datasets[3].backgroundColor = environment.colorDisabledA;
                this.lChartUser.data.datasets[4].backgroundColor = environment.colorDisabledB;
                this.lChartUser.data.datasets[5].backgroundColor = environment.colorDisabledA;
                this.lChartUser.data.datasets[0].data = [this.countL1, this.countL2, this.countL3, this.countL4, this.countL5, this.countLO];
                this.lChartUser.update();
            }



        }


    }

    createDoughnutChartProfilePic() {

        var optionA = this.translate.instant("titles.picYes");
        var optionB = this.translate.instant("titles.picNo");
        var noData = this.translate.instant("messages.no_data");

        if (this.fChartUser == null) {


            if (this.countPicOk == 0 && this.countPicMissing == 0) {

                this.fChartUser = new Chart("fChartUser", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [1, 1],
                            backgroundColor: [
                                environment.colorDisabledA,
                                environment.colorDisabledB,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            noData,
                            noData,
                        ]
                    }
                });

            }
            else {

                this.fChartUser = new Chart("fChartUser", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [this.countPicOk, this.countPicMissing],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            optionA,
                            optionB,
                        ]
                    }
                });
            }




        }
        else {

            if (this.countPicOk == 0 && this.countPicMissing == 0) {


                // update graph
                this.fChartUser.options.plugins.legend.display = true;
                this.fChartUser.data.labels[0] = noData;
                this.fChartUser.data.labels[1] = noData;
                this.fChartUser.data.datasets[0].backgroundColor = environment.colorDisabledA,
                    this.fChartUser.data.datasets[1].backgroundColor = environment.colorDisabledB,
                    this.fChartUser.data.datasets[0].data = [1, 1];
                this.fChartUser.update();

            }
            else {


                // update graph
                this.fChartUser.options.plugins.legend.display = true;
                this.fChartUser.data.labels[0] = optionA;
                this.fChartUser.data.labels[1] = optionB;
                this.fChartUser.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.fChartUser.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.fChartUser.data.datasets[0].data = [this.countPicOk, this.countPicMissing];
                this.fChartUser.update();

            }



        }


    }

    enableSpinners() {


        if (this.pieChartUser != null) {

            this.pieChartUser.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.pieChartUser.data.datasets.length; i++) {

                this.pieChartUser.data.datasets[i].data = [0, 0, 0, 0, 0];

            }
            this.pieChartUser.update();

        }

        if (this.hChartUser != null) {

            this.hChartUser.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.hChartUser.data.datasets.length; i++) {

                this.hChartUser.data.datasets[i].data = [0, 0, 0];

            }
            this.hChartUser.update();

        }

        if (this.lChartUser != null) {


            this.lChartUser.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.lChartUser.data.datasets.length; i++) {

                this.lChartUser.data.datasets[i].data = [0, 0, 0, 0, 0, 0];

            }
            this.lChartUser.update();

        }

        if (this.fChartUser != null) {


            this.fChartUser.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.fChartUser.data.datasets.length; i++) {

                this.fChartUser.data.datasets[i].data = [0, 0];

            }
            this.fChartUser.update();

        }

        this.countMale = 0;
        this.countFemale = 0;
        this.countOther = 0;

        this.countAdmin = 0;
        this.countGeneral = 0;
        this.countPrueba = 0;
        this.countEnrolador = 0;
        this.countMaster = 0;

        this.countL1 = 0;
        this.countL2 = 0;
        this.countL3 = 0;
        this.countL4 = 0;
        this.countL5 = 0;
        this.countLO = 0;

        this.countPicOk = 0;
        this.countPicMissing = 0;


        this.showSpinners = true;
        this.usersData = [];
        this.usersDataPreFiltered = [];

        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = this.showNoData5 = false;

    }

    hideSpinners() {

        this.showSpinners = false;
        this.usersData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
    }



}

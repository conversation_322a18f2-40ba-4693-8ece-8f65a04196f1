import { After<PERSON><PERSON>w<PERSON><PERSON><PERSON>, Component, Element<PERSON>ef, HostListener, OnInit, Renderer2, ViewChild } from '@angular/core';
import { MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from "../../service/app.layout.service";
import { AuthService } from 'src/verazial-common-frontend/core/services/auth-service.service';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { TranslateService } from '@ngx-translate/core';
import { GetAccessByRoleIdUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles-access/get-access-by-role-id.use-case';
import { SubjectEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { Router } from '@angular/router';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { environment } from 'src/environments/environment';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { GetRawStaticResourceByIdUseCase } from 'src/verazial-common-frontend/core/general/storage/domain/use-cases/get-raw-static-resource-by-id.use-case';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { GetSettingsByApplicationUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import { TenantEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/tenant.entity';
import { GetAllTenantsUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/get-all-tenants.use-case';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { ChangeTenantUseCase } from 'src/verazial-common-frontend/core/general/auth/domain/use-cases/change-tenant.use-case';

@Component({
  selector: 'app-topbar',
  templateUrl: './app.topbar.component.html',
  providers: [MessageService]
})
export class AppTopBarComponent implements OnInit, AfterViewInit {

  items!: MenuItem[];

  @ViewChild('menubutton') menuButton!: ElementRef;

  @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;

  @ViewChild('topbarmenu') menu!: ElementRef;

  @ViewChild('userAvatar') userAvatar!: ElementRef;
  @ViewChild('userOptionsMenu') userOptionsMenu!: ElementRef;

  selectedRole: RoleEntity | undefined;
  showRoleSelection: boolean = false;

  subject: SubjectEntity | undefined;

  subjectRoles: RoleEntity[] | undefined;

  subjectId: string | undefined;

  roleName!: string;

  userOptionsMenuVisible = false;

  customTopBarLogo: boolean = false;
  customTopBarLogoSource: string = '';
  managerSettings: GeneralSettings | undefined;

  listOfTenants: TenantEntity[] = [];
  selectedTenant?: TenantEntity;
  showChangeTenant: boolean = false;
  selectedCurrentTenantTmp?: TenantEntity;
  adminToken?: string;

  /* Responsive */
  screenWidth: number = 0;
  screenHeight: number = 0;

  // HostListener to listen to screen resize events
  @HostListener('window:resize', ['$event'])
  onResize(event: Event) {
    this.updateScreenSize();
  }

  constructor(
    private localStorage: LocalStorageService,
    private authService: AuthService,
    public layoutService: LayoutService,
    private translate: TranslateService,
    private getAccessByRoleIdUseCase: GetAccessByRoleIdUseCase,
    private messageService: MessageService,
    private router: Router,
    private renderer: Renderer2,
    private checkPermissions: CheckPermissionsService,
    private loggerService: ConsoleLoggerService,
    private getRawStaticResourceByIdUseCase: GetRawStaticResourceByIdUseCase,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private getAllTenantsUseCase: GetAllTenantsUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private changeTenantUseCase: ChangeTenantUseCase,
  ) {
    this.renderer.listen('window', 'click', (e: Event) => {
      if (!this.userAvatar.nativeElement.contains(e.target)
        && this.userOptionsMenuVisible) {
        this.userOptionsMenuVisible = false;
      }
    });
  }

  ngOnInit(): void {
    this.subject = this.localStorage.getUser();
    this.subjectRoles = this.subject?.roles as RoleEntity[];
    this.subjectId = this.subject?.id;

    let role = this.localStorage.getRole();

    if (role) {
      this.roleName = this.localStorage.getRole()?.name as string;
    }

    this.items = [];
    this.items.push({
      user: `${this.localStorage.getUser().names} ${this.localStorage.getUser().lastNames}`,
      profile: `${this.roleName}`,
      type: "profile",
    });
    this.items.push({
      label: 'tenant.tenant',
      type: "option",
      icon: 'pi pi-cloud',
      command: () => {
        if (this.checkPermissions.hasReadAndWritePermissions(AccessIdentifier.CHANGE_TENANT)) {
          console.log("Change tenant");
          this.showChangeTenant = true;
        }
      }
    })
    if (this.checkPermissions.hasReadAndWritePermissions(AccessIdentifier.USER)) {
      this.items.push({
        label: 'manageProfile',
        type: "option",
        icon: 'pi pi-user-edit',
        command: () => {
          if (!this.disabledItem) this.router.navigate(['/general/user/edit', 'user', this.localStorage.getUser().numId]);
        }
      });
    }
    if (this.localStorage.getUser().roles.length > 1) {
      this.items.push({
        label: 'role.change_role',
        type: "option",
        icon: 'pi pi-arrow-right-arrow-left',
        command: () => {
          if (!this.disabledItem) {
            this.selectedRole = this.localStorage.getRole() ?? undefined;
            this.showRoleSelection = true;
          }
        }
      });
    }
    this.items.push({
      label: 'logout',
      type: "option",
      icon: 'pi pi-power-off',
      command: () => {
        if (!this.disabledItem) this.authService.logout();
      }
    });

    if(this.localStorage.getItem('admin_tenant_token')){
      this.adminToken = this.localStorage.getItem('admin_tenant_token')!;
    }
    else {
      this.localStorage.setItem('admin_tenant_token', this.localStorage.getToken()!);
      this.adminToken = this.localStorage.getItem('admin_tenant_token')!;
    }
    this.getAllTenants();

    let settings = this.localStorage.getSessionSettings();
    if (settings && settings.continued1 && settings.continued1.customPartnerLogo) {
      // this.loggerService.debug("Settings from local storage")
      // this.loggerService.debug(settings)
      this.managerSettings = settings;
      this.setCustomTopBarLogo();
    }
    else {
      // this.loggerService.debug("No settings in local storage")
      this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
        (data) => {
          if (data.settings) {
            this.managerSettings = data.settings;
            this.setCustomTopBarLogo();
          }
          else {
            this.loggerService.error("System Settings MS")
            this.loggerService.error("No Settings")
          }
        },
        (e) => {
          this.loggerService.error("System Settings MS")
          this.loggerService.error(e);
        },
      );
    }
  }

  getAllTenants(){
    this.getAllTenantsUseCase.execute({offset: 0, limit: 1000}).then(
      (data)=>{
        // console.log(data);
        if(data.length>0){
          this.localStorage.setItem('list_tenants', JSON.stringify(data));
          this.listOfTenants = data;
          this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
              if (data) {
                if (data.tenantId){

                  let currentTenant: string | null = this.localStorage.getItem('current_tenant');
                  if (currentTenant == null){
                    currentTenant = JSON.stringify(this.listOfTenants.find((v,k)=> v.id == data.tenantId));
                    this.localStorage.setItem('current_tenant', currentTenant);
                  }
                  let currentSelectedTenant = currentTenant == null ? this.listOfTenants.find((v,k)=> v.id == data.tenantId) : JSON.parse(currentTenant) as TenantEntity;
                  this.selectedTenant = this.listOfTenants.find((v,k)=> v.id == currentSelectedTenant?.id);
                  this.items[1].label = this.selectedTenant?.name;
                }
              }
            },
            error: (e) => {
              console.log(e);
            },
            complete: () => {
            }
          });
        }
      },
      (error)=>{ console.log(error)}
    )
  }

  onTenantChange(event: any) {
    this.selectedCurrentTenantTmp = event.value;
    this.changeTenant(event.value.id);
  }

  changeTenant(tenantId: string) {
    const tenantToken = this.localStorage.getItem('admin_tenant_token') ?? this.localStorage.getToken()!;
    this.changeTenantUseCase.execute({ newTenantId: tenantId, token: tenantToken }).then(
      (data) => {
        if (data) {
          this.localStorage.saveToken(data.token!);
          this.localStorage.setItem('current_tenant', JSON.stringify(this.selectedCurrentTenantTmp));
          window.location.reload();
        }
      },
      (error) => {
        console.log(error)
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant("titles.error_change_tenant"),
          detail: `${this.translate.instant("messages.error_change_tenant")}`
        })
      },
    ).finally(() => {
      this.showChangeTenant = false;
    });
  }

  onResetTenant() {
    this.getKonektorPropertiesUseCase.execute().subscribe({
      next: (data) => {
        if (data) {
          if (data.tenantId) {
            this.selectedCurrentTenantTmp = this.listOfTenants.find((v, k) => v.id == data.tenantId);
            this.changeTenant(data.tenantId);
          }
        }
      },
      error: (e) => {
        console.log(e);
      },
      complete: () => {
      }
    });
  }

  get disabledItem() {
    return this.localStorage.getLockMenu();
  }

  ngAfterViewInit() {
    this.setMenuPosition();
  }

  updateScreenSize(): void {
    this.screenWidth = window.innerWidth;
    this.screenHeight = window.innerHeight;
    this.applyResponsiveLogic();
  }

  // Function to add responsive logic
  applyResponsiveLogic(): void {
    this.layoutService.onMenuResponsive();
  }

  onCancel() {
    this.showRoleSelection = false;
  }

  onAccept(role: RoleEntity | string) {
    let selectedRole!: RoleEntity;
    if (typeof role === "string") {
      let subjectRoles = this.localStorage.getUser().roles as RoleEntity[];
      let roleFilter = subjectRoles.find((v, k) => v.id == Number(role));
      if (roleFilter) {
        selectedRole = roleFilter;
      }
    } else {
      selectedRole = role;
    }
    this.getRoleAccesses(selectedRole)
    this.showRoleSelection = false;
  }

  /**
*
* @param roleId Get all subject's accesses by role ID
*/
  async getRoleAccesses(role: RoleEntity) {
    this.getAccessByRoleIdUseCase.execute({ roleId: role.id as number }).then(
      (data) => {
        // let accesses = data.filter((v,k) => v.application == environment.application || v.application == 'ALL' );
        let accesses = data;
        this.localStorage.saveRole(role);
        this.localStorage.saveAccesses(accesses);
        window.location.reload();
      },
      (e) => {
        this.loggerService.error(e);
        this.messageService.add(
          {
            severity: 'error',
            summary: this.translate.instant("titles.error_role_access"),
            detail: `${this.translate.instant("messages.error_getting_access")}`
          },
        );
      }
    )
  }

  setMenuPosition() {
    if (this.userOptionsMenuVisible && this.userAvatar && this.userOptionsMenu) {
      const buttonRect = this.userAvatar.nativeElement.getBoundingClientRect();
      const menuRect = this.userOptionsMenu.nativeElement.getBoundingClientRect();

      // Calculate top and left based on the viewport position, considering 'fixed' positioning
      const top = buttonRect.bottom;  // Directly below the button
      const left = buttonRect.right - menuRect.width;  // Align to the right edge of the button

      // Apply fixed positioning to the menu
      this.renderer.setStyle(this.userOptionsMenu.nativeElement, 'top', `${top}px`);
      this.renderer.setStyle(this.userOptionsMenu.nativeElement, 'left', `${left}px`);
      this.renderer.setStyle(this.userOptionsMenu.nativeElement, 'position', 'fixed');  // Ensure fixed position
      this.renderer.setStyle(this.userOptionsMenu.nativeElement, 'opacity', '1');
    }
  }

  toggleMenu() {
    this.userOptionsMenuVisible = !this.userOptionsMenuVisible;

    if (this.userOptionsMenuVisible) {
      setTimeout(() => this.setMenuPosition(), 0); // Ensure it's done after view update
    }
  }

  setCustomTopBarLogo() {
    let settings = this.managerSettings;
    if (settings?.continued1?.customPartnerLogo.customTopBarLogoEnabled) {
      const id = settings?.continued1?.customPartnerLogo?.customTopBarLogoRawStaticResourceId;
      if (id != "" && id != null && id != undefined) {
        let localStorageLogo = this.localStorage.getItem('customTopBarLogo');
        if (localStorageLogo) {
          let customTopBarLogoRSR = JSON.parse(localStorageLogo);
          if (customTopBarLogoRSR.id == settings?.continued1?.customPartnerLogo.customTopBarLogoRawStaticResourceId) {
            this.customTopBarLogo = true;
            this.customTopBarLogoSource = customTopBarLogoRSR.content;
          }
          else {
            // this.loggerService.debug("Custom top bar logo id in local storage does not match the one in the settings");
            this.customTopBarLogo = false;
            this.customTopBarLogoSource = '';
            this.localStorage.setItem('customTopBarLogo', '');
            this.getCustomTopBarLogo();
          }
        }
        else {
          //this.loggerService.debug("No custom top bar logo found in local storage");
          this.customTopBarLogo = false;
          this.customTopBarLogoSource = '';
          this.localStorage.setItem('customTopBarLogo', '');
          this.getCustomTopBarLogo();
        }
      }
      else {
        //this.loggerService.debug("No custom top bar logo id found");
      }
    }
    else {
      //this.loggerService.debug("Custom top bar logo is disabled");
      this.customTopBarLogo = false;
      this.customTopBarLogoSource = '';
      this.localStorage.setItem('customTopBarLogo', '');
    }
  }

  getCustomTopBarLogo() {
    const id = this.managerSettings?.continued1?.customPartnerLogo?.customTopBarLogoRawStaticResourceId;
    const type = this.managerSettings?.continued1?.customPartnerLogo?.customTopBarLogoType;
    if (id != "" && id != null && id != undefined) {
      this.getRawStaticResourceByIdUseCase.execute({ id: id }).then(
        (data) => {
          this.loggerService.debug("Obtained the raw static resource with id: " + id);
          this.loggerService.debug(data);
          if (data && data.content) {
            this.customTopBarLogo = true;
            this.customTopBarLogoSource = 'data:' + type + ';base64,' + data.content;
            let topBarLogo = data;
            topBarLogo.content = this.customTopBarLogoSource;
            this.localStorage.setItem('customTopBarLogo', JSON.stringify(topBarLogo));
          }
        },
        (e) => {
          this.loggerService.error("Error obtaining the raw static resource with id: " + id);
          this.loggerService.error(e);
          this.customTopBarLogo = false;
          this.customTopBarLogoSource = '';
          this.localStorage.setItem('customTopBarLogo', '');
        }
      );
    }
    else {
      //this.loggerService.debug("No custom top bar logo id found");
    }
  }
}
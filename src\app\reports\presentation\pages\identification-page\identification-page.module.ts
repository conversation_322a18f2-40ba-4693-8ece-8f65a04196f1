import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { IdentificationPageRoutingModule } from './identification-page-routing.module';
import { IdentificationPageComponent } from './identification-page/identification-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { PaginatorModule } from 'primeng/paginator';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { DiLicenseModule } from 'src/verazial-common-frontend/core/general/license/data/di-license.module';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
  declarations: [
        IdentificationPageComponent
  ],
  imports: [
    CommonModule,
    IdentificationPageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    ProgressSpinnerModule,
    DropdownModule,
    PaginatorModule,
    ProgressBarModule,
    SkeletonModule,
    DiLicenseModule,
    InputTextModule,

  ],
  exports: [
    IdentificationPageComponent
  ]
})
export class IdentificationPageModule { }

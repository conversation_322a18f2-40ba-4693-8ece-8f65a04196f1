import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationWindowEntity } from "../entities/application-window.entity";

export abstract class ApplicationWindowRepository {
    abstract addAppWindow(applicationWindow: ApplicationWindowEntity): Promise<ApplicationWindowEntity>;
    abstract updateAppWindowById(applicationWindow: ApplicationWindowEntity): Promise<SuccessResponse>;
    abstract getAppWindowById(params: { id: string, windowOrder: number }): Promise<ApplicationWindowEntity>;
    abstract getAppWindowByAppId(params: { appId: string }): Promise<ApplicationWindowEntity[]>;
    abstract deleteAppWindowById(params: { id: string, order: number }): Promise<SuccessResponse>;
}
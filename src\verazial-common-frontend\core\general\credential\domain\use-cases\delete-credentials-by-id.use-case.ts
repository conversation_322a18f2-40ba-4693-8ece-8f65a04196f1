import { Observable } from "rxjs";
import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialRepository } from "../repositories/credential.repository";

export class DeleteCredentialsByIdUseCase implements UseCase<{ id: string }, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: { id: string; }): Observable<any> {
        return this.credentialsRepository.deleteCredentialsById(params);
    }

}
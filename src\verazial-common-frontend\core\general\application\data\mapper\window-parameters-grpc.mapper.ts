import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { WindowParametersEntity } from "../../domain/entities/window-parameters.entity";
import { WindowParametersGrpcModel } from "src/verazial-common-frontend/core/generated/application/parameters_pb";
import { PassListEvents } from "src/verazial-common-frontend/core/models/pass-list-events.enum";

export class WindowParametersGrpcMapper extends Mapper<WindowParametersGrpcModel, WindowParametersEntity> {
    override mapFrom(param: WindowParametersGrpcModel): WindowParametersEntity {
        return {
            id: param.getId(),
            applicationWindowId: param.getApplicationwindowid(),
            displayName: param.getDisplayname(),
            name: param.getName(),
            windowOrder: param.getWindoworder(),
            attributeName: param.getAttributename(),
            uiComponentType: param.getUicomponenttype(),
            componentPosition: param.getComponentposition(),
            triggerOrder: param.getTriggerorder(),
            event: (Object.values(PassListEvents) as string[]).includes(param.getEvent())? param.getEvent() as PassListEvents : undefined,
            showComponentToUser: param.getShowcomponenttouser(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }
    override mapTo(param: WindowParametersEntity): WindowParametersGrpcModel {
        let model = new WindowParametersGrpcModel();
        model.setId(param.id!!);
        model.setApplicationwindowid(param.applicationWindowId!!);
        model.setDisplayname(param.displayName!!);
        model.setName(param.name!!);
        model.setWindoworder(param.windowOrder!!);
        model.setAttributename(param.attributeName!!);
        model.setUicomponenttype(param.uiComponentType!!);
        model.setComponentposition(param.componentPosition!!);
        model.setTriggerorder(param.triggerOrder!!);
        model.setEvent(param.event!!);
        model.setShowcomponenttouser(param.showComponentToUser!!);
        return model;
    }

}
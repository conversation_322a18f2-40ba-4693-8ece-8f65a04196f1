import { Action, NewAction } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { NewActionEntity } from "../entity/new-action.entity";
import { ActionsV2Repository } from "../repository/actionsV2.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ActionEntity } from "../entity/action.entity";


export class RegisterActionUseCase implements UseCaseGrpc<{ newAction: NewAction, token?: string },ActionEntity>{
    constructor(private actionsV2Repository: ActionsV2Repository){}
    execute(params: { newAction: NewAction, token?: string }): Promise<ActionEntity> {
        return this.actionsV2Repository.registerAction(params);
    }
}
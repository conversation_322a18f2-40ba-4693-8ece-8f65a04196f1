import { ApplicationEntity } from "../entities/application.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export abstract class ApplicationRepository {
    abstract addApplications(applications: ApplicationEntity[]): Promise<ApplicationEntity[]>;
    abstract updateApplicationById(application: ApplicationEntity): Promise<ApplicationEntity>;
    abstract updateApplicationStatusById(params: { id: string, status: boolean }): Promise<SuccessResponse>;
    abstract updateAllAppsDataSource(params: { dataSourceId: string, newDataSource: string }): Promise<SuccessResponse>;
    abstract getApplicationById(params: { id: string }): Promise<ApplicationEntity>;
    abstract getApplicationByApplicationName(params: { name: string }): Promise<ApplicationEntity>;
    abstract getAllApplications(): Promise<ApplicationEntity[]>;
    abstract getAllActiveApplications(): Promise<ApplicationEntity[]>;
    abstract deleteApplicationById(params: { id: string }): Promise<SuccessResponse>;
}
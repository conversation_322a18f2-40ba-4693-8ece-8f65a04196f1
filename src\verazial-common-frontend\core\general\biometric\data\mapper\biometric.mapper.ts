
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ReplaceAttributesRequestEntity } from "../../domain/entity/replace-attributes-request.entity";
import { ReplaceAttributesRequest } from "src/verazial-common-frontend/core/generated/api-binder/ms-biometric/biometric_pb";

export class BiometricMapper extends Mapper<ReplaceAttributesRequest, ReplaceAttributesRequestEntity> {
    override mapFrom(param: ReplaceAttributesRequest): ReplaceAttributesRequestEntity {
        let biometric = new ReplaceAttributesRequestEntity("",[]);
        return biometric;
    }
    override mapTo(param: ReplaceAttributesRequestEntity): ReplaceAttributesRequest { 
        const request = new ReplaceAttributesRequest();
        request.setSubjectid(param.subjectId);
        request.setAddnewmissinginold(true);
        request.setRemoveoldmissinginnew(false);
        
        const attributes = param.attributes.map(attr => {
            const biometricAttr = new ReplaceAttributesRequest.BiometricAttribute();
            biometricAttr.setName(attr.name);
            biometricAttr.setValue(attr.value);
            return biometricAttr;
        });
    
        request.setAttributesList(attributes);
        return request;
    }
    
}
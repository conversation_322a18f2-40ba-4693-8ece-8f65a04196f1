import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AccessEntity } from 'src/verazial-common-frontend/core/general/access/domain/entity/access.entity';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { RoleAccessResponseEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-access-response.entity';

@Component({
  selector: 'app-access-assignment',
  templateUrl: './access-assignment.component.html',
  styleUrl: './access-assignment.component.css'
})
export class AccessAssignmentComponent implements OnInit, OnDestroy{
  @Input() roleData: RoleEntity | undefined;
  @Input() listOfAccesses: AccessEntity[] = [];
  @Input() listOfSelectedRoleAccesses: RoleAccessResponseEntity[] = [];
  @Output() roleAccesses = new EventEmitter<RoleAccessResponseEntity[]>();
  @Output() remove = new EventEmitter<RoleAccessResponseEntity[]>();
   
  sourceListRoleAccess: RoleAccessResponseEntity[] = [];
  targetListRoleAccess: RoleAccessResponseEntity[] = [];

  originalListOfSelectedRoles: RoleAccessResponseEntity[] = [];

  constructor(
    private fb: FormBuilder
  ){}

  ngOnDestroy(): void {
    this.listOfAccesses = [];
    this.listOfSelectedRoleAccesses = [];
    this.sourceListRoleAccess = [];
    this.targetListRoleAccess = [];
  }
  
  ngOnInit(): void {
    if(this.listOfSelectedRoleAccesses.length == 0){
      this.listOfAccesses.forEach(access => {
        this.sourceListRoleAccess.push({
          id: undefined,
          roleId : this.roleData?.id,
          accessId: access.id,
          name: access.name,
          application: access.application,
          accessCode: access.accessCode,
          type: access.type,
          path: access.path,
          read: true,
          write: true
        });
      });
      // this.sourceListRoleAccess = this.listOfAccesses;
    }else{
      this.originalListOfSelectedRoles = this.listOfSelectedRoleAccesses.map(item => ({ ...item }));

      this.targetListRoleAccess = this.listOfSelectedRoleAccesses.map(item => ({ ...item }));

      const filteredList = this.listOfAccesses.filter(access => 
        !this.listOfSelectedRoleAccesses.some(selectedAccess => access.id === selectedAccess.accessId)
      );

      filteredList.forEach(access => {
        this.sourceListRoleAccess.push({
          id: undefined,
          roleId : this.roleData?.id,
          accessId: access.id,
          name: access.name,
          application: access.application,
          accessCode: access.accessCode,
          type: access.type,
          path: access.path,
          read: true,
          write: true
        });
      });
      // this.sourceListRoleAccess = filteredList;
    }
  }

  public form: FormGroup = this.fb.group({
    readWrite: []
  });

  // Track changes in the target acccess
  targetTrackChanges(){
    // Getting all removed accesses
    const accessesToBeRemoved = this.originalListOfSelectedRoles.filter(access => 
      !this.targetListRoleAccess.some(selectedAccess => access.accessId === selectedAccess.accessId)
    );

    // Getting all new accesses added
    const updatedAccess = this.targetListRoleAccess.map(target => {
      const isNewElement = !this.originalListOfSelectedRoles.some(original => original.accessId === target.accessId);
      if (isNewElement) {
        return { ...target, id: undefined, roleId: this.roleData?.id };
      }
      return target;
    });

    this.roleAccesses.emit(updatedAccess);

    if(accessesToBeRemoved.length > 0){
      this.remove.emit(accessesToBeRemoved);
    }

  }
}

<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<p-toast/>
<app-list-user-subject
    [isLoading]="isLoading"
    [type]="type"
    [lazyLoad]="useLazyLoad"
    [readAndWritePermissions]="canReadAndWrite"
    [readOnly]="readOnly"
    [userIsVerified]="userIsVerified"
    [listOfUsersSubjects]="listOfSubjects"
    [totalRecords]="totalRecords"
    [offset]="getSubjectsRequest.offset"
    [limit]="getSubjectsRequest.limit"
    [allRoles]="listRoles"
    [specificRole]="roleId"
    [managerSettings]="managerSettings"
    [konektorProperties]="konektorProperties"
    (onAdd)="onSubmitAddNewSubject($event)"
    (onMainAction)="onEditSubject($event)"
    (onSecondaryAction)="confirmDelete($event)"
    (onBioSearch)="navigateToSubject($event)"
    (onTableLazyLoadEvent)="onTableLazyLoadEvent($event)"
    (userVerified)="userVerified($event)"
></app-list-user-subject>
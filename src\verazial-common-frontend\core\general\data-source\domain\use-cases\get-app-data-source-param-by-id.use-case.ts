import { DataSourceParametersRepository } from "../repositories/data-source-parameters.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { DataSourceParametersEntity } from "../entities/data-source-parameters.entity";

export class GetAppDataSourceParamByIdUseCase implements UseCaseGrpc<{ id: string }, DataSourceParametersEntity> {
    constructor(private dataSourceParamsRepository: DataSourceParametersRepository) { }
    execute(params: { id: string; }): Promise<DataSourceParametersEntity> {
        return this.dataSourceParamsRepository.getAppDataSourceParamById(params);
    }
}
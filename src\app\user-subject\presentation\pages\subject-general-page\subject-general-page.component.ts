import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Route, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, FilterMetadata } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { UnenrollIdentityUseCase } from 'src/verazial-common-frontend/core/general/api-binder/domain/use-cases/unenroll-identity.use-case';
import { CheckTokenUseCase } from 'src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { KonektorPropertiesEntity } from 'src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { GetSettingsByApplicationUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { GetAllRolesUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import { CreateStaticResourceEntity } from 'src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity';
import { CreateStaticResourceUseCase } from 'src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case';
import { GetStaticResourcesBySubjectIdAndNameUseCase } from 'src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case';
import { SubjectEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { DeleteSubjectByIdUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/delete-subject-by-id.use-case';
import { SaveSubjectUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject.use-case';
import { GetUserByNumIdUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-user-by-num-id.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { UserSubjectActionType } from 'src/verazial-common-frontend/core/models/user-subject-action-type.enum';
import { UserSubjectEnum } from 'src/verazial-common-frontend/core/models/user-subject.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { environment } from 'src/environments/environment';
import { GetSubjectsUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subjects.use-case';
import { GetSubjectsRequestEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/get-subjects-request.entity';
import { SortOrderEnum } from 'src/verazial-common-frontend/core/models/sort-order.enum';
import { GetNumberOfSubjectsUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-number-of-subjects.use-case';
import { GetAllSubjectsUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case';
import { FilterConditionType, FilterEntity } from 'src/verazial-common-frontend/core/models/filter.entity';
import { GetSubjectByNumIdUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case';
import { NewLocationsService } from 'src/verazial-common-frontend/core/services/new-locations.service';

@Component({
  selector: 'app-subject-general-page',
  templateUrl: './subject-general-page.component.html',
  styleUrl: './subject-general-page.component.css',
  providers: [MessageService]
})
export class SubjectGeneralPageComponent implements OnInit, OnDestroy {

  /* Page State */
  type = UserSubjectEnum.SUBJECT;

  /* Settings */
  managerSettings?: GeneralSettings;
  konektorProperties?: KonektorPropertiesEntity;

  /* Flags */
  isLoading: boolean = false;

  /* New Subject */
  subjectData: SubjectEntity | undefined;
  profilePicPlaceholder: string = 'verazial-common-frontend/assets/images/all/UserPic.svg';
  picHistoryName: string = 'profile-picture-history';

  /* List of subjects data */
  listOfSubjects: SubjectEntity[] = [];
  getSubjectsRequest = new GetSubjectsRequestEntity();
  totalRecords: number = 0;
  listRoles: RoleEntity[] = [];
  roleId?: number = undefined;
  useLazyLoad: boolean = false;

  /* User in Session */
  userIsVerified: boolean = false;

  /* Access */
  access_identifier: string = AccessIdentifier.SUBJECT;
  canReadAndWrite: boolean = false;
  readOnly: boolean = false;

  private rejectTimeout: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private translate: TranslateService,
    private checkPermissions: CheckPermissionsService,
    private getSubjectsUseCase: GetSubjectsUseCase,
    private saveSubjectUseCase: SaveSubjectUseCase,
    private deleteSubjectByIdUseCase: DeleteSubjectByIdUseCase,
    private auditTrailService: AuditTrailService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private getAllRolesUseCase: GetAllRolesUseCase,
    private checkTokenUseCase: CheckTokenUseCase,
    private getUserByNumIdUseCase: GetUserByNumIdUseCase,
    private unenrollIdentityUseCase: UnenrollIdentityUseCase,
    private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
    private createStaticResourceUseCase: CreateStaticResourceUseCase,
    private getNumberOfSubjectsUseCase: GetNumberOfSubjectsUseCase,
    private getAllSubjectsUseCase: GetAllSubjectsUseCase,
    private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
    private newLocationsService: NewLocationsService
  ) { }

  async ngOnInit() {
    this.isLoading = true;
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
    if(!this.canReadAndWrite) {
      this.readOnly = this.checkPermissions.hasReadPermissions(this.access_identifier);
    }

    this.refreshSettings(() => {
      this.route.params.subscribe(params => {
        this.getAllRoles(() => {
          this.getNumberOfSubjectsUseCase.execute().then(
            (data) => {
              this.useLazyLoad = data > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000);
              this.totalRecords = data;
              this.getSubjectsRequest.offset = 0;
              this.getSubjectsRequest.limit = 10;
              let roleIdString = params['roleId'];
              if (roleIdString) {
                this.roleId = parseInt(roleIdString);
              }
              else {
                this.roleId = undefined;
              }
              if (this.useLazyLoad) {
                this.getSubjectsRequest.sortField = 'numId';
                this.getSubjectsRequest.sortOrder = SortOrderEnum.ASC;
                let filters: FilterEntity[] = [];
                if (this.roleId) {
                  filters.push({
                    condition: {
                      type: FilterConditionType.EQUALS,
                      field: 'defaultRole',
                      value: this.roleId.toString()
                    }
                  });
                }
                this.getSubjectsRequest.filters = filters;
              }
              this.getAllSubjects();
            },
            (e) => {
              this.loggerService.error(e);
              const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_NUMBER_OF_SUBJECTS, 0, 'ERROR', '', at_attributes);
            }
          );
        });
      });
    });
    if (this.localStorageService.isUserVerified()) {
      await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
        (response) => {
          this.userIsVerified = !response;
        },
        (e) => {
          this.userIsVerified = false;
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.CHECK_TOKEN, 0, 'ERROR', '', at_attributes);
        }
      );
    }
    else {
      this.userIsVerified = false;
    }
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.clearRejectTimeout();
  }

  // Get all subjects
  getAllSubjects() {
    this.isLoading = true;
    if (this.useLazyLoad) {
      this.getSubjectsUseCase.execute(this.getSubjectsRequest).then(
        (data) => {
          this.loggerService.debug(data);
          let subjects = data?.subjects ? [...data.subjects.filter(v => v.email != "" || v.email != null)] : [];
          this.listOfSubjects = subjects;
          this.totalRecords = data.totalRecords!;
        },
        (e) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: `${this.translate.instant('messages.error_retrieving_subjects')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_SUBJECTS, 0, 'ERROR', '', at_attributes);
        }
      )
      .finally(() => {
        this.isLoading = false;
      });
    }
    else {
      this.getNumberOfSubjectsUseCase.execute().then(
        (data) => {
          this.useLazyLoad = data > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000);
          this.totalRecords = data;
        },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_NUMBER_OF_SUBJECTS, 0, 'ERROR', '', at_attributes);
        }
      ).finally(() => {
        this.getAllSubjectsUseCase.execute({ offset: 0, limit: (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000) }).then(
          (data) => {
            this.loggerService.debug(data);
            let subjects = data ? [...data.filter(v => v.email != "" || v.email != null)] : [];
            this.listOfSubjects = subjects;
            if (this.roleId != undefined) {
              this.listOfSubjects = this.listOfSubjects.filter(v => v.defaultRole == this.roleId);
            }
          },
          (e) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: `${this.translate.instant('messages.error_retrieving_subjects')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_SUBJECTS, 0, 'ERROR', '', at_attributes);
          }
        ).finally(() => {
          this.isLoading = false;
        });
      });
    }
  }

  onTableLazyLoadEvent(event: GetSubjectsRequestEntity) {
    this.getSubjectsRequest = event;
    this.getAllSubjects();
  }

  getAllRoles(_callback: Function) {
    this.getAllRolesUseCase.execute().then(
      (data) => {
        data.forEach(role => {
          if (role.type == RoleType.SUBJECT) {
            this.listRoles.push({
              id: role.id,
              name: role.name == 'SYSTEM_USER' ? this.translate.instant('role_names.SYSTEM_USER') : role.name,
              level: role.level,
              type: role.type,
              description: role.description,
              showInMenu: role.showInMenu,
              createdAt: undefined,
              updatedAt: undefined,
            });
          }
        });
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      },
    )
    .finally(() => {
      _callback();
    });
  }

  onSubmitAddNewSubject(subject: SubjectEntity) {
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(subject) },
        { name: AuditTrailFields.SUBJECT_NUM_ID, value: subject.numId },
        { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(subject.roles) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.ADD_SUB, ReasonActionTypeEnum.CREATE, () => { this.onAddNewSubject(subject); }, at_attributes);
  }

  // Adding a new subject
  onAddNewSubject(subject: SubjectEntity) {
    subject!.showPic = subject?.pic != null && subject?.pic != undefined && subject?.pic != "" && subject?.pic != this.profilePicPlaceholder;
    this.saveSubjectUseCase.execute({ subject: subject }).then(
      (data) => {
        // this.listOfSubjects.push(data);
        this.listOfSubjects = [...this.listOfSubjects, data];
        if (subject.showPic) {
          this.updateUserSubjectPicHistory(data, subject.pic!);
        }
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        let errorMessage = 'messages.error_creating_subject';
        if (e.code == 2000) errorMessage = 'messages.duplicate_subject_numId';
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant(errorMessage)}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.ERROR_MESSAGE, value: this.translate.instant(errorMessage) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(subject) },
          { name: AuditTrailFields.SUBJECT_NUM_ID, value: subject.numId },
          { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(subject.roles) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUB, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  updateUserSubjectPicHistory(userSubject: SubjectEntity, pic: string) {
    this.isLoading = true;
    if (pic != null && pic != undefined && pic != "" && pic != this.profilePicPlaceholder) {
      this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: userSubject?.id!, name: this.picHistoryName }).then(
        (data) => {
          if (data) {
            const number = data.length;
            let newStaticResource = new CreateStaticResourceEntity();
            // subjectId?: string;
            newStaticResource.subjectId = userSubject?.id!;
            // name?: string;
            newStaticResource.name = this.picHistoryName;
            // number?: number;
            newStaticResource.number = number;
            // content?: string;
            newStaticResource.content = pic.replace('data:image/jpeg;base64,', '');
            // async?: boolean;
            newStaticResource.async = false;
            this.createStaticResourceUseCase.execute({ createStaticResourceRequest: newStaticResource }).then(
              () => {
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                  { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                  { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                this.messageService.add({
                  severity: 'success',
                  summary: this.translate.instant('content.successTitle'),
                  detail: this.translate.instant('messages.success_general'),
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.isLoading = false;
              },
              (e) => {
                this.isLoading = false;
                this.loggerService.error('Error Creating User/Subject Static Resource:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                  { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                  { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                  severity: 'error',
                  summary: this.translate.instant('content.errorTitle'),
                  detail: `${this.translate.instant('messages.error_uploading_image')}: ${e.message}`,
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              }
            );
          }
          else {
            this.isLoading = false;
          }
        },
        (e) => {
          this.isLoading = false;
          this.loggerService.error('Error Getting User/Subject Static Resources:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.SUBJECT_ID, value: userSubject?.id! },
            { name: AuditTrailFields.RECORD_NAME, value: this.picHistoryName }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: `${this.translate.instant('messages.error_downloading_image')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      )
    }
    else {
      this.isLoading = false;
    }
  }

  onEditSubject(subject: SubjectEntity) {
    this.router.navigate(['/general/subject/edit', 'subject', subject?.numId]);
  }

  navigateToSubject(numId: string) {
    this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
      (data) => {
        if (data) {
          this.router.navigate(['general/subject/edit', 'subject', data.numId], { state: { verified: true } });
        }
        else {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('titles.error_dataInconsistency'),
            detail: this.translate.instant('messages.error_dataInconsistency'),
            life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
          });
        }
      },
      (e) => {
        this.loggerService.error('Error Retrieving Subject Data:');
        this.loggerService.error(e);
        let actionResult: string;
        let messageSummary: string;
        let messageDetail: string;
        if (e.code == 404) {
          actionResult = 'DATA_INCONSISTENCY';
          messageSummary = 'titles.error_dataInconsistency';
          messageDetail = 'messages.error_dataInconsistency';
        }
        else {
          actionResult = 'ERROR';
          messageSummary = 'content.errorTitle';
          messageDetail = 'messages.error_retrieving_subject';
        }
        const at_attributes: ExtraData[] = [
          {
            name: 'error', value: JSON.stringify(e)
          },
          {
            name: 'numId', value: numId
          }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_NUM_ID, 0, actionResult, '', at_attributes);
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant(messageSummary),
          detail: `${this.translate.instant(messageDetail)}: ${e.message}`,
          life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  // Confirmation Dialogs
  confirmDelete(subjects: SubjectEntity[]) {
    let message: string = ""
    if (subjects.length == 1) {
      message = `${this.translate.instant('messages.delete_single_record')} <b>${subjects[0].numId}</b>?`;
    } else {
      message = this.translate.instant('messages.delete_multiple_records');
    }
    this.confirmationService.confirm({
      message: message,
      header: this.translate.instant('messages.delete_confirmation_header'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptIcon: "none",
      rejectIcon: "none",
      acceptLabel: this.translate.instant("delete"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(subjects) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_SUB, ReasonActionTypeEnum.DELETE, () => { this.onDeleteSubject(subjects); }, at_attributes);
      },
      reject: () => {
        this.clearRejectTimeout();
      }
    });

    // Set a timeout to automatically trigger the reject action after 10 seconds
    this.rejectTimeout = setTimeout(() => {
      this.confirmationService.close(); // Close the dialog
    }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
  }

  // Removing multiple subjects from the DB
  onDeleteSubject(subjects: SubjectEntity[]) {
    this.isLoading = true;

    // Create an array of promises
    const unenrollPromises = subjects.map(subject => this.unenrollIdentity(subject));

    // Wait for all unenrollIdentity calls to complete
    Promise.all(unenrollPromises)
      .then(() => {
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      })
      .catch(error => {
        this.loggerService.error('Error while deleting subjects:');
        this.loggerService.error(error);
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  async unenrollIdentity(subject: SubjectEntity) {
    try {
      const user = await this.getUserByNumIdUseCase.execute({ numId: subject.numId! }).catch(e => {
        if (e.code !== 404) {
          this.loggerService.error('Error getting user by id');
          this.loggerService.error(e);
        }
        return undefined; // Handle user not found case
      });

      if (user) {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('titles.unable_to_delete_subject') + ": " + user.numId,
          detail: this.translate.instant('messages.error_subject_has_user') + ": " + user.email + " - " + user.names + " " + user.lastNames,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });

        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: this.translate.instant('titles.unable_to_delete_subject') + ": " + user.numId },
          { name: AuditTrailFields.ERROR_MESSAGE, value: this.translate.instant('messages.error_subject_has_user') + ": " + user.email + " - " + user.names + " " + user.lastNames },
          { name: AuditTrailFields.SUBJECT_NUM_ID, value: subject.numId },
          { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(subject.roles) },
          { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
          { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(user.roles) },
        ];

        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_SUB, 0, 'ERROR', '', at_attributes);

        return; // Exit function early since subject cannot be deleted
      }

      // Unenroll identity
      await this.unenrollIdentityUseCase.execute({ numId: subject.numId! });
      this.loggerService.info('Biometric identity unenrolled');

      // Delete subject
      await this.deleteSubjectByIdUseCase.execute({ id: subject.id! });

      this.listOfSubjects = this.listOfSubjects.filter(v => v.id !== subject.id);

      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(subject) },
        { name: AuditTrailFields.SUBJECT_NUM_ID, value: subject.numId },
        { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(subject.roles) },
      ];

      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_SUB, 0, 'SUCCESS', '', at_attributes);

    } catch (error) {
      this.loggerService.error('Error processing subject deletion:');
      this.loggerService.error(error!);
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: `${this.translate.instant('messages.error_processing_subject_deletion')}: ${error}`,
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });

      const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
      this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.UNENROLL_IDENTITY, 0, 'ERROR', '', at_attributes);
    }
  }

  settingsRetry: boolean = false;
  refreshSettings(_callback: Function) {
    this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
      (data) => {
        if (data.isActive && data.settings) {
          const settings = data.settings;
          this.localStorageService.setSessionSettings(settings);
          this.managerSettings = settings;
          // console.log("new Locations");
          // console.log(this.managerSettings?.continued1?.newLocations!);
          this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
              if (data) {
                this.konektorProperties = data;
              }
              _callback();
            },
            error: (e) => {
              this.loggerService.error('Error Retrieving Konektor Properties:');
              this.loggerService.error(e);
              const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_KONEKTOR_PROPERTIES, 0, 'ERROR', '', at_attributes);
            }
          });
        }
      },
      (e) => {
        this.loggerService.error('Error Retrieving Manager Settings:');
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.APPLICATION_ID, value: environment.application },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
        if (!this.settingsRetry) {
          this.settingsRetry = true;
          this.refreshSettings(_callback);
        }
        else {
          this.settingsRetry = false;
        }
      },
    );
  }

  userVerified(verified: boolean) {
    this.userIsVerified = verified;
  }

  updateModified(modified: boolean) {
    this.localStorageService.setLockMenu(modified);
  }

  private clearRejectTimeout() {
    if (this.rejectTimeout) {
      clearTimeout(this.rejectTimeout);
    }
    this.rejectTimeout = null;
  }
}
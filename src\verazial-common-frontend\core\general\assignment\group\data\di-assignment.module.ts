import { HttpClientModule } from "@angular/common/http";
import { AssignmentRespository } from "../domain/repository/assignment.repository";
import { CreateAssignmentUseCase } from "../domain/use-cases/create-assignment.use-case";
import { DeleteAssignmentByIdUseCase } from "../domain/use-cases/delete-assignment-by-id.use-case";
import { GetAllAssignmentsUseCase } from "../domain/use-cases/get-all-assignments.use-case";
import { GetAssignmentByIdUseCase } from "../domain/use-cases/get-assignment-by-id.use-case";
import { UpdateAssignmentUseCase } from "../domain/use-cases/update-assignment.use-case";
import { AssignmentRepositoryImpl } from "./repository-imp/assignment-impl.repository";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { AddAssignmentElementUseCase } from "../domain/use-cases/add-assignment-element.use-case";
import { DeleteAssignmentElementByIdUseCase } from "../domain/use-cases/delete-assignment-element-by-id.use-case";
import { SearchAssignmentByUseCase } from "../domain/use-cases/search-assignment-by.use-case";

const createAssignmentUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new CreateAssignmentUseCase(assignmentRepository);

const getAllAssignmentsUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new GetAllAssignmentsUseCase(assignmentRepository);

const getAssignmentByIdUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new GetAssignmentByIdUseCase(assignmentRepository);

const deleteAssignmentByIdUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new DeleteAssignmentByIdUseCase(assignmentRepository);

const updateAssignmentUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new UpdateAssignmentUseCase(assignmentRepository);

const addAssignmentElementUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new AddAssignmentElementUseCase(assignmentRepository);

const deleteAssignmentElementByIdUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new DeleteAssignmentElementByIdUseCase(assignmentRepository);

const searchAssignmentByUseCaseFactory =
    (assignmentRepository: AssignmentRespository) => new SearchAssignmentByUseCase(assignmentRepository);


export const createAssignmentUseCaseProvider = {
    provide: CreateAssignmentUseCase,
    useFactory: createAssignmentUseCaseFactory,
    deps: [AssignmentRespository]
}

export const getAllAssignmentsUseCaseProvider = {
    provide: GetAllAssignmentsUseCase,
    useFactory: getAllAssignmentsUseCaseFactory,
    deps: [AssignmentRespository]
}

export const getAssignmentByIdUseCaseProvider = {
    provide: GetAssignmentByIdUseCase,
    useFactory: getAssignmentByIdUseCaseFactory,
    deps: [AssignmentRespository]
}

export const deleteAssignmentByIdUseCaseProvider = {
    provide: DeleteAssignmentByIdUseCase,
    useFactory: deleteAssignmentByIdUseCaseFactory,
    deps: [AssignmentRespository]
}

export const updateAssignmentUseCaseProvider = {
    provide: UpdateAssignmentUseCase,
    useFactory: updateAssignmentUseCaseFactory,
    deps: [AssignmentRespository]
}

export const addAssignmentElementUseCaseProvider = {
    provide: AddAssignmentElementUseCase,
    useFactory: addAssignmentElementUseCaseFactory,
    deps: [AssignmentRespository]
}

export const deleteAssignmentElementByIdUseCaseProvider = {
    provide: DeleteAssignmentElementByIdUseCase,
    useFactory: deleteAssignmentElementByIdUseCaseFactory,
    deps: [AssignmentRespository]
}

export const searchAssignmentByUseCaseProvider = {
    provide: SearchAssignmentByUseCase,
    useFactory: searchAssignmentByUseCaseFactory,
    deps: [AssignmentRespository]
}

@NgModule({
    providers: [
        createAssignmentUseCaseProvider,
        getAllAssignmentsUseCaseProvider,
        getAssignmentByIdUseCaseProvider,
        deleteAssignmentByIdUseCaseProvider,
        updateAssignmentUseCaseProvider,
        addAssignmentElementUseCaseProvider,
        deleteAssignmentElementByIdUseCaseProvider,
        searchAssignmentByUseCaseProvider,
        { provide: AssignmentRespository, useClass: AssignmentRepositoryImpl },
    ],
    imports: [
        CommonModule,
    ],
})

export class DiAssignmentModule { }
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { AppRegistryEntity } from "src/verazial-common-frontend/core/general/app-registry/domain/entities/app-registry.entity";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-application-edit',
    templateUrl: './application-edit.component.html',
    styleUrls: ['./application-edit.component.css'],
})
export class ApplicationEditComponent implements OnInit, OnChanges, OnDestroy {

    /* Parameters */
    // Inputs
    @Input() appRegistry?: AppRegistryEntity;
    @Input() typeOptions: GenericKeyValue[] = [];
    // Outputs
    @Output() outputData = new EventEmitter<AppRegistryEntity>();

    // Form
    public form: FormGroup = this.fb.group({
        name: ['', [Validators.required]],
        baseIdentifier: ['', [Validators.required]],
        description: [],
        type: ['', [Validators.required]],
    });

    constructor(
        private fb: FormBuilder,
        private validatorService: ValidatorService,
    ) { }

    ngOnInit(): void {
        this.form.reset();
        if (this.appRegistry && this.appRegistry.id) {
            this.fillForm();
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['appRegistry'] && changes['appRegistry'].currentValue) {
            this.ngOnInit();
        }
    }

    ngOnDestroy(): void {}

    trackDataChanges() {
        if (this.form.valid) {
            let appRegistry: AppRegistryEntity = {
                name: this.form.get('name')?.value,
                baseIdentifier: this.form.get('baseIdentifier')?.value,
                description: this.form.get('description')?.value,
                type: this.form.get('type')?.value.key,
            };
            this.outputData.emit(appRegistry);
        }
    }

    fillForm() {
        if (this.appRegistry) {
            this.form.get('name')?.setValue(this.appRegistry.name);
            this.form.get('baseIdentifier')?.setValue(this.appRegistry.baseIdentifier);
            this.form.get('description')?.setValue(this.appRegistry.description);
            let type = this.typeOptions.find((option) => option.key === this.appRegistry?.type);
            this.form.get('type')?.setValue(type);
        }
    }

    isValid(field: string): boolean {
      return this.validatorService.isValidField(this.form, field);
    }

    checkSpecificError(field: string, error: string): boolean {
      return this.validatorService.checkSpecificError(this.form, field, error);
    }

    isRequiredField(field: string): boolean {
      return this.validatorService.isRequiredField(this.form, field);
    }
}
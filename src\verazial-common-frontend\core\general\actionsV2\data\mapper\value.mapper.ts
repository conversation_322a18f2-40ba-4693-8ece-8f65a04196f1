
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ValueEntity } from "../../domain/entity/filter.entity";
import { ListValue, NullValue, Struct, Value } from "google-protobuf/google/protobuf/struct_pb";

export class ValueMapper extends Mapper<Value, ValueEntity> {


    getValidValue(arr: any[]): any[] {
        // Filter out falsy values like empty strings, null, or undefined
        return arr.filter(item => item !== null && item !== undefined);
    }
    
    override mapFrom(param: any): ValueEntity {
        if (param instanceof Value) {
            // Normal processing for google.protobuf.Value
            return this.handleValue(param);
        } else if (Array.isArray(param)) {
            // Handle array-like inputs and filter out invalid values
            const filtered = this.getValidValue(param).map(item => this.mapFrom(item));
            // If only one value remains, return it directly
            return filtered.length === 1 ? filtered[0] : filtered;
        } else if (typeof param === 'string' || typeof param === 'number' || typeof param === 'boolean' || param === null) {
            // Handle primitive values
            return param;
        } else {
            throw new Error(`Unsupported input type: ${typeof param}`);
        }
    }

    private handleValue(value: Value): ValueEntity {
        switch (value.getKindCase()) {
            case Value.KindCase.STRING_VALUE:
                return value.getStringValue();
            case Value.KindCase.NUMBER_VALUE:
                return value.getNumberValue();
            case Value.KindCase.BOOL_VALUE:
                return value.getBoolValue();
            case Value.KindCase.NULL_VALUE:
                return null;
            case Value.KindCase.STRUCT_VALUE:
                return Object.fromEntries(
                    value.getStructValue()!.getFieldsMap().toArray().map(([key, val]) => [key, this.mapFrom(val)])
                );
            case Value.KindCase.LIST_VALUE:
                return value.getListValue()!.getValuesList().map(val => this.mapFrom(val));
            default:
                throw new Error('Unsupported Value type');
        }
    }

    override mapTo(param: ValueEntity): Value {
        const value = new Value();

        if (param === null) {
            value.setNullValue(0); // `0` represents `google.protobuf.NullValue.NULL_VALUE`
        } else if (typeof param === 'string') {
            value.setStringValue(param);
        } else if (typeof param === 'number') {
            value.setNumberValue(param);
        } else if (typeof param === 'boolean') {
            value.setBoolValue(param);
        } else if (Array.isArray(param)) {
            const listValue = new ListValue();
            listValue.setValuesList(param.map(item => this.mapTo(item)));
            value.setListValue(listValue);
        } else if (typeof param === 'object') {
            const struct = new Struct();
            Object.entries(param).forEach(([key, val]) => {
                struct.getFieldsMap().set(key, this.mapTo(val));
            });
            value.setStructValue(struct);
        } else {
            if(typeof param !== 'undefined') 
                throw new Error('Unsupported ValueEntity type');
            else
                value.setNullValue(0); // `0` represents `google.protobuf.NullValue.NULL_VALUE`
        }

        return value;
    }
}
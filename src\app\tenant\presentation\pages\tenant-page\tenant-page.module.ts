import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TenantPageRoutingModule } from './tenant-page-routing.module';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { EditTenantComponent } from '../../components/edit-tenant/edit-tenant.component';
import { ListTenantsComponent } from '../../components/list-tenants/list-tenants.component';
import { TenantPageComponent } from './tenant-page/tenant-page.component';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { MessagesModule } from 'primeng/messages';
import { PasswordModule } from 'primeng/password';
import { TagModule } from 'primeng/tag';
import { EditUserSubjectModule } from 'src/verazial-common-frontend/modules/shared/components/edit-user-subject/edit-user-subject.module';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ToastModule } from 'primeng/toast';
import { ProgressBarModule } from 'primeng/progressbar';
import { TenantDatabaseComponent } from '../../components/tenant-database/tenant-database.component';
import { CalendarModule } from 'primeng/calendar';
import { TooltipModule } from 'primeng/tooltip';
import { StepsModule } from 'primeng/steps';
import { SelectButtonModule } from 'primeng/selectbutton';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { InputNumberModule } from 'primeng/inputnumber';



@NgModule({

  declarations: [
    EditTenantComponent,
    ListTenantsComponent,
    TenantPageComponent,
    TenantDatabaseComponent
  ],
  imports: [
    CommonModule,
    TenantPageRoutingModule,
    EmptyModule,
    LoadingSpinnerModule,
    ButtonModule,
    TableModule,
    TagModule,
    ConfirmDialogModule,
    TranslateModule,
    MessagesModule,
    DialogModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    EditUserSubjectModule,
    PasswordModule,
    CheckboxModule,
    DropdownModule,
    InputSwitchModule,
    ToastModule,
    ProgressBarModule,
    CalendarModule,
    TooltipModule,
    StepsModule,
    SelectButtonModule,
    ScrollPanelModule,
    InputNumberModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
]
})
export class TenantPageModule { }

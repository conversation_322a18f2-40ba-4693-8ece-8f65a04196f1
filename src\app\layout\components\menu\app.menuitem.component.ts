import { ChangeDetector<PERSON><PERSON>, Component, Host, HostBinding, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { MenuService } from './app.menu.service';
import { LayoutService } from '../../service/app.layout.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { RoleAccessResponseEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-access-response.entity';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: '[app-menuitem]',
    template: `
		<ng-container>
            <div
                *ngIf="root && item.visible !== false && hasAccess(item.accessCode)"
                class="layout-menuitem-root-text {{ disabledItem ? 'item-disabled' : '' }}"
            >
                {{ item.label | translate }}
            </div>
			<a
                *ngIf="(!item.routerLink || item.items) && item.visible !== false && hasAccess(item.accessCode)"
                [attr.href]="item.url"
                (click)="itemClick($event)"
                [ngClass]="item.class"
                class="{{ disabledItem ? 'item-disabled' : '' }}"
                [attr.target]="item.target"
                tabindex="0"
                pTooltip="{{ layoutService.state.staticMenuDesktopInactive ? (item.label | translate) : '' }}"
            >
               <div class="layout-menu-item-content flex align-items-center">
                @if(item.iconImage){
                   <img [src]="(item.stepper ? (current_step > stepper_steps.indexOf(item.step) ? disabledItem ? item.iconImageCompletedDisabled : item.iconImageCompleted : (disabledItem ? item.iconImageDisabled : (active ? item.iconImageActive : item.iconImage))) : (disabledItem ? item.iconImageDisabled : (active ? item.iconImageActive : item.iconImage)))" alt="" [style]="{'height':'18px', 'width': '19px'}" class="layout-menuitem-icon">
                }@else {
                    <i [ngClass]="item.icon" class="layout-menuitem-icon" style="font-size: 1.2rem; {{ item.icon == 'NONE' ? 'opacity: 0; width: 19px;' : '' }}"></i>
                }
				<span class="layout-menuitem-text">{{ item.label | translate }}</span>
				<i class="pi pi-fw pi-angle-down layout-submenu-toggler" *ngIf="item.items"></i>
               </div>
			</a>
			<a
                *ngIf="(item.routerLink && !item.items) && item.visible !== false && hasAccess(item.accessCode)"
                (click)="itemClick($event)"
                [ngClass]="item.class"
                class="{{ disabledItem ? 'item-disabled' : '' }}"
                [routerLink]="item.routerLink"
                routerLinkActive="active-route"
                [routerLinkActiveOptions]="item.routerLinkActiveOptions||{ paths: 'subset', queryParams: 'ignored', matrixParams: 'ignored', fragment: 'ignored' }"
                [fragment]="item.fragment"
                [queryParamsHandling]="item.queryParamsHandling"
                [preserveFragment]="item.preserveFragment"
                [skipLocationChange]="item.skipLocationChange"
                [replaceUrl]="item.replaceUrl"
                [state]="item.state"
                [queryParams]="item.queryParams"
                [attr.target]="item.target"
                tabindex="0"
                pTooltip="{{ layoutService.state.staticMenuDesktopInactive ? (item.label | translate) : '' }}"
            >
                <div class="layout-menu-item-content flex align-items-center">
                    @if(item.iconImage){
                        <img [src]="(item.stepper ? (current_step > stepper_steps.indexOf(item.step) ? disabledItem ? item.iconImageCompletedDisabled : item.iconImageCompleted : (disabledItem ? item.iconImageDisabled : (active ? item.iconImageActive : item.iconImage))) : (disabledItem ? item.iconImageDisabled : (active ? item.iconImageActive : item.iconImage)))" alt="" [style]="{'height':'18px', 'width': '19px'}" class="layout-menuitem-icon">
                    }@else {
                        <i [ngClass]="item.icon" class="layout-menuitem-icon" style="font-size: 1.2rem; {{ item.icon == 'NONE' ? 'opacity: 0; width: 19px;' : '' }}"></i>
                    }

				    <span class="layout-menuitem-text">{{ item.label | translate }}</span>
				    <i class="pi pi-fw pi-angle-down layout-submenu-toggler" *ngIf="item.items"></i>
                </div>
			</a>

			<ul *ngIf="item.items && item.visible !== false" [@children]="submenuAnimation">
				<ng-template ngFor let-child let-i="index" [ngForOf]="item.items">
                    <li app-menuitem [item]="child" [index]="i" [parentKey]="key" [class]="child.badgeClass"></li>
				</ng-template>
			</ul>
		</ng-container>
    `,
    animations: [
        trigger('children', [
            state('collapsed', style({
                height: '0'
            })),
            state('expanded', style({
                height: '*'
            })),
            transition('collapsed <=> expanded', animate('400ms cubic-bezier(0.86, 0, 0.07, 1)'))
        ])
    ]
})
export class AppMenuitemComponent implements OnInit, OnDestroy {

    @Input() item: any;

    @Input() index!: number;

    @Input() @HostBinding('class.layout-root-menuitem') root!: boolean;

    @Input() parentKey!: string;

    active = false;

    menuSourceSubscription: Subscription;

    menuResetSubscription: Subscription;

    key: string = "";

    roleAccesses: RoleAccessResponseEntity[] = [];

    stepper_steps: string[] = [];
    current_step: number = 0;

    constructor(
        public layoutService: LayoutService,
        private cd: ChangeDetectorRef,
        public router: Router,
        private menuService: MenuService,
        private localStorageService: LocalStorageService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    ) {
        this.menuSourceSubscription = this.menuService.menuSource$.subscribe(value => {
            Promise.resolve(null).then(() => {
                if (value.routeEvent) {
                    this.active = (value.key === this.key || value.key.startsWith(this.key + '-')) ? true : false;
                }
                else {
                    if (value.key !== this.key && !value.key.startsWith(this.key + '-')) {
                        this.active = false;
                    }
                }
            });
        });

        this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {
            this.active = false;
        });

        this.router.events.pipe(filter(event => event instanceof NavigationEnd))
            .subscribe(params => {
                if (this.item.routerLink) {
                    this.updateActiveStateFromRoute();
                }
            });
    }

    ngOnInit() {
        this.roleAccesses = this.localStorageService.getAccesses() as RoleAccessResponseEntity[];

        this.key = this.parentKey ? this.parentKey + '-' + this.index : String(this.index);

        if (this.item.routerLink) {
            this.updateActiveStateFromRoute();
        }
    }

    get disabledItem() {
        return this.localStorageService.getLockMenu();
    }

    updateActiveStateFromRoute() {
        let activeRoute = false;
        if (this.item.routerLinks.length > 1) {
            let activeRoutes: boolean[] = [];
            for (let i = 0; i < this.item.routerLinks.length; i++) {
                let activeRouteTmp = this.router.isActive(this.item.routerLinks[i], { paths: 'subset', queryParams: 'ignored', matrixParams: 'ignored', fragment: 'ignored' });
                activeRoutes.push(activeRouteTmp);
            }
            activeRoute = activeRoutes.some(value => value === true);
        }
        else {
            activeRoute = this.router.isActive(this.item.routerLinks[0], { paths: 'subset', queryParams: 'ignored', matrixParams: 'ignored', fragment: 'ignored' });
        }

        if (activeRoute) {
            this.menuService.onMenuStateChange({ key: this.key, routeEvent: true });
        }
        this.getActiveRouteForStepper();
    }

    itemClick(event: MouseEvent) {
        // avoid processing disabled items
        if (this.item.disabled || this.disabledItem || this.localStorageService.getLockMenu()) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            return;
        }

        // execute command
        if (this.item.command) {
            this.item.command({ originalEvent: event, item: this.item });
        }

        // toggle active state
        if (this.item.items) {
            this.active = !this.active;
        }

        this.menuService.onMenuStateChange({ key: this.key });
    }

    get submenuAnimation() {
        return this.root ? 'expanded' : (this.active ? 'expanded' : 'collapsed');
    }

    @HostBinding('class.active-menuitem')
    get activeClass() {
        return this.active && !this.root;
    }

    getActiveRouteForStepper(){
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                let managerSettings = this.localStorageService.getSessionSettings();
                this.stepper_steps = [];
                this.stepper_steps.push('new');
                if (managerSettings?.payedTechnology?.iris == true && data.enabledTech?.iris == true) {
                    this.stepper_steps.push('iris');
                }
                if (managerSettings?.payedTechnology?.dactilar == true && data.enabledTech?.dactilar == true) {
                    this.stepper_steps.push('fingerprint');
                }
                if (managerSettings?.payedTechnology?.facial == true && data.enabledTech?.facial == true) {
                    this.stepper_steps.push('facial');
                }
                if (managerSettings?.payedTechnology?.dactilarRolled == true && data.enabledTech?.dactilarRolled == true) {
                    this.stepper_steps.push('fingerprintRolled');
                }
                if (managerSettings?.payedTechnology?.palm == true && data.enabledTech?.palm == true) {
                    this.stepper_steps.push('palm');
                }
                this.stepper_steps.push('continued');
                this.stepper_steps.push('summary');
                this.current_step = this.stepper_steps.indexOf(this.router.url.split('/')[3]?.split('?')[0]!);
            },
            error: (error) => {},
        });
    }

    ngOnDestroy() {
        if (this.menuSourceSubscription) {
            this.menuSourceSubscription.unsubscribe();
        }

        if (this.menuResetSubscription) {
            this.menuResetSubscription.unsubscribe();
        }
    }

    hasAccess(accessCode: string) {
        let findAccessCode = this.roleAccesses.find((item) => item.accessCode == accessCode);
        if (findAccessCode) {
            return true
        }
        return false;
    }
}

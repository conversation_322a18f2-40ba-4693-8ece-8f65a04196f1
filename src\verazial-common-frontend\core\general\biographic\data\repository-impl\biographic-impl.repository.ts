
import { environment } from "src/environments/environment";
import { Injectable } from "@angular/core";
import { BiographicMapper } from "../mapper/biographic.mapper";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { BiographicRepository } from "../../domain/repository/biographic.repository";
import { IdentityOperationResultEntity } from "../../domain/entity/identity-operation-result.entity";
import { HttpClient } from "@angular/common/http";
import { CoreBiographicServiceClient } from "src/verazial-common-frontend/core/generated/biographic/BiographicServiceClientPb";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";

@Injectable({
    providedIn: 'root',
})
export class BiographicRepositoryImpl extends BiographicRepository {

    biographicMapper = new BiographicMapper()

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override getActualTime(): Promise<IdentityOperationResultEntity> {
        let request = new Empty();
        let coreBiographicServiceClient = new CoreBiographicServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        return new Promise((resolve, reject) => {
            coreBiographicServiceClient.getActualTime(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.biographicMapper.mapFrom(response));
                }
            });
        });
    }

    // override getActualTime(): Promise<IdentityOperationResultEntity> {
    //     let response = new IdentityOperationResultEntity();
    //     return new Promise((resolve, reject) => {
    //         grpc.invoke(CoreBiographicService.getActualTime, {
    //             request: new Empty(),
    //             host: environment.grpcApiGateway,
    //             /* TODO: Replace this code when interceptor is implemented */
    //             metadata: new grpc.Metadata({
    //                 Authorization: `Bearer ${window.sessionStorage.getItem('auth_token')}`,
    //             }),
    //             onMessage: (message: IdentityOperationResult) => {
    //                 response = this.biographicMapper.mapFrom(message!);
    //             },
    //             onEnd: (code: grpc.Code, msg: string | undefined, trailers: grpc.Metadata) => {
    //                 if (code == grpc.Code.OK) {
    //                     resolve(response);
    //                 } else {
    //                     let failure: FailureResponse = {
    //                         code: code,
    //                         message: msg,
    //                     }
    //                     reject(failure);
    //                 }
    //             },
    //         });
    //     });
    // }
}
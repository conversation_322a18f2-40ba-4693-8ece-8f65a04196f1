.picBorder {
    margin-bottom: 5%; 
    display: flex; 
    /*justify-content: center;*/ 
    align-items: center; 
    /* background: radial-gradient(circle at 100% 100%, #95cfdc 0, #95cfdc 3px, transparent 3px) 0% 0%/8px 8px no-repeat, radial-gradient(circle at 0 100%, #95cfdc 0, #95cfdc 3px, transparent 3px) 100% 0%/8px 8px no-repeat, radial-gradient(circle at 100% 0, #95cfdc 0, #95cfdc 3px, transparent 3px) 0% 100%/8px 8px no-repeat, radial-gradient(circle at 0 0, #95cfdc 0, #95cfdc 3px, transparent 3px) 100% 100%/8px 8px no-repeat, linear-gradient(#95cfdc, #95cfdc) 50% 50%/calc(100% - 10px) calc(100% - 16px) no-repeat, linear-gradient(#95cfdc, #95cfdc) 50% 50%/calc(100% - 16px) calc(100% - 10px) no-repeat, linear-gradient(0deg, transparent 0%, #48abe0 100%), linear-gradient(transparent 0%, #162746 100%);  */
    border-radius:5px; 
    max-height: 120px; 
    min-height: 120px; 
    max-width: 120px; 
    min-width: 120px; 
    mix-blend-mode: multiply; 
    position: relative;
    width: fit-content;
    overflow: hidden;
}

.picBorderStatic {
    margin-bottom: 5%; 
    display: flex; 
    /*justify-content: center;*/ 
    align-items: center; 
    /* background: radial-gradient(circle at 100% 100%, #95cfdc 0, #95cfdc 3px, transparent 3px) 0% 0%/8px 8px no-repeat, radial-gradient(circle at 0 100%, #95cfdc 0, #95cfdc 3px, transparent 3px) 100% 0%/8px 8px no-repeat, radial-gradient(circle at 100% 0, #95cfdc 0, #95cfdc 3px, transparent 3px) 0% 100%/8px 8px no-repeat, radial-gradient(circle at 0 0, #95cfdc 0, #95cfdc 3px, transparent 3px) 100% 100%/8px 8px no-repeat, linear-gradient(#95cfdc, #95cfdc) 50% 50%/calc(100% - 10px) calc(100% - 16px) no-repeat, linear-gradient(#95cfdc, #95cfdc) 50% 50%/calc(100% - 16px) calc(100% - 10px) no-repeat, linear-gradient(0deg, transparent 0%, #48abe0 100%), linear-gradient(transparent 0%, #162746 100%);  */
    border-radius:5px; 
    max-height: 120px; 
    min-height: 120px; 
    max-width: 120px; 
    min-width: 120px; 
    mix-blend-mode: multiply; 
    position: relative;
    width: fit-content;
    overflow: hidden;
}

.container {
    margin-bottom: 5%; 
    display: flex; 
    border-radius:5px; 
    max-height: 120px; 
    min-height: 120px; 
    max-width: 120px; 
    min-width: 120px; 
    mix-blend-mode: multiply; 
    position: relative;
    width: fit-content;
}

.d-none {
    display:none;
}

.imgUser {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6); 
    position: absolute;
    left: -15%; 
    width: 170px; 
    height: 120px;
    border-radius: 5px;
}

.imgUserStatic {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6); 
    position: absolute;
    left: -15%; 
    width: 170px; 
    height: 120px;
    border-radius: 5px;
}

.editPic {
    height:30px; 
    position:absolute; 
    bottom: -7px; 
    left: 97px; 
    cursor:pointer
}

.videoPic {
    left: 10px; 
    width: 170px; 
    /*height: 150px; */
    /*padding:5px*/
}

.captureContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
}

@media (max-width: 775px) {
    .imgUser {
        width: 136px; 
        height: 96px;
    }

    .videoPic {
        width: 136px; 
    }

    .picBorder {
        max-height: 96px;
        min-height: 96px;
        max-width: 96px;
        min-width: 96px;
    }
    
    .container {
        max-height: 96px;
        min-height: 96px;
        max-width: 96px;
        min-width: 96px;
    }
    
}

@media (max-width: 500px) {
    .imgUser {
        width: 81px; 
        height: 57px;
    }

    .videoPic {
        width: 81px; 
    }

    .picBorder {
        max-height: 57px;
        min-height: 57px;
        max-width: 57px;
        min-width: 57px;
    }
    
    .container {
        max-height: 57px;
        min-height: 57px;
        max-width: 57px;
        min-width: 57px;
    }
    
}
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { IdentityOperationResultEntity } from "../entity/identity-operation-result.entity";
import { BiographicRepository } from "../repository/biographic.repository";

export class GetActualTimeUseCase implements UseCaseGrpc<undefined, IdentityOperationResultEntity> {

    constructor(private biographicRepository: BiographicRepository) { }

    execute(): Promise<IdentityOperationResultEntity> {
        return this.biographicRepository.getActualTime();
    }
}
import { Observable } from "rxjs";
import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialRepository } from "../repositories/credential.repository";

export class UpdateCredentialsByNumIdUseCase implements UseCase<{ numId: string, password: string }, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: { numId: string; password: string; }): Observable<any> {
        return this.credentialsRepository.updateCredentialsByNumId(params);
    }

}
import { DataSourceRepository } from "../repositories/data-source.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { DataSourceEntity } from "../entities/data-source.entity";

export class GetAppDataSourceByIdUseCase implements UseCaseGrpc<{ id: string }, DataSourceEntity> {
    constructor(private dataSourceRepository: DataSourceRepository) { }
    execute(params: { id: string; }): Promise<DataSourceEntity> {
        return this.dataSourceRepository.getAppDataSourceById(params);
    }
}
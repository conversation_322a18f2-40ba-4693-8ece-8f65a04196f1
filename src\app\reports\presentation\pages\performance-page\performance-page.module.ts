import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PerformancePageRoutingModule } from './performance-page-routing.module';
import { PerformancePageComponent } from './performance-page/performance-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { DiLicenseModule } from 'src/verazial-common-frontend/core/general/license/data/di-license.module';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
  declarations: [
        PerformancePageComponent
  ],
  imports: [
    CommonModule,
    PerformancePageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    ProgressSpinnerModule,
    DropdownModule,
    ProgressBarModule,
    SkeletonModule,
    DiLicenseModule,
    InputTextModule,

  ],
  exports: [
    PerformancePageComponent
  ]
})
export class PerformancePageModule { }

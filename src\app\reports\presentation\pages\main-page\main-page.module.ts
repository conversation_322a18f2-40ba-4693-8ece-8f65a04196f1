import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainPageRoutingModule } from './main-page-routing.module';
import { MainPageComponent } from './main-page/main-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { DiLicenseModule } from 'src/verazial-common-frontend/core/general/license/data/di-license.module';

@NgModule({
  declarations: [
        MainPageComponent
  ],
  imports: [
    CommonModule,
    MainPageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    TableModule,
    ProgressSpinnerModule,
    ProgressBarModule,
    DropdownModule,
    SkeletonModule,
    DiLicenseModule
  ],
  exports: [
    MainPageComponent
  ]
})
export class MainPageModule { }

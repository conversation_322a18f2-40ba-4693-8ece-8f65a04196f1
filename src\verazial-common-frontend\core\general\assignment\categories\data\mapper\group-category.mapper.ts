import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { GroupCategoryEntity } from "../../domain/entity/group-category.entity";
import { CategoryGrpModel, GroupCategoryTypeGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { GroupCategoryType } from "../../common/models/group-category-type.enum";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";
import { toArrayOfLocations, toArrayOfSubjects, toListOfCategoryLocation, toListOfCategorySubjects } from "../../common/converter/category.converter";
import { CategoryScheduleMapper } from "./category-schedule.mapper";

export class GroupCategoryMapper extends Mapper<CategoryGrpModel, GroupCategoryEntity> {

    scheduleMapper = new CategoryScheduleMapper();

    override mapFrom(param: CategoryGrpModel): GroupCategoryEntity {
        let category = new GroupCategoryEntity();

        category.id = param.getId();
        category.name = param.getName();
        category.type = toEnum(GroupCategoryType, param.getType());
        category.description = param.getDescription();
        category.attributeType = param.getAttributetype();
        category.categoryLocations = toListOfCategoryLocation(param.getCategorylocations());
        category.categorySubjects = toListOfCategorySubjects(param.getCategorysubjects());
        category.categorySchedule = param.getCategoryschedule() ? this.scheduleMapper.mapFrom(param.getCategoryschedule()!) : undefined;
        category.createdAt = new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6));
        category.updatedAt = new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6));

        return category;
    }
    override mapTo(param: GroupCategoryEntity): CategoryGrpModel {
        let category = new CategoryGrpModel();

        category.setId(param.id!);
        category.setName(param.name!);
        category.setType(toEnum(GroupCategoryTypeGrpc, param.type!)!);
        category.setDescription(param.description ? param.description : "");
        category.setAttributetype(param.attributeType ? param.attributeType : "");
        category.setCategorylocations(toArrayOfLocations(param.categoryLocations));
        category.setCategorysubjects(toArrayOfSubjects(param.categorySubjects));
        category.setCategoryschedule(param.categorySchedule ? this.scheduleMapper.mapTo(param.categorySchedule) : undefined);

        return category;
    }

}

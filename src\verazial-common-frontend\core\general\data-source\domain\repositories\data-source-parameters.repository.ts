import { DataSourceParametersEntity } from "../entities/data-source-parameters.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export abstract class DataSourceParametersRepository {
    abstract addAppDataSourceParams(dataSourceParameters: DataSourceParametersEntity[]): Promise<DataSourceParametersEntity[]>;
    abstract updateAppDataSourceParamById(dataSourceParameters: DataSourceParametersEntity): Promise<SuccessResponse>;
    abstract getAppDataSourceParamById(params: { id: string }): Promise<DataSourceParametersEntity>;
    abstract getAllParamsByDataSourceId(params: { dataSourceId: string }): Promise<DataSourceParametersEntity[]>;
    abstract deleteDataSourceParamsById(params: { id: string }): Promise<SuccessResponse>;
    abstract deleteParamByDataSourceId(params: { dataSourceId: string }): Promise<SuccessResponse>;
}
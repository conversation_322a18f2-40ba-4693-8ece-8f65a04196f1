<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<p-toast/>
<app-list-user-subject
    [isLoading]="isLoading"
    [type]="type"
    [lazyLoad]="useLazyLoad"
    [readAndWritePermissions]="canReadAndWrite"
    [readOnly]="readOnly"
    [userIsVerified]="userIsVerified"
    [listOfUsersSubjects]="listOfUser"
    [totalRecords]="totalRecords"
    [offset]="getUsersRequest.offset"
    [limit]="getUsersRequest.limit"
    [allRoles]="listRoles"
    [managerSettings]="managerSettings"
    [konektorProperties]="konektorProperties"
    (onAdd)="onSubmitAddNewUser($event)"
    (onMainAction)="onEditUser($event)"
    (onSecondaryAction)="confirmDelete($event)"
    (onBioSearch)="navigateToUser($event)"
    (onTableLazyLoadEvent)="onTableLazyLoadEvent($event)"
    (userVerified)="userVerified($event)"
></app-list-user-subject>
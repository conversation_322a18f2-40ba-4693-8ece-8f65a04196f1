

export interface FilterEntity {
  condition?: FieldEqualsEntity; // Represents the `oneof` condition with `FieldEquals`.
}

export interface FieldEqualsEntity {
  path: string; // Represents the `path` field.
  value: ValueEntity; // Represents the `google.protobuf.Value`.
}

export type ValueEntity = string | number | boolean | null | { [key: string]: ValueEntity } | ValueEntity[]; // Represents `google.protobuf.Value`.

export function getValidValue(arr: any[]): any {
    // Filter out falsy values like empty strings, null, or undefined
    return arr.filter(item => item !== null && item !== undefined && item !== '' && item !== 'vacío')[0];
}
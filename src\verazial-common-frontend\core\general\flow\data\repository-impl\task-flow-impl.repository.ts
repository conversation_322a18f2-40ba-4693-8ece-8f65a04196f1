import { Injectable } from "@angular/core";
import { TaskFlowRepository } from "../../domain/repository/task-flow.repository";
import { TaskFlowMapper } from "../mapper/task-flow.mapper";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { TaskFlowEntity } from "../../domain/entity/task-flow.entity";
import { CoreTaskFlowServiceClient } from "src/verazial-common-frontend/core/generated/flow/Task_flowServiceClientPb";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { TaskFlowGrpcModel } from "src/verazial-common-frontend/core/generated/flow/task_flow_pb";
import { StringParam } from "src/verazial-common-frontend/core/generated/util_pb";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { HttpClient } from "@angular/common/http";


@Injectable({
    providedIn: 'root',
})
export class TaskFlowRepositoryImpl extends TaskFlowRepository {

    taskflowMapper = new TaskFlowMapper();

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override createTaskFlow(params: { taskFlow: TaskFlowEntity; }): Promise<TaskFlowEntity> {
        let request = this.taskflowMapper.mapTo(params.taskFlow);

        let coreTaskFlowServiceClient = new CoreTaskFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTaskFlowServiceClient.createTaskFlow(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.taskflowMapper.mapFrom(response.getTaskflow()!));
                    }
                }
            });
        });
    }

    override getAllTaskFlows(): Promise<TaskFlowEntity[]> {
        let taskFlowResponse: TaskFlowEntity[] = [];

        let coreTaskFlowServiceClient = new CoreTaskFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreTaskFlowServiceClient.getAllTaskFlows(new Empty);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: TaskFlowGrpcModel) => {
                taskFlowResponse.push(this.taskflowMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(taskFlowResponse);
            });
        });
    }

    override getAllPublishedTaskFlows(): Promise<TaskFlowEntity[]> {
        let taskFlowResponse: TaskFlowEntity[] = [];

        let coreTaskFlowServiceClient = new CoreTaskFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreTaskFlowServiceClient.getAllPublishedTaskFlows(new Empty);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: TaskFlowGrpcModel) => {
                taskFlowResponse.push(this.taskflowMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(taskFlowResponse);
            });
        });
    }

    override getTaskFlowById(params: { id: string; }): Promise<TaskFlowEntity> {
        let request = new StringParam();
        request.setParameter(params.id);

        let coreTaskFlowServiceClient = new CoreTaskFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTaskFlowServiceClient.getTaskFlowById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.taskflowMapper.mapFrom(response.getTaskflow()!));
                    }
                }
            });
        });
    }

    override deleteTaskFlowById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new StringParam();
        request.setParameter(params.id);

        let success!: SuccessResponse;

        let coreTaskFlowServiceClient = new CoreTaskFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreTaskFlowServiceClient.deleteTaskFlowById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override updateTaskFlow(params: { taskFlow: TaskFlowEntity; }): Promise<TaskFlowEntity> {

        let request = this.taskflowMapper.mapTo(params.taskFlow);

        let coreTaskFlowServiceClient = new CoreTaskFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTaskFlowServiceClient.updateTaskFlow(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.taskflowMapper.mapFrom(response.getTaskflow()!));
                    }
                }
            });
        });
    }



}
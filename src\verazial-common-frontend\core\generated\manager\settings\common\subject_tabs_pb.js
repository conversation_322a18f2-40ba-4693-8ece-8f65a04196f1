// source: manager/settings/common/subject_tabs.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var manager_settings_common_custom_field_pb = require('../../../manager/settings/common/custom_field_pb.js');
goog.object.extend(proto, manager_settings_common_custom_field_pb);
var manager_settings_common_custom_field_group_pb = require('../../../manager/settings/common/custom_field_group_pb.js');
goog.object.extend(proto, manager_settings_common_custom_field_group_pb);
var util_pb = require('../../../util_pb.js');
goog.object.extend(proto, util_pb);
goog.exportSymbol('proto.SubjectTabsGrpcModel', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SubjectTabsGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.SubjectTabsGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SubjectTabsGrpcModel.displayName = 'proto.SubjectTabsGrpcModel';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SubjectTabsGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.SubjectTabsGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SubjectTabsGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubjectTabsGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
showadditionaltabs: (f = jspb.Message.getBooleanField(msg, 1)) == null ? undefined : f,
showextendedbiofieldstab: (f = jspb.Message.getBooleanField(msg, 2)) == null ? undefined : f,
restrictextendedbiofieldstabtospecificroles: (f = jspb.Message.getBooleanField(msg, 3)) == null ? undefined : f,
extendedbiofieldsroles: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
showphysicaldatatab: (f = jspb.Message.getBooleanField(msg, 5)) == null ? undefined : f,
restrictphysicaldatatabtospecificroles: (f = jspb.Message.getBooleanField(msg, 6)) == null ? undefined : f,
physicaldataroles: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f,
showprofilepicturetab: (f = jspb.Message.getBooleanField(msg, 8)) == null ? undefined : f,
restrictprofilepicturetabtospecificroles: (f = jspb.Message.getBooleanField(msg, 9)) == null ? undefined : f,
profilepictureroles: (f = jspb.Message.getField(msg, 10)) == null ? undefined : f,
showrelationstab: (f = jspb.Message.getBooleanField(msg, 11)) == null ? undefined : f,
restrictrelationstabtospecificroles: (f = jspb.Message.getBooleanField(msg, 12)) == null ? undefined : f,
relationsroles: (f = jspb.Message.getField(msg, 13)) == null ? undefined : f,
showlocationstab: (f = jspb.Message.getBooleanField(msg, 14)) == null ? undefined : f,
restrictlocationstabtospecificroles: (f = jspb.Message.getBooleanField(msg, 15)) == null ? undefined : f,
locationsroles: (f = jspb.Message.getField(msg, 16)) == null ? undefined : f,
showentriesexitstab: (f = jspb.Message.getBooleanField(msg, 17)) == null ? undefined : f,
restrictentriesexitstabtospecificroles: (f = jspb.Message.getBooleanField(msg, 18)) == null ? undefined : f,
entriesexitsroles: (f = jspb.Message.getField(msg, 19)) == null ? undefined : f,
showentryexitauthorizationstab: (f = jspb.Message.getBooleanField(msg, 20)) == null ? undefined : f,
restrictentryexitauthorizationstabtospecificroles: (f = jspb.Message.getBooleanField(msg, 21)) == null ? undefined : f,
entryexitauthorizationsroles: (f = jspb.Message.getField(msg, 22)) == null ? undefined : f,
showentryexitauthdetails: (f = jspb.Message.getBooleanField(msg, 23)) == null ? undefined : f,
entryexitauthdetailfields: (f = msg.getEntryexitauthdetailfields()) && manager_settings_common_custom_field_pb.ArrayOfCustomField.toObject(includeInstance, f),
entryexitauthdetailfieldgroups: (f = msg.getEntryexitauthdetailfieldgroups()) && manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup.toObject(includeInstance, f),
showfilestab: (f = jspb.Message.getBooleanField(msg, 26)) == null ? undefined : f,
restrictfilestabtospecificroles: (f = jspb.Message.getBooleanField(msg, 27)) == null ? undefined : f,
filesroles: (f = jspb.Message.getField(msg, 28)) == null ? undefined : f,
subjectfiletypes: (f = msg.getSubjectfiletypes()) && util_pb.ArrayOfStrings.toObject(includeInstance, f),
acceptedfiles: (f = jspb.Message.getField(msg, 30)) == null ? undefined : f,
maxfilesize: (f = jspb.Message.getField(msg, 31)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SubjectTabsGrpcModel}
 */
proto.SubjectTabsGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SubjectTabsGrpcModel;
  return proto.SubjectTabsGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SubjectTabsGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SubjectTabsGrpcModel}
 */
proto.SubjectTabsGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowadditionaltabs(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowextendedbiofieldstab(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictextendedbiofieldstabtospecificroles(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setExtendedbiofieldsroles(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowphysicaldatatab(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictphysicaldatatabtospecificroles(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPhysicaldataroles(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowprofilepicturetab(value);
      break;
    case 9:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictprofilepicturetabtospecificroles(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setProfilepictureroles(value);
      break;
    case 11:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowrelationstab(value);
      break;
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictrelationstabtospecificroles(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setRelationsroles(value);
      break;
    case 14:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowlocationstab(value);
      break;
    case 15:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictlocationstabtospecificroles(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setLocationsroles(value);
      break;
    case 17:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowentriesexitstab(value);
      break;
    case 18:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictentriesexitstabtospecificroles(value);
      break;
    case 19:
      var value = /** @type {string} */ (reader.readString());
      msg.setEntriesexitsroles(value);
      break;
    case 20:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowentryexitauthorizationstab(value);
      break;
    case 21:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictentryexitauthorizationstabtospecificroles(value);
      break;
    case 22:
      var value = /** @type {string} */ (reader.readString());
      msg.setEntryexitauthorizationsroles(value);
      break;
    case 23:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowentryexitauthdetails(value);
      break;
    case 24:
      var value = new manager_settings_common_custom_field_pb.ArrayOfCustomField;
      reader.readMessage(value,manager_settings_common_custom_field_pb.ArrayOfCustomField.deserializeBinaryFromReader);
      msg.setEntryexitauthdetailfields(value);
      break;
    case 25:
      var value = new manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup;
      reader.readMessage(value,manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup.deserializeBinaryFromReader);
      msg.setEntryexitauthdetailfieldgroups(value);
      break;
    case 26:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setShowfilestab(value);
      break;
    case 27:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRestrictfilestabtospecificroles(value);
      break;
    case 28:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilesroles(value);
      break;
    case 29:
      var value = new util_pb.ArrayOfStrings;
      reader.readMessage(value,util_pb.ArrayOfStrings.deserializeBinaryFromReader);
      msg.setSubjectfiletypes(value);
      break;
    case 30:
      var value = /** @type {string} */ (reader.readString());
      msg.setAcceptedfiles(value);
      break;
    case 31:
      var value = /** @type {string} */ (reader.readString());
      msg.setMaxfilesize(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SubjectTabsGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SubjectTabsGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SubjectTabsGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubjectTabsGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {boolean} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeBool(
      1,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeBool(
      2,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeBool(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeBool(
      5,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeBool(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeBool(
      8,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeBool(
      9,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 10));
  if (f != null) {
    writer.writeString(
      10,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 11));
  if (f != null) {
    writer.writeBool(
      11,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeBool(
      12,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 13));
  if (f != null) {
    writer.writeString(
      13,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 14));
  if (f != null) {
    writer.writeBool(
      14,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 15));
  if (f != null) {
    writer.writeBool(
      15,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 16));
  if (f != null) {
    writer.writeString(
      16,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 17));
  if (f != null) {
    writer.writeBool(
      17,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 18));
  if (f != null) {
    writer.writeBool(
      18,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 19));
  if (f != null) {
    writer.writeString(
      19,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 20));
  if (f != null) {
    writer.writeBool(
      20,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 21));
  if (f != null) {
    writer.writeBool(
      21,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 22));
  if (f != null) {
    writer.writeString(
      22,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 23));
  if (f != null) {
    writer.writeBool(
      23,
      f
    );
  }
  f = message.getEntryexitauthdetailfields();
  if (f != null) {
    writer.writeMessage(
      24,
      f,
      manager_settings_common_custom_field_pb.ArrayOfCustomField.serializeBinaryToWriter
    );
  }
  f = message.getEntryexitauthdetailfieldgroups();
  if (f != null) {
    writer.writeMessage(
      25,
      f,
      manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup.serializeBinaryToWriter
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 26));
  if (f != null) {
    writer.writeBool(
      26,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 27));
  if (f != null) {
    writer.writeBool(
      27,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 28));
  if (f != null) {
    writer.writeString(
      28,
      f
    );
  }
  f = message.getSubjectfiletypes();
  if (f != null) {
    writer.writeMessage(
      29,
      f,
      util_pb.ArrayOfStrings.serializeBinaryToWriter
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 30));
  if (f != null) {
    writer.writeString(
      30,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 31));
  if (f != null) {
    writer.writeString(
      31,
      f
    );
  }
};


/**
 * optional bool showAdditionalTabs = 1;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowadditionaltabs = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowadditionaltabs = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowadditionaltabs = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowadditionaltabs = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional bool showExtendedBioFieldsTab = 2;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowextendedbiofieldstab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowextendedbiofieldstab = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowextendedbiofieldstab = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowextendedbiofieldstab = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional bool restrictExtendedBioFieldsTabToSpecificRoles = 3;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictextendedbiofieldstabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictextendedbiofieldstabtospecificroles = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictextendedbiofieldstabtospecificroles = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictextendedbiofieldstabtospecificroles = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string extendedBioFieldsRoles = 4;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getExtendedbiofieldsroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setExtendedbiofieldsroles = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearExtendedbiofieldsroles = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasExtendedbiofieldsroles = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional bool showPhysicalDataTab = 5;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowphysicaldatatab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowphysicaldatatab = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowphysicaldatatab = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowphysicaldatatab = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional bool restrictPhysicalDataTabToSpecificRoles = 6;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictphysicaldatatabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictphysicaldatatabtospecificroles = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictphysicaldatatabtospecificroles = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictphysicaldatatabtospecificroles = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string physicalDataRoles = 7;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getPhysicaldataroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setPhysicaldataroles = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearPhysicaldataroles = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasPhysicaldataroles = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional bool showProfilePictureTab = 8;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowprofilepicturetab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowprofilepicturetab = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowprofilepicturetab = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowprofilepicturetab = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional bool restrictProfilePictureTabToSpecificRoles = 9;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictprofilepicturetabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 9, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictprofilepicturetabtospecificroles = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictprofilepicturetabtospecificroles = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictprofilepicturetabtospecificroles = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string profilePictureRoles = 10;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getProfilepictureroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setProfilepictureroles = function(value) {
  return jspb.Message.setField(this, 10, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearProfilepictureroles = function() {
  return jspb.Message.setField(this, 10, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasProfilepictureroles = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional bool showRelationsTab = 11;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowrelationstab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 11, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowrelationstab = function(value) {
  return jspb.Message.setField(this, 11, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowrelationstab = function() {
  return jspb.Message.setField(this, 11, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowrelationstab = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional bool restrictRelationsTabToSpecificRoles = 12;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictrelationstabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 12, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictrelationstabtospecificroles = function(value) {
  return jspb.Message.setField(this, 12, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictrelationstabtospecificroles = function() {
  return jspb.Message.setField(this, 12, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictrelationstabtospecificroles = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional string relationsRoles = 13;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getRelationsroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRelationsroles = function(value) {
  return jspb.Message.setField(this, 13, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRelationsroles = function() {
  return jspb.Message.setField(this, 13, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRelationsroles = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional bool showLocationsTab = 14;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowlocationstab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 14, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowlocationstab = function(value) {
  return jspb.Message.setField(this, 14, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowlocationstab = function() {
  return jspb.Message.setField(this, 14, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowlocationstab = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional bool restrictLocationsTabToSpecificRoles = 15;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictlocationstabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 15, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictlocationstabtospecificroles = function(value) {
  return jspb.Message.setField(this, 15, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictlocationstabtospecificroles = function() {
  return jspb.Message.setField(this, 15, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictlocationstabtospecificroles = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional string locationsRoles = 16;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getLocationsroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setLocationsroles = function(value) {
  return jspb.Message.setField(this, 16, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearLocationsroles = function() {
  return jspb.Message.setField(this, 16, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasLocationsroles = function() {
  return jspb.Message.getField(this, 16) != null;
};


/**
 * optional bool showEntriesExitsTab = 17;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowentriesexitstab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 17, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowentriesexitstab = function(value) {
  return jspb.Message.setField(this, 17, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowentriesexitstab = function() {
  return jspb.Message.setField(this, 17, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowentriesexitstab = function() {
  return jspb.Message.getField(this, 17) != null;
};


/**
 * optional bool restrictEntriesExitsTabToSpecificRoles = 18;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictentriesexitstabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 18, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictentriesexitstabtospecificroles = function(value) {
  return jspb.Message.setField(this, 18, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictentriesexitstabtospecificroles = function() {
  return jspb.Message.setField(this, 18, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictentriesexitstabtospecificroles = function() {
  return jspb.Message.getField(this, 18) != null;
};


/**
 * optional string entriesExitsRoles = 19;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getEntriesexitsroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 19, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setEntriesexitsroles = function(value) {
  return jspb.Message.setField(this, 19, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearEntriesexitsroles = function() {
  return jspb.Message.setField(this, 19, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasEntriesexitsroles = function() {
  return jspb.Message.getField(this, 19) != null;
};


/**
 * optional bool showEntryExitAuthorizationsTab = 20;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowentryexitauthorizationstab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 20, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowentryexitauthorizationstab = function(value) {
  return jspb.Message.setField(this, 20, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowentryexitauthorizationstab = function() {
  return jspb.Message.setField(this, 20, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowentryexitauthorizationstab = function() {
  return jspb.Message.getField(this, 20) != null;
};


/**
 * optional bool restrictEntryExitAuthorizationsTabToSpecificRoles = 21;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictentryexitauthorizationstabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 21, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictentryexitauthorizationstabtospecificroles = function(value) {
  return jspb.Message.setField(this, 21, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictentryexitauthorizationstabtospecificroles = function() {
  return jspb.Message.setField(this, 21, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictentryexitauthorizationstabtospecificroles = function() {
  return jspb.Message.getField(this, 21) != null;
};


/**
 * optional string entryExitAuthorizationsRoles = 22;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getEntryexitauthorizationsroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 22, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setEntryexitauthorizationsroles = function(value) {
  return jspb.Message.setField(this, 22, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearEntryexitauthorizationsroles = function() {
  return jspb.Message.setField(this, 22, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasEntryexitauthorizationsroles = function() {
  return jspb.Message.getField(this, 22) != null;
};


/**
 * optional bool showEntryExitAuthDetails = 23;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowentryexitauthdetails = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 23, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowentryexitauthdetails = function(value) {
  return jspb.Message.setField(this, 23, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowentryexitauthdetails = function() {
  return jspb.Message.setField(this, 23, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowentryexitauthdetails = function() {
  return jspb.Message.getField(this, 23) != null;
};


/**
 * optional ArrayOfCustomField entryExitAuthDetailFields = 24;
 * @return {?proto.ArrayOfCustomField}
 */
proto.SubjectTabsGrpcModel.prototype.getEntryexitauthdetailfields = function() {
  return /** @type{?proto.ArrayOfCustomField} */ (
    jspb.Message.getWrapperField(this, manager_settings_common_custom_field_pb.ArrayOfCustomField, 24));
};


/**
 * @param {?proto.ArrayOfCustomField|undefined} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
*/
proto.SubjectTabsGrpcModel.prototype.setEntryexitauthdetailfields = function(value) {
  return jspb.Message.setWrapperField(this, 24, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearEntryexitauthdetailfields = function() {
  return this.setEntryexitauthdetailfields(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasEntryexitauthdetailfields = function() {
  return jspb.Message.getField(this, 24) != null;
};


/**
 * optional ArrayOfCustomFieldGroup entryExitAuthDetailFieldGroups = 25;
 * @return {?proto.ArrayOfCustomFieldGroup}
 */
proto.SubjectTabsGrpcModel.prototype.getEntryexitauthdetailfieldgroups = function() {
  return /** @type{?proto.ArrayOfCustomFieldGroup} */ (
    jspb.Message.getWrapperField(this, manager_settings_common_custom_field_group_pb.ArrayOfCustomFieldGroup, 25));
};


/**
 * @param {?proto.ArrayOfCustomFieldGroup|undefined} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
*/
proto.SubjectTabsGrpcModel.prototype.setEntryexitauthdetailfieldgroups = function(value) {
  return jspb.Message.setWrapperField(this, 25, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearEntryexitauthdetailfieldgroups = function() {
  return this.setEntryexitauthdetailfieldgroups(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasEntryexitauthdetailfieldgroups = function() {
  return jspb.Message.getField(this, 25) != null;
};


/**
 * optional bool showFilesTab = 26;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getShowfilestab = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 26, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setShowfilestab = function(value) {
  return jspb.Message.setField(this, 26, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearShowfilestab = function() {
  return jspb.Message.setField(this, 26, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasShowfilestab = function() {
  return jspb.Message.getField(this, 26) != null;
};


/**
 * optional bool restrictFilesTabToSpecificRoles = 27;
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.getRestrictfilestabtospecificroles = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 27, false));
};


/**
 * @param {boolean} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setRestrictfilestabtospecificroles = function(value) {
  return jspb.Message.setField(this, 27, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearRestrictfilestabtospecificroles = function() {
  return jspb.Message.setField(this, 27, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasRestrictfilestabtospecificroles = function() {
  return jspb.Message.getField(this, 27) != null;
};


/**
 * optional string filesRoles = 28;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getFilesroles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 28, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setFilesroles = function(value) {
  return jspb.Message.setField(this, 28, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearFilesroles = function() {
  return jspb.Message.setField(this, 28, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasFilesroles = function() {
  return jspb.Message.getField(this, 28) != null;
};


/**
 * optional ArrayOfStrings subjectFileTypes = 29;
 * @return {?proto.ArrayOfStrings}
 */
proto.SubjectTabsGrpcModel.prototype.getSubjectfiletypes = function() {
  return /** @type{?proto.ArrayOfStrings} */ (
    jspb.Message.getWrapperField(this, util_pb.ArrayOfStrings, 29));
};


/**
 * @param {?proto.ArrayOfStrings|undefined} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
*/
proto.SubjectTabsGrpcModel.prototype.setSubjectfiletypes = function(value) {
  return jspb.Message.setWrapperField(this, 29, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearSubjectfiletypes = function() {
  return this.setSubjectfiletypes(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasSubjectfiletypes = function() {
  return jspb.Message.getField(this, 29) != null;
};


/**
 * optional string acceptedFiles = 30;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getAcceptedfiles = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 30, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setAcceptedfiles = function(value) {
  return jspb.Message.setField(this, 30, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearAcceptedfiles = function() {
  return jspb.Message.setField(this, 30, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasAcceptedfiles = function() {
  return jspb.Message.getField(this, 30) != null;
};


/**
 * optional string maxFileSize = 31;
 * @return {string}
 */
proto.SubjectTabsGrpcModel.prototype.getMaxfilesize = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 31, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.setMaxfilesize = function(value) {
  return jspb.Message.setField(this, 31, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectTabsGrpcModel} returns this
 */
proto.SubjectTabsGrpcModel.prototype.clearMaxfilesize = function() {
  return jspb.Message.setField(this, 31, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectTabsGrpcModel.prototype.hasMaxfilesize = function() {
  return jspb.Message.getField(this, 31) != null;
};


goog.object.extend(exports, proto);

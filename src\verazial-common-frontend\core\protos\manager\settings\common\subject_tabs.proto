syntax = "proto3";

import "manager/settings/common/custom_field.proto";
import "manager/settings/common/custom_field_group.proto";
import "util.proto";

option java_multiple_files = true;
option java_package = "com.verazial.microservice.settings.core.config.domain.model.grpc";

message SubjectTabsGrpcModel {
  // Additional Tabs
  optional bool showAdditionalTabs = 1;
  // Extended Bio Fields
  optional bool showExtendedBioFieldsTab = 2;
  optional bool restrictExtendedBioFieldsTabToSpecificRoles = 3;
  optional string extendedBioFieldsRoles = 4;
  // Physical Data
  optional bool showPhysicalDataTab = 5;
  optional bool restrictPhysicalDataTabToSpecificRoles = 6;
  optional string physicalDataRoles = 7;
  // Profile Picture
  optional bool showProfilePictureTab = 8;
  optional bool restrictProfilePictureTabToSpecificRoles = 9;
  optional string profilePictureRoles = 10;
  // Relations
  optional bool showRelationsTab = 11;
  optional bool restrictRelationsTabToSpecificRoles = 12;
  optional string relationsRoles = 13;
  // Locations
  optional bool showLocationsTab = 14;
  optional bool restrictLocationsTabToSpecificRoles = 15;
  optional string locationsRoles = 16;
  // Entries and Exits
  optional bool showEntriesExitsTab = 17;
  optional bool restrictEntriesExitsTabToSpecificRoles = 18;
  optional string entriesExitsRoles = 19;
  // Entry Exit Authorizations
  optional bool showEntryExitAuthorizationsTab = 20;
  optional bool restrictEntryExitAuthorizationsTabToSpecificRoles = 21;
  optional string entryExitAuthorizationsRoles = 22;
  // Entry Exit Auth Details
  optional bool showEntryExitAuthDetails = 23;
  optional ArrayOfCustomField entryExitAuthDetailFields = 24;
  optional ArrayOfCustomFieldGroup entryExitAuthDetailFieldGroups = 25;
  // Files
  optional bool showFilesTab = 26;
  optional bool restrictFilesTabToSpecificRoles = 27;
  optional string filesRoles = 28;
  optional ArrayOfStrings subjectFileTypes = 29;
  optional string acceptedFiles = 30;
  optional string maxFileSize = 31;
}

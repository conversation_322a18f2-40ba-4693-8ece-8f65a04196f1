import { Component, Input, OnChanges, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, MessageService, PrimeNGConfig } from "primeng/api";
import { FileSelectEvent } from "primeng/fileupload";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";

@Component({
    selector: 'app-subject-files',
    templateUrl: './subject-files.component.html',
    styleUrl: './subject-files.component.css',
    providers: [MessageService, ConfirmationService]
})
export class SubjectFilesComponent implements OnInit, OnChanges {
    @Input() readAndWritePermissions: boolean = false;
    @Input() userSubject: SubjectEntity | undefined;
    @Input() userIsVerified: boolean = false;
    @Input() subjectIsVerified: boolean = false;
    @Input() managerSettings?: GeneralSettings;

    /** Flags */
    isLoading: boolean = false;
    showUploadDialog: boolean = false;

    /** Data */
    subjectFileTypes: string[] = [];
    subjectFiles: {
        type: string;
        files: StaticResourceEntity[];
    }[] = [];
    newSubjectFile: {
        type: string;
        files: StaticResourceEntity;
    } = {
        type: '',
        files: new StaticResourceEntity()
    };
    acceptedFiles: string = 'image/*,audio/*,video/*,text/*,application/pdf';
    maxFileSize: string = '16000000'; // 16MB

    /** Upload New */
    public form: FormGroup = this.fb.group({
        alias: ['', [Validators.required]],
        description: ['', [Validators.required]],
    });

    // Date Range Filter
    formGroupDate: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    formGroupDate2: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    constructor(
        private translateService: TranslateService,
        private loggerService: ConsoleLoggerService,
        private fb: FormBuilder,
        private config: PrimeNGConfig,
        private messageService: MessageService,
    ) { }

    ngOnInit(): void {
        this.isLoading = true;
        this.subjectFileTypes = this.managerSettings?.subjectTabsConfig?.subjectFileTypes ?? [];
        this.acceptedFiles = this.managerSettings?.subjectTabsConfig?.acceptedFiles ?? 'image/*,audio/*,video/*,text/*,application/pdf';
        this.maxFileSize = this.managerSettings?.subjectTabsConfig?.maxFileSize ?? '16000000'; // 16MB
        this.subjectFileTypes.forEach(type => {
            this.subjectFiles.push({
                type: type,
                files: []
            });
        });
        this.isLoading = false;
    }

    ngOnChanges(): void {
        throw new Error("Method not implemented.");
    }

    uploadNew(event: any) {
        this.newSubjectFile: {
            type: '',
            files: new StaticResourceEntity()
        };
        this.showUploadDialog = true;
    }

    onUpload(event: any) {
        this.loggerService.debug(event);
    }

    onEdit(data: StaticResourceEntity) {
        this.loggerService.debug(data);
    }

    onDelete(data: StaticResourceEntity) {
        this.loggerService.debug(data);
    }

    onCancelDialog() {
        this.showUploadDialog = false;
    }

    onSelectedFiles(event: FileSelectEvent) {
        this.loggerService.debug(event);

    }

    getLabel(key: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'subjectFileTypes')?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }

    /* Search */
    onFilter(event: any) {
        if (!event.filters['updatedAt'].value) {
            this.rangeDates = null;
            this.formGroupDate.reset();
        }
        if (!event.filters['createdAt'].value) {
            this.rangeDates = null;
            this.formGroupDate2.reset();
        }
    }

    formatSize(bytes: number) {
        const k = 1024;
        const dm = 3;
        const sizes = this.config.translation.fileSizeTypes || [];
        if (bytes === 0) {
            return `0 ${sizes[0]}`;
        }

        const i = Math.floor(Math.log(bytes) / Math.log(k));
        const formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

        return `${formattedSize} ${sizes[i]}`;
    }

}

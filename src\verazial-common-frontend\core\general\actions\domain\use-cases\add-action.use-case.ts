import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { ActionEntity } from "../entity/action.entity";
import { ActionRepository } from "../repository/action.repository";
import { Observable } from "rxjs";

export class AddActionUseCase implements UseCase<{action: ActionEntity}, ActionEntity>{
    constructor(private actionRepository: ActionRepository){}
    execute(params: { action: ActionEntity; }): Observable<ActionEntity> {
        return this.actionRepository.addAction(params);
    }
}
import { NewAction } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { NewActionEntity } from "../../domain/entity/new-action.entity";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { StructMapper } from "./struct.mapper";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";

export class NewActionMapper extends Mapper<NewAction, NewActionEntity>{

    structMapper = new StructMapper();

    override mapFrom(param: NewAction): NewActionEntity {
        return {
            reportedAt: new Date(param.getReportedAt()?.getSeconds()!! * 1000 + Math.round(param.getReportedAt()?.getNanos()!! / 1e6)),
            data: this.structMapper.mapFrom(param.getData()!!),
        }
    }

    override mapTo(param: NewActionEntity): NewAction {
        let newAction = new NewAction();
        newAction.setReportedAt(dateToTimestamp(param.reportedAt!));
        newAction.setData(this.structMapper.mapTo(param.data!));
        return newAction;
    }
}
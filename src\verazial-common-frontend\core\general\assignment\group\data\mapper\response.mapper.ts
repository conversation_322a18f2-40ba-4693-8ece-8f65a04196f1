import { Mapper } from "src/verazial-common-frontend/core/mapper"
import { AssigmentResponseEntity } from "../../domain/entity/assignment-response.entity"
import { AssignmentResponseModel } from "../models/assignment-response.model"

export class ResponseMapper extends Mapper<AssignmentResponseModel, AssigmentResponseEntity> {
    override mapFrom(param: AssignmentResponseModel): AssigmentResponseEntity {
        return {
            code: param.code,
            status: param.status,
            details: param.details,
            data: param.data
        }
    }
    override mapTo(param: AssigmentResponseEntity): AssignmentResponseModel {
        return {
            code: param.code,
            status: param.status,
            details: param.details,
            data: param.data
        }
    }

}
import { WindowParametersRepository } from "../repositories/window-parameters.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class DeleteWindowParamsByWindowIdUseCase implements UseCaseGrpc<{ windowId: string, order: number }, SuccessResponse> {
    constructor(private windowParametersRepository: WindowParametersRepository) { }
    execute(params: { windowId: string; order: number }): Promise<SuccessResponse> {
        return this.windowParametersRepository.deleteWindowParamsByWindowId(params);
    }

}
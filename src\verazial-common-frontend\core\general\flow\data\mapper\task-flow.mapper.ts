import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { TaskFlowEntity } from "../../domain/entity/task-flow.entity";
import { TaskFlowGrpcModel } from "src/verazial-common-frontend/core/generated/flow/task_flow_pb";
import { listOfActionGrpcToListOfAction, listOfActionToListOfActionGrpc } from "../../common/converter/task-flow-converter";

export class TaskFlowMapper extends Mapper<TaskFlowGrpcModel, TaskFlowEntity> {
    override mapFrom(param: TaskFlowGrpcModel): TaskFlowEntity {
        let taskFlowEntity = new TaskFlowEntity();

        taskFlowEntity.id = param.getId();
        taskFlowEntity.name = param.getName();
        taskFlowEntity.description = param.getDescription();
        taskFlowEntity.isPublished = param.getIspublished();
        taskFlowEntity.flowActions = listOfActionGrpcToListOfAction(param.getFlowactions());
        taskFlowEntity.createdAt = new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6));
        taskFlowEntity.updatedAt = new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6));

        return taskFlowEntity;
    }
    override mapTo(param: TaskFlowEntity): TaskFlowGrpcModel {
        let taskFlowModel = new TaskFlowGrpcModel();

        taskFlowModel.setId(param.id!);
        taskFlowModel.setName(param.name!);
        taskFlowModel.setDescription(param.description ? param.description : "");
        taskFlowModel.setIspublished(param.isPublished);
        taskFlowModel.setFlowactions(listOfActionToListOfActionGrpc(param.flowActions));

        return taskFlowModel;
    }
}
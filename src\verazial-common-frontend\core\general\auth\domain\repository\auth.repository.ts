import { AuthByLicenseRequestEntity } from "../entity/auth-by-license-request.entity";
import { AuthEntity } from "../entity/auth.entity";

export abstract class AuthRepository {
    abstract authenticateByUser(params: { tenantId: string, username: string, password: string }): Promise<AuthEntity>;
    abstract checkToken(params: { token: string }): Promise<boolean>;
    abstract refreshByUser(params: { oldToken: string }): Promise<AuthEntity>;
    abstract authenticateByLicense(requestParams: AuthByLicenseRequestEntity): Promise<AuthEntity>;
    abstract changeTenant(params: { newTenantId: string, token: string }): Promise<AuthEntity>;
}
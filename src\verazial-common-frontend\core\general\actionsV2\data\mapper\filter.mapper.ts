import { SearchActionsRequest } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { FieldEqualsEntity, FilterEntity, ValueEntity } from "../../domain/entity/filter.entity";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ValueMapper } from "./value.mapper";


class FieldEqualsMapper extends Mapper<SearchActionsRequest.Filter.FieldEquals,FieldEqualsEntity>{
    
    valueMapper = new ValueMapper();

    override mapFrom(param: SearchActionsRequest.Filter.FieldEquals): FieldEqualsEntity {
        throw new Error("Method not implemented.");
    }

    override mapTo(param: FieldEqualsEntity): SearchActionsRequest.Filter.FieldEquals {
        let fieldEquals = new SearchActionsRequest.Filter.FieldEquals();
        fieldEquals.setPath(param.path);
        fieldEquals.setValue(this.valueMapper.mapTo(param.value));
        return fieldEquals;
    }
}


export class FilterMapper extends Mapper<SearchActionsRequest.Filter,FilterEntity>{

    fieldEqualsMapper = new FieldEqualsMapper();
    
    override mapFrom(param: SearchActionsRequest.Filter): FilterEntity {
        return {
            
        }
    }

    override mapTo(param: FilterEntity): SearchActionsRequest.Filter {
        let filter = new SearchActionsRequest.Filter();
        filter.setFieldEquals(this.fieldEqualsMapper.mapTo(param.condition!));
        return filter;
    }
}
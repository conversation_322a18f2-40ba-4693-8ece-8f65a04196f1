import * as jspb from 'google-protobuf'

import * as manager_settings_common_generic_data_pb from '../../../manager/settings/common/generic_data_pb'; // proto import: "manager/settings/common/generic_data.proto"


export class TenantsLicensesGrpcModel extends jspb.Message {
  getTenantid(): string;
  setTenantid(value: string): TenantsLicensesGrpcModel;

  getLicensesinfo(): ArrayOfTenantLicenses | undefined;
  setLicensesinfo(value?: ArrayOfTenantLicenses): TenantsLicensesGrpcModel;
  hasLicensesinfo(): boolean;
  clearLicensesinfo(): TenantsLicensesGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): TenantsLicensesGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: TenantsLicensesGrpcModel): TenantsLicensesGrpcModel.AsObject;
  static serializeBinaryToWriter(message: TenantsLicensesGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): TenantsLicensesGrpcModel;
  static deserializeBinaryFromReader(message: TenantsLicensesGrpcModel, reader: jspb.BinaryReader): TenantsLicensesGrpcModel;
}

export namespace TenantsLicensesGrpcModel {
  export type AsObject = {
    tenantid: string,
    licensesinfo?: ArrayOfTenantLicenses.AsObject,
  }
}

export class ArrayOfTenantLicenses extends jspb.Message {
  getLicenseList(): Array<manager_settings_common_generic_data_pb.GenericDataGrpcModel>;
  setLicenseList(value: Array<manager_settings_common_generic_data_pb.GenericDataGrpcModel>): ArrayOfTenantLicenses;
  clearLicenseList(): ArrayOfTenantLicenses;
  addLicense(value?: manager_settings_common_generic_data_pb.GenericDataGrpcModel, index?: number): manager_settings_common_generic_data_pb.GenericDataGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfTenantLicenses.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfTenantLicenses): ArrayOfTenantLicenses.AsObject;
  static serializeBinaryToWriter(message: ArrayOfTenantLicenses, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfTenantLicenses;
  static deserializeBinaryFromReader(message: ArrayOfTenantLicenses, reader: jspb.BinaryReader): ArrayOfTenantLicenses;
}

export namespace ArrayOfTenantLicenses {
  export type AsObject = {
    licenseList: Array<manager_settings_common_generic_data_pb.GenericDataGrpcModel.AsObject>,
  }
}

export class ArrayOfTenantsLicenses extends jspb.Message {
  getLicensesList(): Array<TenantsLicensesGrpcModel>;
  setLicensesList(value: Array<TenantsLicensesGrpcModel>): ArrayOfTenantsLicenses;
  clearLicensesList(): ArrayOfTenantsLicenses;
  addLicenses(value?: TenantsLicensesGrpcModel, index?: number): TenantsLicensesGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfTenantsLicenses.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfTenantsLicenses): ArrayOfTenantsLicenses.AsObject;
  static serializeBinaryToWriter(message: ArrayOfTenantsLicenses, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfTenantsLicenses;
  static deserializeBinaryFromReader(message: ArrayOfTenantsLicenses, reader: jspb.BinaryReader): ArrayOfTenantsLicenses;
}

export namespace ArrayOfTenantsLicenses {
  export type AsObject = {
    licensesList: Array<TenantsLicensesGrpcModel.AsObject>,
  }
}


import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { DataSourceEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source.entity';
import { GetAllDataSourcesUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/get-all-data-sources.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-data-source-page',
  templateUrl: './data-source-page.component.html',
  styleUrl: './data-source-page.component.css',
  providers: [MessageService, ConfirmationService]
})
export class DataSourcePageComponent implements OnInit, OnDestroy {

  @Output() dataSource = new EventEmitter<DataSourceEntity[]>();

  showEmpty: boolean = false;
  showListDataSource: boolean = false;
  showNewDataSourceDialog: boolean = false;
  isLoading: boolean = false;

  listDataSource: DataSourceEntity[] = [];

  // Access code identifier
  access_identifier: string = AccessIdentifier.PASS_DATASOURCE;
  canReadAndWrite: boolean = false;

  constructor(
    private checkPermissions: CheckPermissionsService,
    private messageService: MessageService,
    private getAllDataSourcesUseCase: GetAllDataSourcesUseCase,
    private translateService: TranslateService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
  ) { }

  ngOnInit(): void {
    this.isLoading = true;
    this.getDataSource();
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
  }

  ngOnDestroy(): void {
  }

  getDataSource() {
    try {
      this.getAllDataSourcesUseCase.execute().then(
        (data) => {
          this.listDataSource = data
          this.isLoading = false;
        },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_DATA_SOURCES, 0, 'ERROR', '', at_attributes);
        }
      );
    } catch (e) {
      this.loggerService.error(e!);
    }
  }

  createNewDataSource(event: any) {
    this.showNewDataSourceDialog = true;
  }

  cancelNewDataSource() {
    this.showNewDataSourceDialog = false;
  }

  async operationResult(result: OperationStatus) {
    if (result.status == Status.SUCCESS) {
      this.messageService.add({
        severity: 'success',
        summary: this.translateService.instant('content.successTitle'),
        detail: this.translateService.instant('messages.saved_successfully'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
      this.showNewDataSourceDialog = false;
      this.getDataSource();
    } else {
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('content.errorTitle'),
        detail: result.message,
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });

    }
  }
}
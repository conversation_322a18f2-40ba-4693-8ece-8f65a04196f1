import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class DeleteDayTimeScheduleUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.groupCategoryRepository.deleteDayTimeSchedule(params);
    }
}
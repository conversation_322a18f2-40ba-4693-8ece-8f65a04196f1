import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ApplicationEntity } from "../../domain/entities/application.entity";
import { ApplicationModel } from "../model/application.model";

export class ApplicationMapper extends Mapper<ApplicationModel, ApplicationEntity> {
    override mapFrom(param: ApplicationModel): ApplicationEntity {
        return {
            id: param.id,
            appRegistryId: param.appRegistryId,
            dataSourceId: param.dataSourceId,
            applicationName: param.applicationName,
            flowType: param.flowType,
            fullPath: param.fullPath,
            technology: param.technology,
            applicationType: param.applicationType,
            status: param.status,
            createdAt: param.createdAt,
            updatedAt: param.updatedAt
        }
    }
    override mapTo(param: ApplicationEntity): ApplicationModel {
        return {
            id: param.id,
            appRegistryId: param.appRegistryId,
            dataSourceId: param.dataSourceId,
            applicationName: param.applicationName,
            flowType: param.flowType,
            fullPath: param.fullPath,
            technology: param.technology,
            applicationType: param.applicationType,
            status: param.status,
            createdAt: param.createdAt,
            updatedAt: param.updatedAt
        }
    }

}
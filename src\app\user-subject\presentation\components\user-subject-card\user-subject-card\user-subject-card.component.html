<div *ngIf="userSubject else loadingSpinner" class="flex flex-column justify-content-center align-items-center cardContent">
    <div class="flex flex-row justify-content-center">
        <div class="flex leftCardContent mr-2">
            <div class="flex align-items-center">
                <!-- <p-avatar [image]="image" styleClass="mr-2" size="xlarge" [style]="{'min-height': '100px', 'min-width': '100px', 'border': 'solid #9E9E9E', 'border-radius': '6px'}"></p-avatar> -->
                <app-profile-pic [image]="image"></app-profile-pic>
            </div>
            <div class="flex flex-column justify-content-center align-items-start ml-4 gap-4">
                <div class="flex flex-column align-items-start">
                    <label class="subjectNameRoleLabel mb-1">{{ this.userSubject?.names + ' ' + this.userSubject?.lastNames }}</label>
                    <label class="subjectRoleLabel">
                        <div *ngIf="userSubject?.roles?.length > 0 else noRole">
                            <div *ngFor="let role of userSubject.roles">
                                @if (role) {
                                    {{role.name == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : role.name}}
                                }
                            </div>
                        </div>
                        <ng-template #noRole>
                            <div>{{"user.no_roles_assigned" | translate}}</div>
                        </ng-template>
                    </label>
                </div>
                <div class="flex flex-column align-items-start pb-2">
                    <label class="subjectIDInfoLabel">{{"user.num_id" | translate}}</label>
                    <label class="subjectIDInfoData">{{userSubject?.numId}}</label>
                </div>
            </div>
        </div>
        <div class="cartContentDivider">
            <img src="verazial-common-frontend/assets/images/admin/shortDividerVertical.svg">
        </div>
        <div class="flex justify-content-center align-items-center">
            <div class="subjectProfileData">
                <div class="subjectProfileDataLabel mb-3">
                    <label>{{"titles.personalData" | translate}}</label>
                </div>
                <div class="flex">
                    <div>
                        <div class="subjectProfileDataInfo">
                            <label class="subjectIDInfoLabel">{{"user.names" | translate}}</label>
                            <label class="subjectIDInfoDataSmall">{{userSubject?.names}}</label>
                        </div>
                        <div class="subjectProfileDataInfo">
                            <label class="subjectIDInfoLabel">{{"user.lastNames" | translate}}</label>
                            <label class="subjectIDInfoDataSmall">{{userSubject?.lastNames}}</label>
                        </div>
                    </div>
                    <div class="ml-5">
                        <div class="subjectProfileDataInfo">
                            <label class="subjectIDInfoLabel">{{"user.gender" | translate}}</label>
                            <label class="subjectIDInfoDataSmall">{{ userSubject.gender != '' && userSubject.gender != undefined && userSubject.gender != null ? ('options.' + userSubject.gender | translate) : '' }}</label>
                        </div>
                        <div class="subjectProfileDataInfo">
                            <label class="subjectIDInfoLabel">{{"user.birthdate" | translate}}</label>
                            <label class="subjectIDInfoDataSmall">{{ userSubject?.birthdate | date:('dateFormatLong' | translate)}}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="showButtons" class="cartContentDivider">
            <img src="verazial-common-frontend/assets/images/admin/shortDividerVertical.svg">
        </div>
        <div *ngIf="showButtons" class="buttons flex justify-content-center align-items-center gap-2">
            <!-- Edit User/Subject Button -->
            <div>
                <div class="buttonContainer">
                    <p-button [disabled]="disableButtons" [style]="{'min-width': '120px', 'text-align': 'start'}"
                        class="defaultButton" severity="secondary" icon="pi pi-{{ (editMode || editEnabled) && userIsVerified && canReadAndWrite ? 'pencil' : 'eye' }}" size="small"
                        label="{{ ((editMode || editEnabled) && userIsVerified && canReadAndWrite ? 'edit' : 'view_data') | translate }}"
                        (click)="openEditUserSubjectDialog()"></p-button>
                    <p-button [disabled]="disableButtons"
                        class="smallButton" severity="secondary" icon="pi pi-{{ (editMode || editEnabled) && userIsVerified && canReadAndWrite ? 'pencil' : 'eye' }}" size="small"
                        (click)="openEditUserSubjectDialog()"></p-button>
                </div>
            </div>
            <!-- Delete User/Subject Button -->
            <div *ngIf="!editMode && !editEnabled">
                <div *ngIf="showDelete" class="buttonContainer">
                    <p-button pRipple severity="danger" class="defaultButton" [disabled]="disableButtons || !userIsVerified"
                        icon="pi pi-trash" size="small" [style]="{'min-width': '120px', 'text-align': 'start'}"
                        label="{{'delete_entity' | translate}}" (click)="onDeleteUserSubject()"></p-button>
                    <p-button pRipple severity="danger" class="smallButton" icon="pi pi-trash" [disabled]="disableButtons"
                        size="small" (click)="onDeleteUserSubject()"></p-button>
                </div>
            </div>
            <!-- Verify User/Subject Button -->
            <div *ngIf="!isVerified" class="buttonContainer" pTooltip="{{disabledVerifyBtnToolTip | translate }}" tooltipPosition="top">
                <p-button
                    [style]="{'min-width': '120px', 'text-align': 'start', 'backgroundColor': '#204887', 'borderColor': '#204887'}"
                    class="defaultButton" label="{{ 'verify' | translate }}" icon="pi pi-check-square" size="small"
                    [disabled]="!verifyBtnAvailable || disableButtons" (click)="openVerifyDialog()"></p-button>
                <p-button [style]="{'backgroundColor': '#204887', 'borderColor': '#204887'}" class="smallButton"
                    icon="pi pi-check-square" size="small" [disabled]="!verifyBtnAvailable"
                    (click)="openVerifyDialog()"></p-button>
            </div>
        </div>
    </div>
    <div class="smallScreenCardContent">
        <div class="">
            <img src="verazial-common-frontend/assets/images/admin/longerDivider.svg">
        </div>
        <div class="flex flex-row justify-content-start">
            <div class="subjectProfileDataInfo SS">
                <label class="subjectIDInfoLabel">{{"user.names" | translate}}</label>
                <label class="subjectIDInfoDataSmall">{{userSubject?.names}}</label>
            </div>
            <div class="subjectProfileDataInfo">
                <label class="subjectIDInfoLabel">{{"user.lastNames" | translate}}</label>
                <label class="subjectIDInfoDataSmall">{{userSubject?.lastNames}}</label>
            </div>
        </div>
    </div>
</div>

<ng-template #loadingSpinner>
    <div class="flex justify-content-center">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
    </div>
</ng-template>
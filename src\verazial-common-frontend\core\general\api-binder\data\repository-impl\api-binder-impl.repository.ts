import { Injectable } from "@angular/core";
import { ApiBinderRepository } from "../../domain/repository/api-binder.repository";
import { ApiBinderMapper } from "../mapper/api-binder.mapper";
import { UnenrollIdentityResponseEntity } from "../../domain/entity/unenroll-identity.entity";
import { UnenrollIdentityRequest } from "src/verazial-common-frontend/core/generated/api-binder/apibinder_pb";
import { CoreApiBinderServiceClient } from "src/verazial-common-frontend/core/generated/api-binder/ApibinderServiceClientPb";
import { environment } from "src/environments/environment";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { HttpClient } from "@angular/common/http";

@Injectable({
    providedIn: 'root',
})
export class ApiBinderRepositoryImpl extends ApiBinderRepository {

    apiBinderMapper = new ApiBinderMapper()

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override unenrollIdentity(params: { numId: string; }): Promise<UnenrollIdentityResponseEntity> {
        let request = new UnenrollIdentityRequest();
        request.setId(params.numId);

        let coreApiBinderServiceClient = new CoreApiBinderServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] })

        return new Promise((resolve, reject) => {
            coreApiBinderServiceClient.unenrollIdentity(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    resolve(this.apiBinderMapper.mapFrom(response));
                }
            });
        });
    }
}
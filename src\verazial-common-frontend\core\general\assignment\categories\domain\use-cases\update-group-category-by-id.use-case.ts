import { GroupCategoryRepository } from "../repository/group-category.repository";
import { GroupCategoryEntity } from "../entity/group-category.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateGroupCategoryByIdUseCase implements UseCaseGrpc<{ group: GroupCategoryEntity }, GroupCategoryEntity> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { group: GroupCategoryEntity; }): Promise<GroupCategoryEntity> {
        return this.groupCategoryRepository.updateGroupCategoryById(params)
    }
}

<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh' }">
  <div class="grid" [formGroup]="form">
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbName">{{ 'tenant.name' | translate }}</label>
      <input type="text" pInputText formControlName="dbName" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbEngine">{{ 'tenant.engine' | translate }}</label>
      <input type="text" pInputText formControlName="dbEngine" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbHost">{{ 'tenant.host' | translate }}</label>
      <input type="text" pInputText formControlName="dbHost" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbPort">{{ 'tenant.port' | translate }}</label>
      <input type="text" pInputText formControlName="dbPort" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbDatabase">{{ 'tenant.database' | translate }}</label>
      <input type="text" pInputText formControlName="dbDatabase" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbScheme">{{ 'tenant.schema' | translate }}</label>
      <input type="text" pInputText formControlName="dbScheme" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbUsername">{{ 'tenant.username' | translate }}</label>
      <input type="text" pInputText formControlName="dbUsername" />
    </div>
    <div class="col-12 md:col-6 lg:col-6 flex flex-column field">
      <label class="label-form" for="dbPassword">{{ 'tenant.password' | translate }}</label>
      <p-password formControlName="dbPassword" [toggleMask]="true" />
    </div>
  </div>
</p-scrollPanel>
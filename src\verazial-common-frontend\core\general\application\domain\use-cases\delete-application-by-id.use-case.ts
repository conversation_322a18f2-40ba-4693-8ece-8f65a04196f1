import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationRepository } from "../repositories/application.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export class DeleteApplicationByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: { id: string }): Promise<SuccessResponse> {
        return this.applicationRepository.deleteApplicationById(params);
    }
}
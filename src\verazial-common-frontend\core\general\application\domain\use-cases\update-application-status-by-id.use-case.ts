import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationRepository } from "../repositories/application.repository";

export class UpdateApplicationStatusByIdUseCase implements UseCaseGrpc<{ id: string, status: boolean }, SuccessResponse> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: { id: string; status: boolean; }): Promise<SuccessResponse> {
        return this.applicationRepository.updateApplicationStatusById(params)
    }
}
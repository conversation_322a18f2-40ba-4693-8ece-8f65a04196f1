<div class="container-flow">
    <p-toast></p-toast>
    <div class="content-list-flows gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <label class="title-list-flows">{{ "titles.flows" | translate}}</label>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'flow.new_flow'| translate }}" icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNewFlow()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNewFlow()"></p-button>
                </div>
            </div>
        </div>
        <div>

        </div>
        <p-table
            #dt
            [value]="data"
            (onFilter)="onFilter($event, dt)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['name', 'isPublished', 'updatedAt']"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column-sm" pSortableColumn="name">{{'flow.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="isPublished">{{ 'flow.status' | translate }}<p-sortIcon field="status"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th style="width: 5%;" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="isPublished" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {value: true},
                                        {value: false}
                                    ]"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionValue="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        <p-tag [value]="flowStatus(value)" [style]="{'background': getSeverity(value)!}"></p-tag>
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <p-tag [value]="flowStatus(option.value)" [style]="{'background': getSeverity(option.value)!}"></p-tag>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td (click)="editFlow(data)" showDelay="1000" pTooltip="{{data.name}}" tooltipPosition="top" class="ellipsis-cell">{{data.name}}</td>
                    <td (click)="editFlow(data)" showDelay="1000" pTooltip="{{flowStatus(data.isPublished)}}" tooltipPosition="top" class="ellipsis-cell"><p-tag [value]="flowStatus(data.isPublished)" [style]="{'background': getSeverity(data.isPublished)!}"></p-tag></td>
                    <td (click)="editFlow(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{data.updatedAt | date:('dateTimeFormat' | translate)}}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="updateTaskFlow(data)"></button>
                            <button pButton pRipple [disabled]="!readAndWritePermissions" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteFlow(data)"></button>
                            <button pButton pRipple icon="pi pi-arrow-right" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editFlow(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>

    </div>

</div>

<p-dialog [(visible)]="showUpdateTaskFlowDialog" [style]="{ width: '380px' }" header="{{ 'flow.update_flow' | translate }}" [modal]="true" styleClass="p-fluid">
    <ng-template pTemplate="content">
        <div class="flex flex-column gap-2" [formGroup]="form">
            <div class="field">
                <label class="label-form" for="flowName">{{ "flow.name" | translate}}</label>
                <input type="text" pInputText formControlName="flowName" id="flowName" required autofocus [class.ng-invalid]="flowNameError" [class.ng-dirty]="flowNameError"/>
                <div *ngIf="flowNameError">
                    <small class="error" id="lastNames-help">{{ flowNameErrorMessage | translate }}</small>
                </div>
            </div>

            <div class="field">
                <label class="label-form" for="flowDescription">{{ "flow.description" | translate}}</label>
                <textarea id="flowDescription" pInputTextarea formControlName="flowDescription" required rows="2" cols="30" [autoResize]="false" [class.ng-invalid]="flowDescriptionError" [class.ng-dirty]="flowDescriptionError"></textarea>
                <div *ngIf="flowDescriptionError">
                    <small class="error" id="lastNames-help">{{ flowDescriptionErrorMessage | translate }}</small>
                </div>
            </div>
            <div class="flex flex-row justify-content-between">
                <label>{{ 'publish' | translate }}</label>
                <p-inputSwitch id="isPublished" formControlName="isPublished" [(ngModel)]="checked"></p-inputSwitch>
            </div>
        </div>
     </ng-template>

     <ng-template pTemplate="footer">
        <div class="flex flex-row justify-content-center gap-2">
            <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="showUpdateTaskFlowDialog=false"></button>
            <p-button label="{{ 'save' | translate }}" class="p-button-text" (click)="confirmDialogUpdateTaskFlow()"
            [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
            ></p-button>
        </div>

     </ng-template>
</p-dialog>

<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<div [formGroup]="form">
    <div class="flex flex-column mx-auto" style="min-height: 16rem; max-width: 20rem">
        <div class="field p-fluid">
            <label class="text-sm font-semibold" for="dataSourceName">{{ 'content.type' | translate }}</label>
            <p-dropdown 
                appendTo="body" 
                [options]="listDataSourceParamTypes" 
                placeholder="{{'content.select' | translate}}" 
                optionLabel="value"
                formControlName="type"
                [(ngModel)]="selectedType"
                (onChange)="trackDataChange($event)"
                id="type"
                dataKey="key"
                [ngClass]="!isValid('type') && form.controls['type'].touched? 'ng-invalid ng-dirty':'' "
                >
                <ng-template pTemplate="selectedItem">
                    <div class="flex align-items-center gap-2" *ngIf="selectedType">
                        <div>{{selectedType.value}}</div>
                    </div>
                </ng-template>
                <ng-template let-type pTemplate="item">
                    <div class="flex align-items-center gap-2">
                        <div>{{ type.value }}</div>
                    </div>
                </ng-template>
            </p-dropdown>
            <small *ngIf="!isValid('type') && form.controls['type'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
        </div>
        <div class="field p-fluid">
            <label class="text-sm font-semibold" for="parameter">{{ 'pass_datasource.parameter' | translate }}</label>
            <input id="parameter" formControlName="parameter" pInputText id="input" type="text"
            [ngClass]="!isValid('parameter') && form.controls['parameter'].touched? 'ng-invalid ng-dirty':'' "/>
            <small *ngIf="!isValid('parameter') && form.controls['parameter'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
        </div>
        <div class="field p-fluid">
            <label class="text-sm font-semibold" for="value">{{ 'content.value' |translate }}</label>
            <input id="value" formControlName="value" pInputText id="input" type="text" 
            [ngClass]="!isValid('value') && form.controls['value'].touched? 'ng-invalid ng-dirty':'' "/>
            <small *ngIf="!isValid('value') && form.controls['value'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
        </div>
        <div class="flex mt-2 justify-content-left">
            <p-button (onClick)="addParameter()" 
            icon="pi pi-plus"
            [style]="{'width': '200px', 'color': '#FFFFFF', 'border': 'none', 'background': '#64748B' }" 
            label="{{ 'pass_datasource.add_parameter' | translate }}" />
        </div>
        
    </div>
</div>

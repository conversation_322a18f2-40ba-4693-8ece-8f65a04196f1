import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationFlowEntity } from "../entity/application-flow.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationFlowRepository } from "../repository/application-flow.repository";

export class UpdateApplicationFlowByAppIdUseCase implements UseCaseGrpc<ApplicationFlowEntity, SuccessResponse> {
    constructor(private applicationFlowRespository: ApplicationFlowRepository) { }
    execute(params: ApplicationFlowEntity): Promise<SuccessResponse> {
        return this.applicationFlowRespository.updateApplicationFlowByAppId(params);
    }
}
export class ReplaceAttributesRequestEntity {
    subjectId: string;
    attributes: BiometricAttribute[];

    constructor(subjectId: string, attributes: BiometricAttribute[]) {
        this.subjectId = subjectId;
        this.attributes = attributes;
    }
}

export class BiometricAttribute {
    name: string;
    value: string;

    constructor(name: string, value: string) {
        this.name = name;
        this.value = value;
    }
}

export class ReplaceAttributesResult {
    status: Status;
    times: TimeTracker;

    constructor(status: Status, times: TimeTracker) {
        this.status = status;
        this.times = times;
    }
}

export class Status {
    success?: Success;
    failure?: Failure;

    constructor(success?: Success, failure?: Failure) {
        this.success = success;
        this.failure = failure;
    }
}

export class Success {}

export class Failure {
    error: FailureReason;

    constructor(error: FailureReason) {
        this.error = error;
    }
}

export enum FailureReason {
    SUBJECT_NOT_FOUND = 0,
    UNKNOWN = 1
}

export class TimeTracker {
    totalTime: number;

    constructor(totalTime: number) {
        this.totalTime = totalTime;
    }
}
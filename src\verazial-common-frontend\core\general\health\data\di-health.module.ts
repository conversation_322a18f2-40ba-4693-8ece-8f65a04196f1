import { NgModule } from "@angular/core";
import { HealthRepository } from "../domain/repository/health.repository";
import { GetStatusUseCase } from "../domain/use-case/get-status.use-case";
import { CommonModule } from "@angular/common";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { HealthRepositoryGrpcImpl } from "./repository-impl/health-impl-grpc.repository";

const getStatusUseCaseFactory =
    (healthRepository: HealthRepository) => new GetStatusUseCase(healthRepository);

export const getStatusUseCaseProvider = {
    provide: GetStatusUseCase,
    useFactory: getStatusUseCaseFactory,
    deps: [HealthRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        getStatusUseCaseProvider,
        { provide: HealthRepository, useClass: HealthRepositoryGrpcImpl },
        provideHttpClient(withInterceptorsFromDi())
    ]
})
export class DiHealthModule { }
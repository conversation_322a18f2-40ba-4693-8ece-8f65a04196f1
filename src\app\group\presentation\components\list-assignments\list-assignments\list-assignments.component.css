.main{
    padding: 0 0 0 0!important;
    /* min-width: 85%!important; */
    width: 100vw!important;
    height: 100%;
}

.container-assignments{
    margin: 20px 20px 0 20px;;
}

.content-list-assignments{
    width: 100%;
    display: flex;
    flex-direction: column;
    /*align-items: center;*/
}

.header-assignments{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 15px 0 15px 0;
}

.title-list-assignments{
    color: #0AB4BA;
    font-weight: 700;
    font-size: 20px;
    line-height: 32px;
}

.general{
    width: 122px;  
    height: 38px;
    border: none;
    font-weight: 700;
    font-size: 14px;
    color: #495057; 
    border: 1px solid #CED4DA;
    background: white;
    font-family: 'Open Sans';
    margin: 4px 0 4px 0
}

.active{
    width: 122px;  
    height: 38px;
    border: none;
    font-weight: 700;
    font-size: 14px;
    color: white; 
    border: 1px solid #CED4DA;
    background: #818EA1;
    font-family: 'Open Sans';
    margin: 4px 0 4px 0
}


import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ApplicationFlowPageRoutingModule } from './application-flow-page-routing.module';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { EditApplicationFlowModule } from '../../components/edit-application-flow/edit-application-flow.module';
import { ListApplicationFlowsModule } from '../../components/list-application-flows/list-application-flows.module';
import { ApplicationFlowPageComponent } from './application-flow-page/application-flow-page.component';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [
    ApplicationFlowPageComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Routing */
    ApplicationFlowPageRoutingModule,
    /* Custom */
    EmptyModule,
    LoadingSpinnerModule,
    EditApplicationFlowModule,
    ListApplicationFlowsModule
  ],
  exports:[
    ApplicationFlowPageComponent
  ]
})
export class ApplicationFlowPageModule { }

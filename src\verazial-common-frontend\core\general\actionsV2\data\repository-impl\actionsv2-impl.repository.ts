import { Injectable } from "@angular/core";
import { ActionsV2Repository } from "../../domain/repository/actionsV2.repository";
import { NewActionEntity } from "../../domain/entity/new-action.entity";
import { HttpClient } from "@angular/common/http";
import { NewActionMapper } from "../mapper/new-action.mapper";
import { environment } from "src/environments/environment";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { CoreActionsServiceV2Client } from "src/verazial-common-frontend/core/generated/actionsV2/ActionsServiceClientPb";
import { SearchActionsRequestEntity } from "../../domain/entity/search-actions-request.entity";
import { ActionEntity } from "../../domain/entity/action.entity";
import { SearchActionsRequestMapper } from "../mapper/search-actions-request.mapper";
import { Action, NewAction } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { ActionMapper } from "../mapper/action.mapper";
import { CountActionsRequestEntity } from "../../domain/entity/count-actions-request.entity";
import { CountActionsResponseEntity } from "../../domain/entity/count-actions-response.entity";
import { CountActionsRequestMapper } from "../mapper/count-actions-request.mapper";
import { CountActionsResponseMapper } from "../mapper/count-actions-response.mapper";

@Injectable({
    providedIn: 'root',
})
export class ActionsV2RepositoryImpl extends ActionsV2Repository{

    newActionMapper = new NewActionMapper();
    searchActionsRequestMapper = new SearchActionsRequestMapper();
    actionMapper = new ActionMapper();
    countActionsRequestMapper = new CountActionsRequestMapper();
    countActionsResponseMapper = new CountActionsResponseMapper();


    constructor(
        private httpClient: HttpClient,
    ){
        super();
    }

    /**
     * Registering a new action
     * @param newAction
     * @returns Promise<void>
     */
    override registerAction(params: { newAction: NewAction, token?: string }): Promise<ActionEntity> {

        let coreActionsServiceV2Client = params.token ? new CoreActionsServiceV2Client(`${environment.grpcApiGateway}`) : new CoreActionsServiceV2Client(`${environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        let metadata = { Authorization: `Bearer ${params.token}` }

        return new Promise((resolve, reject) => {
            coreActionsServiceV2Client.registerAction(params.newAction, params.token ? metadata : null, (err, response)=>{
                if(err){
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject( failure );
                }else{
                    resolve(this.actionMapper.mapFrom(response));
                }
            });
        });
    }

    /**
     * Searching for actions
     * @param params
     * @returns Promise<ActionEntity[]>
     */
    override searchActions(params: SearchActionsRequestEntity): Promise<ActionEntity[]> {
        let actions: ActionEntity[] = [];

        let request = this.searchActionsRequestMapper.mapTo(params);

        let coreActionsServiceV2Client = new CoreActionsServiceV2Client(`${environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        let grpc = coreActionsServiceV2Client.searchActions(request);

        return new Promise((resolve, reject) => {
            grpc.on('data', (response: Action) => {
                actions.push(this.actionMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject( failure );
            });

            grpc.on('end', () => {
                resolve(actions)
            });
        });
    }


    /**
     * Counting actions
     * @param params CountActionsRequestEntity
     * @returns Promise<CountActionsResponseEntity>
     * */

    override countActions(params: CountActionsRequestEntity): Promise<CountActionsResponseEntity> {
        let request = this.countActionsRequestMapper.mapTo(params);

        let coreActionsServiceV2Client = new CoreActionsServiceV2Client(`${environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreActionsServiceV2Client.countActions(request, null, (err, response)=>{
                if(err){
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject( failure );
                }else{
                    resolve(this.countActionsResponseMapper.mapFrom(response));
                }
            });
        });
    }

}
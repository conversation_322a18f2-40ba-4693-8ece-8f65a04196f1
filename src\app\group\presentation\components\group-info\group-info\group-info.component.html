<div [formGroup]="form">
    <div class="mt-4 ">
        <div class="flex justify-content-end requiredFieldsLabel">
            {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
        </div>
        <div class="field">
            <label class="label-form" for="adminGroupName">{{ 'content.name' | translate }} <span *ngIf="isRequiredField('adminGroupName')" class="requiredStar">*</span></label>
            <input type="text" pInputText formControlName="adminGroupName" (ngModelChange)="trackNameChanges($event)"/>
            <small *ngIf="!isValid('adminGroupName') && form.controls['adminGroupName'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
        </div>
        <div class="field">
            <label class="label-form" for="adminGroupDescription">{{ 'content.description' | translate }} <span *ngIf="isRequiredField('adminGroupDescription')" class="requiredStar">*</span></label>
            <textarea 
                formControlName="adminGroupDescription"
                rows="5"
                cols="30" 
                pInputTextarea
                (ngModelChange)="trackDescriptionChanges($event)"
                >
            </textarea>
        </div>
    </div>
</div>

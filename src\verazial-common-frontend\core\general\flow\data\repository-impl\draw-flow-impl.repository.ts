import { Injectable } from "@angular/core";
import { DrawFlowRepository } from "../../domain/repository/draw-flow.repository";
import { DrawFlowMapper } from "../mapper/draw-flow.mapper";
import { environment } from "src/environments/environment";
import { DrawFlowEntity } from "../../domain/entity/draw-flow.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { StringParam } from "src/verazial-common-frontend/core/generated/util_pb";
import { CoreDrawFlowServiceClient } from "src/verazial-common-frontend/core/generated/flow/Draw_flowServiceClientPb";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { HttpClient } from "@angular/common/http";

@Injectable({
    providedIn: 'root',
})
export class DrawFlowRepositoryImpl extends DrawFlowRepository {


    drawFlowMapper = new DrawFlowMapper();

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override createDrawFlow(params: { drawFlow: DrawFlowEntity; }): Promise<DrawFlowEntity> {
        let request = this.drawFlowMapper.mapTo(params.drawFlow);

        let coreDrawFlowServiceClient = new CoreDrawFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreDrawFlowServiceClient.createDrawFlow(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.drawFlowMapper.mapFrom(response.getDrawflow()!));
                    }
                }
            });
        });
    }

    override getDrawFlowByTaskFlowId(params: { taskFlowId: string; }): Promise<DrawFlowEntity> {
        let request = new StringParam();
        request.setParameter(params.taskFlowId);

        let coreDrawFlowServiceClient = new CoreDrawFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreDrawFlowServiceClient.getDrawFlowByTaskFlowId(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.drawFlowMapper.mapFrom(response.getDrawflow()!));
                    }
                }
            });
        });
    }

    override deleteDrawFlowByTaskFlowId(params: { taskFlowId: string; }): Promise<SuccessResponse> {
        let request = new StringParam();
        request.setParameter(params.taskFlowId);

        let success!: SuccessResponse;

        let coreDrawFlowServiceClient = new CoreDrawFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreDrawFlowServiceClient.deleteDrawFlowByTaskFlowId(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override updateDrawFlow(params: { drawFlow: DrawFlowEntity; }): Promise<DrawFlowEntity> {
        let request = this.drawFlowMapper.mapTo(params.drawFlow);

        let coreDrawFlowServiceClient = new CoreDrawFlowServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreDrawFlowServiceClient.updateDrawFlow(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.drawFlowMapper.mapFrom(response.getDrawflow()!));
                    }
                }
            });
        });
    }

}
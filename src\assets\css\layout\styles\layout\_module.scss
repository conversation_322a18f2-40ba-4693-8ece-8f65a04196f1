.divider{
    margin: 30px 0 20px 0;
    .label-div{
        color: #616161;
        font-weight: 600;
        font-size: 13px;
        line-height: 14px;
        margin: 0px 10px 0px 10px;
    }

    p {
        display: flex;
        flex-direction: row;
     }
     p:before,
     p:after {
        content: "";
        flex: 1 1;
        border-bottom: 1px solid #BDBDBD;
        margin: auto;
     }     
}
    
.lang-button{
    background: #FFFFFF;
    box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    border-radius: 100px;
    .lang-flag-icon{
        width: 50px;
        height: 50px;
    }
    .lang-label{
        font-style: normal;
        font-weight: 700;
        font-size: 17.5px;
        text-align: center;
        color: #204887;
    }
}

.dialog-header{
    font-style: normal;
    font-weight: 600;
    color: #495057;
}

.lang-option-container{
    border: 1px solid #FFFFFF;
    min-width: 110px;
    max-width: 110px;
    padding: 25px 20px 15px 20px;

    &:hover {
        cursor: pointer;
        background: #F5F5F5;
        border: 1px solid #BDBDBD;
        border-radius: 15px;
    }

    .lang-option-flag-icon{
        width: 30px;
        height: 30px;
    }
    .lang-option-label{
        font-style: normal;
        font-weight: 400;
        font-size: 15px;
        color: #495057;
    }
}
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ActionModel } from "../model/action.model";
import { ActionEntity } from "../../domain/entity/action.entity";

export class ActionMapper extends Mapper<ActionModel, ActionEntity>{
    override mapFrom(param: ActionModel): ActionEntity {
        return {
            id: param.actions.id,
            applicationId: param.actions.applicationId,
            eventTimestamp: param.actions.eventTimestamp,
            executorId: param.actions.executorId,
            receiverId: param.actions.receiverId,
            receiverProfile: param.actions.receiverProfile,
            locationId: param.actions.locationId,
            deviceId: param.actions.deviceId,
            segmentId: param.actions.segmentId,
            actionId: param.actions.actionId,
            actionDuration: param.actions.actionDuration,
            actionResult: param.actions.actionResult,
            technologyId: param.actions.technologyId,
            samplesNumber: param.actions.samplesNumber,
            samplesSequentially: param.actions.samplesSequentially,
            sensorBrand: param.actions.sensorBrand,
            sensorModel: param.actions.sensorModel,
            attributes: param.actions.attributes,
            numericAttributes: param.actions.numericAttributes,
            samples: param.actions.samples
        }
    }
    override mapTo(param: ActionEntity): ActionModel {
        return {
            status: "",
            actions: {
                id: param.id,
                applicationId: param.applicationId,
                eventTimestamp: param.eventTimestamp,
                executorId: param.executorId,
                receiverId: param.receiverId,
                receiverProfile: param.receiverProfile,
                locationId: param.locationId,
                deviceId: param.deviceId,
                segmentId: param.segmentId,
                actionId: param.actionId,
                actionDuration: param.actionDuration,
                actionResult: param.actionResult,
                technologyId: param.technologyId,
                samplesNumber: param.samplesNumber,
                samplesSequentially: param.samplesSequentially,
                sensorBrand: param.sensorBrand,
                sensorModel: param.sensorModel,
                attributes: param.attributes,
                numericAttributes: param.numericAttributes,
                samples: param.samples
            }
        }
    }
}
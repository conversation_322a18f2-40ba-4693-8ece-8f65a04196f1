import { DataSourceRepository } from "../repositories/data-source.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export class DeleteDataSourceByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private dataSourceRepository: DataSourceRepository) { }
    execute(params: { id: string }): Promise<SuccessResponse> {
        return this.dataSourceRepository.deleteDataSourceById(params)
    }
}
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ApplicationWindowEntity } from "../../domain/entities/application-window.entity";
import { ApplicationWindowGrpcModel } from "src/verazial-common-frontend/core/generated/application/window_pb";
import { convertToListOfWindowsParams } from "../../common/converter/application.converter";

export class ApplicationWindowGrpcMapper extends Mapper<ApplicationWindowGrpcModel, ApplicationWindowEntity> {
    override mapFrom(param: ApplicationWindowGrpcModel): ApplicationWindowEntity {
        return {
            id: param.getId(),
            applicationId: param.getApplicationid(),
            windowName: param.getWindowname(),
            windowOrder: param.getWindoworder(),
            target: param.getTarget(),
            windowParameters: convertToListOfWindowsParams(param.getParameters()),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }
    override mapTo(param: ApplicationWindowEntity): ApplicationWindowGrpcModel {
        let model = new ApplicationWindowGrpcModel();
        model.setId(param.id!!);
        model.setApplicationid(param.applicationId!!);
        model.setWindowname(param.windowName!!);
        model.setWindoworder(param.windowOrder!!);
        model.setTarget(param.target!!);
        return model;
    }

}
<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<p-toast></p-toast>
<div class="main-container">
    <!--<div  class="main-progress">
        <p-progressBar [value]="progressValue" [color]="progressColor" [showValue]="progressEnabled">
            <ng-template pTemplate="content" let-value style="background-color: #0ab4ba; border: solid 1px white; height: 10px ">
            </ng-template>
        </p-progressBar>
    </div>-->
    <div class="topBarItems" id="topBar">
        <div class="leftSideItems">
            <div class="recrodsTitle">
                {{'menu.reports_verification' | translate}}
            </div>
        </div>
        <div class="rightSideItems">
            <div [formGroup]="datesForm" class="centerDiv searchButtonGroupSmall">
                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.applicationId' | translate }}</label>
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}"
                                    id="application" formControlName="application" [(ngModel)]="selectedApp"
                                    [options]="appOptions" optionLabel="name" dataKey="name" optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedApp">
                                    <div>{{ selectedApp | translate}}</div>
                                </div>
                            </ng-template>
                            <ng-template let-appOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ appOption.name | translate}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.receiverId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <input pInputText type="text"
                        formControlName="subject"
                    />
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-calendar appendTo="body" formControlName="rangeDates" [iconDisplay]="'input'" [showIcon]="true"
                            [(ngModel)]="dates" selectionMode="range" [class.ng-invalid]="dateError"
                            [class.ng-dirty]="dateError" aria-describedby="date-help1" [readonlyInput]="true"
                            inputId="multiple" dateFormat="{{ 'dateFormat' | translate }}" [showButtonBar]="true"[showTime]="true" 
                            [hourFormat]="'24'"></p-calendar>                        <div *ngIf="dateError">
                            <small class="error" id="date-help1">{{ dateErrorMessage | translate }}</small>
                        </div>
                    </div>
                </div>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}" class="searchButton searchButtonNormal" label="{{ 'update' | translate }}" [rounded]="true" (click)="getAllActions()"></p-button>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}" class="searchButton searchButtonSmall" icon="pi pi-refresh" [rounded]="true" (click)="getAllActions()"></p-button>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="container-plot">


            <div class="card-style">
                <p-card header="" [style]="{ width: '550px', height: '340px' }">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tV1' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="500px" height="270px" />
                    </div>
                    <div [hidden]="showSpinners">
                        <canvas style="width: 600px;" id="lChartVerification">{{ lChartVerification }}</canvas>
                    </div>
                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" [style]="{ width: '550px', height: '340px' }" styleClass="ngx-custom">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tV2' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="500px" height="270px" />
                    </div>
                    <div [hidden]="showSpinners">
                        <canvas style="width: 600px;" id="lChartVerificationResult">{{ lChartVerificationResult }}</canvas>
                    </div>
                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" [style]="{ width: '550px', height: '380px' }">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tV3' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="500px" height="310px" />
                    </div>
                    <div [hidden]="showSpinners">
                        <canvas style="width: 600px;" id="lChartVerificationLoc">{{ lChartVerificationLoc }}</canvas>
                        <p-paginator *ngIf="dataAvailable && pageCount > 1" (onPageChange)="onPageChange($event)"
                                     [first]="first"
                                     [rows]="rows"
                                     [totalRecords]="pageCount"
                                     [showCurrentPageReport]="true"
                                     [showPageLinks]="false"
                                     [showJumpToPageDropdown]="false"
                                     currentPageReportTemplate="{first} {{ 'reports.tTo' | translate }} {last} {{ 'reports.tOf' | translate }} {totalRecords} {{ 'reports.tLocations' | translate }}" />
                    </div>
                </p-card>
            </div>


            <div class="card-style">
                <p-card header="" [style]="{ width: '550px', height: '380px' }">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tV4' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="500px" height="310px" />
                    </div>
                    <div [hidden]="showSpinners">
                        <canvas style="width: 600px;" id="lChartVerificationLocResult">{{ lChartVerificationLocResult }}</canvas>
                        <p-paginator *ngIf="dataAvailable && pageCount > 1" (onPageChange)="onPageChange($event)"
                                     [first]="first"
                                     [rows]="rows"
                                     [totalRecords]="pageCount"
                                     [showCurrentPageReport]="true"
                                     [showPageLinks]="false"
                                     [showJumpToPageDropdown]="false"
                                     currentPageReportTemplate="{first} {{ 'reports.tTo' | translate }} {last} {{ 'reports.tOf' | translate }} {totalRecords} {{ 'reports.tLocations' | translate }}" />
                    </div>
                </p-card>
            </div>

        </div>
    </div>
</div>

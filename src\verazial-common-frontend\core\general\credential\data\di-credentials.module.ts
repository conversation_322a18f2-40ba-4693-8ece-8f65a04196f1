import { NgModule } from "@angular/core";
import { CredentialRepository } from "../domain/repositories/credential.repository";
import { AddCredentialsUseCase } from "../domain/use-cases/add-credentials.use-case";
import { DeleteCredentialsByIdUseCase } from "../domain/use-cases/delete-credentials-by-id.use-case";
import { DeleteCredentialsByNumIdUseCase } from "../domain/use-cases/delete-credentials-by-num-id.use-case";
import { GetCredentialsByNumIdAndSubjectAppIdUseCase } from "../domain/use-cases/get-credentials-by-num-id-and-subject-app-id.use-case";
import { GetCredentialsByNumIdUseCase } from "../domain/use-cases/get-credentials-by-num-id.use-case";
import { UpdateCredentialsByNumIdUseCase } from "../domain/use-cases/update-credentials-by-num-id.use-case";
import { CredentialRepositoryImpl } from "./repository-impl/credential-impl.repository";
import { CommonModule } from "@angular/common";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { DeleteCredentialByNumIdAndSubjectAppIdUseCase } from "../domain/use-cases/delete-credential-by-num-id-and-subject-app-id.use-case";
import { UpdateCredentialPasswordByNumIdAndSubjectAppIdUseCase } from "../domain/use-cases/update-credential-password-by-num-id-and-subject-app-id.use-case";

const addCredentialsUseCaseFactory =
    (credentialRepository: CredentialRepository) => new AddCredentialsUseCase(credentialRepository);

const getCredentialsByNumIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new GetCredentialsByNumIdUseCase(credentialRepository);

const getCredentialsByNumIdAndSubjectAppIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new GetCredentialsByNumIdAndSubjectAppIdUseCase(credentialRepository);

const updateCredentialsByNumIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new UpdateCredentialsByNumIdUseCase(credentialRepository);

const updateCredentialPasswordByNumIdAndSubjectAppIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new UpdateCredentialPasswordByNumIdAndSubjectAppIdUseCase(credentialRepository);

const deleteCredentialsByNumIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new DeleteCredentialsByNumIdUseCase(credentialRepository);

const deleteCredentialsByIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new DeleteCredentialsByIdUseCase(credentialRepository);

const deleteCredentialByNumIdAndSubjectAppIdUseCaseFactory =
    (credentialRepository: CredentialRepository) => new DeleteCredentialByNumIdAndSubjectAppIdUseCase(credentialRepository);

export const addCredentialsUseCaseProvider = {
    provide: AddCredentialsUseCase,
    useFactory: addCredentialsUseCaseFactory,
    deps: [CredentialRepository]
}

export const getCredentialsByNumIdUseCaseProvider = {
    provide: GetCredentialsByNumIdUseCase,
    useFactory: getCredentialsByNumIdUseCaseFactory,
    deps: [CredentialRepository]
}

export const getCredentialsByNumIdAndSubjectAppIdUseProvider = {
    provide: GetCredentialsByNumIdAndSubjectAppIdUseCase,
    useFactory: getCredentialsByNumIdAndSubjectAppIdUseCaseFactory,
    deps: [CredentialRepository]
}

export const updateCredentialsByNumIdUseCaseProvider = {
    provide: UpdateCredentialsByNumIdUseCase,
    useFactory: updateCredentialsByNumIdUseCaseFactory,
    deps: [CredentialRepository]
}

export const updateCredentialPasswordByNumIdAndSubjectAppIdUseCaseProvider = {
    provide: UpdateCredentialPasswordByNumIdAndSubjectAppIdUseCase,
    useFactory: updateCredentialPasswordByNumIdAndSubjectAppIdUseCaseFactory,
    deps: [CredentialRepository]
}

export const deleteCredentialsByNumIdUseCaseProvider = {
    provide: DeleteCredentialsByNumIdUseCase,
    useFactory: deleteCredentialsByNumIdUseCaseFactory,
    deps: [CredentialRepository]
}

export const deleteCredentialsByIdUseCaseProvider = {
    provide: DeleteCredentialsByIdUseCase,
    useFactory: deleteCredentialsByIdUseCaseFactory,
    deps: [CredentialRepository]
}

export const deleteCredentialByNumIdAndSubjectAppIdUseCaseProvider = {
    provide: DeleteCredentialByNumIdAndSubjectAppIdUseCase,
    useFactory: deleteCredentialByNumIdAndSubjectAppIdUseCaseFactory,
    deps: [CredentialRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        addCredentialsUseCaseProvider,
        getCredentialsByNumIdUseCaseProvider,
        getCredentialsByNumIdAndSubjectAppIdUseProvider,
        updateCredentialsByNumIdUseCaseProvider,
        updateCredentialPasswordByNumIdAndSubjectAppIdUseCaseProvider,
        deleteCredentialsByNumIdUseCaseProvider,
        deleteCredentialsByIdUseCaseProvider,
        deleteCredentialByNumIdAndSubjectAppIdUseCaseProvider,
        { provide: CredentialRepository, useClass: CredentialRepositoryImpl },
        provideHttpClient(withInterceptorsFromDi())
    ]
})
export class DiCredentialsModule { }
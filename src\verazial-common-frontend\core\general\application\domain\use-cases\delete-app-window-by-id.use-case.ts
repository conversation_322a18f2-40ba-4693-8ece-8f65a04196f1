import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationWindowRepository } from "../repositories/application-window.repository";

export class DeleteAppWindowByIdUseCase implements UseCaseGrpc<{ id: string, order: number }, SuccessResponse> {
    constructor(private applicationWindowRepository: ApplicationWindowRepository) { }
    execute(params: { id: string; order: number }): Promise<SuccessResponse> {
        return this.applicationWindowRepository.deleteAppWindowById(params)
    }
}
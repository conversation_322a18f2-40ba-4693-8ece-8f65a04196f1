import { NullValue, Struct } from "google-protobuf/google/protobuf/struct_pb";
import { Value } from "google-protobuf/google/protobuf/struct_pb"; // For handling values
import { StructEntity } from "../../domain/entity/struct.entity";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ValueMapper } from "./value.mapper";

export class StructMapper extends Mapper<Struct, StructEntity> {

  valueMapper = new ValueMapper();

  override mapFrom(param: Struct): StructEntity {
    const entity: StructEntity = new StructEntity();

    // Use getFieldsMap to get the map of fields in the Struct
    const fieldsMap = param.getFieldsMap();

    fieldsMap.forEach((value: Value, key: string) => {
      var val = this.valueMapper.mapFrom(value);
      if(val)
        entity[key] = val;
    });

    return entity;
  }

  override mapTo(param: StructEntity): Struct {
    const struct = new Struct();

    // Use setFields to set fields individually in the Struct
    const fields = struct.getFieldsMap();
    Object.keys(param).forEach((key) => {
      const value = this.valueMapper.mapTo(param[key]);
      fields.set(key, value); // Set the field with key and value
    });

    return struct;
  }
}
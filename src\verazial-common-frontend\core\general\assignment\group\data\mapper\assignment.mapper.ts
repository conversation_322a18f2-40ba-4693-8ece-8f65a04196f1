import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { AssignmentEntity } from "../../domain/entity/assignment.entity";
import { AssignmentGrpcModel } from "src/verazial-common-frontend/core/generated/assignment/assignment_pb";
import { toArrayOfAssignmentElements, toListOfAssignmentElements } from "../../common/converter/assignment.converter";

export class AssignmentMapper extends Mapper<AssignmentGrpcModel, AssignmentEntity> {
    override mapFrom(param: AssignmentGrpcModel): AssignmentEntity {
        let assignment = new AssignmentEntity();

        assignment.id = param.getId();
        assignment.name = param.getName();
        assignment.description = param.getDescription();
        assignment.elements = toListOfAssignmentElements(param.getElements());
        assignment.isRequiredWithinSchedule = param.getIsrequiredwithinschedule();
        assignment.alertIfAllPerformedWithinSchedule = param.getAlertifallperformedwithinschedule();
        assignment.alertIfNotPerformedWithinSchedule = param.getAlertifnotperformedwithinschedule();
        assignment.alertIfPerformedOutsideSchedule = param.getAlertifperformedoutsideschedule();
        assignment.createdAt = new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6));
        assignment.updatedAt = new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6));

        return assignment;
    }

    override mapTo(param: AssignmentEntity): AssignmentGrpcModel {
        let assignment = new AssignmentGrpcModel();

        assignment.setId(param.id!);
        assignment.setName(param.name ? param.name : "");
        assignment.setDescription(param.description ? param.description : "")
        assignment.setElements(toArrayOfAssignmentElements(param.elements));
        assignment.setIsrequiredwithinschedule(param.isRequiredWithinSchedule!);
        assignment.setAlertifallperformedwithinschedule(param.alertIfAllPerformedWithinSchedule!);
        assignment.setAlertifnotperformedwithinschedule(param.alertIfNotPerformedWithinSchedule!);
        assignment.setAlertifperformedoutsideschedule(param.alertIfPerformedOutsideSchedule!);

        return assignment;
    }


}
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from "@angular/core";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";

export class UserSubjectCardComponentResult {
    resultType: string = '';
    result?: SubjectEntity = undefined;
}
@Component({
    selector: 'app-user-subject-card',
    templateUrl: './user-subject-card.component.html',
    styleUrls: ['./user-subject-card.component.css']
})
export class UserSubjectCardComponent implements OnInit, OnChanges {
    // Inputs
    @Input() canReadAndWrite: boolean = false;
    @Input() userSubject?: SubjectEntity | UserEntity;
    @Input() isVerified: boolean = false;
    @Input() userIsVerified: boolean = false;
    @Input() showButtons: boolean = false;
    @Input() disableButtons: boolean = false;
    @Input() verifyBtnAvailable: boolean = false;
    @Input() disabledVerifyBtnToolTip: string = '';
    @Input() editMode: boolean = false;
    @Input() editEnabled: boolean = false;
    @Input() deleteEnabled: boolean = false;
    // Outputs
    @Output() verified = new EventEmitter<boolean>();
    @Output() result = new EventEmitter<UserSubjectCardComponentResult>();

    image: string = '';
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
    showDelete: boolean = false;

    constructor() {}

    ngOnInit() {
        this.showDelete = this.isVerified || this.deleteEnabled && this.canReadAndWrite;
    }

    ngOnChanges(changes: SimpleChanges) {
        this.showDelete = this.isVerified || this.deleteEnabled && this.canReadAndWrite;
        this.image = (this.userSubject?.pic == this.imagePlaceholder || this.userSubject?.pic == "" || this.userSubject?.pic == undefined || this.userSubject?.pic == null) ? this.imagePlaceholder : this.userSubject.pic.includes('data:image/jpeg;base64,') ? this.userSubject?.pic! : 'data:image/jpeg;base64,' + this.userSubject?.pic!;
    }

    openEditUserSubjectDialog() {
        let result = new UserSubjectCardComponentResult();
        result.resultType = 'edit';
        this.result.emit(result);
    }

    onDeleteUserSubject() {
        let result = new UserSubjectCardComponentResult();
        result.resultType = 'delete';
        this.result.emit(result);
    }

    openVerifyDialog() {
        let result = new UserSubjectCardComponentResult();
        result.resultType = 'verify';
        this.result.emit(result);
    }
}
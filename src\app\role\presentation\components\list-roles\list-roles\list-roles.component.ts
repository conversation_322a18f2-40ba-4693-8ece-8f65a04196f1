import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, FilterService, MenuItem, MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { AccessEntity } from 'src/verazial-common-frontend/core/general/access/domain/entity/access.entity';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { RoleAccessResponseEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-access-response.entity';
import { RoleWithAccessEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-with-access.entity';
import { AttributeData } from 'src/verazial-common-frontend/core/models/attribute-data.model';


@Component({
  selector: 'app-list-roles',
  templateUrl: './list-roles.component.html',
  styleUrl: './list-roles.component.css',
  providers: [MessageService, ConfirmationService]
})
export class ListRolesComponent implements OnInit {
  @Input() readAndWritePermissions: boolean = false;
  @Input() listOfAccesses: AccessEntity[] = [];
  @Input() listRoles: RoleWithAccessEntity[] = [];
  @Output() saveRoleOutput = new EventEmitter<RoleWithAccessEntity>;
  @Output() deleteAccessesOutput = new EventEmitter<RoleAccessResponseEntity[]>;
  @Output() deleteRoleOutput = new EventEmitter<RoleWithAccessEntity>;
  @Output() updateRoleOutput = new EventEmitter<RoleWithAccessEntity>;

  stepOptions: AttributeData[] = [
    { key: 'role.general', value: "0" },
    { key: 'role.accesses', value: "1" },
  ];
  selectedStep: AttributeData | undefined;

  formGroup: FormGroup = new FormGroup({
      value: new FormControl('0')
  });

  showAccessAssignment: boolean = false;

  items: MenuItem[] = [];

  activeIndex: number = 0;

  showNewRoleDialog: boolean = false;

  showEditRoleDialog: boolean = false;

  roleDetails: RoleEntity | undefined;

  selectedRoleAccesses: RoleAccessResponseEntity[] = [];

  searchValue: string | undefined;

  // Date Range Filter
  formGroupDate: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  formGroupDate2: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  constructor(
    private translateService: TranslateService,
    private filterService: FilterService,
  ) {
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnInit(): void {
    this.selectedStep = this.stepOptions[0];
    this.setStepOptions();
  }

  setStepOptions() {
    // this.items = [
    //   { label: this.translateService.instant('role.general'), },
    //   { label: this.translateService.instant('role.accesses'), }
    // ];
    // this.stepOptions = [
    //   { key: this.translateService.instant('role.general'), value: "0" },
    //   { key: this.translateService.instant('role.accesses'), value: "1" },
    // ];

    if(this.roleDetails?.type == RoleType.USER) {
      this.items = [
        { label: this.translateService.instant('role.general'), },
        { label: this.translateService.instant('role.accesses'), }
      ];
      this.stepOptions = [
        { key: this.translateService.instant('role.general'), value: "0" },
        { key: this.translateService.instant('role.accesses'), value: "1" },
      ];
      this.showAccessAssignment = true;
    }
    else {
      this.items = [
        { label: this.translateService.instant('role.general'), },
      ];
      this.stepOptions = [
        { key: this.translateService.instant('role.general'), value: "0" },
      ];
      this.showAccessAssignment = false;
    }
  }

  createNewRole() {
    this.setStepOptions();
    this.activeIndex = 0;
    this.roleDetails = undefined;
    this.selectedRoleAccesses = [];
    this.showNewRoleDialog = true;
  }

  saveRoles() {
    let newRoleWithAccesses = new RoleWithAccessEntity();

    const updatedListOfRoleAccesses = this.selectedRoleAccesses.map(item => ({
      ...item,
      id: undefined,
      roleId: undefined
    }));

    newRoleWithAccesses.name = this.roleDetails?.name;
    newRoleWithAccesses.level = this.roleDetails?.level;
    newRoleWithAccesses.type = this.roleDetails?.type;
    newRoleWithAccesses.description = this.roleDetails?.description;
    newRoleWithAccesses.showInMenu = this.roleDetails?.showInMenu;
    newRoleWithAccesses.roleAccesses = updatedListOfRoleAccesses;

    this.saveRoleOutput.emit(newRoleWithAccesses);
    this.showNewRoleDialog = false;
  }

  onEditRole(role: RoleWithAccessEntity) {
    this.activeIndex = 0;
    this.roleDetails = role;
    this.setStepOptions();
    if (role.roleAccesses) {
      this.selectedRoleAccesses = role.roleAccesses;
    }
    this.showEditRoleDialog = true;
    this.formGroup.controls['value'].setValue(this.activeIndex.toString());
  }

  onDeleteRole(roleWithAccesses: RoleWithAccessEntity) {
    this.deleteRoleOutput.emit(roleWithAccesses)
  }

  accessesToBeRemoved(roleAccesses: RoleAccessResponseEntity[]) {
    this.deleteAccessesOutput.emit(roleAccesses);
  }


  onUpdateRoleOutput() {
    let newRoleWirhAccesses = new RoleWithAccessEntity();
    newRoleWirhAccesses.id = this.roleDetails?.id;
    newRoleWirhAccesses.name = this.roleDetails?.name;
    newRoleWirhAccesses.level = this.roleDetails?.level;
    newRoleWirhAccesses.type = this.roleDetails?.type;
    newRoleWirhAccesses.description = this.roleDetails?.description;
    newRoleWirhAccesses.showInMenu = this.roleDetails?.showInMenu;
    newRoleWirhAccesses.roleAccesses = this.selectedRoleAccesses;

    this.updateRoleOutput.emit(newRoleWirhAccesses);
    this.showEditRoleDialog = false;
  }

  onActiveIndexChange(value: any) {
    this.activeIndex = Number(value.value);
    this.formGroup.controls['value'].setValue(this.activeIndex.toString());
  }

  getRoleDetails(role: RoleEntity) {
    this.roleDetails = role;
    this.showAccessAssignment = this.roleDetails.type == RoleType.USER;
    this.setStepOptions();
  }

  next() {
    this.activeIndex += 1;
    this.formGroup.controls['value'].setValue(this.activeIndex.toString());
  }

  back() {
    this.activeIndex -= 1;
    this.formGroup.controls['value'].setValue(this.activeIndex.toString());
  }

  getSelectedRoleAccesses(roleAccess: RoleAccessResponseEntity[]) {
    this.selectedRoleAccesses = roleAccess;
  }

  updateRoleIdInListOfAccess(roleId: number, roleAccess: RoleAccessResponseEntity[]): RoleAccessResponseEntity[] {
    roleAccess.forEach(access => {
      access.roleId = roleId
    })
    return roleAccess;
  }

  getRoleType(roleType: RoleType): string {
    switch (roleType) {
      case RoleType.SUBJECT:
        return 'content.subject_profile';
      case RoleType.USER:
        return 'content.user_role';
      default:
        return '';
    }
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['updatedAt'].value){
      this.rangeDates = null;
      this.formGroupDate.reset();
    }
    if(!event.filters['createdAt'].value){
      this.rangeDates = null;
      this.formGroupDate2.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    if(field === 'createdAt'){
      this.rangeDates = this.formGroupDate2.get('date')?.value;
    }
    else if(field === 'updatedAt'){
      this.rangeDates = this.formGroupDate.get('date')?.value;
    }
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }
}
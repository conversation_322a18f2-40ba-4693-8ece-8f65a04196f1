<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-content-center w-full h-full mt-4">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)"
            animationDuration=".5s" ariaLabel="loading" [ngClass]="{'spinner': isLoading}"></p-progressSpinner>
    </div>
</ng-template>
<div *ngIf="!isLoading else loadingSpinner" class="subcontainer">
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.entries_and_exits' | translate}}</label>
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <div class="add-action-main-full">
                    <p-button ngClass="add-action-main-full" [disabled]="!(canReadAndWrite && userIsVerified && isVerified && !newEntryMade)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'prisons_tab.new_entry_exit' | translate }}" icon="pi pi-plus" iconPos="right"
                        [rounded]="true" (onClick)="showNewEntryDialogFunc()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button [disabled]="!(canReadAndWrite && userIsVerified && isVerified && !newEntryMade)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true" (onClick)="showNewEntryDialogFunc()"></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <app-time-left [time]="timeEnd" [showTime]="showTime" [isPrisoner]="isPrisoner"></app-time-left>
        <p-table #dt [value]="listOfEntries" (onFilter)="onFilter($event, dt)" [(selection)]="selectedEntries"
            (selectionChange)="onEntrySelectionChange($event)" dataKey="id" [rowHover]="true" [paginator]="true"
            [rows]="10" [rowsPerPageOptions]="[5, 10, 20]" [scrollable]="true" scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }" styleClass="fixed-table" [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['entryDate', 'exitDate', 'type', 'locationId', 'reason', 'description', 'roleId']"
            [sortField]="'entryDate'" [sortOrder]="-1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="entryDate"> {{ 'signaturesTable.date' | translate }}
                        <p-sortIcon field="entryDate"></p-sortIcon>
                    </th>
                    <!--<th class="fixed-column" pSortableColumn="exitDate"> {{ 'prisons_tab.exit_date' | translate }}
                        <p-sortIcon field="exitDate"></p-sortIcon>
                    </th>-->
                    <th class="fixed-column" pSortableColumn="type">{{ 'content.type' | translate }} <p-sortIcon
                            field="type"></p-sortIcon></th>

                    <th class="fixed-column" pSortableColumn="locationId">{{ 'prisons_tab.location' | translate }}
                        <p-sortIcon field="locationId"></p-sortIcon>
                    </th>

                    <th *ngIf="multipleRoles" class="fixed-column" pSortableColumn="roleId"> {{ 'content.role' | translate }}
                        <p-sortIcon field="roleId"></p-sortIcon>
                    </th>

                    <th *ngIf="entryRestrictions.length > 0" class="fixed-column" pSortableColumn="relatedSubject"> {{ 'content.related_subject' | translate
                        }}
                        <p-sortIcon field="relatedSubject"></p-sortIcon>
                    </th>

                    <th class="fixed-column" pSortableColumn="reason"> {{ 'content.reason' | translate }}
                        <p-sortIcon field="reason"></p-sortIcon>
                    </th>

                    <th class="fixed-column" pSortableColumn="description"> {{ 'content.description' | translate }}
                        <p-sortIcon field="description"></p-sortIcon>
                    </th>

                    <th class="fixed-column" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th>
                        <p-columnFilter type="date" field="entryDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar formControlName="date" selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'entryDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'entryDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'entryDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}" appendTo="body"></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="type" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body" [ngModel]="value" [options]="entriesExitsDescriptionArray"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}" [showClear]="true"
                                    optionLabel="value">
                                    <ng-template let-selected pTemplate="selectedItem">
                                        {{ selected.value | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.value | translate }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="locationId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">

                                <p-treeSelect appendTo="body" [filter]="true" [filterInputAutoFocus]="true"
                                    class="md:w-20rem w-full" containerStyleClass="w-full" [options]="lLocationOptions"
                                    placeholder="{{ 'content.select' | translate }}" [showClear]="true"
                                    (onNodeSelect)="filter($event.node.key)" (onClear)="filter(null)">
                                </p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th *ngIf="multipleRoles">
                        <p-columnFilter field="roleId" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body" [ngModel]="value" [options]="userSubject?.roles"
                                    (onChange)="filter($event.value?.name)"
                                    placeholder="{{ 'content.select' | translate }}" [showClear]="true"
                                    optionLabel="name">

                                    <!-- Template for the selected item -->
                                    <ng-template pTemplate="selectedItem" let-selectedOption>
                                        {{ selectedOption?.name }}
                                    </ng-template>

                                    <!-- Template for dropdown items -->
                                    <ng-template let-option pTemplate="item">
                                        {{ option.name }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th *ngIf="entryRestrictions.length > 0">
                        <p-columnFilter type="text" field="relatedSubject" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th>
                        <p-columnFilter field="reason" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body" [ngModel]="value" [options]="lReasonOptions"
                                    (onChange)="filter($event.value?.key)"
                                    placeholder="{{ 'content.select' | translate }}" [showClear]="true"
                                    optionLabel="label">
                                    <ng-template pTemplate="selectedItem">
                                        {{ value }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.key }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th>
                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-entryTable let-rowIndex="rowIndex">
                <tr class="p-selectable-row" [pSelectableRow]="entryTable" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="entryTable"></p-tableCheckbox>
                    </td>
                    @if(isValidDate(entryTable.entryDate)){
                    <td (click)="seeEntry(entryTable)" showDelay="1000"
                        pTooltip="{{stringToDate(entryTable.entryDate) | date:('dateTimeFormat' | translate)}}"
                        tooltipPosition="top" class="ellipsis-cell">{{ stringToDate(entryTable.entryDate) |
                        date:('dateTimeFormat' | translate)}}</td>
                    }@else{
                    <td (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="" tooltipPosition="top"
                        class="ellipsis-cell"></td>
                    }

                    <td (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="{{entryTable.type | translate}}"
                        tooltipPosition="top" class="ellipsis-cell">{{
                        entryTable.type | translate }}</td>

                    <td (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="{{entryTable.locationId}}"
                        tooltipPosition="top" class="ellipsis-cell">{{entryTable.locationId}}</td>

                    <td *ngIf="multipleRoles" (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="{{entryTable.roleId}}"
                        tooltipPosition="top" class="ellipsis-cell">{{
                        entryTable.roleId }}</td>

                    <td *ngIf="entryRestrictions.length > 0" (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="{{entryTable.relatedSubject}}"
                        tooltipPosition="top" class="ellipsis-cell">{{
                        entryTable.relatedSubject }}</td>


                    <td (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="{{entryTable.reason}}"
                        tooltipPosition="top" class="ellipsis-cell">{{
                        entryTable.reason }}</td>

                    <td (click)="seeEntry(entryTable)" showDelay="1000" pTooltip="{{entryTable.description}}"
                        tooltipPosition="top" class="ellipsis-cell">{{
                        entryTable.description }}</td>

                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-eye"
                                [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"
                                (click)="seeEntry(entryTable)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>

        <!-- Create new ID -->
        <p-dialog [(visible)]="showNewEntryDialog" styleClass="p-fluid" [modal]="true"
            [style]="{'width': '70vw'}" (onHide)="onCancelDialog()">
            <ng-template pTemplate="header">
                <div>
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ getTranslatedValue(entry.type) | translate }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <div>
                    <div class="flex justify-content-end pb-3 requiredFieldsLabel">
                        {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                    </div>
                    <form [formGroup]="form">
                        <div class="mb-3" *ngIf="isPrisoner">
                            @if( isNew ){
                            <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex"
                                (activeIndexChange)="onActiveIndexChange($event)"></p-steps>
                            }@else{
                            <div class="flex justify-content-center">
                                <p-selectButton [options]="stepOptions" formControlName="stepOptions"
                                    severity="secondary" multiple="false" allowEmpty="false" optionLabel="key"
                                    optionValue="value" dataKey="value" (onChange)="onActiveTabIndexChange($event)">
                                </p-selectButton>
                            </div>
                            }
                        </div>
                        <div *ngIf="activeIndex==0" class="grid form-fields mt-2">

                            <div class="lg:col-6 sm:col-12 width100" style="padding-top: 0rem;">
                                <div *ngIf="entryExitRole.toString() == prisonsConfig?.prisonerProfileId && (entry.type == entriesExitsEnum.EXIT || entry.type == entriesExitsEnum.DEF_EXIT)">
                                    <div class="pb-1">
                                        <label class="label-form" for="authCode">
                                            {{ 'content.authCode' | translate }}
                                        </label>
                                        <input disabled="true" type="text" pInputText formControlName="authCode" />
                                    </div>
                                </div>
                            </div>  

                            <div class="lg:col-6 sm:col-12 width100" style="padding-top: 0rem;">
                                <div *ngIf="entryExitRole.toString() == prisonsConfig?.prisonerProfileId && (entry.type == entriesExitsEnum.EXIT || entry.type == entriesExitsEnum.DEF_EXIT)">
                                    <div class="pb-1">
                                        <label class="label-form" for="entryExitAuthLimitDate">{{ 'content.plannedArrivalDateTime' | translate }}</label>
                                        <p-calendar appendTo="body" formControlName="entryExitAuthLimitDate"
                                             [iconDisplay]="'input'" [showIcon]="true"
                                            inputId="icondisplay" dateFormat="{{ 'dateFormat' | translate }}"
                                            today="{{ 'today' | translate }}" [showButtonBar]="true" [showTime]="true"
                                            [hourFormat]="'hourFormat' | translate" />
                                        <small *ngIf="!isValid('entryExitAuthLimitDate') && form.controls['entryExitAuthLimitDate'].touched"
                                            [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                            }}</small>
                                    </div>
                                </div>
                            </div>  
                            
                            <div *ngIf="entry.type != entriesExitsEnum.DEF_ENTRY && entryExitRole.toString() == prisonsConfig?.prisonerProfileId" class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="reason">{{ 'content.reason' | translate
                                        }} <span *ngIf="isRequiredField('reason')" class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="reason"
                                        (ngModelChange)="trackDataChange()" />
                                    <small *ngIf="!isValid('reason') && form.controls['reason'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-12 sm:col-12 width100" style=" padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="locationId">
                                        {{ entry.destinyLocationId ? ('content.originLocation' | translate) : ('prisons_tab.location' | translate) }}
                                        <span *ngIf="isRequiredField('locationId')" class="requiredStar">*</span>
                                      </label>
                                    <input type="text" pInputText formControlName="locationId"
                                        (ngModelChange)="trackDataChange()" />
                                    <small *ngIf="!isValid('locationId') && form.controls['locationId'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div *ngIf="entry.destinyLocationId" class="lg:col-12 sm:col-12 width100" style=" padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="destinyLocationId">{{ 'content.destinyLocation' | translate
                                        }} <span *ngIf="isRequiredField('destinyLocationId')"
                                            class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="destinyLocationId"
                                        (ngModelChange)="trackDataChange()" />
                                    <small *ngIf="!isValid('destinyLocationId') && form.controls['destinyLocationId'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-12 sm:col-12 width100" style=" padding-top: 0rem;"
                                *ngIf="this.entry.relatedSubject">
                                <div class="pb-1">
                                    <label class="label-form" for="relatedSubjectId">{{ 'content.related_subject' |
                                        translate
                                        }} <span *ngIf="isRequiredField('relatedSubjectId')"
                                            class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="relatedSubjectId"
                                        (ngModelChange)="trackDataChange()" />
                                    <small
                                        *ngIf="!isValid('relatedSubjectId') && form.controls['relatedSubjectId'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100" style="padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="entryDate">{{ 'signaturesTable.date' | translate }}
                                        <span *ngIf="isRequiredField('entryDate')" class="requiredStar">*</span></label>
                                    <p-calendar appendTo="body" formControlName="entryDate"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                        inputId="icondisplay" dateFormat="{{ 'dateFormat' | translate }}"
                                        today="{{ 'today' | translate }}" [showButtonBar]="true" [showTime]="true"
                                        [hourFormat]="'hourFormat' | translate" />
                                    <small *ngIf="!isValid('entryDate') && form.controls['entryDate'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <!--<div *ngIf="entry.entryExitAuthLimitDate" class="lg:col-6 sm:col-12 width100" style="padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="entryExitAuthLimitDate">{{ 'content.plannedArrivalDateTime' | translate }}
                                        <span *ngIf="isRequiredField('entryExitAuthLimitDate')" class="requiredStar">*</span></label>
                                    <p-calendar appendTo="body" formControlName="entryExitAuthLimitDate"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                        inputId="icondisplay" dateFormat="{{ 'dateFormat' | translate }}"
                                        today="{{ 'today' | translate }}" [showButtonBar]="true" [showTime]="true"
                                        [hourFormat]="'hourFormat' | translate" />
                                    <small *ngIf="!isValid('entryExitAuthLimitDate') && form.controls['entryExitAuthLimitDate'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div *ngIf="entry.transferLimitDate" class="lg:col-6 sm:col-12 width100" style="padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="transferLimitDate">{{ 'content.plannedArrivalDateTime' | translate }}
                                        <span *ngIf="isRequiredField('transferLimitDate')" class="requiredStar">*</span></label>
                                    <p-calendar appendTo="body" formControlName="transferLimitDate"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                        inputId="icondisplay" dateFormat="{{ 'dateFormat' | translate }}"
                                        today="{{ 'today' | translate }}" [showButtonBar]="true" [showTime]="true"
                                        [hourFormat]="'hourFormat' | translate" />
                                    <small *ngIf="!isValid('transferLimitDate') && form.controls['transferLimitDate'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div> -->

                            <div *ngIf="entry.maxTimeVisit" class="lg:col-6 sm:col-12 width100"
                                style="padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="visitTimeMax">{{ 'content.max_inside_time' | translate
                                        }}
                                        <span *ngIf="isRequiredField('visitTimeMax')" class="requiredStar">*</span></label>
                                    <p-calendar appendTo="body" formControlName="visitTimeMax" timeOnly="true"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                        inputId="icondisplay" [disable]="true" />
                                    <small *ngIf="!isValid('visitTimeMax') && form.controls['visitTimeMax'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div *ngIf="entry.actualTimeVisit !== undefined" class="lg:col-6 sm:col-12 width100"
                                style="padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="visitTimeActual">{{ 'content.actual_inside_time' | translate
                                        }}
                                        <span *ngIf="isRequiredField('visitTimeActual')" class="requiredStar">*</span></label>
                                    <p-calendar appendTo="body" formControlName="visitTimeActual" timeOnly="true"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                        inputId="icondisplay" [disable]="true" />
                                    <small *ngIf="!isValid('visitTimeActual') && form.controls['visitTimeActual'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="type">{{ 'content.type' | translate
                                        }} <span *ngIf="isRequiredField('type')" class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="type" (ngModelChange)="trackDataChange()"
                                        appendTo="body" [options]="entriesExitsDescriptionArray" optionLabel="value"
                                        placeholder="{{ 'content.select' | translate }}">
                                        <ng-template let-selected pTemplate="selectedItem">
                                            {{ selected.value | translate }}
                                        </ng-template>
                                        <ng-template let-option pTemplate="item">
                                            {{ option.value | translate }}
                                        </ng-template>
                                    </p-dropdown>
                                    <small *ngIf="!isValid('type') && form.controls['type'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div *ngIf="entry.type != entriesExitsEnum.DEF_ENTRY && entryExitRole.toString() != prisonsConfig?.prisonerProfileId" class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="reason">{{ 'content.reason' | translate
                                        }} <span *ngIf="isRequiredField('reason')" class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="reason" (ngModelChange)="trackDataChange()"
                                        appendTo="body" [filter]="true" [options]="lReasonRoleOptions"
                                        optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                                    <small *ngIf="!isValid('reason') && form.controls['reason'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="description">{{ 'content.description' | translate }}
                                        <span *ngIf="isRequiredField('description')"
                                            class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="description"
                                        (ngModelChange)="trackDataChange()" />
                                    <small *ngIf="!isValid('description') && form.controls['description'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="activeIndex==1" class="grid form-fields">
                            <!-- Reception Signatures -->
                            <div class="flex flex-wrap justify-content-center align-items-center w-full">
                                <div *ngIf="authSignatureData && entryExitRole.toString() == prisonsConfig?.prisonerProfileId" class="w-full flex justify-content-center align-items-center m-3">
                                    <app-bio-signatures
                                        [showSignatureButton]="false"
                                        [showExtraFormFields]="false"
                                        [showSignatureInfo]="true"
                                        [signatureTitle]="'content.authSignature'"
                                        [signatureInputLabel]="'content.subjectWhoAuthrizes'"
                                        [konektorProperties]="konektorProperties"
                                        [managerSettings]="settings"
                                        [signatureData]="authSignatureData"
                                        [userIsVerified]="userIsVerified"
                                        (outputResult)="prueba($event)">
                                    </app-bio-signatures>
                                </div>
                            
                                <div class="w-full flex flex-row justify-content-center align-items-center gap-4 m-3">
                                    <div>
                                        <div *ngIf="!entry.subjectSignatureTech" class="flex flex-column align-items-start gap-2">
                                            <label class="signature-title">{{ 'content.signature_of' | translate }} </label>
                                            <div class="signature-container flex flex-column gap-2">
                                                <label class="label-form">{{ 'content.subject' | translate }}</label>
                                                <input type="text" pInputText value="{{ userSubject.names + ' ' + userSubject.lastNames }}" [disabled]="true" />
                                                <div class="flex flex-column justify-content-center align-items-center">
                                                    <p-button severity="secondary" [outlined]="true"
                                                        label="{{ 'content.sign' | translate }}" icon="pi pi-pencil"
                                                        iconPos="right" (onClick)="openWidgetVerify()"></p-button>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="entry.subjectSignatureTech">
                                            <ng-container *ngTemplateOutlet="subjectReceptionInfo" />
                                        </div>
                                    </div>
                            
                                    <div>
                                        <app-bio-signatures
                                            [showSignatureButton]="entry.userSignatureTech == null || entry.userSignatureTech == undefined || entry.userSignatureTech == ''"
                                            [showExtraFormFields]="false"
                                            [showSignatureInfo]="(entry.userSignatureTech != null && entry.userSignatureTech != undefined && entry.userSignatureTech != '')"
                                            [signatureTitle]="'content.signature_responsible_of'"
                                            [signatureInputLabel]="'content.user'"
                                            [konektorProperties]="konektorProperties"
                                            [managerSettings]="settings"
                                            [signatureData]="receptionSignatureData"
                                            [restrictSubjectRoles]="restrictSignatureRoles"
                                            [subjectRoleSegmentedSearch]="segmentedSearchSignatureRole"
                                            [userIsVerified]="userIsVerified"
                                            (outputResult)="prueba($event)">
                                        </app-bio-signatures>
                                    </div>
                                </div>
                            </div>
                            
                        </div>

                    </form>
                </div>
            </ng-template>
            <ng-template pTemplate="footer">
                <div class="flex flex-row gap-1 mr-3 justify-content-end">
                    @if(activeIndex == 1 || !isPrisoner) {
                    @if(activeIndex == 1)
                    {
                    <p-button *ngIf="isNew"
                        [style]="{ 'pointer-events': 'auto', 'width': '100px', 'height': '36px', 'color': '#64748B' , 'background': '#FFFFFF', 'border': 'none', 'font-family': 'Open Sans', 'font-size': '14px' }"
                        label="{{ 'back' | translate }}" (onClick)="onBack()"></p-button>

                    <p-button *ngIf="canReadAndWrite && userIsVerified && isNew"
                        [disabled]="!entry.subjectSignatureTech || !entry.userSignatureTech || isDisableSaveButton"
                        [style]="{ 'pointer-events': 'auto', 'width': '100px', 'height': '36px', 'color': '#FFFFFF', 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px' }"
                        label="{{ 'save'| translate }}" (onClick)="saveEntry()"></p-button>
                    }
                    @else {
                    <p-button *ngIf="canReadAndWrite && userIsVerified && isNew"
                        [disabled]="!(canReadAndWrite && userIsVerified) || isDisableSaveButton"
                        [style]="{ 'pointer-events': 'auto', 'width': '100px', 'height': '36px', 'color': '#FFFFFF', 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px' }"
                        label="{{ 'save'| translate }}" (onClick)="saveEntry()"></p-button>
                    }
                    }@else{
                    <p-button *ngIf="canReadAndWrite && userIsVerified && isNew"
                        [style]="{ 'pointer-events': 'auto', 'width': '100px', 'height': '36px', 'color': '#FFFFFF', 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px' }"
                        label="{{ 'next' | translate }}" (onClick)="onNext()"></p-button>
                    }


                    <p-button
                        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ (canReadAndWrite && userIsVerified ? 'cancel' : 'close')| translate }}"
                        (onClick)="onCancelDialog()"></p-button>
                </div>
            </ng-template>
        </p-dialog>


        <!-- Select Role-->
        <p-dialog [modal]="true" [draggable]="false" [(visible)]="showRoleDialog" [style]="{ width: '25rem' }"
            header="Header">
            <ng-template pTemplate="header">
                <div class="flex justify-content-center align-items-center align-content-center">
                    <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                        {{'role.select_role' | translate}}</label>
                </div>
            </ng-template>
            <app-role-selection [subjectRoles]="userSubject?.roles" [showDefaultRole]="false"
                (onAccept)="onAcceptSelectRole($event)" (onCancel)="onCancelSelectRole()"></app-role-selection>
        </p-dialog>


        <!-- Select Location -->
        <p-dialog [modal]="true" [draggable]="false" [(visible)]="showSelectLocationsDialog"
            [style]="{ width: '25rem' }" header="Header">
            <ng-template pTemplate="header">
                <div class="flex justify-content-center align-items-center align-content-center">
                    <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                        {{'prisons_tab.select_location_entry' | translate}}</label>
                </div>
            </ng-template>

            <div class="flex flex-column gap-3 mt-4" [formGroup]="dataFormLocations">
                <div class="flex flex-column gap-2">
                    <label>{{'menu.locations' | translate}}</label>
                    <p-dropdown appendTo="body" [options]="listOfLocationsToEntry" [optionLabel]="resolveLocationLabel"
                        optionValue="id" placeholder="{{ 'content.select' | translate }}" formControlName="location" />
                    <small
                        *ngIf="!isValid('location',dataFormLocations) && dataFormLocations.controls['location'].touched"
                        class="mb-3" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>

                </div>
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <p-button label="{{ 'confirm' | translate }}" class="p-button-text"
                        (click)="locationToEnterSelected()"
                        [style]="{'color': '#FFFFFF' , 'background': '#204887' }"></p-button>
                </div>
            </div>

        </p-dialog>

        <!-- Select RelatedSubject -->
        <p-dialog [modal]="true" [draggable]="false" [(visible)]="showSelectRelatedSubjectsDialog"
            [style]="{ width: '25rem' }" header="Header">
            <ng-template pTemplate="header">
                <div class="flex justify-content-center align-items-center align-content-center">
                    <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                        {{'prisons_tab.select_related_subject' | translate}}</label>
                </div>
            </ng-template>

            <div class="flex flex-column gap-3 mt-4" [formGroup]="dataFormRelatedSubjects">
                <div class="flex flex-column gap-2">
                    <label>{{'titles.related_subjects' | translate}}</label>
                    <p-dropdown appendTo="body" [options]="listOfRelatedToEntry"
                        [optionLabel]="resolveRelatedSubjectLabel" optionValue="entity.id"
                        placeholder="{{ 'content.select' | translate }}" formControlName="relatedSubject" />
                    <small
                        *ngIf="!isValid('relatedSubject',dataFormRelatedSubjects) && dataFormRelatedSubjects.controls['relatedSubject'].touched"
                        class="mb-3" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>

                </div>
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <p-button label="{{ 'confirm' | translate }}" class="p-button-text"
                        (click)="relatedSubjectToEnterSelected()"
                        [style]="{'color': '#FFFFFF' , 'background': '#204887' }"></p-button>
                </div>
            </div>

        </p-dialog>

        <!-- Select Transfer -->
        <p-dialog [modal]="true" [draggable]="false" [(visible)]="showSelectTransferDialog"
            [style]="{ width: '25rem' }" header="Header">
            <ng-template pTemplate="header">
                <div class="flex justify-content-center align-items-center align-content-center">
                    <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                        {{'prisons_tab.select_transfer_auth' | translate}}</label>
                </div>
            </ng-template>

            <div class="flex flex-column gap-3 mt-4" [formGroup]="dataFormTransfer">
                <div class="flex flex-column gap-2">
                    <label>{{'titles.transfer_authorization' | translate}}</label>
                    <p-dropdown appendTo="body" [options]="listOfTransferAuths"
                        [optionLabel]="resolveTransfertLabel"
                        placeholder="{{ 'content.select' | translate }}" formControlName="transfer" />
                    <small
                        *ngIf="!isValid('transfer',dataFormTransfer) && dataFormTransfer.controls['transfer'].touched"
                        class="mb-3" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>

                </div>
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <p-button label="{{ 'confirm' | translate }}" class="p-button-text"
                        (click)="transferToExitSelected()"
                        [style]="{'color': '#FFFFFF' , 'background': '#204887' }"></p-button>
                </div>
            </div>

        </p-dialog>

        <!-- Select EntryExitAuth -->
        <p-dialog [modal]="true" [draggable]="false" [(visible)]="showSelectEntryExitAuthDialog"
            [style]="{ width: '25rem' }" header="Header">
            <ng-template pTemplate="header">
                <div class="flex justify-content-center align-items-center align-content-center">
                    <label [style]="{'font-size': '14px', 'font-weight':'600', 'color':'#495057'}">
                        {{'prisons_tab.select_entry_and_exit_auth' | translate}}</label>
                </div>
            </ng-template>

            <div class="flex flex-column gap-3 mt-4" [formGroup]="dataFormEntryExitAuth">
                <div class="flex flex-column gap-2">
                    <label>{{'titles.entry_exit_authorizations' | translate}}</label>
                    <p-dropdown appendTo="body" [options]="listOfEntryExitAuths"
                        [optionLabel]="resolveEntryExitAuth"
                        placeholder="{{ 'content.select' | translate }}" formControlName="entryExitAuth" />
                    <small
                        *ngIf="!isValid('entryExitAuth',dataFormEntryExitAuth) && dataFormEntryExitAuth.controls['entryExitAuth'].touched"
                        class="mb-3" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>

                </div>
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <p-button label="{{ 'confirm' | translate }}" class="p-button-text"
                        (click)="entryExitAuthToSelected()"
                        [style]="{'color': '#FFFFFF' , 'background': '#204887' }"></p-button>
                </div>
            </div>

        </p-dialog>
    </div>
</div>

<!-- Widget User Verify -->
<app-widget-match [numId]="subjectNumId" [subject]="userSubject" [widgetUrl]="widgetUrl" [verified]="false"
    [managerSettings]="settings" [konektorProperties]="konektorProperties" [ready]="verifyReady"
    (result)="onWidgetMatchResult($event)"></app-widget-match>


<ng-template #subjectReceptionInfo>
    <div class="flex flex-column align-items-start gap-2">
        <label class="signature-title">{{ 'content.signature_of' | translate }}</label>
        <div class="signature-container flex flex-column gap-2">
            <div class="flex flex-column gap-4">
                <div class="flex justify-content-center align-items-center gap-3" style="padding-bottom: 0rem;">
                    <img class="imgUser" src={{image}} />
                    <div class="flex flex-column gap-1">
                        <div class="namesLabel mb-2"> {{ userSubject.names + ' ' + userSubject.lastNames }} </div>
                        <div>
                            <span class="dataKey">{{ 'table.numId' | translate }}: </span>
                            <span class="dataValue">{{ userSubject.numId }}</span>
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'content.technology' | translate }}: </span>
                            <img style="width: 20px;"
                                pTooltip="{{ ('content.subjectReceptionSignatureTech' | translate) + ': ' + ('content.' + entry.subjectSignatureTech | translate) }}"
                                [src]="getTechIcon(entry.subjectSignatureTech)" />
                        </div>
                    </div>
                </div>
                <div style="padding-bottom: 0rem;">
                    <div class="signatureDateLabel">{{ ('content.signedOn' | translate) + ' ' +
                        (entry.subjectSignatureDate | date: 'short':'':language) }}</div>
                </div>
            </div>
        </div>
    </div>
</ng-template>
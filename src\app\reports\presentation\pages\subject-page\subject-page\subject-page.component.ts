import { DOCUMENT, formatDate } from '@angular/common';
import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Chart, { ChartType } from 'chart.js/auto';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { GetAllSubjectsUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';

@Component({
    selector: 'app-subject-page',
    templateUrl: './subject-page.component.html',
    styleUrl: './subject-page.component.css'
})
export class SubjectPageComponent implements OnInit {


    selectedProfile: string = "";
    profileOptions: Array<any> = [];
    profiles: Array<any> = environment.profileNames;
    chart: any;
    pieChartSubject: any;
    lChartSubject: any;
    fChartSubject: any;
    hChartSubject: any;
    dChartSubject: any;
    progressValue: string = "";
    progressEnabled: boolean = false;
    progressColor: string = "#0ab4ba";
    countMale: any;
    countFemale: any;
    countOther: any;
    countAdmin: any;
    countGeneral: any;
    countPrueba: any;
    countEnrolador: any;
    countMaster: any;
    countL1: any;
    countL2: any;
    countL3: any;
    countL4: any;
    countL5: any;
    countLO: any;
    countPicOk: any;
    countPicMissing: any;

    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";
    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
    });

    subjectsData: any[] = [];
    subjectsDataPreFiltered: any[] = [];
    showSpinners: boolean = true;

    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;
    showNoData5: boolean = false;

    constructor(
        @Inject(LOCALE_ID) private locale: string,
        @Inject(DOCUMENT) private document: Document,
        private getSubjects: GetAllSubjectsUseCase,
        private reportsService: ReportsService,
        private translate: TranslateService,
        private fb: FormBuilder,
        private loggerService: ConsoleLoggerService,
    ) { }

    ngOnInit(): void {

        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);

        this.profiles.forEach(p => {

            this.profileOptions.push({ "name": p.name });

        });

        this.selectedProfile = environment.profileDefault;


        setTimeout(() => {
            this.getAllSubjects();
        }, 1000);

    }



    getAllSubjects() {

        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";


        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            return;
        }

        if (dateStartDate < dateEndDate) {
            const startDate = dateStartDate.toISOString().split('T')[0] + "T00:00:00.000Z";
            const endDate = dateEndDate.toISOString().split('T')[0] + "T59:59:59.000Z";

            this.progressEnabled = true;
            this.progressValue = "5";

            this.getSubjects.execute({ offset: 0, limit: 10000 }).then(
                (data) => {
                    this.loggerService.debug(data);
                },
                (e) => {
                    this.loggerService.error("Error retrieving subjects: " + e);
                    setTimeout(() => {
                        this.progressEnabled = false;
                        this.progressValue = "0";

                    }, 2000);
                }

            )

            /*this.getSubjects.execute().subscribe({
                next: (subjectsReturn: any) => {


                    setTimeout(() => {

                        if (subjectsReturn.status == "SUCCESS") {

                            this.subjectsDataPreFiltered = subjectsReturn.data;

                            this.loggerService.debug("Subjects retrieved ok.")
                            this.loggerService.debug(subjectsReturn.data);

                            var index = 0;
                            this.countMale = 0;
                            this.countFemale = 0;
                            this.countOther = 0;

                            this.countAdmin = 0;
                            this.countGeneral = 0;
                            this.countPrueba = 0;
                            this.countEnrolador = 0;
                            this.countMaster = 0;

                            this.countL1 = 0;
                            this.countL2 = 0;
                            this.countL3 = 0;
                            this.countL4 = 0;
                            this.countL5 = 0;
                            this.countLO = 0;

                            this.countPicOk = 0;
                            this.countPicMissing = 0;

                            for (index = 0; index < this.subjectsDataPreFiltered.length; index++) {

                                var sub = this.subjectsDataPreFiltered[index];

                                if(new Date(sub.createdAt).toISOString() >= startDate && new Date(sub.createdAt).toISOString() <= endDate) {

                                    this.subjectsData.push(sub);

                                    if (sub.gender == "male") {

                                        this.countMale++;
                                    }
                                    else {

                                        if (sub.gender == "female") {

                                            this.countFemale++;
                                        }
                                        else {
                                            this.countOther++;
                                        }

                                    }

                                    if (sub.profile == "A")
                                        this.countAdmin++;

                                    if (sub.profile == "M")
                                        this.countMaster++;

                                    if (sub.profile == "P")
                                        this.countPrueba++;

                                    if (sub.profile == "G")
                                        this.countGeneral++;

                                    if (sub.profile == "EN")
                                        this.countEnrolador++;

                                    switch (sub.locationId) {

                                        case "Location 1":
                                            this.countL1++;
                                            break;

                                        case "Location 2":
                                            this.countL2++;
                                            break;

                                        case "Location 3":
                                            this.countL3++;
                                            break;

                                        case "Location 4":
                                            this.countL4++;
                                            break;

                                        case "Location 5":
                                            this.countL5++;
                                            break;

                                        default:
                                            this.countLO++;
                                            break;


                                    }

                                    switch (sub.pic) {

                                        case "assets/images/UserPic.svg":
                                            this.countPicMissing++;
                                            break;

                                        default:
                                            this.countPicOk++;
                                            break;
                                    }

                                }

                                this.progressValue = (Math.ceil((100 * index) / this.subjectsDataPreFiltered.length)).toString();

                            }

                            this.hideSpinners();
                           

                            this.progressValue = "100";

                            setTimeout(() => {

                                this.createDoughnutChartProfile();
                                this.createDoughnutChartGender();
                                this.createDoughnutChartLocation();
                                this.createDoughnutChartProfilePic();

                                this.progressEnabled = false;
                                this.progressValue = "0";

                            }, 2000);

                        }
                        else
                        {
                            this.loggerService.error("Error retrieving subjects.")

                            setTimeout(() => {
                                this.progressEnabled = false;
                                this.progressValue = "0";

                            }, 2000);

                        }

                    }, 100)

                    //for (var userIndex = 0; userIndex < userList.data.lenght; )
                    //this.usersData= users;

                },
                error: (e) => {
                    this.loggerService.error("Error retrieving subject: " + e);
                    setTimeout(() => {
                        this.progressEnabled = false;
                        this.progressValue = "0";
                    }, 2000);
                }
            });*/

            this.hideSpinners();


            this.progressValue = "100";

            setTimeout(() => {

                this.createDoughnutChartProfile();
                this.createDoughnutChartGender();
                this.createDoughnutChartLocation();
                this.createDoughnutChartProfilePic();

                this.progressEnabled = false;
                this.progressValue = "0";

            }, 2000);

        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            setTimeout(() => {

                this.progressEnabled = false;
                this.progressValue = "0";

            }, 2000);
        }
    }


    createDoughnutChartProfile() {

        var noData = this.translate.instant("messages.no_data");

        if (this.pieChartSubject == null) {


            if (this.countMaster == 0 && this.countAdmin == 0 && this.countEnrolador == 0 && this.countGeneral == 0 && this.countPrueba == 0) {


                this.pieChartSubject = new Chart("pieChartSubject", {
                    type: 'pie' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        labels: [
                            noData,
                            noData,
                            noData,
                            noData,
                            noData,
                        ],
                        datasets: [{
                            data: [20, 20, 20, 20, 20],
                            backgroundColor: [
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA
                            ],
                            hoverOffset: 4
                        }]
                    }

                });

            }
            else {

                this.pieChartSubject = new Chart("pieChartSubject", {
                    type: 'pie' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        labels: [
                            'Master',
                            'Admin',
                            'Enrolador',
                            'General',
                            'Otros'
                        ],
                        datasets: [{
                            data: [this.countMaster, this.countAdmin, this.countEnrolador, this.countGeneral, this.countPrueba],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,
                                environment.colorProfile3,
                                environment.colorProfile4,
                                environment.colorProfile5
                            ],
                            hoverOffset: 4
                        }]
                    }

                });
            }




        }
        else {


            if (this.countMaster == 0 && this.countAdmin == 0 && this.countEnrolador == 0 && this.countGeneral == 0 && this.countPrueba == 0) {


                // update graph
                this.pieChartSubject.options.plugins.legend.display = true;
                this.pieChartSubject.data.labels[0] = noData;
                this.pieChartSubject.data.labels[1] = noData;
                this.pieChartSubject.data.labels[2] = noData;
                this.pieChartSubject.data.labels[3] = noData;
                this.pieChartSubject.data.labels[4] = noData;

                this.pieChartSubject.data.datasets[0].backgroundColor = environment.colorDisabledA,
                    this.pieChartSubject.data.datasets[1].backgroundColor = environment.colorDisabledB,
                    this.pieChartSubject.data.datasets[2].backgroundColor = environment.colorDisabledA,
                    this.pieChartSubject.data.datasets[3].backgroundColor = environment.colorDisabledB,
                    this.pieChartSubject.data.datasets[4].backgroundColor = environment.colorDisabledA,

                    this.pieChartSubject.data.datasets[0].data = [20, 20, 20, 20, 20],
                    this.pieChartSubject.update();

            }
            else {

                // update graph
                this.pieChartSubject.options.plugins.legend.display = true;
                this.pieChartSubject.data.labels[0] = "Master";
                this.pieChartSubject.data.labels[1] = "Admin";
                this.pieChartSubject.data.labels[2] = "Enrolador";
                this.pieChartSubject.data.labels[3] = "General";
                this.pieChartSubject.data.labels[4] = "Otros";

                this.pieChartSubject.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.pieChartSubject.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.pieChartSubject.data.datasets[2].backgroundColor = environment.colorProfile3;
                this.pieChartSubject.data.datasets[3].backgroundColor = environment.colorProfile4;
                this.pieChartSubject.data.datasets[4].backgroundColor = environment.colorProfile5;

                this.pieChartSubject.data.datasets[0].data = [this.countMaster, this.countAdmin, this.countEnrolador, this.countGeneral, this.countPrueba];
                this.pieChartSubject.update();
            }


        }



    }


    createDoughnutChartGender() {

        var optionA = this.translate.instant("titles.male");
        var optionB = this.translate.instant("titles.female");
        var optionC = this.translate.instant("titles.otherGender");
        var noData = this.translate.instant("messages.no_data");

        if (this.hChartSubject == null) {

            if (this.countMale == 0 && this.countFemale == 0 && this.countOther == 0) {

                this.hChartSubject = new Chart("dChartSubject", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [33, 33, 33],
                            backgroundColor: [
                                environment.colorDisabledA,
                                environment.colorDisabledB,
                                environment.colorDisabledA,
                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            noData,
                            noData,
                            noData,
                        ]
                    }
                });

            }
            else {

                this.hChartSubject = new Chart("dChartSubject", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [this.countMale, this.countFemale, this.countOther],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,
                                environment.colorOther
                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            optionA,
                            optionB,
                            optionC
                        ]
                    }
                });

            }




        } else {

            if (this.countMale == 0 && this.countFemale == 0 && this.countOther == 0) {


                // update graph
                this.hChartSubject.options.plugins.legend.display = true;
                this.hChartSubject.data.datasets[0].data = [33, 33, 33];
                this.hChartSubject.data.labels[0] = noData;
                this.hChartSubject.data.labels[1] = noData;
                this.hChartSubject.data.labels[2] = noData;
                this.hChartSubject.data.datasets[0].backgroundColor = environment.colorDisabledA,
                    this.hChartSubject.data.datasets[1].backgroundColor = environment.colorDisabledB,
                    this.hChartSubject.data.datasets[2].backgroundColor = environment.colorDisabledA,
                    this.hChartSubject.update();


            }
            else {

                // update graph
                this.hChartSubject.options.plugins.legend.display = true;
                this.hChartSubject.data.labels[0] = optionA;
                this.hChartSubject.data.labels[1] = optionB;
                this.hChartSubject.data.labels[2] = optionC;
                this.hChartSubject.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.hChartSubject.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.hChartSubject.data.datasets[2].backgroundColor = environment.colorOther;
                this.hChartSubject.data.datasets[0].data = [this.countMale, this.countFemale, this.countOther];
                this.hChartSubject.update();
            }




        }


    }

    createDoughnutChartLocation() {

        var optionC = this.translate.instant("titles.otherGender");
        var noData = this.translate.instant("messages.no_data");

        if (this.lChartSubject == null) {


            if (this.countL1 == 0 && this.countL2 == 0 && this.countL3 == 0 && this.countL4 == 0 && this.countL5 == 0 && this.countLO == 0) {


                this.lChartSubject = new Chart("lChartSubject", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [16, 16, 16, 16, 16, 16],
                            backgroundColor: [
                                "#ececec",
                                "#acacac",
                                "#ececec",
                                "#acacac",
                                "#ececec",
                                "#acacac",

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            noData,
                            noData,
                            noData,
                            noData,
                            noData,
                            noData,
                        ]
                    }
                });
            }
            else {

                this.lChartSubject = new Chart("lChartSubject", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [this.countL1, this.countL2, this.countL3, this.countL4, this.countL5, this.countLO],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,
                                environment.colorProfile3,
                                environment.colorProfile4,
                                environment.colorProfile5,
                                environment.colorOther,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            'Location 1',
                            'Location 2',
                            'Location 3',
                            'Location 4',
                            'Location 5',
                            optionC,
                        ]
                    }
                });
            }




        }
        else {


            if (this.countL1 == 0 && this.countL2 == 0 && this.countL3 == 0 && this.countL4 == 0 && this.countL5 == 0 && this.countLO == 0) {

                // update graph
                this.lChartSubject.options.plugins.legend.display = true;
                this.lChartSubject.data.labels[0] = noData;
                this.lChartSubject.data.labels[1] = noData;
                this.lChartSubject.data.labels[2] = noData;
                this.lChartSubject.data.labels[3] = noData;
                this.lChartSubject.data.labels[4] = noData;
                this.lChartSubject.data.labels[5] = noData;
                this.lChartSubject.data.datasets[0].backgroundColor = environment.colorDisabledA,
                    this.lChartSubject.data.datasets[1].backgroundColor = environment.colorDisabledB,
                    this.lChartSubject.data.datasets[2].backgroundColor = environment.colorDisabledA,
                    this.lChartSubject.data.datasets[3].backgroundColor = environment.colorDisabledB,
                    this.lChartSubject.data.datasets[4].backgroundColor = environment.colorDisabledA,
                    this.lChartSubject.data.datasets[5].backgroundColor = environment.colorDisabledB,
                    this.lChartSubject.data.datasets[0].data = [this.countL1, this.countL2, this.countL3, this.countL4, this.countL5, this.countLO];
                this.lChartSubject.update();

            }
            else {

                // update graph
                this.lChartSubject.options.plugins.legend.display = true;
                this.lChartSubject.data.labels[0] = 'Location 1';
                this.lChartSubject.data.labels[1] = 'Location 2';
                this.lChartSubject.data.labels[2] = 'Location 3';
                this.lChartSubject.data.labels[3] = 'Location 4';
                this.lChartSubject.data.labels[4] = 'Location 5';
                this.lChartSubject.data.labels[5] = optionC;
                this.lChartSubject.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.lChartSubject.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.lChartSubject.data.datasets[2].backgroundColor = environment.colorOther;
                this.lChartSubject.data.datasets[3].backgroundColor = environment.colorDisabledA,
                    this.lChartSubject.data.datasets[4].backgroundColor = environment.colorDisabledB,
                    this.lChartSubject.data.datasets[5].backgroundColor = environment.colorDisabledA,
                    this.lChartSubject.data.datasets[0].data = [this.countL1, this.countL2, this.countL3, this.countL4, this.countL5, this.countLO];
                this.lChartSubject.update();
            }



        }


    }

    createDoughnutChartProfilePic() {

        var optionA = this.translate.instant("titles.picYes");
        var optionB = this.translate.instant("titles.picNo");
        var noData = this.translate.instant("messages.no_data");

        if (this.fChartSubject == null) {


            if (this.countPicOk == 0 && this.countPicMissing == 0) {

                this.fChartSubject = new Chart("fChartSubject", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [1, 1],
                            backgroundColor: [
                                environment.colorDisabledA,
                                environment.colorDisabledB,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            noData,
                            noData,
                        ]
                    }
                });

            }
            else {

                this.fChartSubject = new Chart("fChartSubject", {
                    type: 'doughnut' as ChartType, //this denotes tha type of chart

                    data: {// values on X-Axis
                        datasets: [{
                            data: [this.countPicOk, this.countPicMissing],
                            backgroundColor: [
                                environment.colorProfile1,
                                environment.colorProfile2,

                            ],
                        }],

                        // These labels appear in the legend and in the tooltips when hovering different arcs
                        labels: [
                            optionA,
                            optionB,
                        ]
                    }
                });
            }




        }
        else {

            if (this.countPicOk == 0 && this.countPicMissing == 0) {


                // update graph
                this.fChartSubject.options.plugins.legend.display = true;
                this.fChartSubject.data.labels[0] = noData;
                this.fChartSubject.data.labels[1] = noData;
                this.fChartSubject.data.datasets[0].backgroundColor = environment.colorDisabledA,
                    this.fChartSubject.data.datasets[1].backgroundColor = environment.colorDisabledB,
                    this.fChartSubject.data.datasets[0].data = [1, 1];
                this.fChartSubject.update();

            }
            else {


                // update graph
                this.fChartSubject.options.plugins.legend.display = true;
                this.fChartSubject.data.labels[0] = optionA;
                this.fChartSubject.data.labels[1] = optionB;
                this.fChartSubject.data.datasets[0].backgroundColor = environment.colorProfile1;
                this.fChartSubject.data.datasets[1].backgroundColor = environment.colorProfile2;
                this.fChartSubject.data.datasets[0].data = [this.countPicOk, this.countPicMissing];
                this.fChartSubject.update();

            }



        }


    }

    enableSpinners() {

        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = this.showNoData5 = false;

        if (this.pieChartSubject != null) {

            this.pieChartSubject.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.pieChartSubject.data.datasets.length; i++) {

                this.pieChartSubject.data.datasets[i].data = [0, 0, 0, 0, 0];

            }
            this.pieChartSubject.update();

        }

        if (this.hChartSubject != null) {


            this.hChartSubject.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.hChartSubject.data.datasets.length; i++) {

                this.hChartSubject.data.datasets[i].data = [0, 0, 0];

            }
            this.hChartSubject.update();

        }

        if (this.lChartSubject != null) {


            this.lChartSubject.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.lChartSubject.data.datasets.length; i++) {

                this.lChartSubject.data.datasets[i].data = [0, 0, 0, 0, 0, 0];

            }
            this.lChartSubject.update();

        }

        if (this.fChartSubject != null) {


            this.fChartSubject.options.plugins.legend.display = false;

            var i = 0;
            for (i = 0; i < this.fChartSubject.data.datasets.length; i++) {

                this.fChartSubject.data.datasets[i].data = [0, 0];

            }
            this.fChartSubject.update();

        }

        this.countMale = 0;
        this.countFemale = 0;
        this.countOther = 0;

        this.countAdmin = 0;
        this.countGeneral = 0;
        this.countPrueba = 0;
        this.countEnrolador = 0;
        this.countMaster = 0;

        this.countL1 = 0;
        this.countL2 = 0;
        this.countL3 = 0;
        this.countL4 = 0;
        this.countL5 = 0;
        this.countLO = 0;

        this.countPicOk = 0;
        this.countPicMissing = 0;

        this.showSpinners = true;
        this.subjectsData = [];
        this.subjectsDataPreFiltered = [];
        this.showSpinners = true;

    }

    hideSpinners() {

        this.showSpinners = false;
        this.subjectsData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
    }

}

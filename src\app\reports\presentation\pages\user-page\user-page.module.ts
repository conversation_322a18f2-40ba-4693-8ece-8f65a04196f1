import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UserPageRoutingModule } from './user-page-routing.module';
import { UserPageComponent } from './user-page/user-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { DiUserModule } from 'src/verazial-common-frontend/core/general/user/data/di-user.module';

@NgModule({
  declarations: [
    UserPageComponent
  ],
  imports: [
    CommonModule,
    UserPageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    TableModule,
    ProgressSpinnerModule,
    DropdownModule,
    ProgressBarModule,
    SkeletonModule,
    DiUserModule,

  ],
  exports: [
    UserPageComponent
  ]
})
export class UserPageModule { }

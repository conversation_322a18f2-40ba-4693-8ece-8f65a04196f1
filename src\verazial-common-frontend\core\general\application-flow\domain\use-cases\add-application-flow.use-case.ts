import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationFlowEntity } from "../entity/application-flow.entity";
import { ApplicationFlowRepository } from "../repository/application-flow.repository";

export class AddApplicationFlowUseCase implements UseCaseGrpc<ApplicationFlowEntity, ApplicationFlowEntity> {
    constructor(private applicationFlowRespository: ApplicationFlowRepository) { }
    execute(params: ApplicationFlowEntity): Promise<ApplicationFlowEntity> {
        return this.applicationFlowRespository.addApplicationFlow(params)
    }
}
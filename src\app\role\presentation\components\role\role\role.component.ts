import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { AttributeData } from 'src/verazial-common-frontend/core/models/attribute-data.model';

@Component({
  selector: 'app-role',
  templateUrl: './role.component.html',
  styleUrl: './role.component.css'
})
export class RoleComponent implements OnInit, OnDestroy {
  @Input() roleData: RoleEntity | undefined;
  @Output() outputData = new EventEmitter<RoleEntity>;

  roleTypes: AttributeData[] = [
    { key: RoleType.SUBJECT, value: this.translateService.instant('content.subject_profile') },
    { key: RoleType.USER, value: this.translateService.instant('content.user_role') },
  ];

  showAddNewRole: boolean = false;
  showRoleShowInMenu: boolean = true;

  public form: FormGroup = this.fb.group({
    roleName: ['', [Validators.required]],
    roleLevel: [0, [Validators.required]],
    roleType: ['', [Validators.required]],
    roleDescription: [],
    roleShowInMenu: [false]
  });

  constructor(
    private translateService: TranslateService,
    private validatorService: ValidatorService,
    private fb: FormBuilder) { }

  ngOnDestroy(): void {
    this.form.reset;
  }

  ngOnInit(): void {
    if (this.roleData) {
      this.fillRoleFields(this.roleData);
    }
  }

  fillRoleFields(role: RoleEntity) {
    this.form.get('roleName')?.setValue(role.name);
    this.form.get('roleLevel')?.setValue(role.level);
    this.form.get('roleType')?.setValue(role.type);
    this.triggerRoleShowInMenu(role.type!);
    this.form.get('roleDescription')?.setValue(role.description);
    this.form.get('roleShowInMenu')?.setValue(role.showInMenu);
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  checkSpecificError(field: string, error: string): boolean {
    return this.validatorService.checkSpecificError(this.form, field, error);
  }

  isRequiredField(field: string): boolean {
    return this.validatorService.isRequiredField(this.form, field);
  }

  trackRoleDataChange() {
    this.triggerRoleShowInMenu(this.form.get('roleType')?.value);
    if (this.isValid('roleName') && this.isValid('roleLevel') && this.isValid('roleType')) {
      let role = new RoleEntity();
      role.id = this.roleData?.id;
      role.name = this.form.get('roleName')?.value;
      role.level = this.form.get('roleLevel')?.value;
      role.type = this.form.get('roleType')?.value;
      role.description = this.form.get('roleDescription')?.value;
      role.showInMenu = this.form.get('roleShowInMenu')?.value;
      // this.loggerService.debug(role);
      this.outputData.emit(role);
    }
  }

  triggerRoleShowInMenu(type: RoleType) {
    if (type == RoleType.SUBJECT) {
      this.showRoleShowInMenu = true;
    }
    else {
      this.showRoleShowInMenu = false;
    }
  }
}
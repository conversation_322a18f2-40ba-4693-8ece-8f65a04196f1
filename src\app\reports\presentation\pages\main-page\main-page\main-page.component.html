<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<p-toast></p-toast>

<div class="main-container">
    <div class="main-progress">
        <p-progressBar [value]="progressValue" [color]="progressColor" [showValue]="progressEnabled" >
            <ng-template pTemplate="content" let-value style="background-color: #0ab4ba; border: solid 1px white; height: 10px ">
            </ng-template>
        </p-progressBar>
    </div>
    <div class="topBarItems" id="topBar">
        <div class="leftSideItems">
            <div class="recrodsTitle">
                {{'menu.reports_general' | translate}}
            </div>
        </div>
        <div class="rightSideItems">
            <div [formGroup]="datesForm" class="centerDiv searchButtonGroupSmall">
                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}"
                                    id="application" formControlName="application" [(ngModel)]="selectedApp"
                                    [options]="appOptions" optionLabel="name" dataKey="name" optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedApp">
                                    <div>{{ selectedApp }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-appOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ appOption.name }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-calendar appendTo="body" formControlName="rangeDates" [iconDisplay]="'input'" [showIcon]="true"
                            [(ngModel)]="dates" selectionMode="range" [class.ng-invalid]="dateError"
                            [class.ng-dirty]="dateError" aria-describedby="date-help1" [readonlyInput]="true"
                            inputId="multiple" dateFormat="{{ 'dateFormat' | translate }}" [showButtonBar]="true"[showTime]="true" 
                            [hourFormat]="'24'"></p-calendar>                        <div *ngIf="dateError">
                            <small class="error" id="date-help1">{{ dateErrorMessage | translate }}</small>
                        </div>
                    </div>
                </div>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}" class="searchButton searchButtonNormal" label="{{ 'update' | translate }}" [rounded]="true" (click)="getAllSubjects()"></p-button>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}" class="searchButton searchButtonSmall" icon="pi pi-refresh" [rounded]="true" (click)="getAllSubjects()"></p-button>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="container-plot">
            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '270px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'titles.subjects' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label *ngIf="!showNoData1" style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tMain5' | translate }} {{ 'reports.tMain3' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData1" style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="subNumber">{{ subjectComputedNumber }}</label>
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData1" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>
                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '280px', height: '120px' }" styleClass="card-m">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'titles.users' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label *ngIf="!showNoData2" style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tMain5' | translate }} {{ 'reports.tMain2' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData2" style="width: 300px; font-size: 32px; text-align: center; color: #0AB4BA; font-weight: 600" id="usrNumber">{{ userComputedNumber }}</label>
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData2" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>
                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '300px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'titles.samples' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label *ngIf="!showNoData3" style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tMain5' | translate }} {{ 'reports.tMain4' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData3" style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="samNumber">{{ samplesComputedNumber }}</label>
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData3" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>
                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '300px', height: '120px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'titles.samples_subjects' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label *ngIf="!showNoData4" style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tMain8' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData4" style="width: 300px; font-size: 32px; text-align: center; color: #0AB4BA; font-weight: 600" id="avrNumber">{{ samplesAverageComputedNumber }}</label>
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData4" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>
                </p-card>
            </div>

            <div class="card-style">
                <p-card header="" subheader="" [style]="{ width: '260px', height: '120px' }">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="text-align:left; font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'titles.profile_pics' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: left;">
                        <label *ngIf="!showNoData5" style="font-size: 14px; color: #6b7280; margin-bottom: .2rem; margin-top: .2rem; font-weight: 500 ">{{ 'reports.tMain5' | translate }} {{ 'reports.tMain9' | translate }}</label>
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton shape="circle" size="1.5rem" styleClass="mr-2" />
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center;">
                        <label *ngIf="!showNoData5" style="width: 300px; font-size: 32px; color: #0AB4BA; font-weight: 600" id="picNumber">{{ picturesComputedNumber }}</label>
                    </div>
                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData5" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>
                </p-card>
            </div>


            <div class="whole-card">

                <p-card header="" subheader="" [style]="{ height: '260px' }">

                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'table.subject_list' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="!showSpinners" style="text-align: center; margin-top:15px">
                        <label *ngIf="showNoData6" style="width: 300px; font-size: 13px; color: darkgray; font-weight: 600">{{ 'reports.noData' | translate }}</label>
                    </div>

                    <p-table *ngIf="!showNoData6" [value]="subjectsData" [scrollable]="true" scrollHeight="540px" [tableStyle]="{'min-width': '40rem'}" class="hoverTable">
                        <ng-template pTemplate="header">
                            <tr>
                                <th *ngIf="!showSpinners">{{ 'table.numId' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.first_name' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.username' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.email' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.profile' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                                <th *ngIf="!showSpinners">{{ 'table.enroll_date' | translate }}</th>
                                <th *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-subjectsData>
                            <tr>
                                <td *ngIf="!showSpinners">{{subjectsData.numId}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{subjectsData.names}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{subjectsData.username}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{subjectsData.email}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{subjectsData.profile}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                                <td *ngIf="!showSpinners">{{subjectsData.createdAt.substring(0,10) + " " + subjectsData.createdAt.substring(11,19)}}</td>
                                <td *ngIf="showSpinners"> <p-skeleton width="5rem" styleClass="mb-2" /></td>
                            </tr>
                        </ng-template>
                    </p-table>
                </p-card>

        </div>

            <div class="card-style-right">
                <p-card header="" [style]="{ width: '520px', height: '300px' }">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tMain6' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>

                    <div *ngIf="showSpinners">
                        <p-skeleton width="480px" height="230px" />
                    </div>
                    <div [hidden]="showSpinners">
                        <canvas style="width: 570px;" id="lChartMain">{{  lChart }}</canvas>
                    </div>
                </p-card>
            </div>

            <div class="card-style-right">
                <p-card header="" [style]="{ width: '520px', height: '300px' }">
                    <div style="text-align: left;">
                        <label *ngIf="!showSpinners" style="font-size: 18px; color: #6b7280; margin-bottom:.2rem; font-weight: 700">{{ 'reports.tMain7' | translate }}</label>
                        <p-skeleton *ngIf="showSpinners" width="5rem" styleClass="mb-2" />
                    </div>
                    <div *ngIf="showSpinners">
                        <p-skeleton width="480px" height="230px" />
                    </div>
                    <div [hidden]="showSpinners">
                        <canvas style="width: 570px;" id="vChartMain">{{ vChart }}</canvas>
                    </div>
                </p-card>
            </div>
        </div>
    </div>

</div>

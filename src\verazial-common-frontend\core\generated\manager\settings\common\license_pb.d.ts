import * as jspb from 'google-protobuf'

import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"


export class LicenseGrpcModel extends jspb.Message {
  getGuid(): string;
  setGuid(value: string): LicenseGrpcModel;
  hasGuid(): boolean;
  clearGuid(): LicenseGrpcModel;

  getSerialnumber(): string;
  setSerialnumber(value: string): LicenseGrpcModel;
  hasSerialnumber(): boolean;
  clearSerialnumber(): LicenseGrpcModel;

  getMacstring(): string;
  setMacstring(value: string): LicenseGrpcModel;
  hasMacstring(): boolean;
  clearMacstring(): LicenseGrpcModel;

  getIpstring(): string;
  setIpstring(value: string): LicenseGrpcModel;
  hasIpstring(): boolean;
  clearIpstring(): LicenseGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): LicenseGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): LicenseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): LicenseGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: LicenseGrpcModel): LicenseGrpcModel.AsObject;
  static serializeBinaryToWriter(message: LicenseGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): LicenseGrpcModel;
  static deserializeBinaryFromReader(message: LicenseGrpcModel, reader: jspb.BinaryReader): LicenseGrpcModel;
}

export namespace LicenseGrpcModel {
  export type AsObject = {
    guid?: string,
    serialnumber?: string,
    macstring?: string,
    ipstring?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum GuidCase { 
    _GUID_NOT_SET = 0,
    GUID = 1,
  }

  export enum SerialnumberCase { 
    _SERIALNUMBER_NOT_SET = 0,
    SERIALNUMBER = 2,
  }

  export enum MacstringCase { 
    _MACSTRING_NOT_SET = 0,
    MACSTRING = 3,
  }

  export enum IpstringCase { 
    _IPSTRING_NOT_SET = 0,
    IPSTRING = 4,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 9,
  }
}

export class ArrayOfLicenses extends jspb.Message {
  getLicensesList(): Array<LicenseGrpcModel>;
  setLicensesList(value: Array<LicenseGrpcModel>): ArrayOfLicenses;
  clearLicensesList(): ArrayOfLicenses;
  addLicenses(value?: LicenseGrpcModel, index?: number): LicenseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfLicenses.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfLicenses): ArrayOfLicenses.AsObject;
  static serializeBinaryToWriter(message: ArrayOfLicenses, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfLicenses;
  static deserializeBinaryFromReader(message: ArrayOfLicenses, reader: jspb.BinaryReader): ArrayOfLicenses;
}

export namespace ArrayOfLicenses {
  export type AsObject = {
    licensesList: Array<LicenseGrpcModel.AsObject>,
  }
}


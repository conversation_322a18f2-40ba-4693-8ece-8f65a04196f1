import { RouterModule, Routes } from "@angular/router";
import { PrisonsVisitsPageComponent } from "./visits-page/visits-page.component";
import { AuthGuard } from "src/verazial-common-frontend/core/guards/auth.guard";
import { NavigationGuard } from "src/verazial-common-frontend/core/guards/navigation.guard";
import { NgModule } from "@angular/core";
import { TransfersPageComponent } from "./transfers-page/transfers-page.component";

const routes: Routes = [
  {
    path:'visits',
    children: [
      {
        path: '',
        component: PrisonsVisitsPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
    ]
  },
  {
    path: 'transfers',
    children: [
      {
        path: '',
        component: TransfersPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PrisonsPageRoutingModule { }
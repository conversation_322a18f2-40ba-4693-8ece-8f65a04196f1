export class StructEntity {
  [key: string]: any; // Represents a map of key-value pairs where the values can be any type.

  constructor(initialData?: StructEntity) {
    if (initialData) {
      Object.assign(this, initialData); // Assign provided data to the instance
    }
  }

  // Recursive function to find a key-value pair
  findKeyValue(keyToFind: string, scope?: string): any {
    // Si se especifica un alcance, limitar la búsqueda al subobjeto
    const baseObject = scope ? this[scope] : this;
  
    if (baseObject && keyToFind in baseObject) {
      const value = baseObject[keyToFind];
      if (Array.isArray(value)) {
        return value.filter(item => item !== null).pop(); // Último valor no nulo
      }
      return value;
    }
  
    for (const key in baseObject) {
      if (baseObject[key] && typeof baseObject[key] === 'object') {
        const result = new StructEntity(baseObject[key]).findKeyValue(keyToFind);
        if (result !== undefined) {
          return result;
        }
      }
    }
  
    return undefined;
  }

  // Method to add a key-value pair
  addKeyValue(key: string, value: any): void {
    if (key) {
      this[key] = value;
    }
  }
}
.main{
    padding: 0 0 0 0!important;
    /* min-width: 85%!important; */
    width: 100vw!important;
    height: 100%;
}

.content-list-flows{
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 40px 20px 10px 20px;
}

.header-flows{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 15px 0 15px 0;
}

.title-list-flows{
    color: #192C4C;
    font-weight: 700;
    font-size: 20px;
    line-height: 32px;
}

.dialog-footer{
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.dialog-content{
    display: flex;
    width: 318px;
    padding: 10px 0 10px 0;
    justify-content: center;
    flex-direction: column;
}

.dialog-content .field{
    display: flex;
    flex-direction: column;
}

.dialog-footer{
    display: flex;
    justify-content: center;
}

.right {
    text-align: right;
    margin-right: 1em;
}
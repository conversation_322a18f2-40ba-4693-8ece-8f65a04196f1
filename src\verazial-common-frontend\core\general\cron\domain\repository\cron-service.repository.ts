import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";
import { ScheduleJobRequestModel } from "../../common/models/schedule-job-request.model";

export abstract class CronServiceRepository {
    abstract triggerJobSchedulerInit(): Promise<SuccessResponse>;
    abstract triggerJobById(params: { id: string }): Promise<SuccessResponse>;
    abstract initJobScheduleByJobId(params: ScheduleJobRequestModel): Promise<SuccessResponse>;
    abstract removeJobScheduleByJobId(params: { id: string }): Promise<SuccessResponse>;
}
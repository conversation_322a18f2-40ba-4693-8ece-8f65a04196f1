import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { WindowParametersEntity } from "../entities/window-parameters.entity";
import { WindowParametersRepository } from "../repositories/window-parameters.repository";

export class GetWindowParamsByAppIdUseCase implements UseCaseGrpc<{ appId: string }, WindowParametersEntity[]> {
    constructor(private windowParametersRepository: WindowParametersRepository) { }
    execute(params: { appId: string; }): Promise<WindowParametersEntity[]> {
        return this.windowParametersRepository.getWindowParamsByAppId(params);
    }
}
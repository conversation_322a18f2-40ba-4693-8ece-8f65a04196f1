import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';
import { MessagesModule } from 'primeng/messages';
import { LocationsPageComponent } from './locations-page/locations-page.component';
import { LocationsPageRoutingModule } from './locations-page-routing.module';
import { TreeTableModule } from 'primeng/treetable';
import { UserNotVerifiedModule } from 'src/verazial-common-frontend/modules/shared/components/user-not-verified/user-not-verified.module';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    LocationsPageComponent
  ],
  imports: [
    CommonModule,
    LocationsPageRoutingModule,
    EmptyModule,
    LoadingSpinnerModule,
    ToastModule,
    ButtonModule,
    TranslateModule,
    ConfirmDialogModule,
    DialogModule,
    MessagesModule,
    TreeTableModule,
    UserNotVerifiedModule,
    InputTextModule,
    FormsModule,
  ]
})
export class LocationsPageModule { }

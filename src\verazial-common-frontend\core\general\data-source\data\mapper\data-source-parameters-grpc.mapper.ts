import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { DataSourceParametersEntity } from "../../domain/entities/data-source-parameters.entity";
import { AppDataSourceParamsGrpcModel } from "src/verazial-common-frontend/core/generated/datasource/datasource_params_pb";

export class DataSourceParametersGrpcMapper extends Mapper<AppDataSourceParamsGrpcModel, DataSourceParametersEntity> {
    override mapFrom(param: AppDataSourceParamsGrpcModel): DataSourceParametersEntity {
        return {
            id: param.getId(),
            dataSourceId: param.getDatasourceid(),
            parameter: param.getParameter(),
            value: param.getValue(),
            type: param.getType(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }
    override mapTo(param: DataSourceParametersEntity): AppDataSourceParamsGrpcModel {
        let model = new AppDataSourceParamsGrpcModel();
        model.setId(param.id!!);
        model.setDatasourceid(param.dataSourceId!!);
        model.setParameter(param.parameter!!);
        model.setValue(param.value!!);
        model.setType(param.type!!);
        return model;
    }

}
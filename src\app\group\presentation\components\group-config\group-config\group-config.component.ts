import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { AssignmentType } from 'src/verazial-common-frontend/core/general/assignment/group/common/enums/assignment-type.enum';
import { AssignmentInfo } from 'src/verazial-common-frontend/core/general/assignment/group/common/interfaces/assignment-info';
import { AssignmentElementEntity } from 'src/verazial-common-frontend/core/general/assignment/group/domain/entity/assignment-elements.entity';
import { AssignmentEntity } from 'src/verazial-common-frontend/core/general/assignment/group/domain/entity/assignment.entity';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';

@Component({
  selector: 'app-group-config',
  templateUrl: './group-config.component.html',
  styleUrl: './group-config.component.css'
})
export class GroupConfigComponent implements OnInit {
  @Input() canReadAndWrite: boolean = false;
  @Input() inputData: AssignmentInfo | undefined;
  @Input() selectedConfigSchedules: AssignmentElementEntity[] = [];
  @Input() selectedConfigUsers: AssignmentElementEntity[] = [];
  @Output() outputData = new EventEmitter<AssignmentEntity>();
  @Output() dataValid = new EventEmitter<Boolean>();

  groupCategoryType = GroupCategoryType;

  public form: FormGroup = this.fb.group({
    isRequiredWithinSchedule: [false, [Validators.required]],
    alertIfAllPerformedWithinSchedule: [false, [Validators.required]],
    alertIfNotPerformedWithinSchedule: [false, [Validators.required]],
    alertIfPerformedOutsideSchedule: [false, [Validators.required]],
  });

  constructor(
    private validatorService: ValidatorService,
    private fb: FormBuilder,
    private loggerService: ConsoleLoggerService,
  ) { }

  ngOnInit(): void {
    if (this.inputData) {
      this.form.get('isRequiredWithinSchedule')?.setValue(this.inputData.isRequiredWithinSchedule);
      this.form.get('alertIfAllPerformedWithinSchedule')?.setValue(this.inputData.alertIfAllPerformedWithinSchedule);
      this.form.get('alertIfNotPerformedWithinSchedule')?.setValue(this.inputData.alertIfNotPerformedWithinSchedule);
      this.form.get('alertIfPerformedOutsideSchedule')?.setValue(this.inputData.alertIfPerformedOutsideSchedule);
    }
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  checkSpecificError(field: string, error: string): boolean {
    return this.validatorService.checkSpecificError(this.form, field, error);
  }

  isRequiredField(field: string): boolean {
    return this.validatorService.isRequiredField(this.form, field);
  }

  trackChanges(event: any) {
    this.emitData()
  }

  emitData() {
    let data: AssignmentInfo;
    data = {
      isRequiredWithinSchedule: this.form.get('isRequiredWithinSchedule')?.value,
      alertIfAllPerformedWithinSchedule: this.form.get('alertIfAllPerformedWithinSchedule')?.value,
      alertIfNotPerformedWithinSchedule: this.form.get('alertIfNotPerformedWithinSchedule')?.value,
      alertIfPerformedOutsideSchedule: this.form.get('alertIfPerformedOutsideSchedule')?.value,
    }
    // this.loggerService.debug(data)
    let output = new AssignmentEntity();
    output.elements = [];
    if (data.isRequiredWithinSchedule || data.alertIfAllPerformedWithinSchedule || data.alertIfNotPerformedWithinSchedule || data.alertIfPerformedOutsideSchedule)
    {
      if (data.isRequiredWithinSchedule) {
        if (this.selectedConfigSchedules.length > 0) {
          this.selectedConfigSchedules.forEach(element => {
            output!.elements!.push(element);
          });
        }
        else {
          this.dataValid.emit(false);
          return;
        }
      }
      output.isRequiredWithinSchedule = data.isRequiredWithinSchedule;
      output.alertIfAllPerformedWithinSchedule = data.alertIfAllPerformedWithinSchedule;
      output.alertIfNotPerformedWithinSchedule = data.alertIfNotPerformedWithinSchedule;
      output.alertIfPerformedOutsideSchedule = data.alertIfPerformedOutsideSchedule
      if (data.alertIfAllPerformedWithinSchedule || data.alertIfNotPerformedWithinSchedule || data.alertIfPerformedOutsideSchedule) {
        if (this.selectedConfigUsers.length > 0) {
          this.selectedConfigUsers.forEach(element => {
            output!.elements!.push(element);
          });
        }
        else {
          this.dataValid.emit(false);
          return;
        }
      }
    }
    this.dataValid.emit(true);
    this.outputData.emit(output);
  }

  getSelectedConfigSchedules(categories: GroupCategoryEntity[]) {
    this.selectedConfigSchedules = [];
    categories.forEach(category => {
      const assignmentElement = new AssignmentElementEntity();

      assignmentElement.type = AssignmentType.CONFIG_SCHEDULES;
      assignmentElement.subtype = undefined;
      assignmentElement.elementId = category.id;

      this.selectedConfigSchedules.push(assignmentElement);
    })
    this.emitData();
  }

  getSelectedConfigUsers(categories: GroupCategoryEntity[]) {
    this.selectedConfigUsers = [];
    categories.forEach(category => {
      const assignmentElement = new AssignmentElementEntity();

      assignmentElement.type = AssignmentType.CONFIG_USERS;
      assignmentElement.subtype = undefined;
      assignmentElement.elementId = category.id;

      this.selectedConfigUsers.push(assignmentElement);
    })
    this.emitData();
  }
}

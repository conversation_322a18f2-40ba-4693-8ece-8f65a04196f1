import { Mapper } from "src/verazial-common-frontend/core/mapper"
import { ApplicationEntity } from "../../domain/entities/application.entity"
import { ApplicationGrpcModel } from "src/verazial-common-frontend/core/generated/application/application_pb";

export class ApplicationGrpcMapper extends Mapper<ApplicationGrpcModel, ApplicationEntity> {
    override mapFrom(param: ApplicationGrpcModel): ApplicationEntity {
        return {
            id: param.getId(),
            appRegistryId: param.getAppregistryid(),
            dataSourceId: param.getDatasourceid(),
            applicationName: param.getApplicationname(),
            flowType: param.getFlowtype(),
            fullPath: param.getFullpath(),
            technology: param.getTechnology(),
            applicationType: param.getApplicationtype(),
            status: param.getStatus(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }
    override mapTo(param: ApplicationEntity): ApplicationGrpcModel {
        let model = new ApplicationGrpcModel();
        model.setId(param.id!!);
        model.setAppregistryid(param.appRegistryId!!);
        model.setDatasourceid(param.dataSourceId!!);
        model.setApplicationname(param.applicationName!!);
        model.setFlowtype(param.flowType!!)
        model.setFullpath(param.fullPath!!);
        model.setTechnology(param.technology!!);
        model.setStatus(param.status!!);
        model.setApplicationtype(param.applicationType!!);
        return model;
    }


}
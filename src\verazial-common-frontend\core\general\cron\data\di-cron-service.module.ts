import { NgModule } from "@angular/core";
import { CronServiceRepository } from "../domain/repository/cron-service.repository";
import { InitJobScheduleByJobIdUseCase } from "../domain/usecases/init-job-schedule-by-job-id.use-case";
import { TriggerJobByIdUseCase } from "../domain/usecases/trigger-job-by-id.use-case";
import { TriggerJobSchedulerInitUseCase } from "../domain/usecases/trigger-job-scheduler-init.use-case";
import { CronServiceRepositoryImpl } from "./repository-impl/cron-service-impl.repository";
import { CommonModule } from "@angular/common";
import { RemoveJobScheduleByJobIdUseCase } from "../domain/usecases/remove-job-schedule-by-job-id.use-case";

const triggerJobSchedulerInitUseCaseFactory =
    (CronServiceRepository: CronServiceRepository) => new TriggerJobSchedulerInitUseCase(CronServiceRepository);

const triggerJobByIdUseCaseFactory =
    (CronServiceRepository: CronServiceRepository) => new TriggerJobByIdUseCase(CronServiceRepository);

const initJobScheduleByJobIdUseCaseFactory =
    (CronServiceRepository: CronServiceRepository) => new InitJobScheduleByJobIdUseCase(CronServiceRepository);

const removeJobScheduleByJobIdUseCaseFactory =
    (CronServiceRepository: CronServiceRepository) => new RemoveJobScheduleByJobIdUseCase(CronServiceRepository);

export const triggerJobSchedulerInitUseCaseProvider = {
    provide: TriggerJobSchedulerInitUseCase,
    useFactory: triggerJobSchedulerInitUseCaseFactory,
    deps: [CronServiceRepository]
};

export const triggerJobByIdUseCaseProvider = {
    provide: TriggerJobByIdUseCase,
    useFactory: triggerJobByIdUseCaseFactory,
    deps: [CronServiceRepository]
};

export const initJobScheduleByJobIdUseCaseProvider = {
    provide: InitJobScheduleByJobIdUseCase,
    useFactory: initJobScheduleByJobIdUseCaseFactory,
    deps: [CronServiceRepository]
};

export const removeJobScheduleByJobIdUseCaseProvider = {
    provide: RemoveJobScheduleByJobIdUseCase,
    useFactory: removeJobScheduleByJobIdUseCaseFactory,
    deps: [CronServiceRepository]
};

@NgModule({
    providers: [
        triggerJobSchedulerInitUseCaseProvider,
        triggerJobByIdUseCaseProvider,
        initJobScheduleByJobIdUseCaseProvider,
        removeJobScheduleByJobIdUseCaseProvider,
        { provide: CronServiceRepository, useClass: CronServiceRepositoryImpl },
    ],
    imports: [
        CommonModule,
    ]
})
export class DiCronServiceModule { }
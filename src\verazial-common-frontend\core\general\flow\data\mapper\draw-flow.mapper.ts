import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { DrawFlowEntity } from "../../domain/entity/draw-flow.entity";
import { DrawFlowGrpcModel } from "src/verazial-common-frontend/core/generated/flow/draw_flow_pb";

export class DrawFlowMapper extends Mapper<DrawFlowGrpcModel, DrawFlowEntity> {
    override mapFrom(param: DrawFlowGrpcModel): DrawFlowEntity {
        let drawFlowEntity = new DrawFlowEntity();
        drawFlowEntity.id = param.getId();
        drawFlowEntity.taskFlowId = param.getTaskflowid();
        drawFlowEntity.drawFlow = param.getDrawflow();
        drawFlowEntity.createdAt = new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6));
        drawFlowEntity.updatedAt = new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6));

        return drawFlowEntity;
    }
    override mapTo(param: DrawFlowEntity): DrawFlowGrpcModel {
        let drawFlowMode = new DrawFlowGrpcModel();
        drawFlowMode.setId(param.id!!)
        drawFlowMode.setTaskflowid(param.taskFlowId!!);
        drawFlowMode.setDrawflow(param.drawFlow!!);
        return drawFlowMode;
    }
}
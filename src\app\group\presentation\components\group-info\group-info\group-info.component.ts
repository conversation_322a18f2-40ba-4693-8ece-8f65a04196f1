import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AssignmentInfo } from 'src/verazial-common-frontend/core/general/assignment/group/common/interfaces/assignment-info';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';

@Component({
  selector: 'app-group-info',
  templateUrl: './group-info.component.html',
  styleUrl: './group-info.component.css'
})
export class GroupInfoComponent implements OnInit{
  
  @Input() inputData: AssignmentInfo | undefined;
  @Output() outputData = new EventEmitter<AssignmentInfo>();
  @Output() dataValid = new EventEmitter<Boolean>();

  constructor(
    private validatorService: ValidatorService,
    private fb: FormBuilder,
  ){}

  ngOnInit(): void {
    if(this.inputData){
      this.form.get('adminGroupName')?.setValue(this.inputData.name);
      this.form.get('adminGroupDescription')?.setValue(this.inputData.description);
    }
  }


  public form: FormGroup = this.fb.group({
    adminGroupName: ['', [Validators.required]],
    adminGroupDescription: ['', [Validators.required]],
  });

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  checkSpecificError(field: string, error: string): boolean {
    return this.validatorService.checkSpecificError(this.form, field, error);
  }

  isRequiredField(field: string): boolean {
    return this.validatorService.isRequiredField(this.form, field);
  }

  trackNameChanges(event: any){
    this.form.get('adminGroupName')?.markAsTouched;
    this.emitData()
  }

  trackDescriptionChanges(event:string){
    this.emitData()
  }

  emitData(){
    let data: AssignmentInfo;
    if(this.isValid('adminGroupName')){
      data = {
        name: this.form.get('adminGroupName')?.value,
        description: this.form.get('adminGroupDescription')?.value,
      }
      this.outputData.emit(data);
      this.dataValid.emit(true);
    }else{
      this.dataValid.emit(false);
    }
  }
}

<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-content-center w-full h-full mt-4">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" [ngClass]="{'spinner': isLoading}"></p-progressSpinner>
    </div>
</ng-template>
<div *ngIf="!isLoading else loadingSpinner" class="subcontainer">
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.related_subjects' | translate}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedRelatedSubjects.length > 0){
                        <div>
                            {{ selectedRelatedSubjects.length + ' ' + ('content.selected' | translate) }}
                        </div>
                        <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedRelatedSubjects.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="editRelatedSubject()"></button>
                        <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteRelatedSubject()"></button>
                    }
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <div class="add-action-main-full">
                    <p-button
                        ngClass="add-action-main-full"
                        [outlined]="true"
                        [rounded]="true"
                        severity="secondary"
                        label="{{ 'content.subject_relationships' | translate }}"
                        [rounded]="true"
                        (onClick)="showRelationsToSubjectDialog = true"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [style]="{'color': '#192C4C !important', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-users"
                        [rounded]="true"
                        (onClick)="showRelationsToSubjectDialog = true"
                    ></p-button>
                </div>
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        ngClass="add-action-main-full"
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'content.new_related_subject' | translate }}"
                        icon="pi pi-plus" iconPos="right"
                        [rounded]="true"
                        (onClick)="showRelatedSubjectDialog = true"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus"
                        [rounded]="true"
                        (onClick)="showRelatedSubjectDialog = true"
                    ></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfRelatedSubjects"
            (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedRelatedSubjects"
            (selectionChange)="onRelatedSubjectSelectionChange($event)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['numId', 'names', 'lastNames', 'roles', 'birthdate', 'gender', 'email']"
            [sortField]="'id'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="id"> {{ 'content.id' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="relatedSubjectId">{{ 'content.related_subject' | translate }} <p-sortIcon field="relatedSubjectId"></p-sortIcon></th>
                    <!-- <th class="fixed-column" pSortableColumn="center">{{ 'table.center' | translate }} <p-sortIcon field="center"></p-sortIcon></th> -->
                    <th class="fixed-column" pSortableColumn="center">{{ 'table.center' | translate }}</th>
                    <th class="fixed-column" pSortableColumn="relationship">{{ 'content.relationship' | translate }} <p-sortIcon field="relationship"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="isVisitor">{{ 'content.isVisitor' | translate }} <p-sortIcon field="isVisitor"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="comments">{{ 'content.comments' | translate }} <p-sortIcon field="comments"></p-sortIcon></th>
                    <th class="fixed-column" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox/>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="relatedSubjectId" [showMenu]="false" matchMode="customString">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="!isUser">
                        <p-columnFilter type="text" field="center" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-treeSelect appendTo="body"
                                    class="md:w-20rem w-full"
                                    containerStyleClass="w-full"
                                    [ngModel]="value"
                                    [options]="lCentreOptions"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    [filter]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                ></p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="relationship" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="isVisitor" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="comments" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-relatedSubject let-rowIndex="rowIndex">
                <tr class="p-selectable-row" [pSelectableRow]="relatedSubject" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="relatedSubject"></p-tableCheckbox>
                    </td>
                    <td (click)="editRelatedSubject(relatedSubject)" showDelay="1000" pTooltip="{{relatedSubject.id}}" tooltipPosition="top" class="ellipsis-cell">{{ relatedSubject.id }}</td>
                    <td (click)="editRelatedSubject(relatedSubject)" showDelay="1000" pTooltip="{{getSubjectName(relatedSubject.relatedSubjectId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getSubjectName(relatedSubject.relatedSubjectId) }}</td>
                    <td (click)="editRelatedSubject(relatedSubject)" showDelay="1000" pTooltip="{{getSubjectLocationName(relatedSubject.relatedSubjectId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getSubjectLocationName(relatedSubject.relatedSubjectId) }}</td>
                    <td (click)="editRelatedSubject(relatedSubject)" showDelay="1000" pTooltip="{{relatedSubject.relationship}}" tooltipPosition="top" class="ellipsis-cell">{{ relatedSubject.relationship }}</td>
                    <td (click)="editRelatedSubject(relatedSubject)" showDelay="1000" pTooltip="{{( relatedSubject.isVisitor ? 'options.true' : 'options.false' ) | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ ( relatedSubject.isVisitor ? 'options.true' : 'options.false' ) | translate }}</td>
                    <td (click)="editRelatedSubject(relatedSubject)" showDelay="1000" pTooltip="{{relatedSubject.comments}}" tooltipPosition="top" class="ellipsis-cell">{{ relatedSubject.comments }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editRelatedSubject(relatedSubject)"></button>
                            <button pButton pRipple [disabled]="!canReadAndWrite || managerSettings?.allowRemoveIdentity == false || !userIsVerified" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteRelatedSubject(relatedSubject)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>

        <!-- Create/Edit Relate Subject -->
        <p-dialog [(visible)]="showRelatedSubjectDialog" styleClass="p-fluid" [modal]="true" [style]="{'width': '40vw'}" (onHide)="onCancelDialog()">
            <ng-template pTemplate="header">
                <div >
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ (isNew ? 'content.new_related_subject' : 'content.related_subject') | translate }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <div>
                    <div class="flex justify-content-end pb-3 requiredFieldsLabel">
                        {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                    </div>
                    <form [formGroup]="form">
                        <div class="grid form-fields">
                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="subjectId">{{ 'content.subject' | translate }} <span
                                            *ngIf="isRequiredField('subjectId')" class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="subjectId" [(ngModel)]="mainSubjectSelected" (ngModelChange)="trackDataChange()"
                                        appendTo="body" [options]="mainSubjectOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                                    <small *ngIf="!isValid('subjectId') && form.controls['subjectId'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>
                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="relatedSubjectId">{{ 'content.related_subject' | translate }} <span
                                            *ngIf="isRequiredField('relatedSubjectId')" class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="relatedSubjectId" [(ngModel)]="relatedSubjectSelected" (ngModelChange)="trackDataChange()"
                                        [filter]="true" filterBy="value"
                                        appendTo="body" [options]="relatedSubjectOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                                    <small *ngIf="!isValid('relatedSubjectId') && form.controls['relatedSubjectId'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>
                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="relationship">{{ 'content.relationship' | translate }} <span
                                            *ngIf="isRequiredField('relationship')" class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="relationship" [(ngModel)]="relationshipSelected" (ngModelChange)="trackDataChange()"
                                        appendTo="body" [options]="relationshipOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                                    <small *ngIf="!isValid('relationship') && form.controls['relationship'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>
                            <div class="lg:col-6 sm:col-12 width100 flex flex-column justify-content-center" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <label class="label-form" for="isVisitor">{{'content.isVisitor' | translate}}</label>
                                <div class="flex flex-row gap-3 justify-content-center align-items-center align-content-center">
                                    <p-inputSwitch formControlName="isVisitor" [binary]="true" (ngModelChange)="trackDataChange()"></p-inputSwitch>
                                    <small *ngIf="!isValid('isVisitor') && form.controls['isVisitor'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>
                            <div class="lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="comments">{{ 'content.comments' | translate }} <span
                                            *ngIf="isRequiredField('comments')" class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="comments" (ngModelChange)="trackDataChange()" />
                                    <small *ngIf="!isValid('comments') && form.controls['comments'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </ng-template>
            <ng-template pTemplate="footer">
                <div class="flex flex-row gap-1 mr-3 justify-content-end">
                    <p-button
                        *ngIf="canReadAndWrite && userIsVerified"
                        [disabled]="!(canReadAndWrite && userIsVerified) || isDisableSaveButton"
                        [style]="{'pointer-events': 'auto', 'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'save'| translate }}"
                        (onClick)="saveRelatedSubject()"
                    ></p-button>
                    <p-button
                        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ (canReadAndWrite && userIsVerified ? 'cancel' : 'close')| translate }}"
                        (onClick)="onCancelDialog()"
                    ></p-button>
                </div>
            </ng-template>
        </p-dialog>

        <!-- Show Subjects Related to this Subject -->
        <p-dialog [(visible)]="showRelationsToSubjectDialog" styleClass="p-fluid" [modal]="true" [style]="{'width': '80vw'}">
            <ng-template pTemplate="header">
                <div class="flex flex-column justify-content-center align-items-center">
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ 'content.subject_relationships' | translate }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <p-organizationChart
                    [value]="data"
                    [collapsible]="true">
                        <ng-template let-node pTemplate="person">
                            <div class="p-2 text-center cursor-pointer" (click)="navigateToSubject(node.data.numId)">
                                <img
                                    [src]="node.data.image ? ((node.data.image.includes('data:image/jpeg;base64,') ? '' : (node.data.image.includes(imagePlaceholder) ? '' : 'data:image/jpeg;base64,')) + node.data.image) : imagePlaceholder"
                                    class="mb-3 w-3rem h-3rem" />
                                <div class="font-bold text-xl">
                                    {{ node.data.name }}
                                </div>
                                <div class="text-color-secondary">
                                    {{ node.data.role }}
                                </div>
                                <div class="text-sm">
                                    {{ node.data.title.includes('content.') ? (node.data.title | translate) : node.data.title }}
                                </div>
                            </div>
                        </ng-template>
                </p-organizationChart>
            </ng-template>
        </p-dialog>
    </div>
</div>
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { StepsModule } from 'primeng/steps';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { ListAssignmentsComponent } from './list-assignments/list-assignments.component';
import { GroupInfoModule } from '../group-info/group-info.module';
import { FlowAssignmentModule } from '../flow-assignment/flow-assignment.module';
import { CategoryAssignmentModule } from '../category-assignment/category-assignment.module';
import { DetailAssignmentModule } from '../detail-assignment/detail-assignment.module';
import { GroupConfigModule } from '../group-config/group-config.module';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';


@NgModule({
  declarations: [
    ListAssignmentsComponent
  ],
  imports: [
    CommonModule,
    /* Form */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG */
    TableModule,
    ToastModule,
    ButtonModule,
    ConfirmDialogModule,
    DialogModule,
    StepsModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    CalendarModule,
    ScrollPanelModule,
    /* Custom */
    GroupInfoModule,
    FlowAssignmentModule,
    CategoryAssignmentModule,
    DetailAssignmentModule,
    GroupConfigModule,
    EmptyModule,
  ],
  exports: [
    ListAssignmentsComponent
  ]
})
export class ListAssignmentsModule { }

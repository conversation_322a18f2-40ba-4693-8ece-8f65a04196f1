import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { WindowParametersRepository } from "../repositories/window-parameters.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export class DeleteWindowParamByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private windowParametersRepository: WindowParametersRepository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.windowParametersRepository.deleteWindowParamById(params);
    }

}
.main{
    display: flex;
    flex-direction: column;
    height: 88vh;
}

.content-flow{
    display: flex;
    position: absolute;
    flex-direction: column;
}

.edit-header{
    display: flex;
    height: 60px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: #DADEE3;
    padding: 0 10px 0 10px;
    border-radius: 10px 10px 0px 0px;
}

.edit{
    flex-direction: column;
}

.button-new-component{
    margin: 10px 0 10px 0;
}

.dialog-footer{
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.edit-header .content{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.main-content{
    /*display: flex;*/
    height: calc(100% - 60px);
    /*width: 100%;
    flex-direction: row;
    position: absolute;*/
}

.menu-actions{
    display: flex;
    flex-direction: column;
    width: 223px;
    /*height: 91vh;*/
    background: #F5F9FF;
    /* padding: 15px 15px 15px 15px;*/
    justify-content: space-between;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border-radius: 0 0 0 10px;
}

.main-content .menu-actions .title{
    font-size: 16px;
    font-weight: 600;
    line-height: 21px;
}

.main-content .menu-actions .text{
    font-size: 14px;
    font-weight: 400;
    line-height: 12px;
}

.main-content .menu-actions .actions{
    padding: 15px 15px 15px 15px;
}

.main-content .flow{
    width: 66%;
    height: 100px;
}

.main-content .footer-menu{
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    width: 100%;
    height: 73px;
    border-radius: 0 0 0 10px;
}

.last-update-div{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.last-update-div .text{
    color: #757575;
    font-weight: 400;
    size: 12px;
    line-height: 21px;
}

.vertical-line {
    border-left: 2px solid #BDBDBD;
    height: 45px;
    margin: 0 10px 0 10px;
}

.horizontal-line {
    border: none;
    height: 1px;
    background: #9FA9B7;
    margin: 10px 0 12px 0;
}

.dashed-horizontal-line {
    border: none;
    height: 1px;
    background: #9FA9B7;
    background: repeating-linear-gradient(90deg,#9FA9B7,#9FA9B7 6px,transparent 6px,transparent 12px);
    margin: 10px 0 12px 0;
  }

.components{
    /*display: block;
    position: relative;
    top: 18vh;
    left: 78vw;*/
    display: flex;
    flex-direction: column;
    width: 300px;
    background: white;
    border-left: 2px solid #33AFB3;
    border-top: 2px solid #33AFB3;
    border-radius: 6px 0 10px 0;
    padding: 20px 15px 20px 15px;
    margin: 5px 0 0 0;
    overflow: auto;
}

.components .field {
    display: flex;
    flex-direction: column;
}

.components .flex {
    justify-content: space-between;
}

.components .flex .dropdown {
    width: 80%;
}

/*#drawflow {
    display: block;
    position: fixed;
    width: 100%;
    height: 700px;
  }*/

.dialog-content{
    display: flex;
    width: 318px;
    padding: 10px 0 10px 0;
    justify-content: center;
    flex-direction: column;
}

.dialog-content .field{
    display: flex;
    flex-direction: column;
}

.dialog-footer{
    display: flex;
    justify-content: center;
}

h5 {
    width: 100%; 
    text-align: center; 
    border-bottom: 1px solid #000; 
    line-height: 0.1em;
    margin: 10px 0 20px; 
 } 
 
 h5 span { 
     background:#fff; 
     padding:0 10px; 
 }

 .div-trash{
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
 }

  
 :host ::ng-deep .verazial-split-button{
    width: 100%; 
    height: 36px; 
    color: red; 
    border: 5px solif #33AFB3; 
    background: #FFFFFF; 
    font-family: Open Sans;
    font-weight: 100;
    margin: 4px 0 4px 0
 }

 .container-checkbox {
    font-family: sans-serif;
    font-weight: 400;
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 16px;
    color: #4B5563;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Hide the browser's default checkbox */
  .container-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  
  /* Create a custom checkbox */
  .checkmark {
    position: absolute;
    border-radius: 6px;
    border: 2px solid #d1d5db;
    top: 1px;
    left: 0;
    height: 22px;
    width: 22px;
    background-color: #FFFFFF;
  }
  
  /* On mouse-over, add a grey background color */
  .container-checkbox:hover input ~ .checkmark {
    /*background-color: #ccc;*/
    border-color: #047F94;
  }
  
  /* When the checkbox is checked, add a blue background */
  .container-checkbox input:checked ~ .checkmark {
    background-color: #059BB4;
    border: 2px solid #047F94;
    border-radius: 6px;
  }
  
  /* Create the checkmark/indicator (hidden when not checked) */
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  
  /* Show the checkmark when checked */
  .container-checkbox input:checked ~ .checkmark:after {
    display: block;
  }
  
  /* Style the checkmark/indicator */
  .container-checkbox .checkmark:after {
    left:6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 1.5px 1.5px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }

.chip-container{
    max-height: 100px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.add-option{
    width: 90%;
}

.zoom{
    display: flex;
    position: relative; 
    width: 85px;
    height: 43px;
    left: -2%;
    top: 51%;
    transform: translateY(-50%);
    color: white;
    background: #757575;
    font-size: 24px;
    padding: 10px 10px 10px 10px;
    gap: 10px;
    border-radius: 6px;
    z-index: 1;
    align-items: center;
    justify-content: center;
}

.listbox{
    width: 100%;
    margin: 10px 0 10px 0;
    padding: 10px 10px 0 10px;
    font-size: 15px;
    font-weight: 400;
    color: #6C757D;
    font-family: sans-serif;
    line-height: 17px;
    border: 1px solid #CED4DA;
    border-radius: 6px;
}

.drawflow-class{
    align-items: center;
    justify-content: center;
}

.empty-flow{
    position: relative; 
    left: 40%;
    transform: translateX(-50%);
}

.empty-flow .content{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid #616161;
    padding: 20px 20px 20px 20px;
    border-radius: 6px;
    border-style: dashed;
}

.right {
    text-align: right;
    margin-right: 1em;
}

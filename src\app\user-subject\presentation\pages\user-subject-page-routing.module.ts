import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SubjectGeneralPageComponent } from './subject-general-page/subject-general-page.component';
import { UserGeneralPageComponent } from './user-general-page/user-general-page.component';
import { UserSubjectEditPageComponent } from './user-subject-edit-page/user-subject-edit-page.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';
import { EnrollSubjectPageComponent } from './enroll-subject-page/enroll-subject-page.component';

const routes: Routes = [
  {
    path:'subject',
    children: [
      {
        path: '',
        component: SubjectGeneralPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
      {
        path: 'edit/:userSubjectType/:numId',
        component: UserSubjectEditPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
    ]
  },
  {
    path:'subjects/:roleId',
    children: [
      {
        path: '',
        component: SubjectGeneralPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
    ]
  },
  {
    path:'user',
    children: [
      {
        path: '',
        component: UserGeneralPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
      {
        path: 'edit/:userSubjectType/:numId',
        component: UserSubjectEditPageComponent,
        canActivate: [AuthGuard, NavigationGuard]
      },
    ]
  },
  {
    path: 'enroll/:step',
    component: EnrollSubjectPageComponent,
    canActivate: [AuthGuard, NavigationGuard]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserSubjectPageRoutingModule { }

import { SearchActionsRequest } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { SearchActionsRequestEntity } from "../../domain/entity/search-actions-request.entity";
import { SortOrderMapper } from "./sort-order.mapper";
import { FilterMapper } from "./filter.mapper";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";

export class SearchActionsRequestMapper extends Mapper<SearchActionsRequest, SearchActionsRequestEntity>{

    sortOrderMapper = new SortOrderMapper();
    filterMapper = new FilterMapper();

    override mapFrom(param: SearchActionsRequest): SearchActionsRequestEntity {
        return {
            startTime: new Date(param.getStartTime()?.getSeconds()!! * 1000 + Math.round(param.getStartTime()?.getNanos()!! / 1e6)),
            endTime: new Date(param.getEndTime()?.getSeconds()!! * 1000 + Math.round(param.getEndTime()?.getNanos()!! / 1e6)),
            sortOrder: this.sortOrderMapper.mapFrom(param.getSortOrder()) || undefined,
            pageSize: param.getPageSize(),
            pageNumber: param.getPageNumber(),
            filters: param.getFiltersList().map(filter => this.filterMapper.mapFrom(filter)),
        }
    }

    override mapTo(param: SearchActionsRequestEntity): SearchActionsRequest {
        let searchActionsRequest = new SearchActionsRequest();
        searchActionsRequest.setStartTime(param.startTime ? dateToTimestamp(param.startTime) : undefined);
        searchActionsRequest.setEndTime(param.endTime ? dateToTimestamp(param.endTime) : undefined);
        searchActionsRequest.setSortOrder(param.sortOrder ? this.sortOrderMapper.mapTo(param.sortOrder!) : 2);
        searchActionsRequest.setPageSize(param.pageSize!);
        searchActionsRequest.setPageNumber(param.pageNumber!);
        searchActionsRequest.setFiltersList(param.filters.map(filter => this.filterMapper.mapTo(filter)));
        return searchActionsRequest;
    }

}
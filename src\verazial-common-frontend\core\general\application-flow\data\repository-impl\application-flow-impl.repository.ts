import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { ApplicationFlowEntity } from "../../domain/entity/application-flow.entity";
import { ApplicationFlowGrpcMapper } from "../mapper/application-flow-grpc.mapper";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { ApplicationFlowRepository } from "../../domain/repository/application-flow.repository";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { Injectable } from "@angular/core";
import { ApplicationFlowRequest } from "src/verazial-common-frontend/core/generated/application-flow/application_flow_pb";
import { CoreApplicationFlowClient } from "src/verazial-common-frontend/core/generated/application-flow/Application_flowServiceClientPb";

@Injectable({
    providedIn: 'root',
})
export class ApplicationFlowRepositoryImpl extends ApplicationFlowRepository {

    applicationFlowGrpcMapper = new ApplicationFlowGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }


    override addApplicationFlow(applicationFlow: ApplicationFlowEntity): Promise<ApplicationFlowEntity> {

        let request = this.applicationFlowGrpcMapper.mapTo(applicationFlow);

        let coreApplicationFlowClient = new CoreApplicationFlowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationFlowClient.addApplicationFlow(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationFlowGrpcMapper.mapFrom(
                            response.getApplicationflowmodel()!));
                    }
                }
            });
        });
    }

    override updateApplicationFlowByAppId(applicationFlow: ApplicationFlowEntity): Promise<SuccessResponse> {

        let request = this.applicationFlowGrpcMapper.mapTo(applicationFlow);

        let success!: SuccessResponse;

        let coreApplicationFlowClient = new CoreApplicationFlowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationFlowClient.updateApplicationFlowByAppId(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override getApplicationFlowByAppId(params: { id: string }): Promise<ApplicationFlowEntity> {
        let request = new ApplicationFlowRequest();

        request.setValue(params.id)

        let coreApplicationFlowClient = new CoreApplicationFlowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationFlowClient.getApplicationByAppId(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationFlowGrpcMapper.mapFrom(
                            response.getApplicationflowmodel()!));
                    }
                }
            });
        });
    }

    override deleteApplicationFlowByAppId(params: { id: string }): Promise<SuccessResponse> {
        let request = new ApplicationFlowRequest();

        request.setValue(params.id);

        let coreApplicationFlowClient = new CoreApplicationFlowClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        let success!: SuccessResponse;

        return new Promise((resolve, reject) => {
            coreApplicationFlowClient.deleteApplicationFlowByAppId(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }
}
import { NgModule } from "@angular/core";
import { ApplicationsListComponent } from "./applications-list/applications-list.component";
import { CommonModule } from "@angular/common";
import { TableModule } from "primeng/table";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { DropdownModule } from "primeng/dropdown";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { RippleModule } from "primeng/ripple";
import { ApplicationEditModule } from "../application-edit/application-edit.module";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { CalendarModule } from "primeng/calendar";
import { DialogModule } from "primeng/dialog";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";

@NgModule({
    declarations: [
        ApplicationsListComponent,
    ],
    imports: [
        /* Angular */
        CommonModule,
        /* Forms */
        FormsModule,
        ReactiveFormsModule,
        /* Translate */
        TranslateModule,
        /* PrimeNG */
        TableModule,
        DropdownModule,
        InputTextModule,
        ButtonModule,
        RippleModule,
        IconFieldModule,
        InputIconModule,
        CalendarModule,
        DialogModule,
        /* Custom */
        ApplicationEditModule,
        EmptyModule,
    ],
    exports: [
        ApplicationsListComponent
    ]
})
export class ApplicationsListModule { }
import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { FilterService } from "primeng/api";
import { Table } from "primeng/table";
import { AppRegistryEntity } from "src/verazial-common-frontend/core/general/app-registry/domain/entities/app-registry.entity";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";

@Component({
    selector: 'app-applications-list',
    templateUrl: './applications-list.component.html',
    styleUrls: ['./applications-list.component.css'],
})
export class ApplicationsListComponent implements OnInit, OnDestroy {
    /* Properties */
    // Inputs
    @Input() canReadAndWrite: boolean = false;
    @Input() listAppRegistries: AppRegistryEntity[] = [];
    // Outputs
    @Output() onAdd = new EventEmitter<AppRegistryEntity>();
    @Output() onEdit = new EventEmitter<AppRegistryEntity>();
    @Output() onDelete = new EventEmitter<AppRegistryEntity>();

    /* Data */
    showDialog: boolean = false;
    appRegistry?: AppRegistryEntity;
    appRegistryToSave?: AppRegistryEntity;
    editMode: boolean = false;
    isSaveDisabled: boolean = true;

    /* Options */
    typeOptions: GenericKeyValue[] = [
        {key: 'DESKTOP', value: 'DESKTOP'},
        {key: 'WEB', value: 'WEB'}
    ];

    /* Filters */
    // Global Search Filter
    searchValue?: string;
    // Date Range Filter
    formGroupDate: FormGroup = new FormGroup({
      date: new FormControl<Date[] | null>(null)
    });
    formGroupDate2: FormGroup = new FormGroup({
      date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
      startDate: null,
      endDate: null
    };
    rangeDates: Date[] | null = null;

    constructor(
        private filterService: FilterService,
    ) {
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
          if (!filter || (!filter.startDate && !filter.endDate)) {
            return true; // If no filter, show all
          }
          const dateValue = new Date(value).getTime();
          const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
          const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
          if (startDate && endDate) {
            return dateValue >= startDate && dateValue <= endDate;
          } else if (startDate) {
            return dateValue >= startDate;
          } else if (endDate) {
            return dateValue <= endDate;
          }
          return false;
        });
    }

    ngOnInit(): void {
    }

    ngOnDestroy(): void {
    }

    createNew() {
        this.editMode = false;
        this.appRegistry = new AppRegistryEntity();
        this.showDialog = true;
    }

    onAddData(data: AppRegistryEntity) {
    }

    onEditData(data: AppRegistryEntity) {
        this.editMode = true;
        this.appRegistry = data;
        this.showDialog = true;
    }

    onDeleteData(data: AppRegistryEntity) {
        this.onDelete.emit(data);
    }

    onOutputData(data: AppRegistryEntity) {
        this.appRegistryToSave = {...data, id: this.editMode ? this.appRegistry?.id : undefined};
        this.isSaveDisabled = false;
    }

    onSubmitData() {
        if (this.appRegistryToSave) {
            if (this.editMode) {
                this.onEdit.emit(this.appRegistryToSave!);
            }
            else {
                this.onAdd.emit(this.appRegistryToSave!);
            }
            this.onCancelData();
        }
    }

    onCancelData() {
        this.showDialog = false;
        this.editMode = false;
        this.appRegistry = undefined;
        this.appRegistryToSave = undefined;
        this.isSaveDisabled = true;
    }

    onFilter(event: any) {
        if (!event.filters['createdAt'].value) {
            this.rangeDates = null;
            this.formGroupDate.reset();
        }
        if (!event.filters['updatedAt'].value) {
            this.rangeDates = null;
            this.formGroupDate2.reset();
        }
    }

    /* Date Range Filter */
    applyDateRangeFilter(dt: Table, field: string) {
        if (field === 'createdAt') {
            this.rangeDates = this.formGroupDate.get('date')?.value;
        }
        else if (field === 'updatedAt') {
            this.rangeDates = this.formGroupDate2.get('date')?.value;
        }
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }
}
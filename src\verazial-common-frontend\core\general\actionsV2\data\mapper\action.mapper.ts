import { Action, NewAction } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { ActionEntity } from "../../domain/entity/action.entity";
import { StructMapper } from "./struct.mapper";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";

export class ActionMapper extends Mapper<Action, ActionEntity>{

    structMapper = new StructMapper();

    override mapFrom(param: Action): ActionEntity {
        return {
            id: param.getId(),
            reportedAt: new Date(param.getReportedAt()?.getSeconds()!! * 1000 + Math.round(param.getReportedAt()?.getNanos()!! / 1e6)),
            createdAt: new Date(param.getCreatedAt()?.getSeconds()!! * 1000 + Math.round(param.getCreatedAt()?.getNanos()!! / 1e6)),
            data: this.structMapper.mapFrom(param.getData()!!),
        }
    }

    override mapTo(param: ActionEntity): Action {
        let newAction = new Action();
        newAction.setId(param.id!);
        newAction.setReportedAt(dateToTimestamp(param.reportedAt!));
        newAction.setCreatedAt(dateToTimestamp(param.createdAt!));
        newAction.setData(this.structMapper.mapTo(param.data!));
        return newAction;
    }

    mapFromNewAction(param: NewAction): ActionEntity {
        return {
            id: "",
            reportedAt: new Date(),
            createdAt: new Date(),
            data: this.structMapper.mapFrom(param.getData()!!),
        }

    }
}
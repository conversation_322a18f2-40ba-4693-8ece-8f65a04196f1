import { Action, NewAction } from "src/verazial-common-frontend/core/generated/actionsV2/actions_pb";
import { ActionEntity } from "../entity/action.entity";
import { CountActionsRequestEntity } from "../entity/count-actions-request.entity";
import { CountActionsResponseEntity } from "../entity/count-actions-response.entity";
import { NewActionEntity } from "../entity/new-action.entity";
import { SearchActionsRequestEntity } from "../entity/search-actions-request.entity";

export abstract class ActionsV2Repository {
    abstract registerAction(params: { newAction: NewAction, token?: string } ): Promise<ActionEntity>;
    abstract searchActions(params: SearchActionsRequestEntity ): Promise<ActionEntity[]>;
    abstract countActions(params: CountActionsRequestEntity ): Promise<CountActionsResponseEntity>;
}
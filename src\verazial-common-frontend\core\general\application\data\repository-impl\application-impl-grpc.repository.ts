import { ApplicationRepository } from "../../domain/repositories/application.repository";
import { ApplicationEntity } from "../../domain/entities/application.entity";
import { environment } from "src/environments/environment";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { ApplicationGrpcMapper } from "../mapper/application-grpc.mapper";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ListApplicationGrpcModel, ApplicationGrpcModel, UpdateAppStatusGrpcRequest, UpdateAppDataSourceGrpcRequest, ApplicationRequest } from "src/verazial-common-frontend/core/generated/application/application_pb";
import { CoreApplicationClient } from "src/verazial-common-frontend/core/generated/application/ApplicationServiceClientPb";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";

@Injectable({
    providedIn: 'root',
})
export class ApplicationRepositoryGrpcImpl extends ApplicationRepository {


    applicationGrpcMapper = new ApplicationGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }

    /**
     * Adding new applications
     * @param applications
     * @returns
     */
    override addApplications(applications: ApplicationEntity[]): Promise<ApplicationEntity[]> {
        let request = new ListApplicationGrpcModel();

        request.setApplicationmodelList(
            applications.map((app) => this.applicationGrpcMapper.mapTo(app))
        );


        let responseApplication: ApplicationEntity[] = [];

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        let grpc = coreApplicationClient.addApplication(request, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: ApplicationGrpcModel) => {
                responseApplication.push(this.applicationGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseApplication);
            });
        });
    }

    /**
     * Update application by ID
     * @param application
     * @returns
     */
    override updateApplicationById(application: ApplicationEntity): Promise<ApplicationEntity> {

        let request = this.applicationGrpcMapper.mapTo(application);

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreApplicationClient.updateApplicationById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationGrpcMapper.mapFrom(response.getApplicationmodel()!));
                    }
                }
            });
        });
    }

    /**
     * Update the application status by ID
     * @param params
     * @returns
     */
    override updateApplicationStatusById(params: { id: string; status: boolean; }): Promise<SuccessResponse> {
        let request = new UpdateAppStatusGrpcRequest();

        request.setId(params.id);
        request.setStatus(params.status);

        let success!: SuccessResponse;

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationClient.updateApplicationStatusById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }


    /**
     * Updating the data source in all applications with the same Data Source ID.
     * @param params
     * @returns
     */
    override updateAllAppsDataSource(params: { dataSourceId: string; newDataSource: string; }): Promise<SuccessResponse> {

        let request = new UpdateAppDataSourceGrpcRequest();

        request.setDatasourceid(params.dataSourceId);
        request.setNewdatasource(params.newDataSource);

        let success!: SuccessResponse;

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationClient.updateAllAppsDataSource(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Getting an application by ID
     * @param params
     * @returns
     */
    override getApplicationById(params: { id: string; }): Promise<ApplicationEntity> {

        let request = new ApplicationRequest();

        request.setValue(params.id)
        let response!: ApplicationEntity;

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreApplicationClient.getApplicationById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationGrpcMapper.mapFrom(response.getApplicationmodel()!));
                    }
                }
            });
        });
    }

    /**
     * Getting an application by the DiplayName
     * @param params
     * @returns
     */
    override getApplicationByApplicationName(params: { name: string; }): Promise<ApplicationEntity> {
        let request = new ApplicationRequest();
        request.setValue(params.name);

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreApplicationClient.getApplicationByApplicationName(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.applicationGrpcMapper.mapFrom(response.getApplicationmodel()!));
                    }
                }
            });
        });
    }
    /**
     * Getting all Applications stored in the system.
     * @returns ApplicationEntity[]
     */
    override getAllApplications(): Promise<ApplicationEntity[]> {

        let responseApplication: ApplicationEntity[] = [];

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        let grpc = coreApplicationClient.getAllApplications(new Empty, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: ApplicationGrpcModel) => {
                responseApplication.push(this.applicationGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseApplication);
            });
        });
    }

    /**
     * Get all active applications
     * @returns
     */
    override getAllActiveApplications(): Promise<ApplicationEntity[]> {

        let responseApplication: ApplicationEntity[] = [];

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        let grpc = coreApplicationClient.getAllActiveApplications(new Empty, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: ApplicationGrpcModel) => {
                responseApplication.push(this.applicationGrpcMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseApplication);
            });
        });
    }

    /**
     * This function allow us to delete applications by ID, it uses gRPC
     * @param params
     * @returns
     */
    override deleteApplicationById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new ApplicationRequest();
        request.setValue(params.id);

        let success!: SuccessResponse;

        let coreApplicationClient = new CoreApplicationClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreApplicationClient.deleteApplicationById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }
}
<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<div *ngIf="isLoading">
  <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<div *ngIf="!isLoading" class="subcontainer">
  <div class="subcontainer-list gap-3">
    <div class="flex flex-row justify-content-between flex-wrap gap-2">
      <label class="subcontainer-title">{{ "menu.locations" | translate }}</label>
    </div>
  </div>

  <p-treeTable #tt [value]="locationsTree" [columns]="cols" editMode="row" [scrollable]="true" [filterMode]="filterMode"
    (onEditComplete)="onEditComplete($event)">
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th *ngFor="let col of columns;" [ttSortableColumn]="col.field">
          {{ col.header | translate }}
        </th>
      </tr>
      <tr>
        <th *ngFor="let col of cols; let last = last">
          <input *ngIf="!last" pInputText type="text"
            (input)="tt.filter($event.target.value, col.field, 'contains')" />
        </th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-rowNode let-rowData="rowData" let-columns="columns">
      <tr [ttRow]="rowNode">
        <td *ngFor="let col of columns; let i = index; let last = last; let first = first" [ttEditableColumn]="rowData"
          [ttEditableColumnField]="'key'">
          <p-treeTableToggler [rowNode]="rowNode" *ngIf="i === 0" />
          <p-treeTableCellEditor *ngIf="rowNode.parent && col == columns[0]; else otherContent">
            <ng-template pTemplate="input">
              <input pInputText type="text" [(ngModel)]="rowData[col.field]"/>
            </ng-template>

            <ng-template pTemplate="output">
              {{ rowData[col.field] }}
            </ng-template>
          </p-treeTableCellEditor>
          <ng-template #otherContent>
            {{ rowData[col.field] }}
          </ng-template>
          <ng-container *ngIf="last">
            <p-button
              [style]="{'min-width': '120px', 'text-align': 'start', 'backgroundColor': '#818EA1', 'borderColor': '#818EA1'}"
              class="defaultButton" label="{{ 'buttons.add_location_table' | translate }}" icon="pi pi-plus"
              size="small" (click)="addSubcategories(rowNode)"></p-button>
            <p-button [style]="{'backgroundColor': '#818EA1', 'borderColor': '#818EA1'}" class="smallButton"
              icon="pi pi-plus" size="small" (click)="addSubcategories(rowNode)"></p-button>
            <p-button *ngIf="rowNode.parent" icon="pi pi-trash" rounded="true" [text]="true"
              (click)="deleteNode(rowData)" />

          </ng-container>
        </td>
      </tr>
    </ng-template>
  </p-treeTable>
</div>
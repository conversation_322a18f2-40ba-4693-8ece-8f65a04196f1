.sub-container{
    width: 99%;
    min-width: 250px;
    padding: 10px 10px 10px 10px;
}

.label-component {
    font-family: "Open Sans";
    font-weight: normal;
    font-size: 14px;
}

table {
    font-size: 14px;
}

table thead {
    position: sticky;
    font-size: 14px;
}

table.rounded-corners{
    border-spacing: 0;
    border-collapse: separate;
    border: 1px solid black;
}

.table-verazial > *{
    font-size: 14px;
}

.table-verazial td{
    border: 0.5px lightgray solid;
    font-family: 'Open Sans';
}

.table-verazial th {
    background: #95CFDC;
    /*border: 0.5px lightgray solid;*/
    font-family: 'Open Sans';
}

.table-verazial th:first-child {
  border-radius: 4px 0 0 0;
}

.table-verazial th:last-child {
  border-radius: 0 4px 0px 0;
}

.align-center{
    text-align:center;
}

.align-left{
    text-align:left;
}

.align-right{
    text-align:right;
}

.button-wrapper{
    display: flex;
    width: 100%;
    padding: 5px 0px 5px 0px;
    align-items: center;
    align-content: center;
    justify-content: center;
}

.button-verify{
    font-family: "Open Sans";
    background: #AED6F1;
    font-size: 14px;
    font-weight: bold;
    height: 30px;
    width: 100%;
    border: none;
}

.button-component{
    font-family: "Open Sans";
    background: #7DCEA0;
    font-size: 14px;
    font-weight: bold;
    height: 30px;
    width: 100%;
    border: none;
}

.accordion-content{
    display: flex;
    align-items: center;
    align-content: center;
    flex-direction: column;
    padding-left: 18px;
    width: 24em;
}

.label-input-component{
    padding-left: 10px;
    width: 100%;
}

.ng-confirm-button{
    background: #204887;
}



.form-div{
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    width: 350px;
}

.font-days{
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
}

.header-small-list{
    color: var(--text-color);
    font-weight: 700;
    line-height: 24px;
    font-size: 14px;
}

.label-form{
    color: var(--text-color);
    font-weight: 600;
    line-height: 21px;
    font-size: 12px;
}

.form-component{
    margin-bottom: 0px;
}

.dialog-container{
    display: flex;
    flex-direction: column;
    padding: 20px 0 20px 0;
}

.dialog-title{
    display: flex;
    align-items: center;
    font-weight: 600;
    size: 14px;
    line-height: 21px;
}

.footer-buttons-container{
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
}

.p-splitButton-custom{
    width: 100%;
    height: 36px;
    font-family: Open Sans;
    margin: 4px 0 4px 0;
  }

  .p-splitButton-custom .p-button{
    color: #495057!important;
    border: 1px solif #33AFB3!important;
    background: #FFFFFF!important;
  }

/* New styles*/
/* error pages styles */
.error-page-title{
    color: #204887;
    font-weight: 600;
    font-size: 18px
}

.error-page-description{
    color: #757575;
    font-weight: 400;
    font-size: 15px
}

.center-container{
    display: flex;
    align-items: center;
    align-content: center;
    width: 345px;

    background: white;
    /* margin: 0 auto;*/
    border-radius: 6px;
    justify-content: space-around;
    overflow: hidden;

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 50px 40px 30px 40px;

    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
}

.subcontainer{
    margin: 20px 20px 0 20px;;
}

.subcontainer-list{
    width: 100%;
    display: flex;
    flex-direction: column;
    /*align-items: center;*/
}

.subcontainer-header{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 15px 0 15px 0;
}

.subcontainer-title{
    color: #0AB4BA;
    font-weight: 700;
    font-size: 20px;
    line-height: 32px;
}

.center-subcontainer{
    display: flex;
    align-items: center;
    align-content: center;
    width: 345px;

    background: var(--surface-ground);
    /* margin: 0 auto;*/
    border-radius: 6px;
    justify-content: space-around;
    overflow: hidden;

    position: absolute;
    top: 50%;
    left: 57%;
    transform: translate(-50%, -50%);
    padding: 50px 40px 30px 40px;

    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
}

.hidden {
    display: none;
}

.width100 {
    width: 100%;
}

.width90 {
    width: 90%;
}

/* Dialog Styles */
.dialogHeaderLabel {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 27px;
    letter-spacing: -0.02em;
    color: #204887;
}

/* User/Subject Physical Data */
.pDataDateLabel {
    font-family: Inter;
    font-size: 12.5px;
    font-weight: 600;
    text-align: left;
}

.pDataImage {
    width: 95%;
    max-height: 195px;
    max-width: 269px;
    cursor: pointer;
}

.carouselImage {
    max-height: 195px;
    min-height: 195px;
    max-width: 269px;
    min-width: 269px;
    cursor: pointer;
}

.pDataImageDialog {
    width: 100%;
    max-height: 195px;
    min-height: 195px;
}

.dialogHeader {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #495057;
}

.carousel-container .prev,
.carousel-container .next {
  background: #475569 !important;
}

/* Stepper */


.stepper-header {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    color: #495057;
}

.stepper-badge {
    /* Auto layout */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px;

    width: 20px;
    height: 20px;

    background: #204887;
    border: 1px solid #E9ECEF;
    border-radius: 14px;

    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    color: #FFFFFF;
}

.stepper-badge-inactive {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px;

    width: 20px;
    height: 20px;

    background: #FFFFFF;
    border: 1px solid #9E9E9E;
    border-radius: 14px;

    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    color: #495057;
}

.p-card-body {
    height: 100%;
}

.custom-border {
    box-shadow: -25px 0px 50px -20px rgb(50, 50, 50)
}

.menu-separator {
    border: 1px dashed #BCC3CD;
}

.requiredFieldsLabel {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 10px;
    color: #495057;
}

.requiredStar {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    font-size: 10px;
    line-height: 10px;
    color: #FF3D32;
}

.tableNumSelectedRowsText {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #424242;
}

.fixed-table {
    table-layout: fixed; /* Ensures the table maintains fixed width columns */
    width: 100%;
}

.fixed-column {
    width: 200px; /* Set this width based on your requirements */
    min-width: 200px; /* Set this width based on your requirements */
}

.fixed-column-sm {
    width: 350px; /* Set this width based on your requirements */
}

.ellipsis-cell {
    overflow: hidden;            /* Hide overflowed content */
    white-space: nowrap;         /* Prevent text from wrapping to the next line */
    text-overflow: ellipsis;     /* Display ellipsis (…) when text is too long */
    max-width: 150px;            /* Set a max width to match the fixed column width */
    vertical-align: middle;      /* Align content vertically if needed */
}

.version-text {
    font-family: 'Open Sans';
    font-style: normal;
    color: #757575;
}

.imgUser {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
    height: 70px;
    border-radius: 5px;
}

.signature-container {
    border: 1px dashed #9E9E9E;
    border-radius: 6px;
    min-width: 240px;
    min-height: 165px;
    padding: 1.5rem;
}

.signature-title {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 15px;
    line-height: 21px;
    color: #757575;
}

.namesLabel {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    color: #424242;
}

.dataKey {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    color: #424242;
}

.dataValue {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 15px;
    color: #424242;
}

.signatureDateLabel {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #424242;
}

@media (max-width: 1250px) {
}

@media (max-width: 900px) {
}

@media (max-width: 790px) {
}

@media (max-height: 650px) {
}

@media (max-width: 500px) {
}
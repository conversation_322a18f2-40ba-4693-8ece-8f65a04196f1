import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AuthEntity } from "../entity/auth.entity";
import { AuthRepository } from "../repository/auth.repository";
import { AuthByLicenseRequestEntity } from "../entity/auth-by-license-request.entity";

export class AuthenticateByLicenseUseCase implements UseCaseGrpc<AuthByLicenseRequestEntity, AuthEntity> {

    constructor(private authRepository: AuthRepository) { }

    execute(requestParams: AuthByLicenseRequestEntity): Promise<AuthEntity> {
        return this.authRepository.authenticateByLicense(requestParams);
    }
}
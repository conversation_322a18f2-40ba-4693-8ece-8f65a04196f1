import { DrawFlowEntity } from "../../entity/draw-flow.entity";
import { DrawFlowRepository } from "../../repository/draw-flow.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class CreateDrawFlowUseCase implements UseCaseGrpc<{ drawFlow: DrawFlowEntity }, DrawFlowEntity> {
    constructor(private drawFlowRepository: DrawFlowRepository) { }
    execute(params: { drawFlow: DrawFlowEntity; }): Promise<DrawFlowEntity> {
        return this.drawFlowRepository.createDrawFlow(params);
    }
}
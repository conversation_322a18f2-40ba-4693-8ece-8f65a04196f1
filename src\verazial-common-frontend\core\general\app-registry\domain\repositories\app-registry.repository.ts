import { SuccessResponse } from "src/verazial-common-frontend/core/models/success-response.interface";
import { AppRegistryEntity } from "../entities/app-registry.entity";

export abstract class AppRegistryRepository {
    abstract getAppRegistries(): Promise<AppRegistryEntity[]>;
    abstract getAppRegistryById(params: { id: string }): Promise<AppRegistryEntity>;
    abstract getAppRegistryByName(params: { name: string }): Promise<AppRegistryEntity>;
    abstract addAppRegistry(params: { applications: AppRegistryEntity[]}): Promise<AppRegistryEntity[]>;
    abstract updateAppRegistry(params: { application: AppRegistryEntity}): Promise<AppRegistryEntity>;
    abstract deleteAppRegistryById(params: { id: string }): Promise<SuccessResponse>;
}
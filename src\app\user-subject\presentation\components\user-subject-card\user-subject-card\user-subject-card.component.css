.p-card-body {
    padding: 0 !important;
}

.cardContent {
    align-items: center;
}

.cartContentDivider {

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    margin-left: 20px;
    margin-right: 20px;
}

.subjectNameRoleLabel {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    color: #009BA9;
}

.subjectRoleLabel {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #6C757D;
}

.subjectIDInfoLabel {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-size: 9px;
    color: #64748B;
}

.subjectIDInfoData {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #212121;
}

.subjectIDInfoDataSmall {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: small;
    color: #212121;
}

.subjectProfileDataLabel {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #009BA9;
}

.subjectProfileDataInfo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 4px;
}

.buttons {
    flex-direction: column;
}

.buttonContainer {
    margin: 5px;
}

.defaultButton {
    display: block;
}

.smallButton {
    display: none;
}

.smallScreenCardContent {
    display: none;
}

.SS {
    margin-right: 100px;
}

@media (max-width: 1250px) {
}

@media (max-width: 900px) {
    .defaultButton {
        display: none;
    }

    .smallButton {
        display: block;
    }
}

@media (max-width: 775px) {
    .subjectProfileData, .cartContentDivider {
        display: none;
    }

    .smallScreenCardContent {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}

@media (max-width: 790px) {
}

@media (max-width: 550px) {
    .leftCardContent{
        align-items: flex-start;
    }

    .subjectNameRole {
        margin-bottom: 5px;
    }

    .subjectNameRoleLabel{
        font-size: medium;
    }

    .subjectRoleLabel {
        font-size: small;
    }

    .subjectIDInfoData {
        font-size: small;
    }
    .buttons {
        flex-direction: row;
    }

    .cardContent {
        align-items: flex-start;
    }
}

@media (max-width: 500px) {
    .buttonContainer {
        margin: 1px;
    }
}

@media (max-height: 650px){
}
import { DataSourceEntity } from "../entities/data-source.entity";
import { DataSourceRepository } from "../repositories/data-source.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class AddAppDataSourceUseCase implements UseCaseGrpc<DataSourceEntity, DataSourceEntity> {
    constructor(private dataSourceRepository: DataSourceRepository) { }
    execute(params: DataSourceEntity): Promise<DataSourceEntity> {
        return this.dataSourceRepository.addAppDataSource(params);
    }
}
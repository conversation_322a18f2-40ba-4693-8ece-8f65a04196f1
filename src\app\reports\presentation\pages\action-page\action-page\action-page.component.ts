import { DOCUMENT, formatDate } from '@angular/common';
import { AfterViewInit, Component, Inject, LOCALE_ID, NgZone, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { environment } from 'src/environments/environment';
import { ActionEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/action.entity';
import { CountActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-request.entity';
import { CountModeResult, Group } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-response.entity';
import { FilterEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/filter.entity';
import { SearchActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/search-actions-request.entity';
import { CountActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/count-actions.use-case';
import { SearchActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/search-actions.use-case';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';

@Component({
    selector: 'app-action-page',
    templateUrl: './action-page.component.html',
    styleUrls: ['./action-page.component.scss', './action-page.component.css'],
    providers: [ConfirmationService]
})
export class ActionPageComponent implements AfterViewInit, OnInit {

    selectedApp: string = "";
    appOptions: Array<any> = [];
    selectedAction: string = "";
    actionOptions: Array<any> = [];
    detailsDialogText: string = "";
    //progressValue: string = "";
    //progressEnabled: boolean = false;
    //progressInterval: any;
    //progressColor: string = "#0ab4ba";
    chart: any;
    pieChart: any;
    hChart: any;
    dChart: any;
    lChart: any;
    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "0";
    verificationComputedNumber: string = "0";
    identificationComputedNumber: string = "0";
    inComputedNumber: string = "0";
    outComputedNumber: string = "0";;
    deletionComputedNumber: string = "0";
    deletionSamplesComputedNumber: string = "0";
    newSubjectComputedNumber: string = "0";
    newPictureComputedNumber: string = "0";
    modBioComputedNumber: string = "0";
    newSampleComputedNumber: string = "0";
    passwordLoginComputedNumber: string = "0";
    actionsComputedNumber: string = "0";
    noDevComputedNumber: string = "0";

    totalTimeNumber: any = "";
    userTimeNumber: any = "";
    netTimeNumber: any = "";
    serverTimeNumber: any = "";


    showSpinners: boolean = true;
    endDate: Date = new Date(new Date().getTime() - ((environment.rangeDaysBefore - 30) * 24 * 60 * 60 * 1000));
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
        action: [],
        subject: '',
    });


    actionsData: any[] = [];
    virtualActionsData: ActionEntity[] = [];


    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;
    showNoData5: boolean = false;

    showNoData6: boolean = false;
    showNoData7: boolean = false;
    showNoData8: boolean = false;
    showNoData9: boolean = false;
    showNoData10: boolean = false;

    showNoData11: boolean = false;
    showNoData12: boolean = false;
    showNoData13: boolean = false;
    showNoData14: boolean = false;
    showNoData15: boolean = false;

    addingData: boolean = true;


    showDetailsDialog: boolean = false;
    detailsDialogAction: ActionEntity = new ActionEntity();

    constructor(
        @Inject(LOCALE_ID) private locale: string,
        //private getActionByUseCase: getActionsByUseCase,
        private translate: TranslateService,
        private fb: FormBuilder,
        @Inject(DOCUMENT) private document: Document,
        private reportsService: ReportsService,
        private countActionsUseCase: CountActionsUseCase,
        private searchActionsUseCase: SearchActionsUseCase,
        private translateService: TranslateService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
    ) { }

    ngOnInit(): void {

        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);
        this.appOptions.push({ "name": "menu.all" });
        this.actionOptions.push({ "name": "menu.all" });

        this.selectedApp = environment.applicationDefault;
        this.selectedAction = environment.actionDefault;
    }

    ngAfterViewInit(): void {
        this.getAllActions();
    }

    ngOnDestroy() {
        /*if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }*/
    }

    async getActionNameOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;

        paramCount.groupByAttributePath.push("actionName");

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            this.loggerService.debug(actionFilterCount);
            paramCount.filters.push(actionFilterCount);
        }

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.actionOptions = [];
                this.actionOptions.push({ "name": "menu.all" });
                countActions.groupByResults.forEach((action: Group) => {
                    this.actionOptions.push({ "name": action.groupValue });
                });
            }
        );
    }

    async getActionApplicationOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;
        paramCount.groupByAttributePath.push("applicationId");

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            this.loggerService.debug(actionFilterCount);
            paramCount.filters.push(actionFilterCount);
        }

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.appOptions = [];
                this.appOptions.push({ "name": "menu.all" });
                countActions.groupByResults.forEach((action: Group) => {
                    this.appOptions.push({ "name": action.groupValue });
                });
            }
        );
    }

    async getListActions(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        let paramSearch: SearchActionsRequestEntity = new SearchActionsRequestEntity();
        paramSearch.startTime = dateStartDate;
        paramSearch.endTime = dateEndDate;
        paramSearch.pageNumber = 0;
        paramSearch.pageSize = environment.maxTable;

        if (this.selectedApp != "menu.all") {
            const appFilterCount: FilterEntity = {
                condition: {
                    path: "applicationId",
                    value: this.selectedApp,
                },
            }
            paramSearch.filters.push(appFilterCount);
        }

        if (this.selectedAction != "menu.all") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "actionName",
                    value: this.selectedAction,
                },
            }
            paramSearch.filters.push(actionFilterCount);
        }

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            this.loggerService.debug(actionFilterCount);
            paramSearch.filters.push(actionFilterCount);
        }


        return this.searchActionsUseCase.execute(paramSearch).then(
            (searchActions) => {

                this.loggerService.debug("Search actions: ");
                this.loggerService.debug(searchActions);

                this.virtualActionsData = searchActions;

            }
        );

    }

    private setTimeNumbers(extraCountResults: CountModeResult[]): void {
        if (extraCountResults.length === 4) {
            this.totalTimeNumber = (extraCountResults[0].mode!.value / 1000).toFixed(2);
            this.userTimeNumber = (extraCountResults[1].mode!.value / 1000).toFixed(2);
            this.netTimeNumber = (extraCountResults[2].mode!.value / 1000).toFixed(2);
            this.serverTimeNumber = (extraCountResults[3].mode!.value / 1000).toFixed(2);
        }
    }


    async getActionsTotals(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;

        if (this.selectedApp != "menu.all") {
            const appFilterCount: FilterEntity = {
                condition: {
                    path: "applicationId",
                    value: this.selectedApp,
                },
            }
            paramCount.filters.push(appFilterCount);
        }

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            this.loggerService.debug(actionFilterCount);
            paramCount.filters.push(actionFilterCount);
        }


        paramCount.groupByAttributePath.push("actionName");

        paramCount.extraCountModes.push({ mode: { attributePath: "timmingAttributes.totalTime" } });
        paramCount.extraCountModes.push({ mode: { attributePath: "timmingAttributes.userTime" } });
        paramCount.extraCountModes.push({ mode: { attributePath: "timmingAttributes.serverTimeCS" } });
        paramCount.extraCountModes.push({ mode: { attributePath: "timmingAttributes.serverTime" } });

        this.loggerService.debug("Counting actions: ");
        this.loggerService.debug(paramCount);
        var rem: number = 0;

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {

                this.loggerService.debug(countActions);

                if (countActions.extraCountResults.length == 4 && this.selectedAction == "menu.all") {
                    this.totalTimeNumber = (countActions.extraCountResults[0].mode!.value / 1000).toFixed(2);
                    this.userTimeNumber = (countActions.extraCountResults[1].mode!.value / 1000).toFixed(2);
                    this.netTimeNumber = (countActions.extraCountResults[2].mode!.value / 1000).toFixed(2);
                    this.serverTimeNumber = (countActions.extraCountResults[3].mode!.value / 1000).toFixed(2);
                }

                countActions.groupByResults.forEach((element: Group) => {

                    if (element.groupValue == "MCH_IDN") {
                        this.identificationComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "MCH_IDN") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }

                    if (element.groupValue == "MCH_VRF") {
                        this.verificationComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "MCH_VRF") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }

                    if (element.groupValue == "REM_SUB_BT" || element.groupValue == "REM_SUB_SAM") {
                        rem += element.groupTotal;
                        if (element.groupValue == "REM_SUB_BT" && this.selectedAction == "REM_SUB_BT" && element.groupExtraCountResults.length == 4) {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                        else if (element.groupValue == "REM_SUB_SAM" && this.selectedAction == "REM_SUB_SAM" && element.groupExtraCountResults.length == 4) {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }
                    if (element.groupValue == "REM_SAM") {
                        this.deletionSamplesComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "REM_SAM") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }

                    }
                    if (element.groupValue == "NEW_SAM") {
                        this.newSampleComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "NEW_SAM") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }
                    if (element.groupValue == "NEW_SUB") {
                        this.newSubjectComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "NEW_SUB") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }
                    if (element.groupValue == "ID_PASS") {
                        this.passwordLoginComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "ID_PASS") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }
                    if (element.groupValue == "ADD_PIC") {
                        this.newPictureComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "ADD_PIC") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }
                    if (element.groupValue == "NO_DEV") {
                        this.noDevComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "NO_DEV") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }
                    if (element.groupValue == "MOD_BIO") {
                        this.modBioComputedNumber = element.groupTotal.toString();
                        if (element.groupExtraCountResults.length == 4 && this.selectedAction == "MOD_BIO") {
                            this.setTimeNumbers(element.groupExtraCountResults);
                        }
                    }


                });
                this.deletionComputedNumber = rem.toString();
            },
            (e) => {
                this.loggerService.error("Error count actions: ");
                this.loggerService.error(e);
            }
        );
    }



    async getAllActions() {

        this.addingData = true;

        //const initDate = new Date()
        this.enableSpinners();
        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);

        this.loggerService.debug("Date range: ");
        this.loggerService.debug(dateStartDate);
        this.loggerService.debug(dateEndDate);
        this.dateError = false;
        this.dateErrorMessage = "";

        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            this.addingData = false;
            return;
        }


        if (dateStartDate < dateEndDate) {

            //this.progressEnabled = true;
            //this.progressValue = "5";

            this.totalTimeNumber = "";
            this.serverTimeNumber = "";
            this.netTimeNumber = "";
            this.userTimeNumber = "";
            this.virtualActionsData = [];

            try {

                await Promise.all([
                    this.getActionApplicationOptions(dateStartDate, dateEndDate),
                    this.getActionNameOptions(dateStartDate, dateEndDate),
                    this.getActionsTotals(dateStartDate, dateEndDate),
                    this.getListActions(dateStartDate, dateEndDate)
                ]);

                //this.progressValue = "100";
                //this.progressEnabled = false;

                //setTimeout(() => {
                //this.progressValue = "0";

                this.hideSpinners();
                //}, 2000);
            } catch (error) {
                this.loggerService.error("Error getting actions:");
                this.loggerService.error(error!);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translate.instant("titles.error_operation"),
                    detail: this.translate.instant("messages.error_getting_actions"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.hideSpinners();
            }
        }
        else {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            this.hideSpinners();
            /*setTimeout(() => {
                this.progressEnabled = false;
                this.progressValue = "0";
            }, 2000);*/
        }
    }



    enableSpinners() {

        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = this.showNoData5 = false;
        this.showNoData6 = this.showNoData7 = this.showNoData8 = this.showNoData9 = this.showNoData10 = false;
        this.showNoData11 = this.showNoData12 = this.showNoData13 = this.showNoData14 = this.showNoData15 = false;

        this.verificationComputedNumber = "0";
        this.identificationComputedNumber = "0";
        this.deletionComputedNumber = "0";
        this.deletionSamplesComputedNumber = "0";
        this.newSubjectComputedNumber = "0";
        this.newSampleComputedNumber = "0";
        this.passwordLoginComputedNumber = "0";
        this.noDevComputedNumber = "0";
        this.actionsComputedNumber = "0";
        this.newPictureComputedNumber = "0";
        this.modBioComputedNumber = "0";

        this.actionsData = [];
        this.virtualActionsData = [];
        this.showSpinners = true;

    }

    hideSpinners() {

        this.addingData = false;

        this.showSpinners = false;

        this.actionsData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
        this.actionsData.length == 0 ? this.showNoData2 = true : this.showNoData2 = false;
        this.actionsData.length == 0 ? this.showNoData3 = true : this.showNoData3 = false;
        this.actionsData.length == 0 ? this.showNoData4 = true : this.showNoData4 = false;
        this.actionsData.length == 0 ? this.showNoData5 = true : this.showNoData5 = false;

        this.actionsData.length == 0 ? this.showNoData6 = true : this.showNoData6 = false;
        this.actionsData.length == 0 ? this.showNoData7 = true : this.showNoData7 = false;
        this.actionsData.length == 0 ? this.showNoData8 = true : this.showNoData8 = false;
        this.actionsData.length == 0 ? this.showNoData9 = true : this.showNoData9 = false;
        this.actionsData.length == 0 ? this.showNoData10 = true : this.showNoData10 = false;

        this.actionsData.length == 0 ? this.showNoData11 = true : this.showNoData11 = false;
        this.actionsData.length == 0 ? this.showNoData12 = true : this.showNoData12 = false;
        this.actionsData.length == 0 ? this.showNoData13 = true : this.showNoData13 = false;
        this.actionsData.length == 0 ? this.showNoData14 = true : this.showNoData14 = false;
        this.actionsData.length == 0 ? this.showNoData15 = true : this.showNoData15 = false;

        this.verificationComputedNumber == "0" ? this.showNoData1 = true : this.showNoData1 = false;
        this.identificationComputedNumber == "0" ? this.showNoData2 = true : this.showNoData2 = false;
        this.newSubjectComputedNumber == "0" ? this.showNoData3 = true : this.showNoData3 = false;
        this.deletionComputedNumber == "0" ? this.showNoData4 = true : this.showNoData4 = false;
        this.modBioComputedNumber == "0" ? this.showNoData5 = true : this.showNoData5 = false;
        this.newSampleComputedNumber == "0" ? this.showNoData6 = true : this.showNoData6 = false;
        this.newPictureComputedNumber == "0" ? this.showNoData7 = true : this.showNoData7 = false;
        this.deletionSamplesComputedNumber == "0" ? this.showNoData8 = true : this.showNoData8 = false;
        this.passwordLoginComputedNumber == "0" ? this.showNoData9 = true : this.showNoData9 = false;
        this.noDevComputedNumber == "0" ? this.showNoData10 = true : this.showNoData10 = false;

        this.virtualActionsData.length == 0 ? this.showNoData11 = true : this.showNoData11 = false;
        this.totalTimeNumber == "" ? this.showNoData12 = true : this.showNoData12 = false;
        this.userTimeNumber == "" ? this.showNoData13 = true : this.showNoData13 = false;
        this.netTimeNumber == "" ? this.showNoData14 = true : this.showNoData14 = false;
        this.serverTimeNumber == "" ? this.showNoData15 = true : this.showNoData15 = false;

    }

    showDetails(action: ActionEntity) {
        this.detailsDialogAction = action;
        this.detailsDialogText = JSON.stringify(action.data, (key, value) => {
            return value === null ? undefined : value;
        }, 2);
        this.loggerService.debug(this.detailsDialogText);
        this.showDetailsDialog = true;
    }


}

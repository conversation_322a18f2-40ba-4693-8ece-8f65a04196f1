import { DataSourceParametersEntity } from "../entities/data-source-parameters.entity";
import { DataSourceParametersRepository } from "../repositories/data-source-parameters.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export class UpdateAppDataSourceParamByIdUseCase implements UseCaseGrpc<DataSourceParametersEntity, SuccessResponse> {
    constructor(private dataSourceParamsRepository: DataSourceParametersRepository) { }
    execute(params: DataSourceParametersEntity): Promise<SuccessResponse> {
        return this.dataSourceParamsRepository.updateAppDataSourceParamById(params);
    }
}
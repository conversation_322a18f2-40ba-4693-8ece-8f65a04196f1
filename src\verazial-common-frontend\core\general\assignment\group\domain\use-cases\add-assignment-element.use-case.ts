import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AssignmentElementEntity } from "../entity/assignment-elements.entity";
import { AssignmentRespository } from "../repository/assignment.repository";

export class AddAssignmentElementUseCase implements UseCaseGrpc<{ elements: AssignmentElementEntity[] }, AssignmentElementEntity[]> {
    constructor(private repository: AssignmentRespository) { }
    execute(params: { elements: AssignmentElementEntity[]; }): Promise<AssignmentElementEntity[]> {
        return this.repository.addAssignmentElement(params)
    }
}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EditApplicationFlowComponent } from './edit-application-flow/edit-application-flow.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { ChipsModule } from 'primeng/chips';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ContextMenuModule } from 'primeng/contextmenu';
import { DialogModule } from 'primeng/dialog';
import { DragDropModule } from 'primeng/dragdrop';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ListboxModule } from 'primeng/listbox';
import { MessagesModule } from 'primeng/messages';
import { SkeletonModule } from 'primeng/skeleton';
import { SplitButtonModule } from 'primeng/splitbutton';
import { ToastModule } from 'primeng/toast';



@NgModule({
  declarations: [
    EditApplicationFlowComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    ButtonModule,
    InputTextModule,
    DragDropModule,
    DialogModule,
    InputTextareaModule,
    AccordionModule,
    DropdownModule,
    CheckboxModule,
    ChipsModule,
    ConfirmDialogModule,
    ContextMenuModule,
    SplitButtonModule,
    ListboxModule,
    SkeletonModule,
    InputSwitchModule,
    MessagesModule,
    ToastModule,
    InputNumberModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule
  ],
  exports:[
    EditApplicationFlowComponent
  ]
})
export class EditApplicationFlowModule { }

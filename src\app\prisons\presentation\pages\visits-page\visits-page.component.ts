import { Component, OnInit } from "@angular/core";
import { ConfirmationService, MessageService } from "primeng/api";
import { CheckTokenUseCase } from "src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
  selector: 'app-prisons-visits-page',
  templateUrl: './visits-page.component.html',
  styleUrl: './visits-page.component.css',
  providers: [MessageService]
})
export class PrisonsVisitsPageComponent implements OnInit {
  isLoading: boolean = false;
  /* User in Session */
  userIsVerified: boolean = false;

  constructor(
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private localStorageService: LocalStorageService,
    private checkTokenUseCase: CheckTokenUseCase,
  ) { }

  ngOnInit() {
    this.checkUserVerified();
  }

  async userVerifying(mod: boolean) {
    this.updateModified(mod);
    await this.checkUserVerified();
  }

  async checkUserVerified() {
    if (this.localStorageService.isUserVerified()) {
      await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
        (response) => {
          this.userIsVerified = !response;
        },
        (e) => {
          this.userIsVerified = false;
        }
      );
    }
    else {
      this.userIsVerified = false;
    }
  }

  updateModified(modified: boolean) {
    this.localStorageService.setLockMenu(modified);
  }
}
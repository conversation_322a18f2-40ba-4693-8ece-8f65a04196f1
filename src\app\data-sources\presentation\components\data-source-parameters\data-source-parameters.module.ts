import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataSourceParametersComponent } from './data-source-parameters/data-source-parameters.component';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';



@NgModule({
  declarations: [
    DataSourceParametersComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    InputTextModule,
    ButtonModule,
    DropdownModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
  ],
  exports: [
    DataSourceParametersComponent
  ]
})
export class DataSourceParametersModule { }

import { AssignmentEntity } from "../entity/assignment.entity";
import { AssignmentElementEntity } from "../entity/assignment-elements.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { AssignmentSearchParametersEntity } from "../entity/search-parameters.entity";
import { AssignmentResponseEntity } from "../entity/assignment-response.entity";

export abstract class AssignmentRespository {
    abstract createAssignment(params: { assignment: AssignmentEntity }): Promise<AssignmentEntity>;
    abstract getAllAssignments(): Promise<AssignmentEntity[]>;
    abstract getAssignmentById(params: { id: string }): Promise<AssignmentEntity>;
    abstract deleteAssignmentById(params: { id: string }): Promise<SuccessResponse>;
    abstract updateAssignment(params: { assignment: AssignmentEntity }): Promise<AssignmentEntity>;
    abstract addAssignmentElement(params: { elements: AssignmentElementEntity[] }): Promise<AssignmentElementEntity[]>;
    abstract deleteAssignmentElementById(params: { elements: AssignmentElementEntity[] }): Promise<SuccessResponse>;
    abstract searchAssignmentBy(params: AssignmentSearchParametersEntity): Promise<AssignmentResponseEntity[]>;
}
import { ApplicationEntity } from "../entities/application.entity";
import { ApplicationRepository } from "../repositories/application.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetAllApplicationsUseCase implements UseCaseGrpc<void, ApplicationEntity[]> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: void): Promise<ApplicationEntity[]> {
        return this.applicationRepository.getAllApplications();
    }
}
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { WindowParametersRepository } from "../domain/repositories/window-parameters.repository";
import { AddWindowParamUseCase } from "../domain/use-cases/add-window-param.use-case";
import { DeleteWindowParamByIdUseCase } from "../domain/use-cases/delete-window-param-by-id.use-case";
import { DeleteWindowParamsByWindowIdUseCase } from "../domain/use-cases/delete-window-params-by-window-id.use-case";
import { GetWindowParamByIdUseCase } from "../domain/use-cases/get-window-param-by-id.use-case";
import { GetWindowParamsByWindowIdUseCase } from "../domain/use-cases/get-window-params-by-window-id.use-case";
import { UpdateWindowParamByIdUseCase } from "../domain/use-cases/update-window-param-by-id.use-case";
import { WindowParametersRepositoryGrpcImpl } from "./repository-impl/window-parameters-impl-grpc.repository";
import { GetWindowParamsByAppIdUseCase } from "../domain/use-cases/get-window-params-by-app-id.use-case";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";

const addWindowParamUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new AddWindowParamUseCase(windowParametersRepository);

const updateWindowParamByIdUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new UpdateWindowParamByIdUseCase(windowParametersRepository);

const getWindowParamByIdUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new GetWindowParamByIdUseCase(windowParametersRepository);

const getWindowParamsByWindowIdUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new GetWindowParamsByWindowIdUseCase(windowParametersRepository);

const getWindowParamsByAppIdUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new GetWindowParamsByAppIdUseCase(windowParametersRepository);

const deleteWindowParamByIdUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new DeleteWindowParamByIdUseCase(windowParametersRepository);

const deleteWindowParamsByWindowIdUseCaseFactory =
    (windowParametersRepository: WindowParametersRepository) => new DeleteWindowParamsByWindowIdUseCase(windowParametersRepository);

export const addWindowParamUseCaseProvider = {
    provide: AddWindowParamUseCase,
    useFactory: addWindowParamUseCaseFactory,
    deps: [WindowParametersRepository]
}

export const updateWindowParamByIdUseCaseProvider = {
    provide: UpdateWindowParamByIdUseCase,
    useFactory: updateWindowParamByIdUseCaseFactory,
    deps: [WindowParametersRepository]
}

export const getWindowParamByIdUseCaseProvider = {
    provide: GetWindowParamByIdUseCase,
    useFactory: getWindowParamByIdUseCaseFactory,
    deps: [WindowParametersRepository]
}

export const getWindowParamsByWindowIdUseCaseProvider = {
    provide: GetWindowParamsByWindowIdUseCase,
    useFactory: getWindowParamsByWindowIdUseCaseFactory,
    deps: [WindowParametersRepository]
}

export const getWindowParamsByAppIdUseCaseProvider = {
    provide: GetWindowParamsByAppIdUseCase,
    useFactory: getWindowParamsByAppIdUseCaseFactory,
    deps: [WindowParametersRepository]
}

export const deleteWindowParamByIdUseCaseProvider = {
    provide: DeleteWindowParamByIdUseCase,
    useFactory: deleteWindowParamByIdUseCaseFactory,
    deps: [WindowParametersRepository]
}

export const deleteWindowParamsByWindowIdUseCaseProvider = {
    provide: DeleteWindowParamsByWindowIdUseCase,
    useFactory: deleteWindowParamsByWindowIdUseCaseFactory,
    deps: [WindowParametersRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        addWindowParamUseCaseProvider,
        updateWindowParamByIdUseCaseProvider,
        getWindowParamByIdUseCaseProvider,
        getWindowParamsByWindowIdUseCaseProvider,
        getWindowParamsByAppIdUseCaseProvider,
        deleteWindowParamByIdUseCaseProvider,
        deleteWindowParamsByWindowIdUseCaseProvider,
        { provide: WindowParametersRepository, useClass: WindowParametersRepositoryGrpcImpl },
        provideHttpClient(withInterceptorsFromDi())
    ]
})
export class DiWindowParametersModule { }
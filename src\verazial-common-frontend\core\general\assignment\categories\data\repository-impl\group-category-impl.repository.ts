import { Injectable } from "@angular/core";
import { GroupCategoryRepository } from "../../domain/repository/group-category.repository";
import { GroupCategoryMapper } from "../mapper/group-category.mapper";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { CategoryLocationEntity } from "../../domain/entity/category-location.entity";
import { CategoryScheduleEntity } from "../../domain/entity/category-schedule.entity";
import { CategorySubjectEntity } from "../../domain/entity/category-subject.entity";
import { DayTimeScheduleEntity } from "../../domain/entity/day-time-schedule.entity";
import { GroupCategoryEntity } from "../../domain/entity/group-category.entity";
import { CategoryLocationMapper } from "../mapper/category-location.mapper";
import { CategorySubjectMapper } from "../mapper/category-subject.mapper";
import { CategoryScheduleMapper } from "../mapper/category-schedule.mapper";
import { CoreGroupCategoryServiceClient } from "src/verazial-common-frontend/core/generated/category/Group_categoryServiceClientPb";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { CategoryGrpModel, CategoryLocationGrpc, CategoryScheduleGrpc, CategorySubjectGrpc, DayTimeScheduleGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { StringParam } from "src/verazial-common-frontend/core/generated/util_pb";
import { toArrayOfDayTime, toArrayOfLocations, toArrayOfSubjects } from "../../common/converter/category.converter";
import { DayTimeScheduleMapper } from "../mapper/day-time-schedule.mapper";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { HttpClient } from "@angular/common/http";

@Injectable({
    providedIn: 'root',
})
export class GroupCategoryRepositoryImpl extends GroupCategoryRepository {

    groupCategoryMapper = new GroupCategoryMapper();
    groupLocationMapper = new CategoryLocationMapper();
    groupSubjectMapper = new CategorySubjectMapper();
    groupScheduleMapper = new CategoryScheduleMapper();
    scheduleDayTimeMapper = new DayTimeScheduleMapper();

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    override createGroupCategory(params: { group: GroupCategoryEntity; }): Promise<GroupCategoryEntity> {
        let request = this.groupCategoryMapper.mapTo(params.group);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.createGroupCategory(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.groupCategoryMapper.mapFrom(response.getGroupcategory()!));
                    }
                }
            });
        });
    }

    override getAllGroupsCategories(): Promise<GroupCategoryEntity[]> {
        let groupCategoryResponse: GroupCategoryEntity[] = [];

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreGroupCategoryServiceClient.getAllGroupsCategories(new Empty);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CategoryGrpModel) => {
                groupCategoryResponse.push(this.groupCategoryMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(groupCategoryResponse);
            });
        });
    }

    override getGroupCategoryById(params: { id: string; }): Promise<GroupCategoryEntity> {
        let request = new StringParam();
        request.setParameter(params.id);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.getGroupCategoryById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.groupCategoryMapper.mapFrom(response.getGroupcategory()!));
                    }
                }
            });
        });
    }

    override updateGroupCategoryById(params: { group: GroupCategoryEntity; }): Promise<GroupCategoryEntity> {
        let request = this.groupCategoryMapper.mapTo(params.group);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.updateGroupCategoryById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.groupCategoryMapper.mapFrom(response.getGroupcategory()!));
                    }
                }
            });
        });
    }
    override deleteGroupCategoryById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new StringParam();
        request.setParameter(params.id);

        let success!: SuccessResponse;

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.deleteGroupCategoryById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override addCategorySubject(params: { listOfSubjects: CategorySubjectEntity[]; }): Promise<CategorySubjectEntity[]> {
        let categorySubjectResponse: CategorySubjectEntity[] = [];

        let request = toArrayOfSubjects(params.listOfSubjects);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreGroupCategoryServiceClient.addCategorySubject(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CategorySubjectGrpc) => {
                categorySubjectResponse.push(this.groupSubjectMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(categorySubjectResponse);
            });
        });
    }

    override deleteCategorySubject(params: { listOfSubjects: CategorySubjectEntity[]; }): Promise<SuccessResponse> {
        let request = toArrayOfSubjects(params.listOfSubjects);

        let success!: SuccessResponse;

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.deleteCategorySubject(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override addCategoryLocation(params: { listOfLocations: CategoryLocationEntity[]; }): Promise<CategoryLocationEntity[]> {
        let categoryLocationResponse: CategoryLocationEntity[] = [];

        let request = toArrayOfLocations(params.listOfLocations);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreGroupCategoryServiceClient.addCategoryLocation(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CategoryLocationGrpc) => {
                categoryLocationResponse.push(this.groupLocationMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(categoryLocationResponse);
            });
        });
    }

    override deleteCategoryLocation(params: { listOfLocations: CategoryLocationEntity[]; }): Promise<SuccessResponse> {
        let request = toArrayOfLocations(params.listOfLocations);

        let success!: SuccessResponse;

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.deleteCategoryLocation(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override updateCategorySchedule(params: { schedule: CategoryScheduleEntity; }): Promise<SuccessResponse> {
        let request = this.groupScheduleMapper.mapTo(params.schedule);

        let success!: SuccessResponse;

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.updateCategorySchedule(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override addDayTimeSchedule(params: { listOfDayTimeSchedule: DayTimeScheduleEntity[]; }): Promise<DayTimeScheduleEntity[]> {
        let dayTimeScheduleResponse: DayTimeScheduleEntity[] = [];

        let request = toArrayOfDayTime(params.listOfDayTimeSchedule);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreGroupCategoryServiceClient.addDayTimeSchedule(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: DayTimeScheduleGrpc) => {
                dayTimeScheduleResponse.push(this.scheduleDayTimeMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(dayTimeScheduleResponse);
            });
        });
    }

    override updateDayTimeSchedule(params: { dayTimeSchedule: DayTimeScheduleEntity; }): Promise<DayTimeScheduleEntity> {
        let request = this.scheduleDayTimeMapper.mapTo(params.dayTimeSchedule);

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.updateDayTimeSchedule(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.scheduleDayTimeMapper.mapFrom(response.getDaytime()!));
                    }
                }
            });
        });
    }

    override deleteDayTimeSchedule(params: { id: string; }): Promise<SuccessResponse> {
        let request = new StringParam();
        request.setParameter(params.id);

        let success!: SuccessResponse;

        let coreGroupCategoryServiceClient = new CoreGroupCategoryServiceClient(`${environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreGroupCategoryServiceClient.deleteDayTimeSchedule(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }


}

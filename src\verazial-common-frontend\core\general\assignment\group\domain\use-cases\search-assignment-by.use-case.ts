import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AssignmentSearchParametersEntity } from "../entity/search-parameters.entity";
import { AssignmentResponseEntity } from "../entity/assignment-response.entity";
import { AssignmentRespository } from "../repository/assignment.repository";

export class SearchAssignmentByUseCase implements UseCaseGrpc<AssignmentSearchParametersEntity, AssignmentResponseEntity[]> {

    constructor(private adminRepository: AssignmentRespository) { }

    execute(params: AssignmentSearchParametersEntity): Promise<AssignmentResponseEntity[]> {
        return this.adminRepository.searchAssignmentBy(params);
    }
}
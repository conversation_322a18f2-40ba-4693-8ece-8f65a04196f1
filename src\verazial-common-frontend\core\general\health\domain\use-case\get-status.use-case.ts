import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { HealthEntity } from "../entity/health.entity";
import { HealthRepository } from "../repository/health.repository";

export class GetStatusUseCase implements UseCaseGrpc<void, HealthEntity> {
    constructor(private healthRepository: HealthRepository) { }
    execute(params: void): Promise<HealthEntity> {
        return this.healthRepository.getStatus();
    }
}
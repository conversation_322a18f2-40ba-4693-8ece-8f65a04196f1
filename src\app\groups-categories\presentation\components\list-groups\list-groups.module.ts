import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ListGroupsComponent } from './list-groups/list-groups.component';
import { TranslateModule } from '@ngx-translate/core';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessagesModule } from 'primeng/messages';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { NewGroupModule } from '../new-group/new-group.module';
import { DropdownModule } from 'primeng/dropdown';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { TooltipModule } from 'primeng/tooltip';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';


@NgModule({
  declarations: [
    ListGroupsComponent
  ],
  imports: [
    CommonModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG */
    ButtonModule,
    TableModule,
    TagModule,
    ConfirmDialogModule,
    MessagesModule,
    ToastModule,
    DialogModule,
    NewGroupModule,
    DropdownModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    CalendarModule,
    TooltipModule,
    /* Custom */
    EmptyModule,
  ],
  exports: [ 
    ListGroupsComponent
  ]
})
export class ListGroupsModule { }

<div class="container">
    <div class="content-list-flows gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2 mb-3">
            <label class="subcontainer-title">{{ "pass_application.application_flows" | translate}}</label>
            <div class="flex flex-row gap-3">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'pass_application.new_application_flow'| translate }}"  icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNewFlow()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNewFlow()"></p-button>
                </div>
            </div>
        </div>
        <p-table
            #dt
            [value]="listApplications"
            (onFilter)="onFilter($event, dt)"
            dataKey="id"
            [showCurrentPageReport]="true"
            [loading]="loading"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['applicationName', 'technology', 'applicationType', 'dataSourceId', 'status', 'createdAt', 'updatedAt']"
            [sortField]="'applicationName'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column" pSortableColumn="appRegistryId">{{'content.appRegistry' | translate}}<p-sortIcon field="appRegistryId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="applicationName">{{'pass_assigment.application_flow_name' | translate}}<p-sortIcon field="applicationName"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="flowType">{{'pass_application.flow_type' | translate}}<p-sortIcon field="flowType"></p-sortIcon></th>
                    <!--th pSortableColumn="path">{{ 'content.full_path' | translate }}<p-sortIcon field="path"></p-sortIcon></th-->
                    <th class="fixed-column" pSortableColumn="technology">{{ 'pass_application.technology' | translate }}<p-sortIcon field="technology"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="applicationType">{{ 'pass_application.application_type' | translate }}<p-sortIcon field="applicationType"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="dataSource">{{ 'pass_application.data_source' | translate }}<p-sortIcon field="dataSource"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="status">{{ 'content.status' | translate }}<p-sortIcon field="status"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedAt">{{ 'created_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th style="width: 5%;" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter field="appRegistryId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="listAppRegistry"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionValue="id"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ getAppRegistryName(value) }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.name }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="applicationName" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="flowType" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="technology" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="applicationType" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="dataSourceId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="dataSourceOptions"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="value"
                                    optionValue="key"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ getDataSourceName(value) }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.value }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="status" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {value: true},
                                        {value: false}
                                    ]"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionValue="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        <p-tag [value]="ApplicationStatus(value)" [style]="{'background': getSeverity(value)!}"></p-tag>
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <p-tag [value]="ApplicationStatus(option.value)" [style]="{'background': getSeverity(option.value)!}"></p-tag>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="max-width: 200px;" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{getAppRegistryName(data.appRegistryId)}}" tooltipPosition="top" class="ellipsis-cell">{{getAppRegistryName(data.appRegistryId)}}</td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{data.applicationName}}" tooltipPosition="top" class="ellipsis-cell"><b>{{data.applicationName}}</b></td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{data.flowType}}" tooltipPosition="top" class="ellipsis-cell">@if(data.flowType){<p-tag [value]="data.flowType"></p-tag>}</td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{data.technology}}" tooltipPosition="top" class="ellipsis-cell">{{data.technology}}</td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{data.applicationType}}" tooltipPosition="top" class="ellipsis-cell">{{data.applicationType}}</td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{getDataSourceName(data.dataSourceId)}}" tooltipPosition="top" class="ellipsis-cell">{{getDataSourceName(data.dataSourceId)}}</td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{ApplicationStatus(data.status)}}" tooltipPosition="top" class="ellipsis-cell"><p-tag [value]="ApplicationStatus(data.status)" [style]="{'background': getSeverity(data.status)!}"></p-tag></td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{data.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{data.createdAt | date:('dateTimeFormat' | translate)}}</td>
                    <td (click)="editApplication(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{data.updatedAt | date:('dateTimeFormat' | translate)}}</td>
                    <td style="max-width: 200px;" alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editApplication(data)"></button>
                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteApplication(data)"></button>
                            <button pButton pRipple icon="pi pi-arrow-right" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="showApplicationFlow(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>

    <div [formGroup]="form">
        <p-dialog [(visible)]="showSaveApplicationDialog" [style]="{ width: '410px' }" header="{{ 'pass_application.save_application_flow' | translate }}" [modal]="true" styleClass="p-fluid">
            <ng-template pTemplate="content">
                <div class="flex flex-column" >
                    <div class="field">
                        <label for="applicationName">{{ "content.name" | translate}}</label>
                        <input type="text" pInputText formControlName="applicationName" id="applicationName" required autofocus />
                    </div>
                    <!-- AppRegistry -->
                    <div class="field">
                        <label for="appRegistryId">{{ 'pass_application.appRegistry' | translate }}</label>
                        <p-dropdown
                            appendTo="body"
                            [options]="listAppRegistry"
                            placeholder="{{'content.select' | translate}}"
                            optionLabel="value"
                            [(ngModel)]="selectedAppRegistry"
                            formControlName="appRegistryId"
                            dataKey="key"
                            id="technology"
                            >
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedAppRegistry">
                                    <div>{{ selectedAppRegistry.name }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-appRegistry pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ appRegistry.name }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <div *ngIf="!isValid('appRegistryId') && form.controls['appRegistryId'].touched">
                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>

                    <div class="field">
                        <label for="applicationPath">{{ "pass_application.full_path" | translate}}</label>
                        <input type="text" pInputText formControlName="applicationPath" id="applicationPath" required autofocus />
                    </div>
                    <!-- Flow type -->
                    <div class="field">
                        <label class="label-text" for="flowType">{{ 'pass_application.flow_type' | translate }}</label>
                        <p-dropdown
                            appendTo="body"
                            [options]="listOfApplicationFlowTypes"
                            placeholder="{{'content.select' | translate}}"
                            optionLabel="value"
                            [(ngModel)]="selectedFlowType"
                            formControlName="flowType"
                            dataKey="key"
                            id="flowType"
                            >
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedFlowType">
                                    <div>{{ selectedFlowType.value }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-flowtype pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ flowtype.value }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <div *ngIf="!isValid('flowType') && form.controls['flowType'].touched">
                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>

                    <!-- Technology -->
                    <div class="field">
                        <label for="technology">{{ 'pass_application.technology' | translate }}</label>
                        <p-dropdown
                            appendTo="body"
                            [options]="listTechnologies"
                            placeholder="{{'content.select' | translate}}"
                            optionLabel="value"
                            [(ngModel)]="selectedTechnology"
                            formControlName="technology"
                            dataKey="key"
                            id="technology"
                            >
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedTechnology">
                                    <div>{{ selectedTechnology.value }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-technology pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ technology.value }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <div *ngIf="!isValid('technology') && form.controls['technology'].touched">
                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>

                    <!-- Application type -->
                    <div class="field">
                        <label for="applicationType">{{ 'pass_application.application_type' | translate }}</label>
                        <p-dropdown
                            appendTo="body"
                            [options]="listApplicationTypes"
                            placeholder="{{'content.select' | translate}}"
                            optionLabel="value"
                            [(ngModel)]="selectedApplicationType"
                            formControlName="applicationType"
                            id="applicationType"
                            >
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedApplicationType">
                                    <div>{{ selectedApplicationType.value }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-appType pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ appType.value }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <div *ngIf="!isValid('applicationType') && form.controls['applicationType'].touched">
                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>

                    <!-- Data source -->

                    <div class="field">
                        <label for="dataSource">{{ 'pass_application.data_source' | translate }}</label>
                        <p-dropdown
                            appendTo="body"
                            [options]="dataSources"
                            placeholder="{{'content.select' | translate}}"
                            optionLabel="name"
                            [(ngModel)]="selectedDataSource"
                            formControlName="dataSource"
                            id="dataSource"
                            >
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedDataSource">
                                    <div>{{ selectedDataSource.name }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-ds pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ ds.name }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <div *ngIf="!isValid('dataSource') && form.controls['dataSource'].touched">
                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    </div>

                    <div class="field flex flex-row justify-content-between align-content-center align-items-center">
                        <label for="">{{ 'publish' | translate }}</label>
                        <p-inputSwitch id="isPublished" formControlName="isPublished"></p-inputSwitch>
                    </div>
                </div>
             </ng-template>

             <ng-template pTemplate="footer">
                <div class="dialog-footer">
                    <div class="flex justify-content-center flex-wrap gap-2">
                        <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="closeSaveApplicationDialog()"></button>
                        <p-button label="{{ 'save' | translate }}" class="p-button-text" (click)="acceptSaveApplication()"
                        [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
                        ></p-button>
                    </div>
                </div>

             </ng-template>
        </p-dialog>
    </div>
</div>

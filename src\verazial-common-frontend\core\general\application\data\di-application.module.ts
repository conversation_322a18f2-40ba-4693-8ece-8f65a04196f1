import { CommonModule } from "@angular/common";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { NgModule } from "@angular/core";
import { ApplicationRepository } from "../domain/repositories/application.repository";
import { AddApplicationsUseCase } from "../domain/use-cases/add-applications.use-case";
import { DeleteApplicationByIdUseCase } from "../domain/use-cases/delete-application-by-id.use-case";
import { GetAllApplicationsUseCase } from "../domain/use-cases/get-all-applications.use-case";
import { GetApplicationByIdUseCase } from "../domain/use-cases/get-application-by-id.use-case";
import { GetApplicationByApplicationNameUseCase } from "../domain/use-cases/get-application-by-application-name.use-case";
import { UpdateAllAppsDataSourceUseCase } from "../domain/use-cases/update-all-apps-data-source.use-case";
import { UpdateApplicationByIdUseCase } from "../domain/use-cases/update-application-by-id.use-case";
import { ApplicationRepositoryGrpcImpl } from "./repository-impl/application-impl-grpc.repository";
import { UpdateApplicationStatusByIdUseCase } from "../domain/use-cases/update-application-status-by-id.use-case";
import { GetAllActiveApplicationsUseCase } from "../domain/use-cases/get-all-active-applications.use-case";

const addApplicationsUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new AddApplicationsUseCase(applicationRepository);

const updateApplicationByIdUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new UpdateApplicationByIdUseCase(applicationRepository);

const updateAllAppsDataSourceUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new UpdateAllAppsDataSourceUseCase(applicationRepository);

const updateApplicationStatusByIdUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new UpdateApplicationStatusByIdUseCase(applicationRepository);

const getApplicationByIdUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new GetApplicationByIdUseCase(applicationRepository);

const getApplicationByApplicationNameUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new GetApplicationByApplicationNameUseCase(applicationRepository);

const getAllApplicationsUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new GetAllApplicationsUseCase(applicationRepository);

const getAllActiveApplicationsUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new GetAllActiveApplicationsUseCase(applicationRepository);

const deleteApplicationByIdUseCaseFactory =
    (applicationRepository: ApplicationRepository) => new DeleteApplicationByIdUseCase(applicationRepository);

export const addApplicationsUseCaseProvider = {
    provide: AddApplicationsUseCase,
    useFactory: addApplicationsUseCaseFactory,
    deps: [ApplicationRepository]
}

export const updateApplicationByIdUseCaseProvider = {
    provide: UpdateApplicationByIdUseCase,
    useFactory: updateApplicationByIdUseCaseFactory,
    deps: [ApplicationRepository]
}

export const updateApplicationStatusByIdUseCaseProvider = {
    provide: UpdateApplicationStatusByIdUseCase,
    useFactory: updateApplicationStatusByIdUseCaseFactory,
    deps: [ApplicationRepository]
}

export const updateAllAppsDataSourceUseCaseProvider = {
    provide: UpdateAllAppsDataSourceUseCase,
    useFactory: updateAllAppsDataSourceUseCaseFactory,
    deps: [ApplicationRepository]
}

export const getApplicationByIdUseCaseProvider = {
    provide: GetApplicationByIdUseCase,
    useFactory: getApplicationByIdUseCaseFactory,
    deps: [ApplicationRepository]
}

export const getApplicationByApplicationNameUseCaseProvider = {
    provide: GetApplicationByApplicationNameUseCase,
    useFactory: getApplicationByApplicationNameUseCaseFactory,
    deps: [ApplicationRepository]
}

export const getAllApplicationsUseCaseProvider = {
    provide: GetAllApplicationsUseCase,
    useFactory: getAllApplicationsUseCaseFactory,
    deps: [ApplicationRepository]
}

export const getAllActiveApplicationsUseCaseProvider = {
    provide: GetAllActiveApplicationsUseCase,
    useFactory: getAllActiveApplicationsUseCaseFactory,
    deps: [ApplicationRepository]
}

export const deleteApplicationByIdUseCaseProvider = {
    provide: DeleteApplicationByIdUseCase,
    useFactory: deleteApplicationByIdUseCaseFactory,
    deps: [ApplicationRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        addApplicationsUseCaseProvider,
        updateApplicationByIdUseCaseProvider,
        updateAllAppsDataSourceUseCaseProvider,
        updateApplicationStatusByIdUseCaseProvider,
        getApplicationByIdUseCaseProvider,
        getApplicationByApplicationNameUseCaseProvider,
        getAllApplicationsUseCaseProvider,
        getAllActiveApplicationsUseCaseProvider,
        deleteApplicationByIdUseCaseProvider,
        { provide: ApplicationRepository, useClass: ApplicationRepositoryGrpcImpl },
        provideHttpClient(withInterceptorsFromDi())
    ]
})
export class DiApplicationModule { }
import { Observable } from "rxjs";
import { UseCase } from "src/verazial-common-frontend/core/use-case";
import { CredentialRepository } from "../repositories/credential.repository";

export class GetCredentialsByNumIdUseCase implements UseCase<{ numId: string }, any> {
    constructor(private credentialsRepository: CredentialRepository) { }
    execute(params: { numId: string; }): Observable<any> {
        return this.credentialsRepository.getCredentialsByNumId(params);
    }

}
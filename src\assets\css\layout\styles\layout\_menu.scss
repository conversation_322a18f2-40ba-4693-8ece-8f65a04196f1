.layout-sidebar {
    position: fixed;
    width: 250px;
    height: calc(100vh - 5rem);
    z-index: 999;
    overflow-y: auto;
    user-select: none;
    top: 5rem;
    // left: 2rem;
    transition: transform $transitionDuration, left $transitionDuration;
    background-color: var(--menu-bar);
    // border-radius: $borderRadius;
    // padding: 0.5rem 1.5rem;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, .02), 0px 0px 2px rgba(0, 0, 0, .05), 0px 1px 4px rgba(0, 0, 0, .08);
}

.layout-menu-item-content{
    padding: 0 1.5rem;
}

.layout-menuitem-text{
    padding-right: 0.8rem;
}

.item-disabled {
    color: #9E9E9E !important;
    cursor: default !important;
    background: var(--menu-bar) !important;
    background-color: var(--menu-bar) !important;
}

.item-disabled:hover {
    background: var(--menu-bar) !important;
    background-color: var(--menu-bar) !important;
}

.layout-menu {
    margin: 0;
    padding: 0;
    list-style-type: none;

    .layout-root-menuitem {
        >.layout-menuitem-root-text {
            font-size: 10px;
            text-transform: uppercase;
            font-weight: 600;
            color: var(--surface-700);
            margin: 1rem 0 1rem 1.8rem;
        }

        >a {
            display: none;
        }
    }

    a {
        user-select: none;
        &.active-menuitem {
            a.layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    li.active-menuitem {
        >a {
            .layout-submenu-toggler {
                // font-size: 14px;
                transform: rotate(-180deg);
            }
        }
    }

    ul {
        margin: 0;
        padding: 0;
        list-style-type: none;

        a {
            display: flex;
            align-items: center;
            position: relative;
            outline: 0 none;
            color: var(--text-color);
            cursor: pointer;
            padding: .75rem 1rem;
            /*border-radius: $borderRadius;*/
            transition: background-color $transitionDuration, box-shadow $transitionDuration;

            .layout-menuitem-icon {
                margin-right: .5rem;
            }

            .layout-submenu-toggler {
                font-size: 75%;
                margin-left: auto;
                transition: transform $transitionDuration;
            }

            &.active-route {
                width: 100%;
                font-weight: 700;
                color: #204887;
                background: #D0E1FD;
            }

            &:hover {
                // background-color: var(--surface-hover);
                background-color: #D0E1FD
            }

            &:focus {
                /*@include focused-inset();*/
            }
        }

        ul {
            overflow: hidden;
            /*border-radius: $borderRadius;*/

            li {
                a {
                    margin-left: 1rem;
                    // padding: 0.5rem 0.8rem 0.6rem 0.6rem;
                }

                li {
                    a {
                        margin-left: 2rem;
                    }

                    li {
                        a {
                            margin-left: 2.5rem;
                        }

                        li {
                            a {
                                margin-left: 3rem;
                            }

                            li {
                                a {
                                    margin-left: 3.5rem;
                                }

                                li {
                                    a {
                                        margin-left: 4rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
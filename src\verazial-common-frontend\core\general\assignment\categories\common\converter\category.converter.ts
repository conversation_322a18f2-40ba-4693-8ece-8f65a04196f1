
import { ArrayOfDayTimeScheduleGrpc, ArrayOfLocations, ArrayOfSubjects } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { CategoryLocationEntity } from "../../domain/entity/category-location.entity";
import { CategoryLocationMapper } from "../../data/mapper/category-location.mapper";
import { CategorySubjectEntity } from "../../domain/entity/category-subject.entity";
import { CategorySubjectMapper } from "../../data/mapper/category-subject.mapper";
import { DayTimeScheduleEntity } from "../../domain/entity/day-time-schedule.entity";
import { DayTimeScheduleMapper } from "../../data/mapper/day-time-schedule.mapper";
import { DateModel } from "../models/date.model";
import { Date } from "src/verazial-common-frontend/core/generated/util_pb";

export function toDateMode(dateGrpc?: Date): DateModel | undefined {
    let dateModel = new DateModel();

    if (!dateGrpc) {
        return undefined;
    }
    dateModel.year = dateGrpc.getYear();
    dateModel.month = dateGrpc.getMonth();
    dateModel.day = dateGrpc.getDay();

    return dateModel;
}

export function toDateGrpc(dateModel?: DateModel | string): Date | undefined {
    let dateGrpc = new Date();

    if (!dateModel) {
        return undefined
    }
    let month: number;
    let day: number;
    let year: number;

    if (typeof dateModel == 'string') {
        [month, day, year] = dateModel.split("/").map(Number);
    } else {
        month = dateModel.month as number;
        day = dateModel.day as number;
        year = dateModel.year as number;
    }

    dateGrpc.setYear(year);
    dateGrpc.setMonth(month);
    dateGrpc.setDay(day);

    return dateGrpc;
}

export function toListOfCategoryLocation(arrayOfLocations?: ArrayOfLocations): CategoryLocationEntity[] {
    let locationMapper = new CategoryLocationMapper();
    let listOfCategoryLocation: CategoryLocationEntity[] = [];

    arrayOfLocations?.getLocationsList().forEach(location => {
        listOfCategoryLocation.push(locationMapper.mapFrom(location));
    })

    return listOfCategoryLocation;
}

export function toArrayOfLocations(categoryLocation?: CategoryLocationEntity[]): ArrayOfLocations {
    let locationMapper = new CategoryLocationMapper();
    let arrayOfLocations = new ArrayOfLocations();

    categoryLocation?.forEach(location => {
        arrayOfLocations.addLocations(locationMapper.mapTo(location));
    });

    return arrayOfLocations;
}

export function toListOfCategorySubjects(arrayOfSubjects?: ArrayOfSubjects): CategorySubjectEntity[] {
    let subjectMapper = new CategorySubjectMapper();
    let listOfSubjects: CategorySubjectEntity[] = [];

    arrayOfSubjects?.getSubjectsList().forEach(subject => {
        listOfSubjects.push(subjectMapper.mapFrom(subject));
    });

    return listOfSubjects;
}

export function toArrayOfSubjects(listOfSubjects?: CategorySubjectEntity[]): ArrayOfSubjects {
    let subjectMapper = new CategorySubjectMapper();
    let arrayOfSubjects = new ArrayOfSubjects();

    listOfSubjects?.forEach(subject => {
        arrayOfSubjects.addSubjects(subjectMapper.mapTo(subject));
    });

    return arrayOfSubjects;
}

export function toArrayOfDayTime(listOfDayTime?: DayTimeScheduleEntity[]): ArrayOfDayTimeScheduleGrpc {
    let dayTimeMapper = new DayTimeScheduleMapper();
    let arrayOfDayTimeSchedule = new ArrayOfDayTimeScheduleGrpc()

    listOfDayTime?.forEach(dayTime => {
        arrayOfDayTimeSchedule.addDaytimeschedule(dayTimeMapper.mapTo(dayTime));
    });

    return arrayOfDayTimeSchedule;
}

export function toListOfDayTime(arrayOfDayTimeSchedule?: ArrayOfDayTimeScheduleGrpc): DayTimeScheduleEntity[] {
    let dayTimeMapper = new DayTimeScheduleMapper();
    let listOfDayTime: DayTimeScheduleEntity[] = [];

    arrayOfDayTimeSchedule?.getDaytimescheduleList().forEach(dayTime => {
        listOfDayTime.push(dayTimeMapper.mapFrom(dayTime));
    });

    return listOfDayTime;
}
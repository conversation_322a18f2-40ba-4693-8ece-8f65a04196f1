import { AssignmentRespository } from "../repository/assignment.repository";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class DeleteAssignmentByIdUseCase implements UseCaseGrpc<{ id: string }, SuccessResponse> {
    constructor(private repository: AssignmentRespository) { }
    execute(params: { id: string; }): Promise<SuccessResponse> {
        return this.repository.deleteAssignmentById(params)
    }
}
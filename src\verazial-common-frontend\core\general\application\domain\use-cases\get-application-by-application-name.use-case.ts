import { ApplicationRepository } from "../repositories/application.repository";
import { ApplicationEntity } from "../entities/application.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetApplicationByApplicationNameUseCase implements UseCaseGrpc<{ name: string }, ApplicationEntity> {
    constructor(private applicationRepository: ApplicationRepository) { }
    execute(params: { name: string; }): Promise<ApplicationEntity> {
        return this.applicationRepository.getApplicationByApplicationName(params);
    }
}
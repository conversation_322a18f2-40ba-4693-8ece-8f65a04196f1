import { CommonModule } from "@angular/common";
import { HttpClientModule } from "@angular/common/http";
import { NgModule } from "@angular/core";
import { TaskFlowRepository } from "../domain/repository/task-flow.repository";
import { CreateTaskFlowUseCase } from "../domain/use-cases/task-flow/create-task-flow.use-case";
import { DeleteTaskFlowByIdUseCase } from "../domain/use-cases/task-flow/delete-task-flow-by-id.use-case";
import { GetAllTaskFlowsUseCase } from "../domain/use-cases/task-flow/get-all-task-flows.use-case";
import { GetTaskFlowByIdUseCase } from "../domain/use-cases/task-flow/get-task-flow-by-id.use-case";
import { UpdateTaskFlowUseCase } from "../domain/use-cases/task-flow/update-task-flow.use-case";
import { TaskFlowRepositoryImpl } from "./repository-impl/task-flow-impl.repository";
import { GetAllPublishedTaskFlowsUseCase } from "../domain/use-cases/task-flow/get-all-published-task-flows.use-case";

const createTaskFlowUseCaseFactory =
    (taskFlowRepository: TaskFlowRepository) => new CreateTaskFlowUseCase(taskFlowRepository);

const getAllTaskFlowsUseCaseFactory =
    (taskFlowRepository: TaskFlowRepository) => new GetAllTaskFlowsUseCase(taskFlowRepository);

const getAllPublishedTaskFlowsUseCaseFactory =
    (taskFlowRepository: TaskFlowRepository) => new GetAllPublishedTaskFlowsUseCase(taskFlowRepository);

const getTaskFlowByIdUseCaseFactory =
    (taskFlowRepository: TaskFlowRepository) => new GetTaskFlowByIdUseCase(taskFlowRepository);

const deleteTaskFlowByIdUseCaseFactory =
    (taskFlowRepository: TaskFlowRepository) => new DeleteTaskFlowByIdUseCase(taskFlowRepository);

const updateTaskFlowUseCaseFactory =
    (taskFlowRepository: TaskFlowRepository) => new UpdateTaskFlowUseCase(taskFlowRepository);

export const createTaskFlowUseCaseProvider = {
    provide: CreateTaskFlowUseCase,
    useFactory: createTaskFlowUseCaseFactory,
    deps: [TaskFlowRepository]
}

export const getAllTaskFlowsUseCaseProvider = {
    provide: GetAllTaskFlowsUseCase,
    useFactory: getAllTaskFlowsUseCaseFactory,
    deps: [TaskFlowRepository]
}

export const getAllPublishedTaskFlowsUseCaseProvider = {
    provide: GetAllPublishedTaskFlowsUseCase,
    useFactory: getAllPublishedTaskFlowsUseCaseFactory,
    deps: [TaskFlowRepository]
}

export const getTaskFlowByIdUseCaseProvider = {
    provide: GetTaskFlowByIdUseCase,
    useFactory: getTaskFlowByIdUseCaseFactory,
    deps: [TaskFlowRepository]
}

export const deleteTaskFlowByIdUseCaseProvider = {
    provide: DeleteTaskFlowByIdUseCase,
    useFactory: deleteTaskFlowByIdUseCaseFactory,
    deps: [TaskFlowRepository]
}

export const updateTaskFlowUseCaseProvider = {
    provide: UpdateTaskFlowUseCase,
    useFactory: updateTaskFlowUseCaseFactory,
    deps: [TaskFlowRepository]
}

@NgModule({
    providers: [
        createTaskFlowUseCaseProvider,
        getAllTaskFlowsUseCaseProvider,
        getAllPublishedTaskFlowsUseCaseProvider,
        getTaskFlowByIdUseCaseProvider,
        deleteTaskFlowByIdUseCaseProvider,
        updateTaskFlowUseCaseProvider,
        { provide: TaskFlowRepository, useClass: TaskFlowRepositoryImpl },
    ],
    imports: [
        CommonModule,
        HttpClientModule,
    ],
})
export class DiTaskFlowModule { }
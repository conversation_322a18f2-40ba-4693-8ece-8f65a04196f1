import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CategoryLocationEntity } from "../entity/category-location.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class DeleteCategoryLocationUseCase implements UseCaseGrpc<{ listOfLocations: CategoryLocationEntity[] }, SuccessResponse> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { listOfLocations: CategoryLocationEntity[]; }): Promise<SuccessResponse> {
        return this.groupCategoryRepository.deleteCategoryLocation(params);
    }
}
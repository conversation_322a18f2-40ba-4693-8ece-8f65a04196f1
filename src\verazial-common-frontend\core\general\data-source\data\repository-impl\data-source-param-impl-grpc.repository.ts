import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { DataSourceParametersEntity } from "../../domain/entities/data-source-parameters.entity";
import { DataSourceParametersRepository } from "../../domain/repositories/data-source-parameters.repository";
import { DataSourceParametersGrpcMapper } from "../mapper/data-source-parameters-grpc.mapper";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ListAppDataSourceParamsGrpcModel, AppDataSourceParamsGrpcModel, AppDataSourceParamsRequest } from "src/verazial-common-frontend/core/generated/datasource/datasource_params_pb";
import { CoreAppDataSourceParamsClient } from "src/verazial-common-frontend/core/generated/datasource/Datasource_paramsServiceClientPb";

@Injectable({
    providedIn: 'root',
})
export class DataSourceParamRepositoryGrpcImpl extends DataSourceParametersRepository {

    dataSourceParametersGrpcMapper = new DataSourceParametersGrpcMapper();

    constructor(
        private localStorage: LocalStorageService,

    ) {
        super();
    }

    /** Adding a new DataSource Parameters */
    override addAppDataSourceParams(dataSourceParameters: DataSourceParametersEntity[]): Promise<DataSourceParametersEntity[]> {
        let request = new ListAppDataSourceParamsGrpcModel();

        request.setAppdatasourceparamsmodelList(
            dataSourceParameters.map((param) => this.dataSourceParametersGrpcMapper.mapTo(param))
        );

        let responseDatasourceParams: DataSourceParametersEntity[] = [];

        let coreAppDataSourceParamsClient = new CoreAppDataSourceParamsClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        let grpc = coreAppDataSourceParamsClient.addAppDataSourceParams(request, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: AppDataSourceParamsGrpcModel) => {
                responseDatasourceParams.push(
                    this.dataSourceParametersGrpcMapper.mapFrom(response)
                );
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseDatasourceParams);
            });
        });
    }

    /** Updating DataSource Parameters */
    override updateAppDataSourceParamById(dataSourceParameters: DataSourceParametersEntity): Promise<SuccessResponse> {

        let request = this.dataSourceParametersGrpcMapper.mapTo(dataSourceParameters);

        let success!: SuccessResponse;

        let coreAppDataSourceParamsClient = new CoreAppDataSourceParamsClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreAppDataSourceParamsClient.updateAppDataSourceParamById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /** Getting the DataSource Parameter by ID */
    override getAppDataSourceParamById(params: { id: string; }): Promise<DataSourceParametersEntity> {
        let request = new AppDataSourceParamsRequest();

        request.setValue(params.id);

        let coreAppDataSourceParamsClient = new CoreAppDataSourceParamsClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` };

        return new Promise((resolve, reject) => {
            coreAppDataSourceParamsClient.getAppDataSourceParamById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.dataSourceParametersGrpcMapper.mapFrom(
                            response.getAppdatasourceparamsmodel()!
                        ));
                    }
                }
            });
        });
    }

    /** Getting all DataSource Parameters by DataSource ID */
    override getAllParamsByDataSourceId(params: { dataSourceId: string; }): Promise<DataSourceParametersEntity[]> {
        let request = new AppDataSourceParamsRequest();
        request.setValue(params.dataSourceId)

        let responseDatasourceParams: DataSourceParametersEntity[] = [];

        let coreAppDataSourceParamsClient = new CoreAppDataSourceParamsClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        let grpc = coreAppDataSourceParamsClient.getAllParamsByDataSourceId(request, metadata);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: AppDataSourceParamsGrpcModel) => {
                responseDatasourceParams.push(
                    this.dataSourceParametersGrpcMapper.mapFrom(response)
                );
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(responseDatasourceParams);
            });
        });
    }

    /** Deleting DataSource Parameters by ID */
    override deleteDataSourceParamsById(params: { id: string; }): Promise<SuccessResponse> {
        let request = new AppDataSourceParamsRequest();

        request.setValue(params.id);
        let success!: SuccessResponse;

        let coreAppDataSourceParamsClient = new CoreAppDataSourceParamsClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreAppDataSourceParamsClient.deleteDataSourceParamsById(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /** Deleting DataSource Parameters by DataSource ID */
    override deleteParamByDataSourceId(params: { dataSourceId: string; }): Promise<SuccessResponse> {
        let request = new AppDataSourceParamsRequest();

        request.setValue(params.dataSourceId);
        let success!: SuccessResponse;

        let coreAppDataSourceParamsClient = new CoreAppDataSourceParamsClient(`${environment.grpcApiGateway}`);

        let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}` }

        return new Promise((resolve, reject) => {
            coreAppDataSourceParamsClient.deleteParamByDataSourceId(request, metadata, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

}
<div class="admin-groups card mt-4">
    <p-pickList
        [source]="sourceListRoleAccess"
        [target]="targetListRoleAccess"
        sourceHeader="{{ 'content.available_access_permissions' | translate }}"
        targetHeader="{{ 'content.selected_access_permissions' | translate }}"
        [dragdrop]="true"
        [showSourceControls]="false"
        [showTargetControls]="false"
        filterBy="name,application"
        [responsive]="true"
        [sourceStyle]="{ height: '20rem', width: '27rem' }"
        [targetStyle]="{ height: '20rem', width: '27rem' }"
        (onMoveToSource)="targetTrackChanges()"
        (onMoveToTarget)="targetTrackChanges()"
        (onMoveAllToSource)="targetTrackChanges()"
        (onMoveAllToTarget)="targetTrackChanges()"
        breakpoint="1400px">
    <ng-template let-access pTemplate="item">
        <div class="flex flex-wrap p-2 align-items-center gap-3 align-content-between">
            <div class="flex-1 flex flex-column gap-2">
                <span class="font-bold">{{ access.name }}</span>
                <div class="flex align-items-center gap-2">
                    <i class="pi pi-th-large"></i>
                    <span>
                        {{ access.application }}
                    </span>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center align-content-center gap-2">
                <label for="">R & W</label>
                <p-inputSwitch (onChange)="targetTrackChanges()" [(ngModel)]="access.write"/>
            </div>
        </div>
    </ng-template>
    </p-pickList>
</div>
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AccessEntity } from "../entity/access.entity";
import { AccessRepository } from "../repository/access.repository";

export class GetAccessByIdUseCase implements UseCaseGrpc<{ id: number }, AccessEntity> {
    constructor(private accessRepository: AccessRepository) { }
    execute(params: { id: number; }): Promise<AccessEntity> {
        return this.accessRepository.getAccessById(params);
    }
}
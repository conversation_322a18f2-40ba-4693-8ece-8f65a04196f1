import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { FilterService } from 'primeng/api';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActionEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/action.entity';

@Component({
  selector: 'app-list-actions',
  templateUrl: './list-actions.component.html',
  styleUrl: './list-actions.component.css',
  providers: [MessageService]
})
export class ListActionsComponent implements OnInit {

  @Input() readAndWritePermissions: boolean = false;
  @Input() readOnly: boolean = false;
  @Input() listOfActions: ActionEntity[] = [];
  @Input() audit: boolean = false;
  @Output() details: EventEmitter<ActionEntity> = new EventEmitter<ActionEntity>();
  @Input() isAddingData: boolean = false;

  allDataSources: { key: string | undefined, value: string | undefined, data: any }[] = [];

  searchValue: string | undefined;

  tittleText: string = "";
  buttonText: string = "";
  dialogBoxText: string = "";

  filteredValues: any[] = [];

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    createdAt: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  constructor(
    private router: Router,
    private messageService: MessageService,
    private translateService: TranslateService,
    private loggerService: ConsoleLoggerService,
    private filterService: FilterService,
  ) {
    this.filterService.register('customStringArray', (value: any, filter: any): boolean => {
      if (!filter) return true; // If no filter provided, show all
      else if (typeof value === 'object' && typeof filter === 'string') {
        return value.map((role: RoleEntity) => role.name).join(', ').toLowerCase().includes(filter.toLowerCase());
      }
      return false;
    });
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  async ngOnInit() {

  }

  // Convert the list of roles to string
  listOfRolesToString(roles?: RoleEntity[]): string {
    let stringOfRoles: string = "";

    if (roles && roles?.length > 0) {
      stringOfRoles = roles?.map(role => role.name).join(', ');
    }

    return stringOfRoles
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    this.filteredValues = event.filteredValue;
    if (!event.filters['createdAt'].value) {
      this.rangeDates = null;
      this.formGroup.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    this.rangeDates = this.formGroup.get('createdAt')?.value;
    // this.loggerService.debug('rangeDates', this.rangeDates);
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
  }

  emitDetails(action: ActionEntity) {
    this.details.emit(action);
  }

  formatDate(date: Date): string {
    const lang = this.translateService.currentLang || 'en';
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hourCycle: 'h23', // Ensures 24-hour format
    };

    if (lang === 'es') {
      // Spanish format: DD-MM-YYYY HH:mm
      return date.toLocaleDateString('es-ES', options).replace(',', '');
    } else {
      // English format: MM-DD-YYYY HH:mm
      return date.toLocaleDateString('en-US', options).replace(',', '');
    }
  }
}
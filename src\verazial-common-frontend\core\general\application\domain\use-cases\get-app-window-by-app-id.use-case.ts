import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationWindowEntity } from "../entities/application-window.entity";
import { ApplicationWindowRepository } from "../repositories/application-window.repository";

export class GetAppWindowByAppIdUseCase implements UseCaseGrpc<{ appId: string }, ApplicationWindowEntity[]> {
    constructor(private applicationWindowRepository: ApplicationWindowRepository) { }
    execute(params: { appId: string; }): Promise<ApplicationWindowEntity[]> {
        return this.applicationWindowRepository.getAppWindowByAppId(params)
    }

}
import { NgModule } from "@angular/core";
import { ApplicationEditComponent } from "./application-edit/application-edit.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { DropdownModule } from "primeng/dropdown";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { RippleModule } from "primeng/ripple";
import { InputTextareaModule } from "primeng/inputtextarea";

@NgModule({
    declarations: [
        ApplicationEditComponent,
    ],
    imports: [
        /* Angular */
        CommonModule,
        /* Forms */
        FormsModule,
        ReactiveFormsModule,
        /* Translate */
        TranslateModule,
        /* PrimeNG */
        DropdownModule,
        InputTextModule,
        ButtonModule,
        RippleModule,
        InputTextareaModule,
        /* Custom */
    ],
    exports: [
        ApplicationEditComponent,
    ]
})
export class ApplicationEditModule { }
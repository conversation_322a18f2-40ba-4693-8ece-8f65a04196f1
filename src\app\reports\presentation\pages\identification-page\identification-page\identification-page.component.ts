import { DOCUMENT } from '@angular/common';
import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Chart, { ChartType } from 'chart.js/auto';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { CountActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/count-actions.use-case';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { GetAllLicensesUseCase } from 'src/verazial-common-frontend/core/general/license/domain/use-cases/get-all-licenses.use-case';
import { ActionEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/action.entity';
import { CountActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-request.entity';
import { FilterEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/filter.entity';
import { group } from '@angular/animations';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { MessageService } from 'primeng/api';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { Group } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-response.entity';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';

interface PageEvent {
    first: number;
    rows: number;
    page: number;
    pageCount: number;
}

@Component({
    selector: 'app-identification-page',
    templateUrl: './identification-page.component.html',
    styleUrl: './identification-page.component.css',
    providers: [MessageService]
})


export class IdentificationPageComponent implements OnInit {


    selectedApp: string = "";
    appOptions: Array<any> = [];
    first: number = 0;
    rows: number = 5;
    page: number = 0;
    pageCount: number = 0;
    //progressValue: string = "";
    //progressEnabled: boolean = false;
    dataAvailable: boolean = false;
    //progressColor: string = "#0ab4ba";

    chart: any;
    pieChart: any;
    hChart: any;
    dChart: any;
    lChart1N: any;
    lChart1NResult: any;
    lChart1NLocResult: any;
    lChart1NLoc: any;

    identificationsNumberTotal: number = 0;
    identificationsNumber: number[] = [];

    identificationsNumberTotalOk: number = 0;
    identificationsNumberOk: number[] = [];

    identificationsNumberFailed: number[] = [];
    identificationsNumberTotalFailed: number = 0;

    identificationsNumberTotalQ: number = 0;
    identificationsNumberQ: number[] = [];

    identificationsNumberTotalTimeout: number = 0;
    identificationsNumberTimeout: number[] = [];

    identificationsNumberTotalServerError: number = 0;
    identificationsNumberServerError: number[] = [];

    identificationsNumberTotalO: number = 0;
    identificationsNumberO: number[] = [];

    identificationsNumberLTotal: number = 0;
    identificationsNumberL: number[] = [];

    identificationsNumberLOk: number[] = [];
    identificationsNumberLFailed: number[] = [];
    identificationsNumberLQ: number[] = [];
    identificationsNumberLServerError: number[] = [];
    identificationsNumberLTimeout: number[] = [];
    identificationsNumberLO: number[] = [];

    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";
    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
        subject: '',
    });

    actionsData: ActionEntity[] = [];
    showSpinners: boolean = true;
    locations: any[] = [];

    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;

    constructor(
        private countActionsUseCase: CountActionsUseCase,
        private getAllTenantLicensesUseCase: GetAllLicensesUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private translate: TranslateService,
        private reportService: ReportsService,
        private messageService: MessageService,
        private localStorageService: LocalStorageService,
        private fb: FormBuilder,
        private loggerService: ConsoleLoggerService,
    ) { }


    onPageChange(event: PageEvent) {

        this.first = event.first;
        this.rows = event.rows;


        this.createLineChartLoc(event.first, event.first + event.rows);
        this.createLineChartLocResult(event.first, event.first + event.rows);

    }

    ngOnInit(): void {
        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);

        this.appOptions.push({ "name": "menu.all" });

        this.selectedApp = environment.applicationDefault;

        this.getConfigurationData();

    }


    getConfigurationData() {
        const initArray = (length: number) => new Array(length).fill(0);

        this.identificationsNumberTotal = 0;
        this.identificationsNumber = initArray(4);
        this.identificationsNumberOk = initArray(4);
        this.identificationsNumberFailed = initArray(4);
        this.identificationsNumberTimeout = initArray(4);
        this.identificationsNumberServerError = initArray(4);
        this.identificationsNumberO = initArray(4);

        this.locations = [];

        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                const currentTenant = data.tenantId;

                this.getAllTenantLicensesUseCase.execute({ tenantId: currentTenant }).then(
                    (data) => {
                        this.locations = Array.from(
                            new Set(
                                data.map((license) => license.location?.loc1).filter((loc1): loc1 is string => loc1 !== undefined)
                            )
                        );

                        this.locations.push(this.translate.instant("titles.unknown"));



                        this.getAllActions();
                    },
                    (error) => this.handleError(error)
                );
            },
            error: (e) => this.handleError(e)
        });
    }

    handleError(error?: any) {
        this.loggerService.error("Error get config from manager: " + error);
        this.locations = [this.translate.instant("titles.unknown")];
        setTimeout(() => this.getAllActions(), 1000);
    }

    initializeLocationArrays(length: number) {
        this.identificationsNumberLOk = new Array(length).fill(0);
        this.identificationsNumberLFailed = new Array(length).fill(0);
        this.identificationsNumberLQ = new Array(length).fill(0);
        this.identificationsNumberLServerError = new Array(length).fill(0);
        this.identificationsNumberLO = new Array(length).fill(0);
        this.identificationsNumberLTotal = 0;
        this.identificationsNumberL = new Array(length).fill(0);
    }

    addTechActionResult(actionResult: string, total: number, techIndex: number) {
        switch (actionResult) {
            case "SUCCESS":
                this.identificationsNumberTotalOk = total;
                this.identificationsNumberOk[techIndex] = total;
                break;

            case "Error NOT_IDENTIFY":
            case "Error NO_IDENTITY":
                this.identificationsNumberTotalFailed = total;
                this.identificationsNumberFailed[techIndex] = total;
                break;

            case "Error QUALITY_ERROR":
                this.identificationsNumberTotalQ = total;
                this.identificationsNumberQ[techIndex] = total;
                break;

            case "Error TIMEOUT_ERROR":
                this.identificationsNumberTotalTimeout = total;
                this.identificationsNumberTimeout[techIndex] = total;
                break;

            default:
                if (actionResult.includes("_ERROR")) {
                    this.identificationsNumberServerError[techIndex] += total;
                    this.identificationsNumberTotalServerError += total;
                }
                else {
                    this.identificationsNumberO[techIndex] += total;
                    this.identificationsNumberTotalO += total;
                }
                break;
        }
    }

    addLocActionResult(actionResult: string, total: number, techIndex: number) {

        switch (actionResult) {
            case "SUCCESS":
                this.identificationsNumberLOk[techIndex] = total;
                break;

            case "Error NOT_IDENTIFY":
            case "Error NO_IDENTITY":
                this.identificationsNumberLFailed[techIndex] = total;
                break;

            case "Error QUALITY_ERROR":
                this.identificationsNumberLQ[techIndex] = total;
                break;

            case "Error TIMEOUT_ERROR":
                this.identificationsNumberLTimeout[techIndex] = total;
                break;

            default:
                if (actionResult.includes("_ERROR")) {
                    this.identificationsNumberLServerError[techIndex] += total;
                }
                else {
                    this.identificationsNumberLO[techIndex] += total;
                }
                break;
        }
    }

    async getResultForTechnology(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;

        if (this.selectedApp != "menu.all") {
            const appFilterCount: FilterEntity = {
                condition: {
                    path: "applicationId",
                    value: this.selectedApp,
                },
            }
            paramCount.filters.push(appFilterCount);
        }

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }


        const actionFilterCount: FilterEntity = {
            condition: {
                path: "actionName",
                value: "MCH_IDN",
            },
        }
        paramCount.filters.push(actionFilterCount);
        paramCount.groupByAttributePath.push("commonAttributes.technologyId");
        paramCount.groupByAttributePath.push("commonAttributes.actionResult");

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.loggerService.debug(countActions);

                countActions.groupByResults.forEach(group => {

                    if (group.groupValue == "FINGER") {
                        this.identificationsNumber[0] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 0);
                        });
                    }
                    else if (group.groupValue == "FACE") {
                        this.identificationsNumber[1] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 1);
                        });
                    }
                    else if (group.groupValue == "IRIS") {
                        this.identificationsNumber[2] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 2);
                        });
                    }
                    else {
                        this.identificationsNumber[3] += group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 3);
                        });
                    }

                });

            }
        );
    }

    async getResultForLocation(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;
        const actionFilterCount: FilterEntity = {
            condition: {
                path: "actionName",
                value: "MCH_IDN",
            },
        }

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }


        if (this.selectedApp != "menu.all") {
            const appFilterCount: FilterEntity = {
                condition: {
                    path: "applicationId",
                    value: this.selectedApp,
                },
            }
            paramCount.filters.push(appFilterCount);
        }

        paramCount.filters.push(actionFilterCount);
        paramCount.groupByAttributePath.push("commonAttributes.locationId");
        paramCount.groupByAttributePath.push("commonAttributes.actionResult");

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {

                this.loggerService.debug(countActions);

                countActions.groupByResults.forEach(group => {

                    var index = this.locations.indexOf(group.groupValue);

                    if (index != -1) {
                        this.identificationsNumberL[index] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addLocActionResult(subGroup.groupValue, subGroup.groupTotal, index);
                        });
                    }
                    else {
                        this.identificationsNumberL[this.locations.length - 1] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addLocActionResult(subGroup.groupValue, subGroup.groupTotal, this.locations.length - 1);
                        });
                    }
                });
            }
        );
    }

    async getActionApplicationOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }



        paramCount.groupByAttributePath.push("applicationId");

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.appOptions = [];
                this.appOptions.push({ "name": "menu.all" });
                countActions.groupByResults.forEach((action: Group) => {
                    this.appOptions.push({ "name": action.groupValue });
                });
            }
        );
    }


    async getAllActions() {

        this.initializeLocationArrays(this.locations.length);

        this.pageCount = Math.ceil(this.locations.length / this.rows);

        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";

        if (this.reportService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            return;
        }

        if (dateStartDate < dateEndDate) {

            //this.progressEnabled = true;
            //this.progressValue = "5";

            try {
                await Promise.all([
                    this.getActionApplicationOptions(dateStartDate, dateEndDate),
                    this.getResultForTechnology(dateStartDate, dateEndDate),
                    this.getResultForLocation(dateStartDate, dateEndDate),
                ]);
            } catch (error) {
                this.loggerService.error("Error getting actions: ");
                this.loggerService.error(error!);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translate.instant("titles.error_operation"),
                    detail: this.translate.instant("messages.error_getting_actions"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }


            this.hideSpinners();

            //this.progressValue = "100";

            setTimeout(() => {
                this.createLineChart();
                this.createLineChartResult();
                this.createLineChartLoc(this.first, this.rows)
                this.createLineChartLocResult(this.first, this.rows);
                //this.progressEnabled = false;
                //this.progressValue = "0";
            }, 100);
        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            this.hideSpinners();
            /*setTimeout(() => {
                this.progressEnabled = false;
                this.progressValue = "0";
            }, 2000);*/
        }
    }

    checkEq(str1: string, str2: string) {
        var arr1 = str1.split('');
        var arr2 = str2.split('');
        var counter = 0;
        for (var i = 0; i < arr1.length; i++) {
            if (arr1[i] == arr2[i]) {
                counter++;
            }
        }
        return counter;
    }

    createLineChart() {


        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");
        var option4 = this.translate.instant("titles.unknown");
        var optionR = this.translate.instant("titles.requests");
        var noDataFound = this.translate.instant("messages.no_data_found");

        const labels = [option1, option2, option3, option4];
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: '# ' + optionR,
                    data: this.identificationsNumber,
                    backgroundColor: environment.colorRequest,
                    barThickness: environment.barThickness,
                    xAxes: [{
                        id: 'x',

                        display: true,
                        title: {
                            display: true,
                            text: ''
                        }
                    }],
                    yAxes: [{
                        id: 'y',
                        display: true,
                        title: {
                            display: true,
                            text: ''
                        }
                    }]
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled
                    }
                }
            },
        };

        var index = 0;
        var noDataAvailable = true;
        for (index = 0; index < config.data.datasets[0].data.length; index++) {

            if (config.data.datasets[0].data[index] > 0)
                noDataAvailable = false;

        }


        if (this.lChart1N == null) {

            if (noDataAvailable) {

                config.options.plugins.title.text = noDataFound;
                config.data.datasets[0].label = noDataFound;
                config.data.datasets[0].backgroundColor = environment.colorDisabled,
                    config.data.datasets[0].data = [5, 10, 15, 20];
                this.lChart1N = new Chart("lChart1N", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: config.data,
                    options: config.options

                })
            }
            else {

                config.options.plugins.title.text = "";
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.identificationsNumber;

                this.lChart1N = new Chart("lChart1N", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: config.data,
                    options: config.options

                })

            }


        }
        else {


            if (noDataAvailable) {

                config.options.plugins.title.text = noDataFound;
                config.data.datasets[0].label = noDataFound;
                config.data.datasets[0].backgroundColor = environment.colorDisabled,
                    config.data.datasets[0].data = [5, 10, 15, 20];


            }
            else {

                config.options.plugins.title.text = "";
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.identificationsNumber;

            }

        }

        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }
        else {

            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;
        }

        this.lChart1N.data = config.data;
        this.lChart1N.options = config.options;
        this.lChart1N.update();



    }

    createLineChartLoc(startIndex: number, endIndex: number) {


        var optionR = this.translate.instant("titles.requests");
        var noDataFound = this.translate.instant("messages.no_data_found");

        const labels = this.locations.slice(startIndex, endIndex);
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: '# ' + optionR,
                    data: this.identificationsNumberL.slice(startIndex, endIndex),
                    backgroundColor: environment.colorRequest,
                    barThickness: environment.barThickness
                }

            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    datalabels: {
                        color: '#0000FF',
                        font: {
                            size: 8,
                        }
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled
                    }
                }
            },
        };

        var index = 0;
        var noDataAvailable = true;
        for (index = 0; index < this.identificationsNumberL.length; index++) {

            if (this.identificationsNumberL[index] > 0)
                noDataAvailable = false;

        }


        if (this.lChart1NLoc == null) {

            if (noDataAvailable) {

                this.dataAvailable = false;
                config.options.plugins.title.display = true;
                config.options.plugins.title.text = noDataFound;
                config.data.datasets[0].label = noDataFound;
                config.data.datasets[0].backgroundColor = "#cccccc";

                config.data.datasets[0].data = (new Array(this.locations.length)).fill(0);
                for (index = 0; index < this.locations.length; index++) {

                    config.data.datasets[0].data[index] = 5 + (index * 5);
                }

            }
            else {

                this.dataAvailable = true;
                config.options.plugins.title.display = false;
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.identificationsNumberL.slice(startIndex, endIndex);

            }

            this.lChart1NLoc = new Chart("lChart1NLoc", {
                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options

            });

        }
        else {

            if (noDataAvailable) {


                config.data.datasets[0].label = noDataFound;
                config.data.datasets[0].backgroundColor = environment.colorDisabled,
                    config.data.datasets[0].data = (new Array(this.locations.length)).fill(0);

                for (index = 0; index < this.locations.length; index++) {

                    config.data.datasets[0].data[index] = 5 + (index * 5);
                }

                config.options.plugins.title.display = true;
                config.options.plugins.title.text = noDataFound;
                config.options.plugins.legend.display = false;

            }
            else {
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.identificationsNumberL.slice(startIndex, endIndex);
                config.options.plugins.legend.display = true;
            }


        }


        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }

        else {

            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;
        }

        this.lChart1NLoc.data = config.data;
        this.lChart1NLoc.options = config.options;
        this.lChart1NLoc.update();


    }


    createLineChartResult() {


        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");
        var option4 = this.translate.instant("titles.unknown");

        var optionA = this.translate.instant("titles.found");
        var optionB = this.translate.instant("titles.notFound");
        var optionC = this.translate.instant("titles.qFailed");
        var optionD = this.translate.instant("titles.tError");
        var optionE = this.translate.instant("titles.sError");
        var optionF = this.translate.instant("titles.other");

        var noDataFound = this.translate.instant("messages.no_data_found");

        const labels = [option1, option2, option3, option4];
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: optionA,
                    data: this.identificationsNumberOk,
                    backgroundColor: environment.colorVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionB,
                    data: this.identificationsNumberFailed,
                    backgroundColor: environment.colorNotVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionC,
                    data: this.identificationsNumberQ,
                    backgroundColor: environment.colorQualityError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionD,
                    data: this.identificationsNumberTimeout,
                    backgroundColor: environment.colorTimeoutError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionE,
                    data: this.identificationsNumberServerError,
                    backgroundColor: environment.colorServerError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionF,
                    data: this.identificationsNumberO,
                    backgroundColor: environment.colorOther,
                    barThickness: environment.barThickness
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled
                    }
                }
            },
        };

        var noDataAvailable = true;
        var index = 0;

        var datasetIndex = 0;

        for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

            for (index = 0; index < config.data.datasets[datasetIndex].data.length; index++) {

                if (config.data.datasets[datasetIndex].data[index] > 0)
                    noDataAvailable = false;

            }

        }

        if (noDataAvailable) {

            config.options.plugins.title.display = true;
            config.options.plugins.title.text = noDataFound;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {


                config.data.datasets[datasetIndex].label = "";
                config.data.datasets[datasetIndex].backgroundColor = environment.colorDisabled,
                    config.data.datasets[datasetIndex].data = (new Array(this.locations.length)).fill(0);
                for (index = 0; index < this.locations.length; index++) {

                    config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                }

            }
        }
        else {

            config.options.plugins.title.display = false;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionA : (datasetIndex == 1 ? optionB : (datasetIndex == 2 ? optionC : (datasetIndex == 3 ? optionD : (datasetIndex == 4 ? optionE : (datasetIndex == 5 ? optionF : "")))));
                config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorVerified : (datasetIndex == 1 ? environment.colorNotVerified : (datasetIndex == 2 ? environment.colorQualityError : (datasetIndex == 3 ? environment.colorTimeoutError : (datasetIndex == 4 ? environment.colorServerError : (datasetIndex == 6 ? environment.colorOther : "")))));
            }
        }


        if (this.lChart1NResult == null) {

            this.lChart1NResult = new Chart("lChart1NResult", {
                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options

            });

        }

        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }

        else {
            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;

        }

        this.lChart1NResult.data = config.data;
        this.lChart1NResult.options = config.options;
        this.lChart1NResult.update();

    }

    createLineChartLocResult(startIndex: number, endIndex: number) {


        var optionA = this.translate.instant("titles.found");
        var optionB = this.translate.instant("titles.notFound");
        var optionC = this.translate.instant("titles.qFailed");
        var optionD = this.translate.instant("titles.tError");
        var optionE = this.translate.instant("titles.sError");
        var optionF = this.translate.instant("titles.other");

        var noDataFound = this.translate.instant("messages.no_data_found");

        const labels = this.locations.slice(startIndex, endIndex);
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: optionA,
                    data: this.identificationsNumberLOk.slice(startIndex, endIndex),
                    backgroundColor: environment.colorVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionB,
                    data: this.identificationsNumberLFailed.slice(startIndex, endIndex),
                    backgroundColor: environment.colorNotVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionC,
                    data: this.identificationsNumberLQ.slice(startIndex, endIndex),
                    backgroundColor: environment.colorQualityError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionD,
                    data: this.identificationsNumberLTimeout.slice(startIndex, endIndex),
                    backgroundColor: environment.colorTimeoutError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionE,
                    data: this.identificationsNumberLServerError.slice(startIndex, endIndex),
                    backgroundColor: environment.colorServerError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionF,
                    data: this.identificationsNumberLO.slice(startIndex, endIndex),
                    backgroundColor: environment.colorOther,
                    barThickness: environment.barThickness
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    datalabels: {
                        color: '#0000FF',
                        font: {
                            size: 8,
                        }
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled
                    }
                }
            },
        };

        var noDataAvailable = true;
        var index = 0;

        var datasetIndex = 0;

        for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

            for (index = 0; index < this.identificationsNumberLOk.length; index++) {

                if (this.identificationsNumberLOk[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.identificationsNumberLFailed.length; index++) {

                if (this.identificationsNumberLFailed[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.identificationsNumberLQ.length; index++) {

                if (this.identificationsNumberLQ[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.identificationsNumberLTimeout.length; index++) {

                if (this.identificationsNumberLTimeout[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.identificationsNumberLServerError.length; index++) {

                if (this.identificationsNumberLServerError[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.identificationsNumberLO.length; index++) {

                if (this.identificationsNumberLO[index] > 0)
                    noDataAvailable = false;

            }

        }

        if (noDataAvailable) {

            this.dataAvailable = false;
            config.options.plugins.title.display = true;
            config.options.plugins.title.text = noDataFound;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = noDataFound;
                config.data.datasets[datasetIndex].backgroundColor = environment.colorDisabled,
                    config.data.datasets[datasetIndex].data = (this.identificationsNumberLOk).fill(0);
                for (index = 0; index < (this.locations.length); index++) {

                    config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                }
            }
        }
        else {

            this.dataAvailable = true;
            config.options.plugins.title.display = false;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionA : (datasetIndex == 1 ? optionB : (datasetIndex == 2 ? optionC : (datasetIndex == 3 ? optionD : (datasetIndex == 4 ? optionE : (datasetIndex == 6 ? optionF : "")))));
                config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorVerified : (datasetIndex == 1 ? environment.colorNotVerified : (datasetIndex == 2 ? environment.colorQualityError : (datasetIndex == 3 ? environment.colorTimeoutError : (datasetIndex == 4 ? environment.colorServerError : (datasetIndex == 6 ? environment.colorOther : "")))));
            }
        }


        if (this.lChart1NLocResult == null) {

            this.lChart1NLocResult = new Chart("lChart1NLocResult", {
                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options

            });

        }
        else {

            if (noDataAvailable) {

                for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                    config.data.datasets[datasetIndex].data = (new Array(this.locations.length)).fill(0);
                    for (index = 0; index < this.locations.length; index++) {

                        config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                    }
                }

                config.options.plugins.title.text = noDataFound;

            }
            else {
                for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                    config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionA : (datasetIndex == 1 ? optionB : (datasetIndex == 2 ? optionC : (datasetIndex == 3 ? optionD : (datasetIndex == 4 ? optionE : (datasetIndex == 6 ? optionF : "")))));
                    config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorVerified : (datasetIndex == 1 ? environment.colorNotVerified : (datasetIndex == 2 ? environment.colorQualityError : (datasetIndex == 3 ? environment.colorTimeoutError : (datasetIndex == 4 ? environment.colorServerError : (datasetIndex == 6 ? environment.colorOther : "")))));
                }

            }


        }

        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }
        else {
            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;
        }

        this.lChart1NLocResult.data = config.data;
        this.lChart1NLocResult.options = config.options;
        this.lChart1NLocResult.update();


    }

    enableSpinners() {


        this.actionsData = [];

        this.showSpinners = true;

        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = false;

        if (this.lChart1N != null) {

            this.lChart1N.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerification: ' + this.lChartVerification.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChart1N.data.datasets.length; i++) {

                this.lChart1N.data.datasets[i].data = [0, 0, 0, 0];

            }
            this.lChart1N.update();

        }

        if (this.lChart1NLoc != null) {

            this.lChart1NLoc.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerificationLoc: ' + this.lChartVerificationLoc.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChart1NLoc.data.datasets.length; i++) {

                this.lChart1NLoc.data.datasets[i].data = [0, 0, 0, 0, 0];

            }
            this.lChart1NLoc.update();

        }

        if (this.lChart1NResult != null) {

            this.lChart1NLocResult.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerificationResult: ' + this.lChartVerificationResult.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChart1NResult.data.datasets.length; i++) {

                this.lChart1NResult.data.datasets[i].data = [0, 0, 0, 0];

            }
            this.lChart1NResult.update();

        }

        if (this.lChart1NLocResult != null) {

            this.lChart1NLocResult.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerificationLocResult: ' + this.lChartVerificationLocResult.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChart1NLocResult.data.datasets.length; i++) {

                this.lChart1NLocResult.data.datasets[i].data = [0, 0, 0, 0, 0];

            }
            this.lChart1NLocResult.update();

        }
    }

    hideSpinners() {

        this.showSpinners = false;
        this.actionsData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
        this.actionsData.length == 0 ? this.showNoData2 = true : this.showNoData2 = false;
        this.actionsData.length == 0 ? this.showNoData3 = true : this.showNoData3 = false;
        this.actionsData.length == 0 ? this.showNoData4 = true : this.showNoData4 = false;
    }

}
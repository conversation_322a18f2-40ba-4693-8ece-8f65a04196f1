@media screen and (min-width: 1960px) {
    .layout-main, .landing-wrapper {
        width: 100%;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    .layout-sidebar {
        width: 300px;
    }

}

@media (min-width: 992px) {
    .layout-wrapper {
        &.layout-overlay {
            .layout-main-container {
                margin-left: 0;
                // padding-left: 2rem;
            }

            .layout-sidebar {
                transform: translateX(-100%);
                left: 0;
                top: 0;
                height: 100vh;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            &.layout-overlay-active {
                .layout-sidebar {
                    transform: translateX(0);
                }
            }
        }

        &.layout-static {
            .layout-main-container {
                margin-left: 300px;
            }

            &.layout-static-inactive {
                .layout-sidebar {
                    // transform: translateX(-100%);
                    // left: 0;
                    width: 80px;
                    overflow: hidden;
                }

                .layout-menu ul ul li a {
                    margin-left: 0;
                }

                .layout-menuitem-text {
                    display: none;
                }

                .active-route {
                    width: 100% !important;
                }

                .layout-main-container {
                    margin-left: 80px;
                }

                .layout-menu-item-content {
                    padding: 0 35%;
                }
            }
        }

        .layout-mask {
            display: none;
        }
    }
}
@media (max-width: 991px) {
    .layout-wrapper {
        &.layout-overlay {
            .layout-main-container {
                margin-left: 0;
                // padding-left: 2rem;
            }

            .layout-sidebar {
                transform: translateX(-100%);
                left: 0;
                top: 0;
                height: 100vh;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            &.layout-overlay-active {
                .layout-sidebar {
                    transform: translateX(0);
                }
            }
        }

        &.layout-static {
            .layout-main-container {
                margin-left: 300px;
            }

            &.layout-static-inactive {
                .layout-sidebar {
                    // transform: translateX(-100%);
                    // left: 0;
                    width: 80px;
                    overflow: hidden;
                }

                .layout-menu ul ul li a {
                    margin-left: 0;
                }

                .layout-menuitem-text {
                    display: none;
                }

                .active-route {
                    width: 100% !important;
                }

                .layout-main-container {
                    margin-left: 80px;
                }

                .layout-menu-item-content {
                    padding: 0 35%;
                }
            }
        }

        .layout-mask {
            display: none;
        }
    }
}

@media (max-width: 600px) {
    .layout-topbar {
        padding: 0 2rem 0 0;
    }
    .blocked-scroll {
        overflow: hidden;
    }
    .layout-wrapper {
        .layout-main-container {
            margin-left: 0px;
        }


        &.layout-static {
            .layout-main-container {
                margin-left: 0px;
            }

            .layout-sidebar {
                display: none;
                transform: translateX(-100%);
                left: 0;
                top: 0;
                height: 100vh;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
            &.layout-static-inactive {
                .layout-sidebar {
                    width: 250px;
                    overflow: hidden;
                }

                .layout-menu ul ul li a {
                    margin-left: 0;
                }

                .layout-menuitem-text {
                    display: inline;
                }
                .layout-menu-item-content {
                    padding: 0 20px;
                }
            }

        }

        &.layout-mobile-active {
            .layout-sidebar {
                display: block;
                width: 250px;
                transform: translateX(0);
            }
            .layout-mask {
                display: block;
                animation: fadein $transitionDuration;
            }
            .layout-menuitem-text {
                display: inline;
            }
        }

        .layout-mask {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 998;
            width: 100%;
            height: 100%;
            background-color: var(--maskbg);
        }
    }
}
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ReplaceAttributesRequestEntity } from "../entity/replace-attributes-request.entity";
import { BiometricRepository } from "../repository/biometric.repository";

export class ReplaceAttributesUseCase implements UseCaseGrpc<ReplaceAttributesRequestEntity, void> {

    constructor(private biometricRepository: BiometricRepository) { }

    execute(replaceAttribute: ReplaceAttributesRequestEntity): Promise<void> {
        return this.biometricRepository.replaceAttributesRequest(replaceAttribute);
    }
}
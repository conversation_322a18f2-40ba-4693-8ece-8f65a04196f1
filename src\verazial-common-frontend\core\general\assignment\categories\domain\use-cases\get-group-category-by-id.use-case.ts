import { GroupCategoryEntity } from "../entity/group-category.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class GetGroupCategoryByIdUseCase implements UseCaseGrpc<{ id: string }, GroupCategoryEntity> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { id: string; }): Promise<GroupCategoryEntity> {
        return this.groupCategoryRepository.getGroupCategoryById(params);
    }
}

export class CountActionsResponseEntity {
    totalResults: number = 0;
    groupByResults: Group[] = [];
    extraCountResults: CountModeResult[] = [];
}

export interface Group {
    groupTotal: number;
    groupValue: string;
    subGroups: Group[];
    groupExtraCountResults: CountModeResult[];
}

export interface CountModeResult {
    mode?: AverageValue;
}

export interface AverageValue {
    attributePath: string;
    value: number;
}
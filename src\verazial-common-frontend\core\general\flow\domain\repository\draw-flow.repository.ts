import { TaskDrawFlow } from "src/verazial-common-frontend/core/models/task-draw-flow.interface";
import { DrawFlowEntity } from "../entity/draw-flow.entity";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";

export abstract class DrawFlowRepository {
    abstract createDrawFlow(params: { drawFlow: DrawFlowEntity }): Promise<DrawFlowEntity>;
    abstract getDrawFlowByTaskFlowId(params: { taskFlowId: string }): Promise<DrawFlowEntity>;
    abstract deleteDrawFlowByTaskFlowId(params: { taskFlowId: string }): Promise<SuccessResponse>;
    abstract updateDrawFlow(params: { drawFlow: DrawFlowEntity }): Promise<DrawFlowEntity>;
}
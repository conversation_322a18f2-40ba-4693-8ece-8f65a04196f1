import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { DataSourceEntity } from "../../domain/entities/data-source.entity";
import { AppDataSourceGrpcModel } from "src/verazial-common-frontend/core/generated/datasource/datasource_pb";

export class DataSourceGrpcMapper extends Mapper<AppDataSourceGrpcModel, DataSourceEntity> {
    override mapFrom(param: AppDataSourceGrpcModel): DataSourceEntity {
        return {
            id: param.getId(),
            name: param.getName(),
            sourceType: param.getSourcetype(),
            method: param.getMethod(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6))
        }
    }
    override mapTo(param: DataSourceEntity): AppDataSourceGrpcModel {
        let model = new AppDataSourceGrpcModel();
        model.setId(param.id!!);
        model.setName(param.name!!);
        model.setSourcetype(param.sourceType!!);
        model.setMethod(param.method!!);
        return model;
    }

}
import { GroupCategoryRepository } from "../repository/group-category.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { GroupCategoryEntity } from "../entity/group-category.entity";

export class CreateGroupCategoryUseCase implements UseCaseGrpc<{ group: GroupCategoryEntity }, GroupCategoryEntity> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { group: GroupCategoryEntity; }): Promise<GroupCategoryEntity> {
        return this.groupCategoryRepository.createGroupCategory(params);
    }
}

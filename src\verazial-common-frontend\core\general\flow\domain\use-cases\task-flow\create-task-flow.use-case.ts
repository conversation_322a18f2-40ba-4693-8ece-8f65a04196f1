import { TaskFlowEntity } from "../../entity/task-flow.entity";
import { TaskFlowRepository } from "../../repository/task-flow.repository";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class CreateTaskFlowUseCase implements UseCaseGrpc<{ taskFlow: TaskFlowEntity }, TaskFlowEntity> {
    constructor(private taskFlowRepository: TaskFlowRepository) { }
    execute(params: { taskFlow: TaskFlowEntity; }): Promise<TaskFlowEntity> {
        return this.taskFlowRepository.createTaskFlow(params)
    }
}
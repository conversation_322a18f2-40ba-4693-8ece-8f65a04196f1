import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AppRegistryEntity } from "../entities/app-registry.entity";
import { AppRegistryRepository } from "../repositories/app-registry.repository";

export class UpdateAppRegistryUseCase implements UseCaseGrpc<{ application: AppRegistryEntity }, AppRegistryEntity> {
    constructor(
        private appRegistryRepository: AppRegistryRepository
    ) { }
    execute(params: { application: AppRegistryEntity }): Promise<AppRegistryEntity> {
        return this.appRegistryRepository.updateAppRegistry(params);
    }
}
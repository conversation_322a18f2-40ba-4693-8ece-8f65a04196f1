<p-toast />
<app-user-not-verified [userIsVerified]="userIsVerified" [subjectType]="isUser ? '_user' : ''" [verifyReady]="true" (modified)="userVerifying($event)"></app-user-not-verified>
<div class="subcontainer">
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div *ngIf="showHeader" class="pr-3">
                    <label class="subcontainer-title">{{ (headerText | translate) + (specificRole ? ' (' + (specificRoleName == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : specificRoleName) + ')' : '')}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedUsersSubjects.length > 0){
                        <div>
                            {{ selectedUsersSubjects.length + ' ' + ('content.selected' | translate) }}
                        </div>
                        <button *ngIf="showMainButton" pButton [disabled]="!readAndWritePermissions && !readOnly || selectedUsersSubjects.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : mainButtonIcon }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="mainActionUserSubject()"></button>
                        <button *ngIf="showSecondaryButton" pButton [disabled]="!readAndWritePermissions || managerSettings?.allowRemoveIdentity == false || !userIsVerified" icon="pi pi-{{secondaryButtonIcon}}" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="secondaryActionUserSubject()"></button>
                    }
                </div>
            </div>
            <div *ngIf="showBioSearch || showGlobalSearch || showTableRecordButton" class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <div *ngIf="showBioSearch" class="flex flex-row items-center gap-2">
                    <i *ngIf="showVerificationIcon" [pTooltip]="'table.verification' | translate" tooltipPosition="top" class="pi pi-user" style="font-size: 1.7rem; margin-top:0.45rem"></i>
                    <i *ngIf="showSegmentedSearchIcon" [pTooltip]="'table.segmented_search' | translate" tooltipPosition="top" class="pi pi-users" style="font-size: 1.7rem; margin-top:0.45rem"></i>
                    <app-bio-tech-buttons
                        [fingerprintEnabled]="managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (filteredValues.length == 1 || konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.dactilar == true : managerSettings?.allowSearch?.dactilar == true)"
                        [facialEnabled]="managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (filteredValues.length == 1 || konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.facial == true : managerSettings?.allowSearch?.facial == true)"
                        [irisEnabled]="managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (filteredValues.length == 1 || konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.iris == true : managerSettings?.allowSearch?.iris == true)"
                        [konektorProperties]="konektorProperties"
                        (tech)="widgetSearch($event)"
                    ></app-bio-tech-buttons>
                </div>
                <p-iconField *ngIf="showGlobalSearch" iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div *ngIf="showTableRecordButton" class="add-action-main-full">
                    <p-button
                    ngClass="add-action-main-full"
                    [disabled]="!readAndWritePermissions"
                    [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    label="{{ tableRecordButtonText | translate }}"
                    icon="pi pi-{{tableRecordButtonIcon}}" iconPos="right"
                    [rounded]="true"
                    (onClick)="tableRecordAction()"
                    ></p-button>
                </div>
                <div *ngIf="showTableRecordButton" class="add-action-main-small">
                    <p-button
                    [disabled]="!readAndWritePermissions"
                    [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    icon="pi pi-{{tableRecordButtonIcon}}"
                    [rounded]="true"
                    (onClick)="tableRecordAction()"
                    ></p-button>
                </div>
            </div>
        </div>
        <p-table
            #dt
            [value]="listOfUsersSubjects"
            [selectionPageOnly]="lazyLoad"
            [(selection)]="selectedUsersSubjects"
            (selectionChange)="onUserSubjectSelectionChange($event)"
            dataKey="id"
            [paginator]="true"
            [first]="offset"
            [rows]="limit"
            [totalRecords]="totalRecords"
            [rowsPerPageOptions]="[5, 10, 20]"
            [rowHover]="true"
            [scrollable]="true"
            scrollDirection="horizontal"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['numId', 'names', 'lastNames','defaultRole', 'roles', 'birthdate', 'gender', 'email']"
            (onFilter)="onFilter($event, dt)"
            [sortField]="'numId'"
            [sortOrder]="1"
            [lazy]="lazyLoad"
            (onLazyLoad)="loadUserSubjects($event)"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
        >
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="numId"> {{ 'table.num_id' | translate }} <p-sortIcon field="numId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="names">{{ 'table.names' | translate }} <p-sortIcon field="names"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="lastNames">{{ 'table.lastNames' | translate }} <p-sortIcon field="lastnames"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="defaultRole">{{ ( isUser ? 'table.main_role' : 'table.main_profile') | translate }} <p-sortIcon field="defaultRole"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="roles">{{ ( isUser ? 'table.roles' : 'table.profiles') | translate }} <p-sortIcon field="roles"></p-sortIcon></th>
                    <!-- <th class="fixed-column">{{ ( isUser ? 'table.roles' : 'table.profiles') | translate }}</th> -->
                    <th *ngIf="!isUser" class="fixed-column" pSortableColumn="center">{{ 'table.center' | translate }} <p-sortIcon field="center"></p-sortIcon></th>
                    <th *ngIf="!isUser" class="fixed-column" pSortableColumn="locationId">{{ 'table.especific_location' | translate }} <p-sortIcon field="locationId"></p-sortIcon></th>
                    <th *ngIf="!isUser" class="fixed-column" pSortableColumn="inside">{{ 'table.present/absent' | translate }} <p-sortIcon field="inside"></p-sortIcon></th>
                    <th *ngIf="!isUser" class="fixed-column" pSortableColumn="lastActions">{{ 'table.lastActions' | translate }} <p-sortIcon field="lastActions"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="birthdate">{{ 'table.birthdate' | translate }} <p-sortIcon field="birthdate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="gender">{{ 'table.gender' | translate }} <p-sortIcon field="gender"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="email">{{ 'content.email' | translate }} <p-sortIcon field="email"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="datasource">{{ 'table.datasource' | translate }} <p-sortIcon field="datasource"></p-sortIcon></th>
                    <th *ngIf="showMainButton || showSecondaryButton" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox/>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="numId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input  pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="names" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="lastNames" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="defaultRole" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [(ngModel)]="selectedRoleFilter"
                                    [options]="allRoles"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="name"
                                    optionValue="id"
                                >
                                    <ng-template pTemplate="selectedItem" let-selected>
                                        {{ selected?.name == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : selected?.name }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.name == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : option.name }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="roles" [showMenu]="false" matchMode="customStringArray">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="allRoles"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="name"
                                    [optionValue]="lazyLoad ? 'id' : 'name'"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : value }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.name == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : option.name }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="!isUser">
                        <p-columnFilter type="text" field="center" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-treeSelect 
                                    [selectionMode]="'single'"
                                    [(ngModel)]="selectedCenter"
                                    [options]="lCentreOptions"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    [filter]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                    [disabled]="!canSeeAllLocations">
                                </p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="!isUser">
                        <p-columnFilter type="text" field="locationId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-treeSelect appendTo="body"
                                    class="md:w-20rem w-full"
                                    containerStyleClass="w-full"
                                    [options]="lLocationOptions"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    [filter]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                ></p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="!isUser">
                        <p-columnFilter field="inside" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {value: InsideEnum.INSIDE},
                                        {value: InsideEnum.OUTSIDE},
                                        {value: InsideEnum.TRANSFER},
                                    ]"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionValue="value"
                                >   <!--{value: InsideEnum.TRANSFER}-->
                                    <ng-template pTemplate="selectedItem">
                                        <p-tag [value]="getInsideName(value)" [style]="{'background': '#64748B'}"></p-tag>
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <p-tag [value]="getInsideName(option.value)" [style]="{'background': '#64748B'}"></p-tag>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="!isUser">
                        <p-columnFilter type="text" field="lastActions" [showMenu]="false" matchMode="requiredScheduleActionStatus">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="requiredScheduleActionStatusOptions"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionValue="value"
                                >   <!--{value: InsideEnum.TRANSFER}-->
                                    <ng-template pTemplate="selectedItem">
                                        <p-tag value="{{'requiredActionStatus.' + value | translate}}" [style]="{'background': getActionColor(value)}"></p-tag>
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <p-tag value="{{'requiredActionStatus.' + option.value | translate}}" [style]="{'background': getActionColor(option.value)}"></p-tag>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="birthdate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'birthdate')"
                                    (onInput)="applyDateRangeFilter(dt, 'birthdate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'birthdate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="gender" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {label: 'options.male', value: 'male'},
                                        {label: 'options.female', value: 'female'}
                                    ]"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                    optionValue="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ 'options.' + value | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label | translate }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="email" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="datasource" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="allDataSources"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="value"
                                    optionValue="key"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ getDataSourceName(value) }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.value }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th *ngIf="showMainButton || showSecondaryButton" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-user let-rowIndex="rowIndex">
                <tr class="p-selectable-row" [pSelectableRow]="user" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="user"></p-tableCheckbox>
                    </td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{user.numId}}" tooltipPosition="top" class="ellipsis-cell">{{ user.numId }}</td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{user.names}}" tooltipPosition="top" class="ellipsis-cell">{{ user.names }}</td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{user.lastNames}}" tooltipPosition="top" class="ellipsis-cell">{{ user.lastNames }}</td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{principalRoleToString(user.defaultRole)}}" tooltipPosition="top" class="ellipsis-cell">{{principalRoleToString(user.defaultRole)}}</td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{listOfRolesToString(user.roles)}}" tooltipPosition="top" class="ellipsis-cell">{{ listOfRolesToString(user.roles) }}</td>
                    <td *ngIf="!isUser" (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{getLocationName(user.center)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLocationName(user.center) }}</td>
                    <td *ngIf="!isUser" (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{getLocationName(user.locationId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLocationName(user.locationId) }}</td>
                    <td *ngIf="!isUser" (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)"
                        class="ellipsis-cell">
                        <ng-container *ngIf="insideStatus(user.inside, user.entryDate, user.exitDate, user.visitTime, user.roles) as status">
                            <p-tag *ngIf="status"
                                pTooltip="{{status}}"  showDelay="1000" tooltipPosition="top"
                                [value]="status"
                                [style]="{'background': getTimeRemainingColor(user.visitTime)}">
                            </p-tag>
                        </ng-container>
                    </td>
                    <td *ngIf="!isUser" (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)"
                        class="ellipsis-cell">
                        <ng-container *ngIf="user.lastActions.length > 0">
                            <div class="flex flex-column gap-1">
                                <ng-container *ngFor="let action of user.lastActions; let i = index">
                                    <div *ngIf="getActionData(action) != ''">
                                        @if (getActionData(action) == requiredScheduleActionStatus.COMPLETED_ON_TIME || getActionData(action) == requiredScheduleActionStatus.COMPLETED_LATE) {
                                            <p-tag
                                                pTooltip="{{getFlowName(action.flowId)}}" showDelay="500" tooltipPosition="top"
                                                value="{{('content.' + getActionData(action) | translate) + ' ' + (action.actionDateTime | date:('dateTimeFormat' | translate))}}"
                                                [style]="{'background': getActionColor(getActionData(action))}"
                                            ></p-tag>
                                        }
                                        @else if (getActionData(action) == requiredScheduleActionStatus.ON_TIME || getActionData(action) == requiredScheduleActionStatus.LATE){
                                            <p-tag
                                                pTooltip="{{getFlowName(action.flowId)}}" showDelay="500" tooltipPosition="top"
                                                [value]="('content.' + getActionData(action) | translate) + ' ' + getTimeRemainingFromString(action.nextRequiredDateTimeEnd)"
                                                [style]="{'background': getActionColor(getActionData(action))}"
                                            ></p-tag>
                                        }
                                    </div>
                                </ng-container>
                            </div>
                        </ng-container>
                    </td>
                    @if(isValidDate(user.birthdate)){
                        <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{user.birthdate | date:('dateFormatLong' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ user.birthdate | date:('dateFormatLong' | translate) }}</td>
                    }@else{
                        <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{user.gender != '' && user.gender != undefined && user.gender != null ? ('options.' + user.gender | translate) : ''}}" tooltipPosition="top" class="ellipsis-cell">{{ user.gender != '' && user.gender != undefined && user.gender != null ? ('options.' + user.gender | translate) : '' }}</td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{user.email}}" tooltipPosition="top" class="ellipsis-cell">{{ user.email }}</td>
                    <td (click)="mainActionOnRowClick ? mainActionUserSubject(user) : updateSelection(user)" showDelay="1000" pTooltip="{{getDataSourceName(user.datasource)}}" tooltipPosition="top" class="ellipsis-cell">{{ getDataSourceName(user.datasource) }}</td>
                    <td *ngIf="showMainButton || showSecondaryButton" alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button *ngIf="showMainButton" pButton pRipple [disabled]="!readAndWritePermissions && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified || !checkPermisions(user.datasource).allowSave ? 'eye' : mainButtonIcon }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="mainActionUserSubject(user)"></button>
                            <button *ngIf="showSecondaryButton" pButton pRipple [disabled]="!readAndWritePermissions || managerSettings?.allowRemoveIdentity == false || !userIsVerified || !checkPermisions(user.datasource).allowDelete" icon="pi pi-{{secondaryButtonIcon}}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="secondaryActionUserSubject(user)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<!-- Create new User/Subject -->
<p-dialog [(visible)]="showNewUserSubjectDialog" styleClass="p-fluid" [modal]="true" [closable]="true">
    <ng-template pTemplate="header">
        <div >
            <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                {{ dialogBoxText | translate }}
            </label>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-edit-user-subject
            [readAndWritePermissions]="readAndWritePermissions"
            [isUser]="isUser"
            [isVerified]="true"
            [userIsVerified]="userIsVerified"
            [actionType]="actionType"
            (outputData)="onAddUserSubject($event)"
            (allowSave)="allowSave($event)"
        ></app-edit-user-subject>
    </ng-template>
    <ng-template pTemplate="footer">
        <div class="flex flex-row gap-1 mr-3 justify-content-end">
            <p-button
            [disabled]="!readAndWritePermissions || isDisableSaveButton || !saveAllowed"
            pTooltip="{{ ((!saveAllowed) ? ('messages.cannot_' + ((actionType == userSubjectActionTypes.CREATE) ? 'create' : ((actionType == userSubjectActionTypes.UPDATE) ? 'update': 'save')) + '_subject_user_ds') : '') | translate }}" tooltipPosition="top"
            [style]="{'pointer-events': 'auto', 'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px'}"
            label="{{ 'save'| translate }}"
            (onClick)="saveUserSubject()"
            ></p-button>
            <p-button
            [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}"
            label="{{ 'cancel'| translate }}"
            (onClick)="showNewUserSubjectDialog = false"
            ></p-button>
        </div>
    </ng-template>
</p-dialog>

<!-- Widget Verify -->
<app-widget-search
    [numId]="numId"
    [widgetUrl]="widgetUrl"
    [managerSettings]="managerSettings"
    [ready]="searchReady"
    [technology]="tech"
    [segmentedSearchAttributes]="segmentedSearchAttributes"
    (result)="onWidgetSearchResult($event)"
></app-widget-search>


<p-dialog [(visible)]="showEnterNumId" [modal]="true" [style]="{ background: '#FFFFFF', 'border-radius': '6px', width: '25rem' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="dialogHeader mt-3 ml-3">{{ 'content.verification_1to1_enabled' | translate }}</div>
        <div class="flex flex-column align-items-center justify-content-center gap-3 my-3" [formGroup]="formNumId">
            <p-floatLabel style="width: 90%;" class="mt-6 mb-4">
                <input id="numId" type="text" pInputText formControlName="numId" class="flex-auto" autocomplete="off" style="width: 100%;"/>
                <label for="numId">{{ 'content.id_number' | translate}}</label>
            </p-floatLabel>
            <div class="flex justify-content-center gap-2">
                <button pButton label="{{ 'cancel' | translate }}" class="p-button-text" severity="secondary" (click)="closeDialog()"></button>
                <p-button label="{{ 'verify' | translate}}" severity="secondary" (onClick)="onDialogSubmit()" />
            </div>
        </div>
    </ng-template>
</p-dialog>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ (headerText | translate) + (specificRole ? ' (' + (specificRoleName == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : specificRoleName) + ')' : '')}}</label>
                </div>
            </div>
        </div>
    </div>
    <div style="height: 65vh;" class="flex justify-content-center align-items-center">
        <app-empty
            [readAndWritePermissions]="readAndWritePermissions"
            [buttonLabel]="tableRecordButtonText"
            [titleLabel]="noDataText"
            contentHeight="300px"
            (clicked)="tableRecordAction()">
        </app-empty>
    </div>
</ng-template>
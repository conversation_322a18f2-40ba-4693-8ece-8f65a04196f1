import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { AuthEntity } from "../entity/auth.entity";
import { AuthRepository } from "../repository/auth.repository";

export class ChangeTenantUseCase implements UseCaseGrpc<{ newTenantId: string, token: string }, AuthEntity> {
    constructor(private authRepository: AuthRepository) { }
    execute(params: { newTenantId: string; token: string; }): Promise<AuthEntity> {
        return this.authRepository.changeTenant(params);
    }
}
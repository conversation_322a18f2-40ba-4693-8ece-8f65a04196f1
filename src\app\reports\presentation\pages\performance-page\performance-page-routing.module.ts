import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PerformancePageComponent } from './performance-page/performance-page.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';

const routes: Routes = [
    { 
      path: '', 
      component: PerformancePageComponent,
      canActivate: [AuthGuard, NavigationGuard]
    }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PerformancePageRoutingModule { }

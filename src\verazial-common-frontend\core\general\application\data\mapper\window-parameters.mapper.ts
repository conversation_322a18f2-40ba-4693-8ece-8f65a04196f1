import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { WindowParametersModel } from "../model/window-parameters.model";
import { WindowParametersEntity } from "../../domain/entities/window-parameters.entity";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";
import { PassListEvents } from "src/verazial-common-frontend/core/models/pass-list-events.enum";

export class WindowParametersMapper extends Mapper<WindowParametersModel, WindowParametersEntity> {
    override mapFrom(param: WindowParametersModel): WindowParametersEntity {
        return {
            id: param.id,
            applicationWindowId: param.applicationWindowId,
            name: param.name,
            windowOrder: param.windowOrder,
            attributeName: param.attributeName,
            uiComponentType: param.uiComponentType,
            componentPosition: param.componentPosition,
            triggerOrder: param.triggerOrder,
            event: toEnum(PassListEvents, param.event!),
            createdAt: param.createdAt,
            updatedAt: param.updatedAt,
        }
    }
    override mapTo(param: WindowParametersEntity): WindowParametersModel {
        return {
            id: param.id,
            applicationWindowId: param.applicationWindowId,
            name: param.name,
            windowOrder: param.windowOrder,
            attributeName: param.attributeName,
            uiComponentType: param.uiComponentType,
            componentPosition: param.componentPosition,
            triggerOrder: param.triggerOrder,
            event: param.event,
            createdAt: param.createdAt,
            updatedAt: param.updatedAt,
        }
    }
}
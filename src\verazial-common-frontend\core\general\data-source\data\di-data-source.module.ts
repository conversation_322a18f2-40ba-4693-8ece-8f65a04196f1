import { NgModule } from "@angular/core";
import { DataSourceRepository } from "../domain/repositories/data-source.repository";
import { AddAppDataSourceUseCase } from "../domain/use-cases/add-app-data-source.use-case";
import { DeleteDataSourceByIdUseCase } from "../domain/use-cases/delete-data-source-by-id.use-case";
import { GetAllDataSourcesUseCase } from "../domain/use-cases/get-all-data-sources.use-case";
import { GetAppDataSourceByIdUseCase } from "../domain/use-cases/get-app-data-source-by-id.use-case";
import { GetAppDataSourceByNameUseCase } from "../domain/use-cases/get-app-data-source-by-name.use-case";
import { CommonModule } from "@angular/common";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { DataSourceRepositoryGrpcImpl } from "./repository-impl/data-source-impl-grpc.repository";
import { UpdateAppDataSourceByIdUseCase } from "../domain/use-cases/update-app-data-source-by-id.use-case";

const addAppDataSourceuseCaseFactory =
    (dataSourceRepository: DataSourceRepository) => new AddAppDataSourceUseCase(dataSourceRepository);

const updateAppDataSourceByIdUseCaseFactory =
    (dataSourceRepository: DataSourceRepository) => new UpdateAppDataSourceByIdUseCase(dataSourceRepository);

const getAppDataSourceByIdUseCaseFactory =
    (dataSourceRepository: DataSourceRepository) => new GetAppDataSourceByIdUseCase(dataSourceRepository);

const getAppDataSourceByNameUseCaseFactory =
    (dataSourceRepository: DataSourceRepository) => new GetAppDataSourceByNameUseCase(dataSourceRepository);

const getAllDataSourcesUseCaseFactory =
    (dataSourceRepository: DataSourceRepository) => new GetAllDataSourcesUseCase(dataSourceRepository);

const deleteDataSourceByIdUseCaseFactory =
    (dataSourceRepository: DataSourceRepository) => new DeleteDataSourceByIdUseCase(dataSourceRepository);

export const addAppDataSourceuseCaseProvider = {
    provide: AddAppDataSourceUseCase,
    useFactory: addAppDataSourceuseCaseFactory,
    deps: [DataSourceRepository]
}

export const updateAppDataSourceByIdUseCaseProvider = {
    provide: UpdateAppDataSourceByIdUseCase,
    useFactory: updateAppDataSourceByIdUseCaseFactory,
    deps: [DataSourceRepository]
}

export const getAppDataSourceByIdUseCaseProvider = {
    provide: GetAppDataSourceByIdUseCase,
    useFactory: getAppDataSourceByIdUseCaseFactory,
    deps: [DataSourceRepository]
}

export const getAppDataSourceByNameUseCaseProvider = {
    provide: GetAppDataSourceByNameUseCase,
    useFactory: getAppDataSourceByNameUseCaseFactory,
    deps: [DataSourceRepository]
}

export const getAllDataSourcesUseCaseProvider = {
    provide: GetAllDataSourcesUseCase,
    useFactory: getAllDataSourcesUseCaseFactory,
    deps: [DataSourceRepository]
}

export const deleteDataSourceByIdUseCaseProvider = {
    provide: DeleteDataSourceByIdUseCase,
    useFactory: deleteDataSourceByIdUseCaseFactory,
    deps: [DataSourceRepository]
}

@NgModule({
    imports: [CommonModule], providers: [
        addAppDataSourceuseCaseProvider,
        updateAppDataSourceByIdUseCaseProvider,
        getAppDataSourceByIdUseCaseProvider,
        getAppDataSourceByNameUseCaseProvider,
        getAllDataSourcesUseCaseProvider,
        deleteDataSourceByIdUseCaseProvider,
        { provide: DataSourceRepository, useClass: DataSourceRepositoryGrpcImpl },
        provideHttpClient(withInterceptorsFromDi())
    ]
})
export class DiDataSourceModule { }


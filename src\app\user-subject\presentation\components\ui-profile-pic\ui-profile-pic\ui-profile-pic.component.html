<div class="captureContainer">
    <div [ngClass]="{'picBorder': responsive, 'picBorderStatic': !responsive}">
        <img #profPic id="userProfilePicImg" [ngClass]="{'imgUser': responsive, 'imgUserStatic': !responsive}" class="" src={{image}} />
        <video #video autoplay id="userProfilePicVideo" class="d-none videoPic"></video>
        <canvas #canvas id="userProfilePicCanvas" class="d-none"></canvas>
    </div>
    <p-button *ngIf="edit" icon="pi pi-camera" [rounded]="true" (click)="startUserPictureTake()"></p-button>
</div>

import { CustomFieldGroupModel } from "./custom-field-group.model";
import { CustomFieldModel } from "./custom-field.model";

export class SubjectTabsConfig {
    showAdditionalTabs?: boolean;
    // Extended Bio Fields
    showExtendedBioFieldsTab?: boolean;
    restrictExtendedBioFieldsTabToSpecificRoles?: boolean;
    extendedBioFieldsRoles?: string;
    // Physical Data
    showPhysicalDataTab?: boolean;
    restrictPhysicalDataTabToSpecificRoles?: boolean;
    physicalDataRoles?: string;
    // Profile Picture
    showProfilePictureTab?: boolean;
    restrictProfilePictureTabToSpecificRoles?: boolean;
    profilePictureRoles?: string;
    // Relations
    showRelationsTab?: boolean;
    restrictRelationsTabToSpecificRoles?: boolean;
    relationsRoles?: string;
    // Locations
    showLocationsTab?: boolean;
    restrictLocationsTabToSpecificRoles?: boolean;
    locationsRoles?: string;
    // Entries Exits
    showEntriesExitsTab?: boolean;
    restrictEntriesExitsTabToSpecificRoles?: boolean;
    entriesExitsRoles?: string;
    // Entry Exit Authorizations
    showEntryExitAuthorizationsTab?: boolean;
    restrictEntryExitAuthorizationsTabToSpecificRoles?: boolean;
    entryExitAuthorizationsRoles?: string;
    showEntryExitAuthDetails?: boolean;
    entryExitAuthDetailFields?: CustomFieldModel[];
    entryExitAuthDetailFieldGroups?: CustomFieldGroupModel[];
    // files
    showFilesTab?: boolean;
    restrictFilesTabToSpecificRoles?: boolean;
    filesRoles?: string;
    subjectFileTypes?: string[];
    acceptedFiles?: string;
    maxFileSize?: string;
}
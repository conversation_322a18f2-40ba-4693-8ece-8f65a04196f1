import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild, ViewContainerRef } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { FilterService, MenuItem, MessageService, TreeNode } from "primeng/api";
import { Table } from "primeng/table";
import { TreeNodeSelectEvent } from "primeng/tree";
import { DynamicFormAttributes, DynamicFormComponent } from "src/app/user-subject/presentation/components/dynamic-form/dynamic-form/dynamic-form.component";
import { environment } from "src/environments/environment";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { DetailsDataEntity } from "src/verazial-common-frontend/core/general/common/entity/details-data.entity";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { AttributeData } from "src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { CustomFieldTypes } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field-type.enum";
import { CustomFieldModel } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field.model";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";
import { AuthStatus } from "src/verazial-common-frontend/core/general/prisons/common/enums/transfer-auth-status.enum";
import { TransferAuthDetailEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth-detail.entity";
import { TransferAuthUserSubjectEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth-user-subject.entity";
import { TransferAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth.entity";
import { CreateTransferAuthUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/create-transfer-auth.use-case";
import { GetTransferAuthByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/get-transfer-auth-by-id.use-case";
import { UpdateTransferAuthUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/update-transfer-auth-by-id.use-case";
import { RoleType } from "src/verazial-common-frontend/core/general/role/common/enum/role-type.enum";
import { GetSubjectsRequestEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/get-subjects-request.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { GetSubjectByNumIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { UserSubjectEnum } from "src/verazial-common-frontend/core/models/user-subject.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { BioSignatureResult } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures/bio-signatures.component";
import { WidgetResult } from "src/verazial-common-frontend/modules/shared/models/widget-response.model";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-transfer-auth-edit',
    templateUrl: './transfer-auth-edit.component.html',
    styleUrl: './transfer-auth-edit.component.css'
})
export class TransferAuthEditComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() canReadAndWrite: boolean = false;
    @Input() userIsVerified: boolean = false;
    @Input() operationType!: OperationType;
    @Input() transferAuth?: TransferAuthEntity;
    @Input() listLocations: TreeNode[] = [];
    @Input() listLocationsDestiny: TreeNode[] = [];
    @Input() listOfAllSubjects: SubjectEntity[] = [];
    @Input() listOfAllRoles: RoleEntity[] = [];
    @Input() managerSettings?: GeneralSettings;
    @Input() createUpdateButtonTitle: string = this.translateService.instant('save');
    // Outputs
    @Output() operationStatus = new EventEmitter<OperationStatus>();

    isLoading: boolean = false;
    editEnabled: boolean = true;
    isExpired: boolean = false;
    isInProgress: boolean = false;
    modified: boolean = false;
    private rejectTimeout: any;

    items?: MenuItem[];
    stepOptions?: AttributeData[]
    activeIndex: number = 0;
    opType = OperationType;
    minDate: Date | undefined;

    // Subjects
    subjectType = UserSubjectEnum.SUBJECT;
    useLazyLoad: boolean = false;
    getSubjectsRequest = new GetSubjectsRequestEntity();
    listOfAllPrisoners: SubjectEntity[] = [];
    selectedPrisoners: SubjectEntity[] = [];
    listOfAllResponsibleUsers: SubjectEntity[] = [];
    selectedResponsibleUsers: SubjectEntity[] = [];
    allResponsibleUserRoles: RoleEntity[] = [];

    // Transfer Auth Form
    selectedTransferAuth?: TransferAuthEntity;
    selectAuthReason?: GenericKeyValue;
    authReasonOptions: GenericKeyValue[] = [];
    authReasonParameter = 'transfer-auth-reasons';
    selectCancelReason?: GenericKeyValue;
    cancelReasonOptions: GenericKeyValue[] = [];
    cancelReasonParameter = 'transfer-auth-cancel-reasons';
    cancelObservation: string = '';
    selectedOriginLocationId?: TreeNode;
    selectedDestinyLocationId?: TreeNode;
    detailsList: TransferAuthDetailEntity[] = [];
    showTransferAuthDetails: boolean = false;
    public form: FormGroup = this.fb.group({
        authCode: [],
        authReason: ['', [Validators.required]],
        // authRegistrationDate
        // authExpirationDate
        originLocationId: ['', [Validators.required]],
        destinyLocationId: ['', [Validators.required]],
        plannedDepartureDateTime: ['', [Validators.required]],
        plannedArrivalDateTime: ['', [Validators.required]],
        // details
        // listOfPrisoners
        // listOfResponsibleUsers
        observations: [],
        // status
        // authUserId
        // authUserNumId
        // authUserSignatureDate
        // authUserSignatureTech
        // isCompleted
        // actualDepartureDateTime
        // actualArrivalDateTime
        // elapsedTime
        // isCancelled
        // cancelDate
        // cancelReason
        // cancelObservation
        // cancelUserId
        // cancelUserNumId
        // cancelUserSignatureDate
        // cancelUserSignatureTech
        // createdBy
        // updatedBy
        // createdAt
        // updatedAt
        stepOptions: ['0'],
    });
    /* Custom Field Details Form */
    detailsData: DetailsDataEntity[] = [];
    @ViewChild('dynamicFormContent', { read: ViewContainerRef, static: true }) dynamicFormContent: ViewContainerRef;
    formAttributes: DynamicFormAttributes[] = [];
    showDynamicForm: boolean = false;
    componentRef: any;
    formModified: boolean = false;
    // Details Form
    public detailItemForm: FormGroup = this.fb.group({
        key: ['', [Validators.required]],
        value: ['', [Validators.required]],
    });
    public detailItemRowForm: FormGroup = this.fb.group({
        key: ['', [Validators.required]],
        value: ['', [Validators.required]],
    });
    clonedDetailItem: { [s: string]: TransferAuthDetailEntity } = {};

    // Date Range Filter
    dateFormGroup: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    // Signatures
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
    authSignatureWidget: boolean = false;
    authUser?: SubjectEntity;
    authUserImage: string = '';
    authSignatureData?: {
        id?: string,
        numId?: string,
        tech?: string,
        date?: Date,
    };
    hasAuthSignature: boolean = false;
    restrictAuthRoles: string[] = [];
    segmentedSearchAuthRole: string = '';
    cancelUser?: SubjectEntity;
    cancelUserImage: string = '';
    cancelSignatureData?: {
        id?: string,
        numId?: string,
        tech?: string,
        date?: Date,
        reason?: string,
        observation?: string,
    };
    hasCancelSignature: boolean = false;
    restrictCancelRoles: string[] = [];
    segmentedSearchCancelRole: string = '';

    // Widget Functions
    widgetUrl: string = '';
    konektorProperties?: KonektorPropertiesEntity;
    segmentedSearchAttributes: { name: string, value: string, secondSearch?: string }[] = [];
    userNumId: string = '';
    subjectNumId: string = '';
    verifyReady: boolean = false;
    searchReady: boolean = false;
    tech: string = '';
    // Enter NumId (1:1 Verification)
    showEnterNumId: boolean = false;
    formNumId: FormGroup = new FormGroup({
        numId: new FormControl('', Validators.required)
    });
    verificationSubjectId: string = "";

    constructor(
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private translateService: TranslateService,
        private filterService: FilterService,
        private messageService: MessageService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private auditTrailService: AuditTrailService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
        private getTransferAuthByIdUseCase: GetTransferAuthByIdUseCase,
        private createTransferAuthUseCase: CreateTransferAuthUseCase,
        private updateTransferAuthUseCase: UpdateTransferAuthUseCase,
        private newLocationsService: NewLocationsService,
    ) {
        this.filterService.register('customStringArray', (value: any, filter: any): boolean => {
            if (!filter) return true; // If no filter provided, show all
            else if (typeof value === 'object' && typeof filter === 'string') {
                return value.map((role: RoleEntity) => role.name).join(', ').toLowerCase().includes(filter.toLowerCase());
            }
            return false;
        });
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
        this.dynamicFormContent = {} as ViewContainerRef;
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }

    ngOnInit(): void {
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                if (data) {
                    this.konektorProperties = data;
                }
            },
            error: (e) => {
                this.loggerService.error(e);
            },
        });
        this.form.reset();
        this.items = [
            { label: this.translateService.instant('content.general'), },
            { label: this.translateService.instant('content.details'), },
            { label: this.translateService.instant('content.listOfPrisoners'), },
            { label: this.translateService.instant('content.listOfResponsiblePersonel'), },
            { label: this.translateService.instant('content.signatures'), },
        ];
        this.stepOptions = [
            { key: this.translateService.instant('content.general'), value: "0" },
            { key: this.translateService.instant('content.details'), value: "1" },
            { key: this.translateService.instant('content.listOfPrisoners'), value: "2" },
            { key: this.translateService.instant('content.listOfResponsiblePersonel'), value: "3" },
            { key: this.translateService.instant('content.signatures'), value: "4" },
        ];
        this.getSubjectsRequest.offset = 0;
        this.getSubjectsRequest.limit = 10;
        let settings = this.managerSettings;
        if (settings) {
            // this.loggerService.debug(settings.catalogs!);
            let options = settings.catalogs?.find((catalog) => catalog.parameter === this.authReasonParameter)?.options;
            this.authReasonOptions = options ? (JSON.parse(options) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.authReasonParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
            options = settings.catalogs?.find((catalog) => catalog.parameter === this.cancelReasonParameter)?.options;
            this.cancelReasonOptions = options ? (JSON.parse(options) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.cancelReasonParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }
        this.isLoading = true;
        if (this.operationType == OperationType.UPDATE && this.transferAuth && this.transferAuth.id) {
            setTimeout(() => {
                this.getTransferAuthById(this.transferAuth?.id!);
                // this.fillFields();
            }, 300);
        } else {
            this.onCancel();
            this.componentRef?.destroy();
            this.detailsData = [];
            this.buildDynamicForm();
        }
        this.ngOnChanges();
    }

    handleBeforeUnload(event: Event) {
      this.ngOnDestroy();
    }

    ngOnDestroy(): void {
      this.updateModified(false);
      // Clean up the timeout if the component is destroyed
      this.clearRejectTimeout();
    }

    ngOnChanges(): void {
        this.showTransferAuthDetails = this.managerSettings?.continued1?.prisonsSettings?.transferAuthConfig?.showTransferAuthDetails ?? false;
        if (this.showTransferAuthDetails) {
            this.items = [
                { label: this.translateService.instant('content.general'), },
                { label: this.translateService.instant('content.details'), },
                { label: this.translateService.instant('content.listOfPrisoners'), },
                { label: this.translateService.instant('content.listOfResponsiblePersonel'), },
                { label: this.translateService.instant('content.signatures'), },
            ];
            this.stepOptions = [
                { key: this.translateService.instant('content.general'), value: "0" },
                { key: this.translateService.instant('content.details'), value: "1" },
                { key: this.translateService.instant('content.listOfPrisoners'), value: "2" },
                { key: this.translateService.instant('content.listOfResponsiblePersonel'), value: "3" },
                { key: this.translateService.instant('content.signatures'), value: "4" },
            ];
        }
        else {
            this.items = [
                { label: this.translateService.instant('content.general'), },
                // { label: this.translateService.instant('content.details'), },
                { label: this.translateService.instant('content.listOfPrisoners'), },
                { label: this.translateService.instant('content.listOfResponsiblePersonel'), },
                { label: this.translateService.instant('content.signatures'), },
            ];
            this.stepOptions = [
                { key: this.translateService.instant('content.general'), value: "0" },
                // { key: this.translateService.instant('content.details'), value: "1" },
                { key: this.translateService.instant('content.listOfPrisoners'), value: "1" },
                { key: this.translateService.instant('content.listOfResponsiblePersonel'), value: "2" },
                { key: this.translateService.instant('content.signatures'), value: "3" },
            ];
        }
        if (this.canReadAndWrite && this.userIsVerified && this.editEnabled) {
            this.form.enable();
        }
        else {
            this.form.disable();
            this.form.controls['stepOptions'].enable();
        }
        if (this.authUser) {
            this.authUserImage = (this.authUser.pic == this.imagePlaceholder || this.authUser.pic == "" || this.authUser.pic == undefined || this.authUser.pic == null) ? this.imagePlaceholder : this.authUser.pic.includes('data:image/jpeg;base64,') ? this.authUser?.pic! : 'data:image/jpeg;base64,' + this.authUser?.pic!;
        }
        if (this.cancelUser) {
            this.cancelUserImage = (this.cancelUser.pic == this.imagePlaceholder || this.cancelUser.pic == "" || this.cancelUser.pic == undefined || this.cancelUser.pic == null) ? this.imagePlaceholder : this.cancelUser.pic.includes('data:image/jpeg;base64,') ? this.cancelUser?.pic! : 'data:image/jpeg;base64,' + this.cancelUser?.pic!;
        }
        if (this.listOfAllRoles.length > 0) {
            this.allResponsibleUserRoles = this.listOfAllRoles.filter((role) => role.id != Number(this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId!) && role.type == RoleType.SUBJECT);
            let responsibleUserRoles = this.managerSettings?.continued1?.prisonsSettings?.transferAuthConfig?.responsibleSubjectRoles ?? [];
            if (responsibleUserRoles.length > 0) {
                this.allResponsibleUserRoles = this.allResponsibleUserRoles.filter((role) => responsibleUserRoles.includes(role.id!.toString()));
            }
        }
        this.restrictAuthRoles = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.authorizeTransferAuthsRoles ?? [];
        this.segmentedSearchAuthRole = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.authorizeTransferAuthsMainRole ?? '';
        this.restrictCancelRoles = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.cancelTransferAuthsRoles ?? [];
        this.segmentedSearchCancelRole = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.cancelTransferAuthsMainRole ?? '';
        this.getSubjects();
    }

    getSubjects() {
        // Prisoners
        if (this.form.get('originLocationId')?.value) {
            this.listOfAllPrisoners = this.listOfAllSubjects.filter((subject) => subject.defaultRole == this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId && subject.locationId && this.newLocationsService.isParentOrSame(this.listLocations,this.form.get('originLocationId')?.value.key, subject.locationId));
        }
        else {
            this.listOfAllPrisoners = this.listOfAllSubjects.filter((subject) => subject.defaultRole == this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId);
        }
        // Not Prisoners
        this.listOfAllResponsibleUsers = this.listOfAllSubjects.filter((subject) => subject.defaultRole != this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId);
        let responsibleUserRoles = this.managerSettings?.continued1?.prisonsSettings?.transferAuthConfig?.responsibleSubjectRoles ?? [];
        if (responsibleUserRoles.length > 0) {
            this.listOfAllResponsibleUsers = this.listOfAllResponsibleUsers.filter((subject) => responsibleUserRoles.includes(subject.defaultRole!));
        }
        if (this.transferAuth?.isCancelled || this.transferAuth?.isCompleted || (this.transferAuth?.authUserSignatureTech != null && this.transferAuth?.authUserSignatureTech != undefined && this.transferAuth?.authUserSignatureTech != '')) {
            // Prisoners
            this.listOfAllPrisoners = this.listOfAllSubjects.filter((subject) => this.transferAuth?.listOfPrisoners?.map((userSubject) => userSubject.subjectId).includes(subject.id));
            // Not Prisoners
            this.listOfAllResponsibleUsers = this.listOfAllSubjects.filter((subject) => this.transferAuth?.listOfResponsibleUsers?.map((userSubject) => userSubject.subjectId).includes(subject.id));
        }
    }

    updateListOfPrisoners(event: TreeNodeSelectEvent) {
        if (!(this.transferAuth?.isCancelled || this.transferAuth?.isCompleted || (this.transferAuth?.authUserSignatureTech != null && this.transferAuth?.authUserSignatureTech != undefined && this.transferAuth?.authUserSignatureTech != ''))) {
            this.listOfAllPrisoners = this.listOfAllSubjects.filter((subject) => subject.defaultRole == this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId && subject.locationId && this.newLocationsService.isParentOrSame(this.listLocations,this.form.get('originLocationId')?.value.key, subject.locationId));
        }
    }

    getTransferAuthById(id: string) {
        this.getTransferAuthByIdUseCase.execute({ id: id }).then(
            (data) => {
                this.loggerService.debug(data);
                this.transferAuth = data;
                this.fillFields();
            },
            (e) => {
                this.loggerService.error(e);
            }
        )
        .finally(() => {
            this.fillFields();
        });
    }

    fillFields() {
        if (!this.transferAuth) {
            this.onCancel();
            return;
        }
        this.form.get('authCode')?.setValue(this.transferAuth?.authCode);
        this.selectAuthReason = this.authReasonOptions.find((option) => option.key == this.transferAuth?.authReason);
        this.form.get('authReason')?.setValue(this.selectAuthReason);
        // this.form.get('origin')?.setValue(this.findNodeById(this.listLocations, this.authSchedule?.locationId!));
        this.form.get('originLocationId')?.setValue(this.findNodeById(this.listLocations, this.transferAuth?.originLocationId!));
        this.form.get('destinyLocationId')?.setValue(this.findNodeById(this.listLocationsDestiny, this.transferAuth?.destinyLocationId!));
        this.form.get('plannedDepartureDateTime')?.setValue(this.transferAuth?.plannedDepartureDateTime);
        this.form.get('plannedArrivalDateTime')?.setValue(this.transferAuth?.plannedArrivalDateTime);
        this.detailsList = this.transferAuth?.details ?? [];
        this.loggerService.debug(this.transferAuth?.listOfPrisoners)
        this.selectedPrisoners = this.transferAuth?.listOfPrisoners?.map((userSubject) => this.listOfAllSubjects.find((subject) => subject.id == userSubject.subjectId)).filter((subject): subject is SubjectEntity => subject !== undefined) ?? [];
        this.loggerService.debug(this.selectedPrisoners)
        this.selectedResponsibleUsers = this.transferAuth?.listOfResponsibleUsers?.map((userSubject) => this.listOfAllSubjects.find((subject) => subject.id == userSubject.subjectId)).filter((subject): subject is SubjectEntity => subject !== undefined) ?? [];
        this.form.get('observations')?.setValue(this.transferAuth?.observations);
        this.isInProgress = this.transferAuth?.status == AuthStatus.IN_PROGRESS;
        this.hasAuthSignature = this.transferAuth?.authUserNumId != '' && this.transferAuth?.authUserNumId != null && this.transferAuth?.authUserNumId != undefined;
        this.authSignatureData = {
            id: this.hasAuthSignature ? this.transferAuth?.authUserId : undefined,
            numId: this.hasAuthSignature ? this.transferAuth?.authUserNumId : undefined,
            tech: this.hasAuthSignature ? this.transferAuth?.authUserSignatureTech : undefined,
            date: this.hasAuthSignature ? this.transferAuth?.authUserSignatureDate : undefined,
        }
        if (!this.hasAuthSignature) {
            this.authSignatureData = undefined;
        }
        this.hasCancelSignature = this.transferAuth?.cancelUserNumId != '' && this.transferAuth?.cancelUserNumId != null && this.transferAuth?.cancelUserNumId != undefined;
        this.cancelSignatureData = {
            id: this.hasCancelSignature ? this.transferAuth?.cancelUserId : undefined,
            numId: this.hasCancelSignature ? this.transferAuth?.cancelUserNumId : undefined,
            tech: this.hasCancelSignature ? this.transferAuth?.cancelUserSignatureTech : undefined,
            date: this.hasCancelSignature ? this.transferAuth?.cancelUserSignatureDate : undefined,
            reason: this.hasCancelSignature ? this.transferAuth?.cancelReason : undefined,
            observation: this.hasCancelSignature ? this.transferAuth?.cancelObservation : undefined,
        }
        if (!this.hasCancelSignature) {
            this.cancelSignatureData = undefined;
        }
        if (this.hasAuthSignature || this.hasCancelSignature) {
            this.form.disable();
            this.form.controls['stepOptions'].enable();
            this.editEnabled = false;
        }
        else {
            this.form.enable();
            this.editEnabled = true;
        }
        this.activeIndex = 0;
        this.form.controls['stepOptions'].setValue(this.activeIndex.toString());
        this.componentRef?.destroy();
        this.detailsData = this.transferAuth?.detailsData ?? [];
        this.buildDynamicForm();
        this.ngOnChanges();
        this.isLoading = false;
    }

    onActiveTabIndexChange(event: any){
      this.activeIndex = Number(event.value);
      this.showDynamicForm = this.showTransferAuthDetails ? this.activeIndex == 1 : this.activeIndex == -1;
      this.componentRef.instance.showForm = this.showDynamicForm;
      this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onNext() {
        this.form.markAllAsTouched();
        if (this.activeIndex == 0) {
            let originLocationId = this.form.get('originLocationId')?.value;
            let destinyLocationId = this.form.get('destinyLocationId')?.value;
            if (originLocationId == destinyLocationId) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('messages.error_locations'),
                    detail: this.translateService.instant('messages.error_origin_destiny_locations')
                });
                return;
            }
            let plannedDepartureDateTime: Date = this.form.get('plannedDepartureDateTime')?.value;
            let plannedArrivalDateTime: Date = this.form.get('plannedArrivalDateTime')?.value;
            if (plannedDepartureDateTime >= plannedArrivalDateTime) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('messages.error_dates'),
                    detail: this.translateService.instant('messages.error_departure_arrival_dates')
                });
                return;
            }
        }
        if (this.showTransferAuthDetails ? this.activeIndex == 2 : this.activeIndex == 1) {
            if (this.selectedPrisoners.length == 0) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('messages.error_list_of_prisoners'),
                    detail: this.translateService.instant('messages.error_at_least_one_subject')
                });
                return;
            }
        }
        if (this.showTransferAuthDetails ? this.activeIndex === 3 : this.activeIndex === 2) {
            if (this.selectedResponsibleUsers.length == 0) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('messages.error_list_of_responsible_personel'),
                    detail: this.translateService.instant('messages.error_at_least_one_subject')
                });
                return;
            }
        }
        if (this.form.valid) {
            this.activeIndex += 1;
            this.showDynamicForm = this.showTransferAuthDetails ? this.activeIndex == 1 : this.activeIndex == -1;
            this.componentRef.instance.showForm = this.showDynamicForm;
            this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
        }
    }

    onBack() {
        this.activeIndex -= 1;
        this.showDynamicForm = this.showTransferAuthDetails ? this.activeIndex == 1 : this.activeIndex == -1;
        this.componentRef.instance.showForm = this.showDynamicForm;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onCancel() {
        this.transferAuth = undefined;
        this.form.reset();
        this.selectAuthReason = undefined;
        this.selectedOriginLocationId = undefined;
        this.selectedDestinyLocationId = undefined;
        this.detailItemForm.reset();
        this.detailItemRowForm.reset();
        this.clonedDetailItem = {};
        this.selectedOriginLocationId = undefined;
        this.selectedDestinyLocationId = undefined;
        this.authUser = undefined;
        this.cancelUser = undefined;
        this.authSignatureData = undefined;
        this.cancelSignatureData = undefined;
        this.hasAuthSignature = false;
        this.hasCancelSignature = false;
        this.isLoading = false;
    }

    onClose() {
        this.onCancel();
        let result: OperationStatus = {
            status: Status.SUCCESS,
            message: 'CLOSE'
        }
        this.operationStatus.emit(result);
    }

    // Details Table

    addDetailItem() {
        if (this.detailItemForm.valid) {
            let detailItem: TransferAuthDetailEntity = {
                key: this.detailItemForm.get('key')?.value,
                value: this.detailItemForm.get('value')?.value,
                createdBy: this.localStorageService.getUser()?.id,
                updatedBy: this.localStorageService.getUser()?.id,
                createdAt: undefined,
                updatedAt: undefined,
            };
            this.detailsList.push(detailItem);
            this.detailItemForm.reset();
        }
    }

    // Details Table Row Edit

    onDetailItemRowEditInit(item: TransferAuthDetailEntity) {
        this.clonedDetailItem[item.id?.toString() as string] = { ...item };
        this.detailItemRowForm.get('key')?.setValue(item.key);
        this.detailItemRowForm.get('value')?.setValue(item.value);
    }

    onDetailItemRowEditSave(item: TransferAuthDetailEntity, index: number) {
        if (this.detailItemRowForm.valid) {
            this.detailsList[index].key = this.detailItemRowForm.get('key')?.value;
            this.detailsList[index].value = this.detailItemRowForm.get('comments')?.value;
            delete this.clonedDetailItem[item.id?.toString() as string];
            this.detailItemRowForm.reset();
        }
    }

    onDetailItemRowEditCancel(item: TransferAuthDetailEntity, index: number) {
        this.detailsList[index] = this.clonedDetailItem[item.id?.toString() as string];
        delete this.clonedDetailItem[item.id?.toString() as string];
        this.detailItemRowForm.reset();
    }

    onDetailItemRowRemoveItem(item: TransferAuthDetailEntity) {
        this.detailsList = this.detailsList.filter((val, i) => val.id !== item.id);
    }

    saveTransferAuth() {
        this.isLoading = true;
        if (this.form.invalid || this.selectedPrisoners.length == 0 || this.selectedResponsibleUsers.length == 0) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('messages.error_transfer_authorization'),
                detail: this.translateService.instant('messages.all_required_fields_details_prisoners_responsible_personnel')
            });
            this.isLoading = false;
            return;
        }

        let transferAuthToSave: TransferAuthEntity = this.transferAuth ?? {};
        transferAuthToSave.id = this.transferAuth?.id ?? undefined;
        transferAuthToSave.authCode = this.form.get('authCode')?.value;
        transferAuthToSave.authReason = this.form.get('authReason')?.value.value;
        transferAuthToSave.authRegistrationDate = this.operationType == OperationType.INSERT ? new Date() : this.transferAuth?.authRegistrationDate;
        transferAuthToSave.authExpirationDate = this.calcExpirationDate(new Date());
        transferAuthToSave.originLocationId = this.form.get('originLocationId')?.value.key;
        transferAuthToSave.destinyLocationId = this.form.get('destinyLocationId')?.value.key;
        transferAuthToSave.plannedDepartureDateTime = this.form.get('plannedDepartureDateTime')?.value;
        transferAuthToSave.plannedArrivalDateTime = this.form.get('plannedArrivalDateTime')?.value;
        transferAuthToSave.details = this.detailsList;
        transferAuthToSave.listOfPrisoners = this.selectedPrisoners.map((subject) => {
            let transferAuthUserSubject: TransferAuthUserSubjectEntity = {}
            if (this.transferAuth?.listOfPrisoners?.map((userSubject) => userSubject.subjectId).includes(subject.id)) {
                transferAuthUserSubject = this.transferAuth?.listOfPrisoners?.find((userSubject) => userSubject.subjectId == subject.id)!;
                transferAuthUserSubject.userId = undefined;
                transferAuthUserSubject.updatedBy = this.localStorageService.getUser()?.id;
            }
            else {
                transferAuthUserSubject = {
                    subjectId: subject.id,
                    userSubjectType: UserSubjectEnum.SUBJECT,
                    createdBy: this.localStorageService.getUser()?.id,
                    updatedBy: this.localStorageService.getUser()?.id,
                    createdAt: undefined,
                    updatedAt: undefined,
                };
            }
            return transferAuthUserSubject;
        });
        transferAuthToSave.listOfResponsibleUsers = this.selectedResponsibleUsers.map((subject) => {
            let transferAuthUserSubject: TransferAuthUserSubjectEntity = {}
            if (this.transferAuth?.listOfResponsibleUsers?.map((userSubject) => userSubject.subjectId).includes(subject.id)) {
                transferAuthUserSubject = this.transferAuth?.listOfResponsibleUsers?.find((userSubject) => userSubject.subjectId == subject.id)!;
                transferAuthUserSubject.userId = undefined;
                transferAuthUserSubject.updatedBy = this.localStorageService.getUser()?.id;
            }
            else {
                transferAuthUserSubject = {
                    subjectId: subject.id,
                    userSubjectType: UserSubjectEnum.USER,
                    createdBy: this.localStorageService.getUser()?.id,
                    updatedBy: this.localStorageService.getUser()?.id,
                    createdAt: undefined,
                    updatedAt: undefined,
                };
            }
            return transferAuthUserSubject;
        });
        transferAuthToSave.observations = this.form.get('observations')?.value ?? undefined;
        transferAuthToSave.status = this.getTransferAuthStatus();
        transferAuthToSave.detailsData = this.detailsData ?? '[]';
        transferAuthToSave.authUserId = this.authSignatureData?.id ?? undefined;
        transferAuthToSave.authUserNumId = this.authSignatureData?.numId ?? undefined;
        transferAuthToSave.authUserSignatureDate = this.authSignatureData?.date ?? undefined;
        transferAuthToSave.authUserSignatureTech = this.authSignatureData?.tech ?? undefined;
        transferAuthToSave.isCompleted = false;
        transferAuthToSave.isCancelled = this.hasCancelSignature;
        transferAuthToSave.cancelDate = this.cancelSignatureData?.date ?? undefined;
        transferAuthToSave.cancelReason = this.cancelSignatureData?.reason ?? undefined;
        transferAuthToSave.cancelObservation = this.cancelSignatureData?.observation ?? undefined;
        transferAuthToSave.cancelUserId = this.cancelSignatureData?.id ?? undefined;
        transferAuthToSave.cancelUserNumId = this.cancelSignatureData?.numId ?? undefined;
        transferAuthToSave.cancelUserSignatureDate = this.cancelSignatureData?.date ?? undefined;
        transferAuthToSave.cancelUserSignatureTech = this.cancelSignatureData?.tech ?? undefined;
        transferAuthToSave.createdBy = this.transferAuth?.createdBy ?? this.localStorageService.getUser()?.id;
        transferAuthToSave.updatedBy = this.localStorageService.getUser()?.id;
        transferAuthToSave.createdAt = this.isValidDate(this.transferAuth?.createdAt?.toString()!) ? this.transferAuth?.createdAt : undefined;
        transferAuthToSave.updatedAt = undefined;

        this.loggerService.debug(transferAuthToSave);
        if (transferAuthToSave.id && this.operationType == OperationType.UPDATE) {
            this.getTransferAuthByIdUseCase.execute({ id: transferAuthToSave.id }).then(
                (data) => {
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(transferAuthToSave) },
                    ];
                    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_TRANSFER_AUTHORIZATION, ReasonActionTypeEnum.UPDATE, () => {
                        this.updateTransferAuthUseCase.execute({ transferAuth: transferAuthToSave }).then(
                            (data) => {
                                this.transferAuth = data;
                                let status: OperationStatus = {
                                    status: Status.SUCCESS,
                                    message: this.translateService.instant('messages.success_transfer_authorization_updated')
                                }
                                this.operationStatus.emit(status);
                            },
                            (e) => {
                                this.loggerService.error(e);
                            }
                        )
                        .finally(() => {
                            this.isLoading = false;
                        });
                    }, at_attributes);
                },
                (e) => {
                    this.loggerService.error(e);
                    let responseStatus: OperationStatus = {
                        status: Status.ERROR,
                        message: e.message
                    }
                    this.operationStatus.emit(responseStatus);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.RECORD_ID, value: transferAuthToSave.id!.toString() },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_TRANSFER_AUTHORIZATION_BY_ID, 0, 'ERROR', '', at_attributes);
                }
            );
        }
        else {
            const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(transferAuthToSave) },
            ];
            this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_TRANSFER_AUTHORIZATION, ReasonActionTypeEnum.CREATE, () => {
                this.createTransferAuthUseCase.execute({ transferAuth: transferAuthToSave }).then(
                    (data) => {
                        this.transferAuth = data;
                        let status: OperationStatus = {
                            status: Status.SUCCESS,
                            message: this.translateService.instant('messages.success_transfer_authorization_created')
                        }
                        this.operationStatus.emit(status);
                    },
                    (e) => {
                        this.loggerService.error(e);
                    }
                )
                .finally(() => {
                    this.isLoading = false;
                });
            }, at_attributes);
        }
    }

    /* Dynamic Form Functions */

    buildDynamicForm() {
        const customFields: CustomFieldModel[] = this.managerSettings?.continued1?.prisonsSettings?.transferAuthConfig?.transferAuthDetailFields || [];
        if (customFields.length != 0) {
            this.loggerService.info('There are Fields to submit');
            this.formAttributes = [];
            // Classify the attributes of the action
            customFields.forEach((customField: CustomFieldModel) => {
                if (customField.type == CustomFieldTypes.INPUT || customField.type == CustomFieldTypes.DROPDOWN || customField.type == CustomFieldTypes.TOGGLE) {
                    let type: CustomFieldTypes = customField.type;
                    let required = false;
                    let maxCharacters = 0;
                    let options: string[] = [];
                    customField.fieldData?.forEach((data: any) => {
                        if (data.key.includes('required-checkbox')) {
                            required = data.value == 'true';
                        }
                        if (data.key.includes('options-listbox')) {
                            options = JSON.parse(data.value);
                        }
                        if (data.key.includes('max-characters-textbox')) {
                            maxCharacters = data.value;
                        }
                    });
                    if (type == CustomFieldTypes.INPUT && maxCharacters > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 25)) {
                        type = CustomFieldTypes.TEXTAREA;
                    }
                    const formAttribute: DynamicFormAttributes = {
                        type: type,
                        label: customField.name,
                        key: customField.parameter,
                        value: this.detailsData?.find((detail: DetailsDataEntity) => detail.parameter == customField.parameter)?.value || '',
                        detailId: this.detailsData?.find((detail: DetailsDataEntity) => detail.parameter == customField.parameter)?.id || '',
                        required: required,
                        options: options,
                        disabled: !this.canReadAndWrite,
                        group: customField.group?.name ?? '',
                        groupRole: customField.group?.roleId ?? '',
                        tooltip: customField.description ?? '',
                        translations: this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'transferAuthDetailFields')?.translations?.find((t: TranslationModel) => t.key === customField.parameter) || new TranslationModel(),
                    };
                    this.formAttributes.push(formAttribute);
                }
            });
            this.loggerService.debug(this.formAttributes);
            // If the action requires to show a form, render the dynamic form
            if (this.formAttributes.length > 0) {
                this.loggerService.info('There is a Form to Render');
                const formFields = this.convertFormControlArrayToObject(this.formAttributes);
                this.loggerService.debug(formFields);
                this.componentRef = this.dynamicFormContent.createComponent(DynamicFormComponent);
                this.componentRef.instance.controlsConfig = formFields;
                this.componentRef.instance.showForm = this.showDynamicForm;
                this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && this.userIsVerified && this.editEnabled;
                // this.componentRef.instance.userSubjectRoles = this.userSubject?.roles;
                this.componentRef.instance.groupTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'transferAuthDetailFieldGroups')?.translations || [];

                // Subscribe to the form submission event
                this.componentRef.instance.formSubmitted.subscribe((formData: any) => {
                    this.loggerService.debug('Form Data:');
                    this.loggerService.debug(formData);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(formData) }
                    ];
                    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_TRANSFER_AUTHORIZATION, ReasonActionTypeEnum.UPDATE, () => { this.updateDetails(formData); }, at_attributes, false);
                });
                // Subscribe to the form modified event
                this.componentRef.instance.formModified.subscribe((modified: boolean) => {
                    this.updateModified(modified);
                });
            }
        }
    }

    toggleDynamicForm() {
        if (this.componentRef) {
            this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && this.userIsVerified;
            this.componentRef.instance.toggleFormState(this.canReadAndWrite && this.userIsVerified);
        }
    }

    /* Details */
    updateDetails(formData: any) {
        this.loggerService.debug("Updating Details");
        Object.keys(formData).forEach((key: string) => {
            const dynamicFormField = this.formAttributes.find((attr: DynamicFormAttributes) => attr.key == key);
            const foundDetail = this.detailsData?.find((detail: DetailsDataEntity) => detail.id == dynamicFormField?.detailId);

            let detail: DetailsDataEntity = new DetailsDataEntity();
            //id
            detail.id = foundDetail ? foundDetail.id : (this.detailsData?.length! + 1).toString();
            //name
            detail.name = dynamicFormField?.label;
            //parameter
            detail.parameter = key;
            //value
            detail.value = formData[key].toString();
            if (foundDetail) {
                this.detailsData?.splice(this.detailsData?.indexOf(foundDetail), 1, detail);
            } else {
                this.detailsData?.push(detail);
            }
        });
        this.updateModified(false);
        // this.messageService.add({
        //     severity: 'success',
        //     summary: this.translateService.instant('content.successTitle'),
        //     detail: this.translateService.instant('messages.success_general'),
        //     life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        // });
    }

    isRequiredField(field: string, form: FormGroup = this.form): boolean {
        return this.validatorService.isRequiredField(form, field);
    }

    isValid(field: string, form: FormGroup = this.form): boolean {
        return this.validatorService.isValidField(form, field);
    }

    isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    /* Search */
    onFilter(event: any, dt: Table) {
        if (!event.filters['birthdate'].value) {
            this.rangeDates = null;
            this.dateFormGroup.reset();
        }
    }

    /* Date Range Filter */
    applyDateRangeFilter(dt: Table, field: string) {
        this.rangeDates = this.dateFormGroup.get('date')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    getSelection(): any[] {
        if (!this.canReadAndWrite || !this.userIsVerified || !this.editEnabled) {
            this.selectedPrisoners = this.transferAuth?.listOfPrisoners?.map((userSubject) => this.listOfAllSubjects.find((subject) => subject.id == userSubject.subjectId)).filter((subject): subject is SubjectEntity => subject !== undefined) ?? [];
            this.selectedResponsibleUsers = this.transferAuth?.listOfResponsibleUsers?.map((userSubject) => this.listOfAllSubjects.find((subject) => subject.id == userSubject.subjectId)).filter((subject): subject is SubjectEntity => subject !== undefined) ?? [];
        }
        return (this.showTransferAuthDetails ? this.activeIndex === 2 : this.activeIndex === 1) ? this.selectedPrisoners : this.selectedResponsibleUsers;
    }

    setSelection(event: any): void {
        if (this.showTransferAuthDetails ? this.activeIndex === 2 : this.activeIndex === 1) {
            this.selectedPrisoners = [... event];
        } else {
            this.selectedResponsibleUsers = [... event];
        }
        if (!this.editEnabled) {
            let selectedPrisoners = this.transferAuth?.listOfPrisoners?.map((userSubject) => this.listOfAllSubjects.find((subject) => subject.id == userSubject.subjectId)).filter((subject): subject is SubjectEntity => subject !== undefined) ?? [];
            this.selectedPrisoners = [... selectedPrisoners];
            let selectedResponsibleUsers = this.transferAuth?.listOfResponsibleUsers?.map((userSubject) => this.listOfAllSubjects.find((subject) => subject.id == userSubject.subjectId)).filter((subject): subject is SubjectEntity => subject !== undefined) ?? [];
            this.selectedResponsibleUsers = [... selectedResponsibleUsers];
        }
    }

    // Convert the list of roles to string
    listOfRolesToString(roles?: RoleEntity[]): string {
        let stringOfRoles: string = "";

        if (roles && roles?.length > 0) {
            stringOfRoles = roles?.map(role => role.name == 'SYSTEM_USER' ? this.translateService.instant('role_names.SYSTEM_USER') : role.name).join(', ');
        }

        return stringOfRoles
    }

    calcExpirationDate(date: Date): Date {
        let expirationDate = new Date(date);
        let expirationTime = this.managerSettings?.continued1?.expirations?.prisonsTransferAuthExpiration?.expirationTime ?? 0;
        let expirationTimeIn = this.managerSettings?.continued1?.expirations?.prisonsTransferAuthExpiration?.expirationTimeIn ?? 'NONE';
        switch (expirationTimeIn) {
            case 'SECONDS':
                expirationDate.setSeconds(expirationDate.getSeconds() + expirationTime);
                break;
            case 'MINUTES':
                expirationDate.setMinutes(expirationDate.getMinutes() + expirationTime);
                break;
            case 'HOURS':
                expirationDate.setHours(expirationDate.getHours() + expirationTime);
                break;
            case 'DAYS':
                expirationDate.setDate(expirationDate.getDate() + expirationTime);
                break;
            default:
                break;
        }
        return expirationDate;
    }

    // getAuthSignatureSubjectByNumId(numId: string) {
    //     this.loggerService.debug("getAuthSignatureSubjectByNumId", numId);
    //     this.isLoading = true;
    //     if (numId) {
    //         this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
    //             (data) => {
    //                 this.authUser = data;
    //                 this.authSignatureData.id = this.authUser.id!;
    //                 this.hasAuthSignature = true;
    //             },
    //             (e) => {
    //                 this.loggerService.error(e);
    //             }
    //         )
    //         .finally(() => {
    //             this.ngOnChanges();
    //             this.isLoading = false;
    //         });
    //     }
    //     else {
    //         this.authSignatureData = undefined;
    //         this.isLoading = false;
    //     }
    // }

    // getCancelSignatureSubjectByNumId(numId: string) {
    //     this.isLoading = true;
    //     if (numId) {
    //         this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
    //             (data) => {
    //                 this.cancelUser = data;
    //                 this.cancelSignatureData.id = this.cancelUser.id!;
    //                 this.hasCancelSignature = true;
    //             },
    //             (e) => {
    //                 this.loggerService.error(e);
    //             }
    //         )
    //         .finally(() => {
    //             this.ngOnChanges();
    //             this.isLoading = false;
    //         });
    //     }
    //     else {
    //         this.cancelSignatureData = {} as any;
    //         this.isLoading = false;
    //     }
    // }

    findNodeById(nodes: TreeNode[], targetId: string): TreeNode | undefined {
        for (let node of nodes || []) {
            if (node.key === targetId) {
                return node; // Return the node if the ID matches
            }
            const foundNode = this.findNodeById(node.children!, targetId); // Recursive search in children
            if (foundNode) {
                return foundNode; // Return the node if found in children
            }
        }
        return undefined; // Return null if not found
    }

    userAuthSignatureResult(event: BioSignatureResult) {
        this.authSignatureData = {
            id: event.id,
            numId: event.numId,
            tech: event.tech,
            date: event.date,
        }
        this.hasAuthSignature = true;
    }

    userCancelSignatureResult(event: BioSignatureResult) {
        this.cancelSignatureData = {
            id: event.id,
            numId: event.numId,
            tech: event.tech,
            date: event.date,
            reason: event.reason,
            observation: event.observation,
        }
        this.hasCancelSignature = true;
    }

    // Widget

    widgetSearch(tech: string, authSignatureWidget: boolean) {
        this.authSignatureWidget = authSignatureWidget;
        if (!authSignatureWidget) {
            this.loggerService.debug(this.selectCancelReason);
            if (!this.selectCancelReason) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('titles.important'),
                    detail: this.translateService.instant('messages.error_must_select_cancel_reason'),
                    life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
                })
                return;
            }
        }
        this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
        this.tech = tech;
        if (this.konektorProperties?.verificationEnabled) {
            if (this.konektorProperties?.verificationSubjectId) {
                this.userNumId = this.konektorProperties.verificationSubjectId;
                this.startSearch();
            }
            else {
                this.showEnterNumId = true;
                return;
            }
        }
        else {
            // this.userNumId = '';
            this.startSearch();
        }
    }

    startSearch() {
        let allowWidget = false;
        switch (this.tech) {
            case 'fingerprint':
                allowWidget = this.managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (this.userNumId == '' ? this.managerSettings?.allowSearch?.dactilar == true : this.managerSettings?.allowVerify?.dactilar == true);
                break;
            case 'facial':
                allowWidget = this.managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (this.userNumId == '' ? this.managerSettings?.allowSearch?.facial == true : this.managerSettings?.allowVerify?.facial == true);
                break;
            case 'iris':
                allowWidget = this.managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (this.userNumId == '' ? this.managerSettings?.allowSearch?.iris == true : this.managerSettings?.allowVerify?.iris == true);
                break;
        }
        if (allowWidget) {
            this.searchReady = true;
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('titles.access_denied'),
                detail: this.translateService.instant('messages.error_technology_not_allowed'),
                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    onWidgetSearchResult(event: WidgetResult) {
        if (!this.searchReady) {
            return;
        }
        this.loggerService.debug(event);
        switch (event.action) {
            case "verify":
                this.searchReady = false;
                if (event.result == "success") {
                    if (event.data.isMatched) {
                        if (this.authSignatureWidget) {
                            this.authSignatureData = {
                                id: '',
                                numId: this.userNumId,
                                tech: event.data.tech,
                                date: new Date(),
                            }
                            // this.getAuthSignatureSubjectByNumId(this.userNumId);
                        }
                        else {
                            this.cancelSignatureData = {
                                id: undefined,
                                numId: this.userNumId,
                                tech: event.data.tech,
                                date: new Date(),
                                reason: this.selectCancelReason?.value.toString() ?? undefined,
                                observation: this.cancelObservation ?? undefined,
                            }
                            this.transferAuth!.isCancelled = true;
                            // this.getCancelSignatureSubjectByNumId(this.userNumId);
                        }
                        const at_attributes = [
                            { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                        ]
                        this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.USER_VERIFY, 0, 'SUCCESS', event.data.tech, at_attributes);
                    }
                }
                break;
            case 'search':
                const responseData = event.data.nId;
                if (responseData) {
                    if (this.authSignatureWidget) {
                        this.authSignatureData = {
                            id: '',
                            numId: responseData,
                            tech: this.tech,
                            date: new Date(),
                        }
                        // this.getAuthSignatureSubjectByNumId(responseData);
                    }
                    else {
                        this.cancelSignatureData = {
                            id: '',
                            numId: responseData,
                            tech: this.tech,
                            date: new Date(),
                            reason: this.selectCancelReason?.value.toString() ?? undefined,
                            observation: this.cancelObservation ?? undefined,
                        }
                        this.transferAuth!.isCancelled = true;
                        // this.getCancelSignatureSubjectByNumId(responseData);
                    }
                    const at_attributes = [
                        { name: AuditTrailFields.REGISTRATION_CODE, value: 'IDN_BIO' },
                    ]
                    this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.USER_SEARCH, 0, 'SUCCESS', this.tech, at_attributes);
                }
                this.searchReady = false;
                break;
            case "process":
                break;
            case "close_search":
            case "error":
                this.searchReady = false;
                this.userNumId = '';
                this.tech = '';
                break;
        }
    }

    closeNumIdDialog() {
        this.formNumId.reset();
        this.showEnterNumId = false;
    }

    onNumIdDialogSubmit() {
        this.formNumId.markAllAsTouched();
        const numId = this.formNumId.get('numId')?.value;
        if (numId && numId != '') {
            this.formNumId.reset();
            this.showEnterNumId = false;
            this.userNumId = numId;
            this.startSearch();
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("titles.important"),
                detail: this.translateService.instant("messages.ERROR_INVALID_NUMID"),
                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
            })
        }
    }

    getTechIcon(tech: string): string {
        let fingerprintIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/fingerprint.svg";
        let facialIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/facial.svg";
        let irisIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/iris.svg";
        switch (tech) {
            case 'fingerprint':
                return fingerprintIcon;
            case 'facial':
                return facialIcon;
            case 'iris':
                return irisIcon;
            default:
                return '';
        }
    }

    getTransferAuthStatus(): AuthStatus {
        if (this.transferAuth?.status != '' && this.transferAuth?.status != null && this.transferAuth?.status != undefined) {
            if (this.transferAuth?.isCompleted || this.transferAuth?.isCancelled) {
                return this.transferAuth?.status as AuthStatus;
            }
            if (this.transferAuth.status == AuthStatus.IN_PROGRESS || this.transferAuth.status == AuthStatus.EXPIRED) {
                return this.transferAuth?.status as AuthStatus;
            }
        }
        if (this.hasCancelSignature) {
            return AuthStatus.CANCELLED;
        }
        if (this.hasAuthSignature) {
            return AuthStatus.AUTHORIZED;
        }
        return AuthStatus.CREATED;
    }

    /**
     * Convert an array of form controls to an object
     * @param arr Array of form controls
     * @returns Object with the form controls
     */
    convertFormControlArrayToObject(arr: any[]) {
      const result: any = {};
      arr.forEach(item => {
        result[item.key] = item;
      });
      return result;
    }

    updateModified(modified: boolean) {
      this.modified = modified;
    }

    private clearRejectTimeout() {
      if (this.rejectTimeout) {
        clearTimeout(this.rejectTimeout);
      }
      this.rejectTimeout = null;
    }

    getLabel(key: string, catalogParameter: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter)?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}
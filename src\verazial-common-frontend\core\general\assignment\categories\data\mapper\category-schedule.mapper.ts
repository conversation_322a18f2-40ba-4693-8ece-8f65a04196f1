import { CategoryScheduleGrpc } from "src/verazial-common-frontend/core/generated/category/group_category_pb";
import { CategoryScheduleEntity } from "../../domain/entity/category-schedule.entity";
import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { toArrayOfDayTime, toDateGrpc, toDateMode, toListOfDayTime } from "../../common/converter/category.converter";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";
import { DayTimeScheduleMapper } from "./day-time-schedule.mapper";

export class CategoryScheduleMapper extends Mapper<CategoryScheduleGrpc, CategoryScheduleEntity> {
    dayTimeSchedule = new DayTimeScheduleMapper();
    override mapFrom(param: CategoryScheduleGrpc): CategoryScheduleEntity {
        let categorySchedule = new CategoryScheduleEntity();

        categorySchedule.id = param.getId();
        categorySchedule.groupCategoryId = param.getGroupcategoryid();
        categorySchedule.hasDateRange = param.getHasdaterange();
        categorySchedule.timeInit = new Date(param.getTimeinit()?.getSeconds()!! * 1000 + Math.round(param.getTimeinit()?.getNanos()!! / 1e6));
        categorySchedule.timeEnd = new Date(param.getTimeend()?.getSeconds()!! * 1000 + Math.round(param.getTimeend()?.getNanos()!! / 1e6));
        categorySchedule.dateInit = toDateMode(param.getDateinit());
        categorySchedule.dateEnd = toDateMode(param.getDateend());
        categorySchedule.dayTime = param.getDaytime() ? toListOfDayTime(param.getDaytime()) : undefined;

        return categorySchedule;
    }
    override mapTo(param: CategoryScheduleEntity): CategoryScheduleGrpc {
        let categorySchedule = new CategoryScheduleGrpc();

        categorySchedule.setId(param.id!);
        categorySchedule.setGroupcategoryid(param.groupCategoryId!);
        categorySchedule.setHasdaterange(param.hasDateRange!);
        categorySchedule.setTimeinit(dateToTimestamp(param.timeInit!));
        categorySchedule.setTimeend(dateToTimestamp(param.timeEnd!));
        categorySchedule.setDateinit(toDateGrpc(param.dateInit));
        categorySchedule.setDateend(toDateGrpc(param.dateEnd));
        categorySchedule.setDaytime(toArrayOfDayTime(param.dayTime));

        return categorySchedule;
    }

}
import { NgModule } from "@angular/core";
import { SubjectFilesComponent } from "./subject-files/subject-files.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { AccordionModule } from "primeng/accordion";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { DropdownModule } from "primeng/dropdown";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { FloatLabelModule } from "primeng/floatlabel";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { ToastModule } from "primeng/toast";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { InputTextModule } from "primeng/inputtext";
import { FileUploadModule } from 'primeng/fileupload';
import { BadgeModule } from "primeng/badge";
import { TooltipModule } from "primeng/tooltip";

@NgModule({
  declarations: [
    SubjectFilesComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Forms */
    ReactiveFormsModule,
    FormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG Modules */
    AccordionModule,
    DialogModule,
    ButtonModule,
    DropdownModule,
    ProgressSpinnerModule,
    FloatLabelModule,
    ConfirmDialogModule,
    ToastModule,
    InputTextModule,
    FileUploadModule,
    BadgeModule,
    TooltipModule,
    /* Custom Modules */
    EmptyModule,
  ],
  exports: [
    SubjectFilesComponent
  ]
})
export class SubjectFilesModule { }
import { NgModule } from "@angular/core";
import { SubjectFilesComponent } from "./subject-files/subject-files.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { AccordionModule } from "primeng/accordion";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { DropdownModule } from "primeng/dropdown";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { FloatLabelModule } from "primeng/floatlabel";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { ToastModule } from "primeng/toast";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { InputTextModule } from "primeng/inputtext";
import { FileUploadModule } from 'primeng/fileupload';
import { BadgeModule } from "primeng/badge";
import { TooltipModule } from "primeng/tooltip";
import { TableModule } from "primeng/table";
import { LoadingSpinnerModule } from "src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module";

@NgModule({
  declarations: [
    SubjectFilesComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Forms */
    ReactiveFormsModule,
    FormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG Modules */
    AccordionModule,
    DialogModule,
    ButtonModule,
    DropdownModule,
    ProgressSpinnerModule,
    FloatLabelModule,
    ConfirmDialogModule,
    ToastModule,
    InputTextModule,
    FileUploadModule,
    BadgeModule,
    TooltipModule,
    TableModule,
    /* Custom Modules */
    EmptyModule,
    LoadingSpinnerModule,
  ],
  exports: [
    SubjectFilesComponent
  ]
})
export class SubjectFilesModule { }
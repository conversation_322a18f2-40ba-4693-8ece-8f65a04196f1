import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { KonektorPropertiesEntity } from 'src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { GetSettingsByApplicationUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { GetAllRolesUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import { CreateStaticResourceEntity } from 'src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity';
import { CreateStaticResourceUseCase } from 'src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case';
import { GetStaticResourcesBySubjectIdAndNameUseCase } from 'src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case';
import { SubjectEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { GetSubjectByNumIdUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case';
import { SaveSubjectUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject.use-case';
import { UserEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/user.entity';
import { DeleteUserByIdUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/delete-user-by-id.use-case';
import { GetAllUsersUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-all-users.use-case';
import { SaveUserUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/save-user.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { UserSubjectActionType } from 'src/verazial-common-frontend/core/models/user-subject-action-type.enum';
import { UserSubjectEnum } from 'src/verazial-common-frontend/core/models/user-subject.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { UnenrollIdentityService } from 'src/verazial-common-frontend/core/services/unenroll-identity.service';
import { environment } from 'src/environments/environment';
import { PasswordRecoveryRequestModel } from 'src/verazial-common-frontend/core/general/user/domain/entity/password-recovery.entity';
import { Location } from '@angular/common';
import { PasswordRecoveryRequestUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/password-recovery-request.use-case';
import { GetUsersRequestEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/get-users-request.entity';
import { SortOrderEnum } from 'src/verazial-common-frontend/core/models/sort-order.enum';
import { GetUsersUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-users.use-case';
import { GetNumberOfUsersUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-number-of-users.use-case';
import { GetUserByNumIdUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-user-by-num-id.use-case';
import { GetUserByEmailUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-user-by-email.use-case';
import { CheckTokenUseCase } from 'src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case';

@Component({
  selector: 'app-user-general-page',
  templateUrl: './user-general-page.component.html',
  styleUrl: './user-general-page.component.css',
  providers: [MessageService]
})
export class UserGeneralPageComponent implements OnInit, OnDestroy {

  /* Page State */
  type = UserSubjectEnum.USER;

  /* Settings */
  managerSettings?: GeneralSettings;
  konektorProperties?: KonektorPropertiesEntity;

  /* Flags */
  isLoading: boolean = false;

  /* New User */
  userData: UserEntity | undefined;
  profilePicPlaceholder: string = 'verazial-common-frontend/assets/images/all/UserPic.svg';
  picHistoryName: string = 'profile-picture-history';

  /* List of user data */
  listOfUser: UserEntity[] = [];
  getUsersRequest = new GetUsersRequestEntity();
  totalRecords: number = 0;
  listRoles: RoleEntity[] = [];
  listOfAllRoles: RoleEntity[] = [];
  useLazyLoad: boolean = false;

  /* Access */
  access_identifier: string = AccessIdentifier.USER;
  canReadAndWrite: boolean = false;
  readOnly: boolean = false;

  /* User in Session */
  userIsVerified: boolean = false;

  private rejectTimeout: any;

  constructor(
    private router: Router,
    private location: Location,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private translate: TranslateService,
    private checkPermissions: CheckPermissionsService,
    private getAllUsersUseCase: GetAllUsersUseCase,
    private getUsersUseCase: GetUsersUseCase,
    private saveUserUseCase: SaveUserUseCase,
    private deleteUserByIdUseCase: DeleteUserByIdUseCase,
    private auditTrailService: AuditTrailService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private getAllRolesUseCase: GetAllRolesUseCase,
    private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
    private saveSubjectUseCase: SaveSubjectUseCase,
    private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
    private createStaticResourceUseCase: CreateStaticResourceUseCase,
    private passwordRecoveryRequestUseCase: PasswordRecoveryRequestUseCase,
    private getNumberOfUsersUseCase: GetNumberOfUsersUseCase,
    private getUserByNumIdUseCase: GetUserByNumIdUseCase,
    private getUserByEmailUseCase: GetUserByEmailUseCase,
    private checkTokenUseCase: CheckTokenUseCase,
  ) { }

  async ngOnInit() {
    this.isLoading = true;
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
    if(!this.canReadAndWrite) {
      this.readOnly = this.checkPermissions.hasReadPermissions(this.access_identifier);
    }
    this.refreshSettings(() => {
      this.getAllRoles(() => {
        this.getNumberOfUsersUseCase.execute().then(
          (data) => {
            this.useLazyLoad = data > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000);
            this.totalRecords = data;
            this.getUsersRequest.offset = 0;
            this.getUsersRequest.limit = 10;
            if (this.useLazyLoad) {
              this.getUsersRequest.sortField = 'numId';
              this.getUsersRequest.sortOrder = SortOrderEnum.ASC;
            }
            this.getAllUsers();
          },
          (e) => {
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_NUMBER_OF_USERS, 0, 'ERROR', '', at_attributes);
          }
        );
      });
    });
    if (this.localStorageService.isUserVerified()) {
      await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
        (response) => {
          this.userIsVerified = !response;
        },
        (e) => {
          this.userIsVerified = false;
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.CHECK_TOKEN, 0, 'ERROR', '', at_attributes);
        }
      );
    }
    else {
      this.userIsVerified = false;
    }
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.clearRejectTimeout();
  }

  // Get all users
  getAllUsers() {
    this.isLoading = true;
    if (this.useLazyLoad) {
      this.getUsersUseCase.execute(this.getUsersRequest).then(
        (data) => {
          this.loggerService.debug(data);
          let users = data?.users ? [...data.users.filter(v => v.email != "" || v.email != null)] : [];
          this.listOfUser = users;
          this.totalRecords = data?.totalRecords!;
        },
        (e) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: `${this.translate.instant('messages.error_retrieving_users')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_USERS, 0, 'ERROR', '', at_attributes);
        }
      )
      .finally(() => {
        this.isLoading = false;
      });
    }
    else {
      this.getNumberOfUsersUseCase.execute().then(
        (data) => {
          this.useLazyLoad = data > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000);
          this.totalRecords = data;
        },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_NUMBER_OF_USERS, 0, 'ERROR', '', at_attributes);
        }
      )
      .finally(() => {
        this.getAllUsersUseCase.execute({ offset: 0, limit: (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000) }).then(
          (data) => {
            this.loggerService.debug(data);
            let users = data ? [...data.filter(v => v.email != "" || v.email != null)] : [];
            this.listOfUser = users;
          },
          (e) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('content.errorTitle'),
              detail: `${this.translate.instant('messages.error_retrieving_users')}: ${e.message}`,
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_USERS, 0, 'ERROR', '', at_attributes);
          }
        )
        .finally(() => {
          this.isLoading = false;
        });
      });
    }
  }

  onTableLazyLoadEvent(event: GetUsersRequestEntity) {
    this.getUsersRequest = event;
    this.getAllUsers();
  }

  getAllRoles(_callback: Function) {
    this.getAllRolesUseCase.execute().then(
      (data) => {
        this.listOfAllRoles = data;
        data.forEach(role => {
          if (role.type == RoleType.USER) {
            this.listRoles.push({
              id: role.id,
              name: role.name,
              level: role.level,
              type: role.type,
              description: role.description,
              showInMenu: role.showInMenu,
              createdAt: undefined,
              updatedAt: undefined,
            });
          }
        });
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      },
    )
    .finally(() => {
      _callback();
    });
  }

  // Adding a new user
  onSubmitAddNewUser(user: UserEntity) {
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(user) },
        { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
        { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(user.roles) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.USER, AuditTrailActions.ADD_USR, ReasonActionTypeEnum.CREATE, () => { this.onAddNewUser(user); }, at_attributes, false);
  }

  onAddNewUser(user: UserEntity) {
    user!.showPic = user?.pic != null && user?.pic != undefined && user?.pic != "" && user?.pic != this.profilePicPlaceholder;
    this.saveUserUseCase.execute({ user: user }).then(
      (data) => {
        // this.listOfUser.push(data);
        this.listOfUser = [...this.listOfUser, data];
        let userSubjectData: SubjectEntity | undefined = undefined;
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.USER_NUM_ID, value: data.numId },
          { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(data.roles) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USR, 0, 'SUCCESS', '', at_attributes);
        this.sendPasswordRecovery(user);
        this.isLoading = true;
        this.getSubjectByNumIdUseCase.execute({ numId: user.numId! })
        .then(
          (data) => {
            if (data) {
              this.loggerService.debug('User Subject Data Retrieved Successfully:');
              this.loggerService.debug(data);
              userSubjectData = data;
            }
          },
          (e) => {
            this.loggerService.error('Error Retrieving Subject Data:');
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.SUBJECT_NUM_ID, value: user.numId },
              { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(user.roles) },
              { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_NUM_ID, 0, 'ERROR', '', at_attributes);
          },
        )
        .finally(() => {
          if(!userSubjectData) {
            let userSubject = new SubjectEntity();
            userSubject.names = user.names;
            userSubject.lastNames = user.lastNames;
            userSubject.gender = user.gender;
            userSubject.pic = user.pic;
            userSubject.username = user.username;
            userSubject.email = user.email;
            userSubject.numId = user.numId;
            userSubject.birthdate = user.birthdate;
            userSubject.showPic = user.showPic;
            userSubject.roles = [];
            userSubject.roles.push(this.listOfAllRoles.find((r) => r.name == 'SYSTEM_USER')!);
            this.onAddNewSubject(userSubject);
          }
          else {
            this.isLoading = false;
            if (user.showPic) {
              this.updateUserSubjectPicHistory(userSubjectData, user.pic!);
            }
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('titles.user_creation'),
              detail: this.translate.instant('messages.user_created_success'),
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
        });
      },
      (e) => {
        this.loggerService.error(e);
        let errorMessage = 'messages.error_creating_user';
        if (e.code == 3001) errorMessage = 'messages.duplicate_user_numId_or_email';
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('titles.user_creation'),
          detail: `${this.translate.instant(errorMessage)}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const password = user.password;
        user.password = '';
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.ERROR_MESSAGE, value: this.translate.instant(errorMessage) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(user) },
          { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
          { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(user.roles) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_USR, 0, 'ERROR', '', at_attributes);
        user.password = password;
      }
    )
  }

  updateUserSubjectPicHistory(userSubject: SubjectEntity | UserEntity, pic: string) {
    this.isLoading = true;
    if (pic != null && pic != undefined && pic != "" && pic != this.profilePicPlaceholder) {
      this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: userSubject?.id!, name: this.picHistoryName }).then(
        (data) => {
          if (data) {
            const number = data.length;
            let newStaticResource = new CreateStaticResourceEntity();
            // subjectId?: string;
            newStaticResource.subjectId = userSubject?.id!;
            // name?: string;
            newStaticResource.name = this.picHistoryName;
            // number?: number;
            newStaticResource.number = number;
            // content?: string;
            newStaticResource.content = pic.replace('data:image/jpeg;base64,', '');
            // async?: boolean;
            newStaticResource.async = false;
            this.createStaticResourceUseCase.execute({ createStaticResourceRequest: newStaticResource }).then(
              () => {
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                  { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                  { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                // this.messageService.add({
                //   severity: 'success',
                //   summary: this.translate.instant('content.successTitle'),
                //   detail: this.translate.instant('messages.success_general'),
                //   life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                // });
                this.isLoading = false;
              },
              (e) => {
                this.isLoading = false;
                this.loggerService.error('Error Creating User/Subject Static Resource:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                  { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                  { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                  severity: 'error',
                  summary: this.translate.instant('titles.add_profile_pic_history'),
                  detail: `${this.translate.instant('messages.error_uploading_image')}: ${e.message}`,
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              }
            );
          }
          else {
            this.isLoading = false;
          }
        },
        (e) => {
          this.isLoading = false;
          this.loggerService.error('Error Getting User/Subject Static Resources:');
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.SUBJECT_ID, value: userSubject?.id! },
            { name: AuditTrailFields.RECORD_NAME, value: this.picHistoryName }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.add_profile_pic_history'),
            detail: `${this.translate.instant('messages.error_downloading_image')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      );
    }
    else {
      this.isLoading = false;
    }
  }

  // Add a new subject
  onAddNewSubject(subject: SubjectEntity) {
    this.isLoading = true;
    this.saveSubjectUseCase.execute({ subject: subject }).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.SUBJECT_NUM_ID, value: data.numId },
          { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(data.roles) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUB, 0, 'SUCCESS', '', at_attributes);
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('titles.subject_creation'),
          detail: this.translate.instant('messages.a_subject_was_created_based_on_the_user'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        let errorMessage = 'messages.error_creating_subject';
        if (e.code == 2000) errorMessage = 'messages.duplicate_subject_numId';
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('titles.subject_creation'),
          detail: `${this.translate.instant(errorMessage)}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.ERROR_MESSAGE, value: this.translate.instant(errorMessage) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(subject) },
          { name: AuditTrailFields.SUBJECT_NUM_ID, value: subject.numId },
          { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(subject.roles) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUB, 0, 'ERROR', '', at_attributes);
      }
    )
    .finally(() => {
      this.isLoading = false;
    });
  }

  onEditUser(user: UserEntity) {
    this.router.navigate(['/general/user/edit', 'user', user?.numId]);
  }

  navigateToUser(numId: string) {
    this.getUserByNumIdUseCase.execute({ numId: numId }).then(
      (data) => {
        if (data) {
          this.router.navigate(['general/user/edit', 'user', data.numId], { state: { verified: true } });
        }
        else {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('titles.error_dataInconsistency'),
            detail: this.translate.instant('messages.error_dataInconsistency'),
            life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
          });
        }
      },
      (e) => {
        this.loggerService.error('Error Retrieving User Data:');
        this.loggerService.error(e);
        let actionResult: string;
        let messageSummary: string;
        let messageDetail: string;
        if (e.code == 404) {
          actionResult = 'DATA_INCONSISTENCY';
          messageSummary = 'titles.error_dataInconsistency';
          messageDetail = 'messages.error_dataInconsistency';
        }
        else {
          actionResult = 'ERROR';
          messageSummary = 'content.errorTitle';
          messageDetail = 'messages.error_retrieving_user';
        }
        const at_attributes: ExtraData[] = [
          {
            name: 'error', value: JSON.stringify(e)
          },
          {
            name: 'numId', value: numId
          }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_USER_BY_NUM_ID, 0, actionResult, '', at_attributes);
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant(messageSummary),
          detail: `${this.translate.instant(messageDetail)}: ${e.message}`,
          life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  // Confirmation Dialogs
  confirmDelete(users: UserEntity[]) {
    let message: string = ""
    if (users.length == 1) {
      message = `${this.translate.instant('messages.delete_single_record')} <b>${users[0].numId}</b>?`;
    } else {
      message = this.translate.instant('messages.delete_multiple_records');
    }
    this.confirmationService.confirm({
      message: message,
      header: this.translate.instant('messages.delete_confirmation_header'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptIcon: "none",
      rejectIcon: "none",
      acceptLabel: this.translate.instant("delete"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(users) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.USER, AuditTrailActions.DEL_USR, ReasonActionTypeEnum.DELETE, () => { this.onDeleteUser(users); }, at_attributes, false);
      },
      reject: () => {
        this.clearRejectTimeout();
      }
    });

    // Set a timeout to automatically trigger the reject action after 10 seconds
    this.rejectTimeout = setTimeout(() => {
      this.confirmationService.close(); // Close the dialog
    }, (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000); // 10,000 ms = 10 seconds
  }

  // Removing multiple users from the DB
  onDeleteUser(users: UserEntity[]) {
    this.isLoading = true;

    // Create an array of promises
    const deletePromises = users.map(user =>
      this.deleteUserByIdUseCase.execute({ id: user.id! }).then(
        () => {
          this.listOfUser = this.listOfUser.filter(v => v.id !== user.id);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(user) },
            { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
            { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(user.roles) },
            { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_USR, 0, 'SUCCESS', '', at_attributes);
        },
        (e) => {
          this.loggerService.error(e);
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: `${this.translate.instant('messages.error_deleting_user')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(user) },
            { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
            { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(user.roles) },
            { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_USR, 0, 'ERROR', '', at_attributes);
        }
      )
    );

    // Wait for all delete operations to complete
    Promise.all(deletePromises).finally(() => {
      this.isLoading = false;
      this.messageService.add({
        severity: 'success',
        summary: this.translate.instant('content.successTitle'),
        detail: this.translate.instant('messages.success_general'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    });
  }

  /* Password Recovery */
  sendPasswordRecovery(user: UserEntity) {
    this.isLoading = true;
    const email = user.email;
    const request: PasswordRecoveryRequestModel = {
      email: email,
      url: window.location.origin + this.location.prepareExternalUrl(''),
      isNewUser: true,
    };
    this.passwordRecoveryRequestUseCase.execute({ request: request }).then(
      (data) => {
        this.loggerService.debug(data);
        const at_attributes = [
          { name: AuditTrailFields.USER_EMAIL, value: email },
          { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.PW_RECOVERY_REQUEST, 0, 'SUCCESS', '', at_attributes);
        this.messageService.add(
          {
            severity: 'success',
            summary: this.translate.instant("content.successTitle"),
            detail: `${this.translate.instant("messages.password_reset_email_sent")}`,
            life: this.managerSettings?.timeoutMessages ? this.managerSettings.timeoutMessages * 1000 : 5000
          },
        );
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes = [
          { name: AuditTrailFields.USER_EMAIL, value: email },
          { name: AuditTrailFields.USER_NUM_ID, value: user.numId },
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.PW_RECOVERY_REQUEST, 0, 'ERROR', e.message, at_attributes);
        this.messageService.add(
          {
            severity: 'error',
            summary: this.translate.instant("content.errorTitle"),
            detail: `${this.translate.instant("messages.error_resetting_password")}`
          },
        );
      }
    )
    // .finally(() => {
    //   this.isLoading = false;
    // });
  }

  settingsRetry: boolean = false;
  refreshSettings(_callback: Function) {
    this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
      (data) => {
        if (data.isActive && data.settings) {
          const settings = data.settings;
          this.localStorageService.setSessionSettings(settings);
          this.managerSettings = settings;
          this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
              if (data) {
                this.konektorProperties = data;
              }
              _callback();
            },
            error: (e) => {
              this.loggerService.error('Error Retrieving Konektor Properties:');
              this.loggerService.error(e);
              const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_KONEKTOR_PROPERTIES, 0, 'ERROR', '', at_attributes);
            }
          });
        }
      },
      (e) => {
        this.loggerService.error('Error Retrieving Manager Settings:');
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.APPLICATION_ID, value: environment.application },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
        if (!this.settingsRetry) {
          this.settingsRetry = true;
          this.refreshSettings(_callback);
        }
        else {
          this.settingsRetry = false;
        }
      },
    );
  }

  userVerified(verified: boolean) {
    this.userIsVerified = verified;
  }

  private clearRejectTimeout() {
    if (this.rejectTimeout) {
      clearTimeout(this.rejectTimeout);
    }
    this.rejectTimeout = null;
  }
}
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserApplicationsPageComponent } from './user-applications-page/user-applications-page.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';
import { ApplicationsPageComponent } from './applications-page/applications-page.component';

const routes: Routes = [
  {
    path: 'apps',
    component: ApplicationsPageComponent,
    canActivate: [AuthGuard, NavigationGuard]
  },
  {
    path: 'assign',
    component: UserApplicationsPageComponent,
    canActivate: [AuthGuard, NavigationGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ApplicationPagesRoutingModule { }

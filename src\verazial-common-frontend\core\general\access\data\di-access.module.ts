import { CommonModule } from "@angular/common";
import { AccessRepository } from "../domain/repository/access.repository";
import { AddAccessUseCase } from "../domain/use-case/add-access.use-case";
import { DeleteAccessByIdUseCase } from "../domain/use-case/delete-access-by-id.use-case";
import { GetAccessByIdUseCase } from "../domain/use-case/get-access-by-id.use-case";
import { GetAllAccessesUseCase } from "../domain/use-case/get-all-accesses.use-case";
import { UpdateAccessByIdUseCase } from "../domain/use-case/update-access-by-id.use-case";
import { AccessRepositoryImpl } from "./repository/access-impl.repository";
import { NgModule } from "@angular/core";

const addAccessUseCaseFactory =
    (accessRepository: AccessRepository) => new AddAccessUseCase(accessRepository);

const getAllAccessesUseCaseFactory =
    (accessRepository: AccessRepository) => new GetAllAccessesUseCase(accessRepository);

const getAccessByIdUseCaseFactory =
    (accessRepository: AccessRepository) => new GetAccessByIdUseCase(accessRepository);

const deleteAccessByIdUseCaseFactory =
    (accessRepository: AccessRepository) => new DeleteAccessByIdUseCase(accessRepository);

const updateAccessByIdUseCaseFactory =
    (accessRepository: AccessRepository) => new UpdateAccessByIdUseCase(accessRepository);

export const addAccessUseCaseProvider = {
    provide: AddAccessUseCase,
    useFactory: addAccessUseCaseFactory,
    deps: [AccessRepository]
}

export const getAllAccessesUseCaseProvider = {
    provide: GetAllAccessesUseCase,
    useFactory: getAllAccessesUseCaseFactory,
    deps: [AccessRepository]
}

export const getAccessByIdUseCaseProvider = {
    provide: GetAccessByIdUseCase,
    useFactory: getAccessByIdUseCaseFactory,
    deps: [AccessRepository]
}

export const deleteAccessByIdUseCaseProvider = {
    provide: DeleteAccessByIdUseCase,
    useFactory: deleteAccessByIdUseCaseFactory,
    deps: [AccessRepository]
}

export const updateAccessByIdUseCaseProvider = {
    provide: UpdateAccessByIdUseCase,
    useFactory: updateAccessByIdUseCaseFactory,
    deps: [AccessRepository]
}

@NgModule({
    providers: [
        addAccessUseCaseProvider,
        getAllAccessesUseCaseProvider,
        getAccessByIdUseCaseProvider,
        deleteAccessByIdUseCaseProvider,
        updateAccessByIdUseCaseProvider,
        { provide: AccessRepository, useClass: AccessRepositoryImpl }
    ],
    imports: [
        CommonModule,
    ]
})
export class DiAccessModule { }
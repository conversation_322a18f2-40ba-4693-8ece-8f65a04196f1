import { DOCUMENT } from '@angular/common';
import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import Chart, { ChartType } from 'chart.js/auto';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { FilterEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/filter.entity';
import { MessageService } from 'primeng/api';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { ActionEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/action.entity';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { GetAllLicensesUseCase } from 'src/verazial-common-frontend/core/general/license/domain/use-cases/get-all-licenses.use-case';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { CountActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-request.entity';
import { CountActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/count-actions.use-case';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { Group } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-response.entity';

interface PageEvent {
    first: number;
    rows: number;
    page: number;
    pageCount: number;
}

@Component({
    selector: 'app-verification-page',
    templateUrl: './verification-page.component.html',
    styleUrl: './verification-page.component.css',
    providers: [MessageService]
})


export class VerificationPageComponent implements OnInit {


    selectedApp: string = "";
    appOptions: Array<any> = [];
    first: number = 0;
    rows: number = 5;
    page: number = 0;
    pageCount: number = 0;
    dataAvailable: boolean = false;

    chart: any;
    pieChart: any;
    hChart: any;
    dChart: any;
    lChartVerification: any;
    lChartVerificationResult: any;
    lChartVerificationLocResult: any;
    lChartVerificationLoc: any;

    verificationsNumberTotal: number = 0;
    verificationsNumber: number[] = [];

    verificationsNumberTotalOk: number = 0;
    verificationsNumberOk: number[] = [];

    verificationsNumberFailed: number[] = [];
    verificationsNumberTotalFailed: number = 0;

    verificationsNumberTotalQ: number = 0;
    verificationsNumberQ: number[] = [];

    verificationsNumberTotalTimeout: number = 0;
    verificationsNumberTimeout: number[] = [];

    verificationsNumberTotalServerError: number = 0;
    verificationsNumberServerError: number[] = [];

    verificationsNumberTotalO: number = 0;
    verificationsNumberO: number[] = [];

    verificationsNumberLTotal: number = 0;
    verificationsNumberL: number[] = [];

    verificationsNumberLOk: number[] = [];
    verificationsNumberLFailed: number[] = [];
    verificationsNumberLQ: number[] = [];
    verificationsNumberLServerError: number[] = [];
    verificationsNumberLTimeout: number[] = [];
    verificationsNumberLO: number[] = [];

    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";

    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
        subject: '',
    });


    actionsData: ActionEntity[] = [];
    showSpinners: boolean = true;
    locations: any[] = [];

    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;

    constructor(
        private countActionsUseCase: CountActionsUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getAllTenantLicensesUseCase: GetAllLicensesUseCase,
        private translate: TranslateService,
        private loggerService: ConsoleLoggerService,
        private reportsService: ReportsService,
        private messageService: MessageService,
        private localStorageService: LocalStorageService,
        private fb: FormBuilder) { }


    onPageChange(event: PageEvent) {

        this.first = event.first;
        this.rows = event.rows;


        this.createLineChartLoc(event.first, event.first + event.rows);
        this.createLineChartLocResult(event.first, event.first + event.rows);

    }

    ngOnInit(): void {

        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);

        this.appOptions.push({ "name": "menu.all" });

        this.selectedApp = environment.applicationDefault;

        this.getConfigurationData();

    }

    getConfigurationData() {

        this.locations = [];

        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                const currentTenant = data.tenantId;

                this.getAllTenantLicensesUseCase.execute({ tenantId: currentTenant }).then(
                    (data) => {
                        this.locations = Array.from(
                            new Set(
                                data.map((license) => license.location?.loc1).filter((loc1): loc1 is string => loc1 !== undefined)
                            )
                        );

                        this.locations.push(this.translate.instant("titles.unknown"));
                        this.getAllActions();
                    },
                    (error) => this.handleError(error)
                );
            },
            error: (e) => this.handleError(e)
        });
    }

    handleError(error?: any) {
        this.loggerService.error("Error get config from manager: " + error);
        this.locations = [this.translate.instant("titles.unknown")];
        setTimeout(() => this.getAllActions(), 1000);
    }

    initializeVerificationArrays(length: number) {
        this.verificationsNumberLOk = new Array(length).fill(0);
        this.verificationsNumberLFailed = new Array(length).fill(0);
        this.verificationsNumberLQ = new Array(length).fill(0);
        this.verificationsNumberLServerError = new Array(length).fill(0);
        this.verificationsNumberLO = new Array(length).fill(0);
        this.verificationsNumberLTotal = 0;
        this.verificationsNumberL = new Array(length).fill(0);

        this.verificationsNumber = new Array(4).fill(0);
        this.verificationsNumberOk = new Array(4).fill(0);
        this.verificationsNumberFailed = new Array(4).fill(0);
        this.verificationsNumberQ = new Array(4).fill(0);
        this.verificationsNumberTimeout = new Array(4).fill(0);
        this.verificationsNumberServerError = new Array(4).fill(0);
        this.verificationsNumberO = new Array(4).fill(0);
        this.verificationsNumberTotal = 0;
    }

    async getResultForTechnology(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;
        const actionFilterCount: FilterEntity = {
            condition: {
                path: "actionName",
                value: "MCH_VRF",
            },
        }
        paramCount.filters.push(actionFilterCount);

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }


        if (this.selectedApp != "menu.all") {
            const appFilterCount: FilterEntity = {
                condition: {
                    path: "applicationId",
                    value: this.selectedApp,
                },
            }
            paramCount.filters.push(appFilterCount);
        }

        paramCount.groupByAttributePath.push("commonAttributes.technologyId");
        paramCount.groupByAttributePath.push("commonAttributes.actionResult");


        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.loggerService.debug(countActions);

                countActions.groupByResults.forEach(group => {

                    if (group.groupValue == "FINGER") {
                        this.verificationsNumber[0] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 0);
                        });
                    }
                    else if (group.groupValue == "FACE") {
                        this.verificationsNumber[1] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 1);
                        });
                    }
                    else if (group.groupValue == "IRIS") {
                        this.verificationsNumber[2] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 2);
                        });
                    }
                    else {
                        this.verificationsNumber[3] += group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addTechActionResult(subGroup.groupValue, subGroup.groupTotal, 3);
                        });
                    }

                });

            }
        );
    }

    async getResultForLocation(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;
        const actionFilterCount: FilterEntity = {
            condition: {
                path: "actionName",
                value: "MCH_VRF",
            },
        }
        paramCount.filters.push(actionFilterCount);


        if (this.selectedApp != "menu.all") {
            const appFilterCount: FilterEntity = {
                condition: {
                    path: "applicationId",
                    value: this.selectedApp,
                },
            }
            paramCount.filters.push(appFilterCount);
        }

        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }


        paramCount.groupByAttributePath.push("commonAttributes.locationId");
        paramCount.groupByAttributePath.push("commonAttributes.actionResult");

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {

                this.loggerService.debug(countActions);

                countActions.groupByResults.forEach(group => {

                    var index = this.locations.indexOf(group.groupValue);

                    if (index != -1) {
                        this.verificationsNumberL[index] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addLocActionResult(subGroup.groupValue, subGroup.groupTotal, index);
                        });
                    }
                    else {
                        this.verificationsNumberL[this.locations.length - 1] = group.groupTotal;
                        group.subGroups.forEach(subGroup => {
                            this.addLocActionResult(subGroup.groupValue, subGroup.groupTotal, this.locations.length - 1);
                        });
                    }
                });
            }
        );
    }

    async getActionApplicationOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {

        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;


        if (this.datesForm.controls['subject'].value != "") {
            const actionFilterCount: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: this.datesForm.controls['subject'].value,
                },
            }
            paramCount.filters.push(actionFilterCount);
        }

        paramCount.groupByAttributePath.push("applicationId");

        return this.countActionsUseCase.execute(paramCount).then(
            (countActions) => {
                this.appOptions = [];
                this.appOptions.push({ "name": "menu.all" });
                countActions.groupByResults.forEach((action: Group) => {
                    this.appOptions.push({ "name": action.groupValue });
                });
            }
        );
    }


    async getAllActions() {
        this.initializeVerificationArrays(this.locations.length);

        this.pageCount = Math.ceil(this.locations.length / this.rows);

        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);
        this.dateError = false;
        this.dateErrorMessage = "";

        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            return;
        }

        if (dateStartDate < dateEndDate) {

            try {

                await Promise.all([
                    this.getActionApplicationOptions(dateStartDate, dateEndDate),
                    this.getResultForTechnology(dateStartDate, dateEndDate),
                    this.getResultForLocation(dateStartDate, dateEndDate),
                ]);
            } catch (error) {
                this.loggerService.error("Error getting actions: ");
                this.loggerService.error(error!);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translate.instant("titles.error_operation"),
                    detail: this.translate.instant("messages.error_getting_actions"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.hideSpinners();
            }

            this.hideSpinners();

            setTimeout(() => {
                this.createLineChart();
                this.createLineChartResult();
                this.createLineChartLoc(this.first, this.rows)
                this.createLineChartLocResult(this.first, this.rows);
            }, 100);
        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            this.hideSpinners();
        }
    }

    addTechActionResult(actionResult: string, total: number, techIndex: number) {
        switch (actionResult) {
            case "SUCCESS":
                this.verificationsNumberTotalOk = total;
                this.verificationsNumberOk[techIndex] = total;
                break;

            case "Error NOT_VERIFY":
            case "Error NO_IDENTITY":
                this.verificationsNumberTotalFailed = total;
                this.verificationsNumberFailed[techIndex] = total;
                break;

            case "Error QUALITY_ERROR":
                this.verificationsNumberTotalQ = total;
                this.verificationsNumberQ[techIndex] = total;
                break;

            case "Error TIMEOUT_ERROR":
                this.verificationsNumberTotalTimeout = total;
                this.verificationsNumberTimeout[techIndex] = total;
                break;

            default:
                if (actionResult.includes("_ERROR")) {
                    this.verificationsNumberServerError[techIndex] += total;
                    this.verificationsNumberTotalServerError += total;
                }
                else {
                    this.verificationsNumberO[techIndex] += total;
                    this.verificationsNumberTotalO += total;
                }
                break;
        }
    }

    addLocActionResult(actionResult: string, total: number, techIndex: number) {

        switch (actionResult) {
            case "SUCCESS":
                this.verificationsNumberLOk[techIndex] = total;
                break;

            case "Error NOT_VERIFY":
            case "Error NO_IDENTITY":
                this.verificationsNumberLFailed[techIndex] = total;
                break;

            case "Error QUALITY_ERROR":
                this.verificationsNumberLQ[techIndex] = total;
                break;

            case "Error TIMEOUT_ERROR":
                this.verificationsNumberLTimeout[techIndex] = total;
                break;

            default:
                if (actionResult.includes("_ERROR")) {
                    this.verificationsNumberLServerError[techIndex] += total;
                }
                else {
                    this.verificationsNumberLO[techIndex] += total;
                }
                break;
        }
    }

    checkEq(str1: string, str2: string) {
        var arr1 = str1.split('');
        var arr2 = str2.split('');
        var counter = 0;
        for (var i = 0; i < arr1.length; i++) {
            if (arr1[i] == arr2[i]) {
                counter++;
            }
        }
        return counter;
    }

    createLineChart() {

        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");
        var option4 = this.translate.instant("titles.unknown");
        var optionR = this.translate.instant("titles.requests");
        var noData = this.translate.instant("messages.no_data_found");

        const labels = [option1, option2, option3, option4];

        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: '# ' + optionR,
                    data: this.verificationsNumber,
                    backgroundColor: environment.colorRequest,
                    barThickness: environment.barThickness,
                    xAxes: [{
                        id: 'x',

                        display: true,
                        title: {
                            display: true,
                            text: ''

                        }
                    }],
                    yAxes: [{
                        id: 'y',
                        display: true,
                        title: {
                            display: true,
                            text: ''
                        }
                    }]
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled,
                    }
                }
            },
        };


        var index = 0;
        var noDataAvailable = true;
        for (index = 0; index < config.data.datasets[0].data.length; index++) {

            if (config.data.datasets[0].data[index] > 0)
                noDataAvailable = false;

        }


        if (this.lChartVerification == null) {

            if (noDataAvailable) {
                config.options.plugins.title.text = noData;
                config.data.datasets[0].label = noData;
                config.data.datasets[0].backgroundColor = environment.colorDisabled,
                    config.data.datasets[0].data = [5, 10, 15, 20];
                this.lChartVerification = new Chart("lChartVerification", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: config.data,
                    options: config.options

                })

            }
            else {

                config.options.plugins.title.text = "";
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.verificationsNumber;
                this.lChartVerification = new Chart("lChartVerification", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: config.data,
                    options: config.options

                })
            }


        }
        else {

            if (noDataAvailable) {

                config.options.plugins.title.text = noData;
                config.data.datasets[0].label = noData;
                config.data.datasets[0].backgroundColor = environment.colorDisabled,
                    config.data.datasets[0].data = [5, 10, 15, 20];


            }
            else {

                config.options.plugins.title.text = "";
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.verificationsNumber;

            }


        }

        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }
        else {

            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;
        }

        this.lChartVerification.data = config.data;
        this.lChartVerification.options = config.options;
        this.lChartVerification.update();
    }

    createLineChartLoc(startIndex: number, endIndex: number) {


        var optionR = this.translate.instant("titles.requests");
        var noData = this.translate.instant("messages.no_data_found");

        const labels = this.locations.slice(startIndex, endIndex);
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: '# ' + optionR,
                    data: this.verificationsNumberL.slice(startIndex, endIndex),
                    backgroundColor: environment.colorRequest,
                    barThickness: environment.barThickness
                }

            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    datalabels: {
                        color: '#333333',
                        font: {
                            size: 12,
                        }
                    },
                    title: {
                        display: false,
                        text: "",
                        color: "#cccccc"
                    }
                }
            },
        };

        var index = 0;
        var noDataAvailable = true;
        for (index = 0; index < this.verificationsNumberL.length; index++) {

            if (this.verificationsNumberL[index] > 0)
                noDataAvailable = false;

        }


        if (this.lChartVerificationLoc == null) {


            if (noDataAvailable) {

                this.dataAvailable = false;
                config.options.plugins.title.display = true;
                config.options.plugins.title.text = noData;
                config.data.datasets[0].label = noData;
                config.data.datasets[0].backgroundColor = "#cccccc";

                config.data.datasets[0].data = (new Array(this.locations.length)).fill(0);
                for (index = 0; index < this.locations.length; index++) {

                    config.data.datasets[0].data[index] = 5 + (index * 5);
                }


                this.lChartVerificationLoc = new Chart("lChartVerificationLoc", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: config.data,
                    options: config.options

                });

            }
            else {

                this.dataAvailable = true;
                config.options.plugins.title.display = false;
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.verificationsNumberL.slice(startIndex, endIndex);
                this.lChartVerificationLoc = new Chart("lChartVerificationLoc", {
                    type: 'bar' as ChartType, //this denotes tha type of chart
                    data: config.data,
                    options: config.options

                });
            }



        }
        else {

            if (noDataAvailable) {


                config.data.datasets[0].label = noData;
                config.data.datasets[0].backgroundColor = environment.colorDisabled,

                    config.data.datasets[0].data = (new Array(this.locations.length)).fill(0);
                for (index = 0; index < this.locations.length; index++) {

                    config.data.datasets[0].data[index] = 5 + (index * 5);
                }

                config.options.plugins.title.display = true;
                config.options.plugins.title.text = noData;
                config.options.plugins.legend.display = false;

            }
            else {
                config.data.datasets[0].label = '# ' + optionR,
                    config.data.datasets[0].backgroundColor = environment.colorRequest;
                config.data.datasets[0].data = this.verificationsNumberL.slice(startIndex, endIndex);
                config.options.plugins.legend.display = true;
            }


        }

        if (noDataAvailable) {


            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }

        else {

            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;
        }

        this.lChartVerificationLoc.data = config.data;
        this.lChartVerificationLoc.options = config.options;
        this.lChartVerificationLoc.update();



    }


    createLineChartResult() {


        var option1 = this.translate.instant("titles.fingerPrint");
        var option2 = this.translate.instant("titles.facial");
        var option3 = this.translate.instant("titles.iris");
        var option4 = this.translate.instant("titles.unknown");

        var optionA = this.translate.instant("titles.verified");
        var optionB = this.translate.instant("titles.notVerified");
        var optionC = this.translate.instant("titles.qFailed");
        var optionD = this.translate.instant("titles.tError");
        var optionE = this.translate.instant("titles.sError");
        var optionF = this.translate.instant("titles.other");

        var noData = this.translate.instant("messages.no_data_found");

        const labels = [option1, option2, option3, option4];
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: optionA,
                    data: this.verificationsNumberOk,
                    backgroundColor: environment.colorVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionB,
                    data: this.verificationsNumberFailed,
                    backgroundColor: environment.colorNotVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionC,
                    data: this.verificationsNumberQ,
                    backgroundColor: environment.colorQualityError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionD,
                    data: this.verificationsNumberTimeout,
                    backgroundColor: environment.colorTimeoutError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionE,
                    data: this.verificationsNumberServerError,
                    backgroundColor: environment.colorServerError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionF,
                    data: this.verificationsNumberO,
                    backgroundColor: environment.colorOther,
                    barThickness: environment.barThickness
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    title: {
                        display: false,
                        text: "",
                        color: "#cccccc"
                    }
                }
            },
        };


        var noDataAvailable = true;
        var index = 0;

        var datasetIndex = 0;

        for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

            for (index = 0; index < config.data.datasets[datasetIndex].data.length; index++) {

                if (config.data.datasets[datasetIndex].data[index] > 0)
                    noDataAvailable = false;

            }

        }

        if (noDataAvailable) {

            config.options.plugins.title.display = true;
            config.options.plugins.title.text = noData;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {


                config.data.datasets[datasetIndex].label = "";
                config.data.datasets[datasetIndex].backgroundColor = environment.colorDisabled,
                    config.data.datasets[datasetIndex].data = (new Array(this.locations.length)).fill(0);
                for (index = 0; index < this.locations.length; index++) {

                    config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                }

            }
        }
        else {

            config.options.plugins.title.display = false;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionA : (datasetIndex == 1 ? optionB : (datasetIndex == 2 ? optionC : (datasetIndex == 3 ? optionD : (datasetIndex == 4 ? optionE : (datasetIndex == 6 ? optionF : "")))));
                config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorVerified : (datasetIndex == 1 ? environment.colorNotVerified : (datasetIndex == 2 ? environment.colorQualityError : (datasetIndex == 3 ? environment.colorTimeoutError : (datasetIndex == 4 ? environment.colorServerError : (datasetIndex == 6 ? environment.colorOther : "")))));
            }
        }


        if (this.lChartVerificationResult == null) {

            this.lChartVerificationResult = new Chart("lChartVerificationResult", {
                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options

            });

        }


        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }

        else {
            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;

        }

        this.lChartVerificationResult.data = config.data;
        this.lChartVerificationResult.options = config.options;
        this.lChartVerificationResult.update();



    }

    createLineChartLocResult(startIndex: number, endIndex: number) {


        var optionA = this.translate.instant("titles.verified");
        var optionB = this.translate.instant("titles.notVerified");
        var optionC = this.translate.instant("titles.qFailed");
        var optionD = this.translate.instant("titles.tError");
        var optionE = this.translate.instant("titles.sError");
        var optionF = this.translate.instant("titles.other");
        var noData = this.translate.instant("messages.no_data_found");

        const labels = this.locations.slice(startIndex, endIndex);
        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: optionA,
                    data: this.verificationsNumberLOk.slice(startIndex, endIndex),
                    backgroundColor: environment.colorVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionB,
                    data: this.verificationsNumberLFailed.slice(startIndex, endIndex),
                    backgroundColor: environment.colorNotVerified,
                    barThickness: environment.barThickness
                },
                {
                    label: optionC,
                    data: this.verificationsNumberLQ.slice(startIndex, endIndex),
                    backgroundColor: environment.colorQualityError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionD,
                    data: this.verificationsNumberLTimeout.slice(startIndex, endIndex),
                    backgroundColor: environment.colorTimeoutError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionE,
                    data: this.verificationsNumberLServerError.slice(startIndex, endIndex),
                    backgroundColor: environment.colorServerError,
                    barThickness: environment.barThickness
                },
                {
                    label: optionF,
                    data: this.verificationsNumberLO.slice(startIndex, endIndex),
                    backgroundColor: environment.colorOther,
                    barThickness: environment.barThickness
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                // Elements options apply to all of the options unless overridden in a dataset
                // In this case, we are setting the border of each horizontal bar to be 2px wide
                elements: {
                    bar: {
                        borderWidth: 2,
                    }
                },
                responsive: true,
                plugins: {
                    legend: {
                        position: undefined,
                        display: false,
                    },
                    datalabels: {
                        color: '#333333',
                        font: {
                            size: 12,
                        }
                    },
                    title: {
                        display: false,
                        text: "",
                        color: environment.colorDisabled,
                    }
                }
            },
        };


        var noDataAvailable = true;
        var index = 0;

        var datasetIndex = 0;

        for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {

            for (index = 0; index < this.verificationsNumberLOk.length; index++) {

                if (this.verificationsNumberLOk[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.verificationsNumberLFailed.length; index++) {

                if (this.verificationsNumberLFailed[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.verificationsNumberLQ.length; index++) {

                if (this.verificationsNumberLQ[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.verificationsNumberLTimeout.length; index++) {

                if (this.verificationsNumberLTimeout[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.verificationsNumberLServerError.length; index++) {

                if (this.verificationsNumberLServerError[index] > 0)
                    noDataAvailable = false;

            }

            for (index = 0; index < this.verificationsNumberLO.length; index++) {

                if (this.verificationsNumberLO[index] > 0)
                    noDataAvailable = false;

            }

        }

        if (noDataAvailable) {

            this.dataAvailable = false;
            config.options.plugins.title.display = true;
            config.options.plugins.title.text = noData;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = noData;
                config.data.datasets[datasetIndex].backgroundColor = environment.colorDisabled,
                    config.data.datasets[datasetIndex].data = (this.verificationsNumberLOk).fill(0);
                for (index = 0; index < (this.locations.length); index++) {

                    config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                }
            }
        }
        else {

            this.dataAvailable = true;
            config.options.plugins.title.display = false;

            for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionA : (datasetIndex == 1 ? optionB : (datasetIndex == 2 ? optionC : (datasetIndex == 3 ? optionD : (datasetIndex == 4 ? optionE : (datasetIndex == 6 ? optionF : "")))));
                config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorVerified : (datasetIndex == 1 ? environment.colorNotVerified : (datasetIndex == 2 ? environment.colorQualityError : (datasetIndex == 3 ? environment.colorTimeoutError : (datasetIndex == 4 ? environment.colorServerError : (datasetIndex == 6 ? environment.colorOther : "")))));
            }
        }



        if (this.lChartVerificationLocResult == null) {

            this.lChartVerificationLocResult = new Chart("lChartVerificationLocResult", {
                type: 'bar' as ChartType, //this denotes tha type of chart
                data: config.data,
                options: config.options

            });

        }
        else {

            if (noDataAvailable) {

                for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                    config.data.datasets[datasetIndex].data = (new Array(this.locations.length)).fill(0);
                    for (index = 0; index < this.locations.length; index++) {

                        config.data.datasets[datasetIndex].data[index] = 5 + (index * 5);
                    }
                }

                config.options.plugins.title.text = noData;

            }
            else {
                for (datasetIndex = 0; datasetIndex < config.data.datasets.length; datasetIndex++) {
                    config.data.datasets[datasetIndex].label = datasetIndex == 0 ? optionA : (datasetIndex == 1 ? optionB : (datasetIndex == 2 ? optionC : (datasetIndex == 3 ? optionD : (datasetIndex == 4 ? optionE : (datasetIndex == 5 ? optionF : "")))));
                    config.data.datasets[datasetIndex].backgroundColor = datasetIndex == 0 ? environment.colorVerified : (datasetIndex == 1 ? environment.colorNotVerified : (datasetIndex == 2 ? environment.colorQualityError : (datasetIndex == 3 ? environment.colorTimeoutError : (datasetIndex == 4 ? environment.colorServerError : (datasetIndex == 6 ? environment.colorOther : "")))));
                }

            }


        }


        if (noDataAvailable) {

            config.options.plugins.legend.display = false;
            config.options.plugins.title.display = true;
        }
        else {
            config.options.plugins.legend.display = true;
            config.options.plugins.title.display = false;
        }

        this.lChartVerificationLocResult.data = config.data;
        this.lChartVerificationLocResult.options = config.options;
        this.lChartVerificationLocResult.update();



    }

    enableSpinners() {


        this.actionsData = [];

        this.showSpinners = true;

        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = false;

        if (this.lChartVerification != null) {

            this.lChartVerification.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerification: ' + this.lChartVerification.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChartVerification.data.datasets.length; i++) {

                this.lChartVerification.data.datasets[i].data = [0, 0, 0, 0];

            }
            this.lChartVerification.update()

        }

        if (this.lChartVerificationLoc != null) {

            this.lChartVerificationLoc.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerificationLoc: ' + this.lChartVerificationLoc.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChartVerificationLoc.data.datasets.length; i++) {

                this.lChartVerificationLoc.data.datasets[i].data = [0, 0, 0, 0, 0];

            }
            this.lChartVerificationLoc.update();

        }

        if (this.lChartVerificationResult != null) {

            this.lChartVerificationResult.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerificationResult: ' + this.lChartVerificationResult.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChartVerificationResult.data.datasets.length; i++) {

                this.lChartVerificationResult.data.datasets[i].data = [0, 0, 0, 0];

            }
            this.lChartVerificationResult.update();

        }

        if (this.lChartVerificationLocResult != null) {

            this.lChartVerificationLocResult.options.plugins.legend.display = false;

            //this.loggerService.debug('Erasing datasets lChartVerificationLocResult: ' + this.lChartVerificationLocResult.data.datasets.length);

            var i = 0;
            for (i = 0; i < this.lChartVerificationLocResult.data.datasets.length; i++) {

                this.lChartVerificationLocResult.data.datasets[i].data = [0, 0, 0, 0, 0];

            }
            this.lChartVerificationLocResult.update();

        }
    }

    hideSpinners() {

        this.showSpinners = false;
        this.actionsData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
        this.actionsData.length == 0 ? this.showNoData2 = true : this.showNoData2 = false;
        this.actionsData.length == 0 ? this.showNoData3 = true : this.showNoData3 = false;
        this.actionsData.length == 0 ? this.showNoData4 = true : this.showNoData4 = false;
    }

}

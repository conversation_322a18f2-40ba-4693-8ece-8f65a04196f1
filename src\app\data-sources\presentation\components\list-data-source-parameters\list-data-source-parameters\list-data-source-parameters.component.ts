import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DataSourceParametersEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source-parameters.entity';
import { DeleteDataSourceParamsByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/delete-data-source-params-by-id.use-case';
import { UpdateAppDataSourceParamByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/update-app-data-source-param-by-id.use-case';
import { SourceParamTypes } from 'src/verazial-common-frontend/core/models/source-params-type.enum';


@Component({
  selector: 'app-list-data-source-parameters',
  templateUrl: './list-data-source-parameters.component.html',
  styleUrl: './list-data-source-parameters.component.css'
})
export class ListDataSourceParametersComponent implements OnInit {

  @Input() readAndWritePermissions: boolean = false;
  @Input() inputData: DataSourceParametersEntity[] = [];
  @Output() outputData = new EventEmitter<DataSourceParametersEntity[]>();
  @Output() removed = new EventEmitter<DataSourceParametersEntity>();
  @Output() edited = new EventEmitter<DataSourceParametersEntity>();

  clonedParameters: { [s: string]: DataSourceParametersEntity } = {};

  listDataSourceParamTypes = [
    {value: SourceParamTypes.API_TOKEN, label: this.translateService.instant(`content.${SourceParamTypes.API_TOKEN}`)},
    {value: SourceParamTypes.API_ENDPOINT_PARAMETER, label: this.translateService.instant(`content.${SourceParamTypes.API_ENDPOINT_PARAMETER}`)},
    {value: SourceParamTypes.API_USERNAME, label: this.translateService.instant(`content.${SourceParamTypes.API_USERNAME}`)},
    {value: SourceParamTypes.API_PASSWORD, label: this.translateService.instant(`content.${SourceParamTypes.API_PASSWORD}`)},
    {value: SourceParamTypes.API_RESULT_PARAM, label: this.translateService.instant(`content.${SourceParamTypes.API_RESULT_PARAM}`)},
    {value: SourceParamTypes.API_SEARCH_FIELD, label: this.translateService.instant(`content.${SourceParamTypes.API_SEARCH_FIELD}`)},
    {value: SourceParamTypes.LDAP_PASSWORD, label: this.translateService.instant(`content.${SourceParamTypes.LDAP_PASSWORD}`)},
    {value: SourceParamTypes.LDAP_USERNAME, label: this.translateService.instant(`content.${SourceParamTypes.LDAP_USERNAME}`)},
    {value: SourceParamTypes.LDAP_DOMAIN, label: this.translateService.instant(`content.${SourceParamTypes.LDAP_DOMAIN}`)},
    {value: SourceParamTypes.LDAP_BIND_DN, label: this.translateService.instant(`content.${SourceParamTypes.LDAP_BIND_DN}`)},
    {value: SourceParamTypes.LDAP_SEARCH_BASE, vallabelue: this.translateService.instant(`content.${SourceParamTypes.LDAP_SEARCH_BASE}`)},
    {value: SourceParamTypes.LDAP_PORT, label: this.translateService.instant(`content.${SourceParamTypes.LDAP_PORT}`)},
    {key: SourceParamTypes.LDAP_SSL, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_SSL}`)},
    {value: SourceParamTypes.LOCAL_METHOD, label: this.translateService.instant(`content.${SourceParamTypes.LOCAL_METHOD}`)},
    {value: SourceParamTypes.LOGIN_USERNAME, label: this.translateService.instant(`content.${SourceParamTypes.LOGIN_USERNAME}`)},
    {value: SourceParamTypes.LOGIN_PASSWORD, label: this.translateService.instant(`content.${SourceParamTypes.LOGIN_PASSWORD}`)},
  ];

  ngOnInit(): void {
  }

  constructor(
    private translateService: TranslateService
  ){}

  onRowEditInit(data: DataSourceParametersEntity){
    this.clonedParameters[data.id as string] = { ...data };
  }

  onRowEditSave(data: DataSourceParametersEntity){
    this.edited.emit(data);
    /*if(data.dataSourceId){
      this.updateParameter(data);
    }
    delete this.clonedParameters[data.id as string];
    this.outputData.emit(this.inputData);*/
  }

  onRowEditCancel(data: DataSourceParametersEntity, index: number){
    this.inputData[index] = this.clonedParameters[data.id as string];
    delete this.clonedParameters[data.id as string];
  }

  onRowDelete(data: DataSourceParametersEntity){
    this.removed.emit(data);
    /*if(data.dataSourceId){
      this.deleteParameter(data);
    }
    this.inputData = [...this.inputData.filter(param=> param.id != data.id)];
    this.outputData.emit(this.inputData);*/
  }

  /*updateParameter(parameter: DataSourceParametersEntity){
    this.updateDataSourceParamById.execute(parameter).then(
      (response)=>{
        this.loggerService.debug(response);
      },
      (error)=>{
        this.loggerService.error(error);
      }
    );
  }

  deleteParameter(parameter: DataSourceParametersEntity){
    this.deleteDataSourceParamsById.execute({id: parameter.id}).then(
      (data) => {
        this.loggerService.debug(data);
      },
      (error) => {
        this.loggerService.error(error);
      }
    );
  }*/

}

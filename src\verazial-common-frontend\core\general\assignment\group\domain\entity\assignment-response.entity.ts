import { TaskFlowEntity } from "src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity";
import { GroupCategoryEntity } from "../../../categories/domain/entity/group-category.entity";

export class AssignmentResponseEntity {
    id: string | undefined;
    name: string | undefined;
    description: string | undefined;
    flows: TaskFlowEntity[] | undefined;
    locations: GroupCategoryEntity[] | undefined;
    subjects: GroupCategoryEntity[] | undefined;
    schedules: GroupCategoryEntity[] | undefined;
    isRequiredWithinSchedule: boolean | undefined;
    requiredSchedule: GroupCategoryEntity[] | undefined;
    alertIfAllPerformedWithinSchedule: boolean | undefined;
    alertIfNotPerformedWithinSchedule: boolean | undefined;
    alertIfPerformedOutsideSchedule: boolean | undefined;
    usersToAlert: GroupCategoryEntity[] | undefined;
}
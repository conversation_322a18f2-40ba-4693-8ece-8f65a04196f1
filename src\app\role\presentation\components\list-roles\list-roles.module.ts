import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListRolesComponent } from './list-roles/list-roles.component';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { MessagesModule } from 'primeng/messages';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { TableModule } from 'primeng/table';
import { StepsModule } from 'primeng/steps';
import { RoleModule } from '../role/role.module';
import { AccessAssignmentModule } from '../access-assignment/access-assignment.module';
import { StepperModule } from 'primeng/stepper';
import { BadgeModule } from 'primeng/badge';
import { SelectButtonModule } from 'primeng/selectbutton';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';

@NgModule({
  declarations: [
    ListRolesComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Form */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG */
    ButtonModule,
    TableModule,
    TagModule,
    ConfirmDialogModule,
    MessagesModule,
    ToastModule,
    DialogModule,
    StepsModule,
    BadgeModule,
    SelectButtonModule,
    DropdownModule,
    CalendarModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    /* Custom */
    RoleModule,
    AccessAssignmentModule,
    EmptyModule,
  ],
  exports: [
    ListRolesComponent
  ]
})
export class ListRolesModule { }

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SubjectPageRoutingModule } from './subject-page-routing.module';
import { SubjectPageComponent } from './subject-page/subject-page.component';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { CalendarModule } from 'primeng/calendar';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { SkeletonModule } from 'primeng/skeleton';
import { DiSubjectModule } from 'src/verazial-common-frontend/core/general/subject/data/di-subject.module';


@NgModule({
  declarations: [
    SubjectPageComponent
  ],
  imports: [
    CommonModule,
    SubjectPageRoutingModule,
    CardModule,
    ToastModule,
    CalendarModule,
    TranslateModule,
    /* Foms */
    ReactiveFormsModule,
    FormsModule,
    TableModule,
    ProgressSpinnerModule,
    DropdownModule,
    ProgressBarModule,
    SkeletonModule,
    DiSubjectModule,
  ],
  exports: [
    SubjectPageComponent
  ]
})
export class SubjectPageModule { }

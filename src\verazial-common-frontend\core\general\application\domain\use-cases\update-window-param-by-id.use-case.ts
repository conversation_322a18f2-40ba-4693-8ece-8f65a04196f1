import { WindowParametersRepository } from "../repositories/window-parameters.repository";
import { WindowParametersEntity } from "../entities/window-parameters.entity";
import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";

export class UpdateWindowParamByIdUseCase implements UseCaseGrpc<WindowParametersEntity, WindowParametersEntity> {
    constructor(private windowParametersRepository: WindowParametersRepository) { }
    execute(params: WindowParametersEntity): Promise<WindowParametersEntity> {
        return this.windowParametersRepository.updateWindowParamById(params);
    }

}
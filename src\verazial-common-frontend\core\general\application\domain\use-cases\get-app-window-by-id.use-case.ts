import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationWindowEntity } from "../entities/application-window.entity";
import { ApplicationWindowRepository } from "../repositories/application-window.repository";

export class GetAppWindowByIdUseCase implements UseCaseGrpc<{ id: string, windowOrder: number }, ApplicationWindowEntity> {
    constructor(private applicationWindowRepository: ApplicationWindowRepository) { }
    execute(params: { id: string; windowOrder: number}): Promise<ApplicationWindowEntity> {
        return this.applicationWindowRepository.getAppWindowById(params)
    }
}
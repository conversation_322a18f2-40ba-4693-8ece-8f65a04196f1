<div class="container">
    <p-toast></p-toast>
    <p-confirmDialog />
    <div *ngIf="inputData.length > 0 else empty" class="content-list-flows gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2 mb-3">
            <label class="subcontainer-title">{{ "pass_datasource.datasources" | translate}}</label>
            <div class="flex flex-row gap-3">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'pass_datasource.new_datasource'| translate }}"  icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNewDataSource()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNewDataSource()"></p-button>
                </div>
            </div>
        </div>
        <p-table
            #dt
            [loading]="loading"
            [value]="inputData"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            styleClass="p-datatable-sm"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['name', 'method', 'sourceType', 'parameters']"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column-sm" pSortableColumn="name">{{'content.name' | translate}}<p-sortIcon field="applicationName"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="method">{{ 'pass_datasource.method' | translate }}<p-sortIcon field="applicationType"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="sourceType">{{ 'content.type' | translate }}<p-sortIcon field="dataSource"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="parameters">{{ 'pass_datasource.parameters' | translate }}<p-sortIcon field="status"></p-sortIcon></th>
                    <th style="width: 5%;" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="method" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="sourceType" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="listSourceTypes"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionValue="key"
                                    optionLabel="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{getApiTypeName(value)}}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{getApiTypeName(option.key)}}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th></th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td showDelay="1000" pTooltip="{{data.name}}" tooltipPosition="top" class="ellipsis-cell" (click)="editDataSource(data)">{{data.name}}</td>
                    <td showDelay="1000" pTooltip="{{data.method}}" tooltipPosition="top" class="ellipsis-cell" (click)="editDataSource(data)">{{data.method}}</td>
                    <td showDelay="1000" pTooltip="{{getApiTypeName(data.sourceType)}}" tooltipPosition="top" class="ellipsis-cell" (click)="editDataSource(data)">{{getApiTypeName(data.sourceType)}}</td>
                    <td>
                        <div class="flex align-items-center justify-content-start">
                            <label>{{ 'edit' | translate }}</label>
                            <button pButton pRipple icon="pi pi-file-edit" [text]="true" style="padding: 0; width: 1.5rem;" (click)="getParameters(data)"></button>
                        </div>
                    </td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-{{ !readAndWritePermissions ? 'eye' : 'pencil' }}" [text]="true" class="mx-2" style="padding: 0; width: 1.5rem;" (click)="editDataSource(data)"></button>
                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" [disabled]="!readAndWritePermissions" (click)="confirmationDeleteDataSource(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>

    <ng-template #empty>
        <div class="subcontainer">
            <label class="subcontainer-title">{{ "pass_datasource.datasources" | translate}}</label>
            <app-empty
                [readAndWritePermissions]="readAndWritePermissions"
                buttonLabel="pass_datasource.new_datasource"
                titleLabel="pass_datasource.no_datasource_available"
                (clicked)="createNewDataSource($event)">
            </app-empty>
        </div>
    </ng-template>

    <p-dialog [(visible)]="showNewDataSourceDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
        <ng-template pTemplate="header">
            <div></div>
            <div class="flex justify-content-center" [style]="{'color': '#495057', 'font-weight':'600', 'font-size':'14px'}">
                {{ 'pass_datasource.add_datasource' | translate }}
            </div>
        </ng-template>
        <app-data-sources (cancel)="cancelNewDataSource()" (operationStatus)="operationResult($event)"></app-data-sources>
    </p-dialog>

    <p-dialog [(visible)]="showEditDataSourceDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
        <ng-template pTemplate="header">
            <div></div>
            <div class="text-center mt-3 mb-3 text-l font-semibold">
                {{ 'pass_datasource.update_datasource' | translate }}
            </div>
        </ng-template>

        <ng-template pTemplate="content">
            <div class="flex flex-column gap-1 mx-auto" style="min-height: 16rem; max-width: 20rem" [formGroup]="form">
                <div class="field p-fluid">
                    <label class="text-sm font-semibold" for="dataSourceName">{{ 'content.name' | translate }}</label>
                    <input id="dataSourceName" formControlName="dataSourceName" pInputText id="input" type="text" />
                    <small *ngIf="!isValid('dataSourceName') && form.controls['dataSourceName'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>
                <div class="field p-fluid">
                    <label class="text-sm font-semibold" for="dataSourceName">{{ 'content.type' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listSourceTypes"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="value"
                        [(ngModel)]="selectedSourceType"
                        formControlName="sourceType"
                        id="sourceType"
                        dataKey="key"
                        [ngClass]="!isValid('sourceType') && form.controls['sourceType'].touched? 'ng-invalid ng-dirty':'' "
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedSourceType">
                                <div>{{selectedSourceType.value}}</div>
                            </div>
                        </ng-template>
                        <ng-template let-sourceType pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ sourceType.value }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <small *ngIf="!isValid('sourceType') && form.controls['sourceType'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>
                <div class="field p-fluid">
                    <label class="text-sm font-semibold" for="method">{{ 'pass_datasource.method' | translate }}</label>
                    <input id="method" formControlName="method" pInputText id="input" type="text" />
                    <small *ngIf="!isValid('method') && form.controls['method'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>

                <!-- div class="dashed-line-vertical"></div-->
                <div class="flex mb-4 justify-content-center">
                    <div class="flex pt-4 gap-3 justify-content-center">
                        <p-button (onClick)="cancelEditDataSource()"
                        [style]="{'color': '#000000', 'border': 'none', 'background': '#FFFFFF' }"
                        label="{{ 'cancel' | translate }}" />
                        <p-button (onClick)="confirmationUpdateDataSource()"
                        [disabled]="!isValid('dataSourceName') || !isValid('sourceType') || !isValid('method')? true : false"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#204887' }"
                        label="{{ 'save' | translate }}" />
                    </div>
                </div>
            </div>
        </ng-template>
    </p-dialog>

    <p-dialog [(visible)]="showEditParametersDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
        <ng-template pTemplate="header">
            <div></div>
            <div class="text-center mt-3 mb-3 text-l font-semibold">
                {{ 'pass_datasource.add_parameters' | translate }}
            </div>
        </ng-template>
        <ng-template pTemplate="content">
            <div class="flex flex-column">
                <div class="flex gap-3">
                    <app-data-source-parameters *ngIf="readAndWritePermissions" (outputData)="addNewParameters($event)"></app-data-source-parameters>
                    <div class="dashed-line-horizontal"></div>
                    <app-list-data-source-parameters
                        [readAndWritePermissions]="readAndWritePermissions"
                        [inputData]="listDataSourceParameters"
                        (edited)="updateParameter($event)"
                        (removed)="deleteParameter($event)">
                    </app-list-data-source-parameters>
                </div>
                <div class="dashed-line-vertical"></div>
                <div class="flex mb-4 justify-content-center">
                    <div class="flex pt-4 gap-3 justify-content-center">
                        <p-button (onClick)="cancelEditDataSource()"
                        [style]="{'color': '#000000', 'border': 'none', 'background': '#FFFFFF' }"
                        label="{{ 'cancel' | translate }}" />
                        <p-button (onClick)="confirmationUpdateDataSourceParameters()"
                        [disabled]="newListDataSourceParameters.length==0? true : false"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#204887' }"
                        label="{{ 'save' | translate }}" />
                    </div>
                </div>
            </div>
        </ng-template>
    </p-dialog>

    <p-dialog [(visible)]="showListParametersDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
        <ng-template pTemplate="header">
            <div></div>
            <div class="text-center mt-3 mb-3 text-l font-semibold">
                {{ 'headers.update_parameters' | translate }}
            </div>
        </ng-template>

        <ng-template pTemplate="content">
            <app-list-data-source-parameters [inputData]="listDataSourceParameters" (edited)="updateParameter($event)" (removed)="deleteParameter($event)"></app-list-data-source-parameters>
        </ng-template>
    </p-dialog>
</div>
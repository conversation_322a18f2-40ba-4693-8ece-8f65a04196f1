import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { ApplicationWindowEntity } from "../entities/application-window.entity";
import { ApplicationWindowRepository } from "../repositories/application-window.repository";

export class AddAppWindowUseCase implements UseCaseGrpc<ApplicationWindowEntity, ApplicationWindowEntity> {
    constructor(private applicationWindowRepository: ApplicationWindowRepository) { }
    execute(params: ApplicationWindowEntity): Promise<ApplicationWindowEntity> {
        return this.applicationWindowRepository.addAppWindow(params);
    }
}
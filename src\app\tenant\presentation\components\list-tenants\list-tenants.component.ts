import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { FilterService } from 'primeng/api';
import { Table } from 'primeng/table';
import { TenantStatus } from 'src/verazial-common-frontend/core/general/tenant/common/models/tenant-status.enum';
import { AddTenantRequestEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/add-tenant-request.entity';
import { TenantEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/tenant.entity';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';

@Component({
  selector: 'app-list-tenants',
  templateUrl: './list-tenants.component.html',
  styleUrl: './list-tenants.component.css'
})
export class ListTenantsComponent implements OnInit {
  @Input() readAndWritePermissions: boolean = false;
  @Input() listOfTenants: TenantEntity[] = [];
  @Output() tenantDataOutput = new EventEmitter<TenantEntity>();
  @Output() addTenantDataOutput = new EventEmitter<AddTenantRequestEntity>();
  @Output() deleteTenant = new EventEmitter<TenantEntity>();

  showTenantId: boolean = false;

  selectedTenants: TenantEntity[] = [];

  showNewTenantDialog: boolean = false;
  showTenantDBDialog: boolean = false;
  searchValue: string | undefined;
  selectedTenant: TenantEntity | undefined;
  isDisableSaveButton: boolean = true;
  tenantData: TenantEntity | undefined;
  tenantId: string | undefined;

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  formGroupDate: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  constructor(
    private filterService: FilterService,
    private checkPermissionsService: CheckPermissionsService,
  ) {
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnInit(): void {
    this.showTenantId = this.checkPermissionsService.hasReadPermissions(AccessIdentifier.SHOW_TENANT_IDS);
  }

  onTenantsSelectionChange(event: any){

  }

  editDatabase(data: TenantEntity){
    this.tenantId = data.id;
    this.showTenantDBDialog = true;
  }

  editTenant(tenant: TenantEntity){
    this.selectedTenant = tenant;
    this.showNewTenantDialog = true;
  }

  onDeleteTenant(tenant: TenantEntity){
    this.deleteTenant.emit(tenant);
  }

  getTenantData(tenant: TenantEntity){
    this.tenantData = tenant;
    if(this.tenantData){
      this.isDisableSaveButton = false;
    }
  }

  getTenantStatus(status: TenantStatus){
    switch (status) {
      case TenantStatus.ACTIVE:
        return 'content.active';
      case TenantStatus.INACTIVE:
        return 'content.inactive';
      case TenantStatus.UNDER_VERIFICATION:
        return 'content.under_verification';
    }
  }

  saveTenant(data: AddTenantRequestEntity){
    this.tenantData = data.tenantModel;
    if (this.tenantData?.id == undefined) {
      // this.loggerService.debug('Add Tenant');
      // this.loggerService.debug(data);
      this.addTenantDataOutput.emit(data);
    }
    else {
      // this.loggerService.debug('Update Tenant');
      // this.loggerService.debug(this.tenantData);
      this.tenantDataOutput.emit(this.tenantData);
    }
    this.showNewTenantDialog = false;
  }

  cancelTenant(){
    this.tenantData = undefined;
    this.selectedTenant = undefined;
    this.showNewTenantDialog = false;
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['createdAt'].value){
      this.rangeDates = null;
      this.formGroup.reset();
    }
    if(!event.filters['updatedAt'].value){
      this.rangeDates = null;
      this.formGroupDate.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    if(field === 'createdAt'){
      this.rangeDates = this.formGroup.get('date')?.value;
    }
    else if(field === 'updatedAt'){
      this.rangeDates = this.formGroupDate.get('date')?.value;
    }
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

}


import { Mapper } from "src/verazial-common-frontend/core/mapper";
import { AccessEntity } from "../../domain/entity/access.entity";
import { AccessGrpcModel } from "src/verazial-common-frontend/core/generated/access/access_pb";

export class AccessMapper extends Mapper<AccessGrpcModel, AccessEntity> {
    override mapFrom(param: AccessGrpcModel): AccessEntity {
        return {
            id: param.getId(),
            name: param.getName(),
            application: param.getApplication(),
            accessCode: param.getAccesscode(),
            type: param.getType(),
            path: param.getPath(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6)),
        }
    }
    override mapTo(param: AccessEntity): AccessGrpcModel {
        let accessModel = new AccessGrpcModel();
        accessModel.setId(param.id!);
        accessModel.setName(param.name!);
        accessModel.setApplication(param.application!);
        accessModel.setAccesscode(param.accessCode!);
        accessModel.setType(param.type!);
        accessModel.setPath(param.path!);
        return accessModel;
    }

}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RolePageRoutingModule } from './role-page-routing.module';
import { RolePageComponent } from './role-page/role-page.component';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { ListRolesModule } from '../../components/list-roles/list-roles.module';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { StepsModule } from 'primeng/steps';
import { ToastModule } from 'primeng/toast';
import { GroupInfoModule } from 'src/app/group/presentation/components/group-info/group-info.module';
import { RoleModule } from '../../components/role/role.module';
import { MessagesModule } from 'primeng/messages';


@NgModule({
  declarations: [
    RolePageComponent
  ],
  imports: [
    CommonModule,
    RolePageRoutingModule,
    EmptyModule,
    LoadingSpinnerModule,
    ListRolesModule,
    ToastModule,
    ButtonModule,
    TranslateModule,
    ConfirmDialogModule,
    DialogModule,
    StepsModule,
    GroupInfoModule,
    RoleModule,
    MessagesModule
  ]
})
export class RolePageModule { }

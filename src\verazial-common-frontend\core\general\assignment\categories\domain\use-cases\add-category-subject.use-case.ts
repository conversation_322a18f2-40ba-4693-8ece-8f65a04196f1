import { UseCaseGrpc } from "src/verazial-common-frontend/core/use-case-grpc";
import { CategorySubjectEntity } from "../entity/category-subject.entity";
import { GroupCategoryRepository } from "../repository/group-category.repository";

export class AddCategorySubjectUseCase implements UseCaseGrpc<{ listOfSubjects: CategorySubjectEntity[] }, CategorySubjectEntity[]> {
    constructor(private groupCategoryRepository: GroupCategoryRepository) { }
    execute(params: { listOfSubjects: CategorySubjectEntity[]; }): Promise<CategorySubjectEntity[]> {
        return this.groupCategoryRepository.addCategorySubject(params);
    }
}